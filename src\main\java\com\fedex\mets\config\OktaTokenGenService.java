package com.fedex.mets.config;


import com.fedex.airops.mss.auth.AuthResult;
import com.fedex.airops.mss.auth.SimpleAuthConfigurationManager;
import com.fedex.airops.mss.auth.SimpleOktaAuthentication;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.net.MalformedURLException;

@Service
public class OktaTokenGenService {
	private static final Logger logger = LoggerFactory.getLogger(OktaTokenGenService.class);

	private static String accessToken;

	@Autowired
	private Environment env;

	public String generateToken() throws MalformedURLException {
		SimpleAuthConfigurationManager.setAppToAppTokenCacheEnabled(true);

		SimpleAuthConfigurationManager.setClientId(env.getProperty("okta.client.id"));
		logger.info("***Client ID**** : "+env.getProperty("okta.client.id"));
		SimpleAuthConfigurationManager.setIssuer(env.getProperty("okta.issuer"));
		logger.info("---Issuer--- : "+env.getProperty("okta.issuer"));
		//Local we need to enable this proxy setting
		SimpleAuthConfigurationManager.setProxy(null);
		SimpleAuthConfigurationManager.setScope("Custom_Scope");
		// for webapps, you probably want "openid profile" and for apptoapp, you probably want "Custom_Scope"
		SimpleAuthConfigurationManager.applyToGlobalAuthenticationFacadeInstance();
		AuthResult r = SimpleOktaAuthentication.globalInstance()
				.authenticateAppToApp(env.getProperty("okta.client.secret"));
		logger.info("----secret----: "+env.getProperty("okta.client.secret"));


		if (r.hasError()) {
			// authentication/misc error
			Throwable e = r.getError();
			String msg = r.getErrorMessage();
			logger.info(e.getMessage()+ " "+msg);
		} else {
			// if you want DSS specific logic
			boolean usedDss = r.usedDss();

			accessToken = r.getAccessToken(); // this will be the Okta Access Token JWT OR the MSS token from DSS
			// there will never be a client ID token in an App To App scenario
			logger.info("******Access Token********* : "+accessToken);
		}
		return accessToken;
	}

}