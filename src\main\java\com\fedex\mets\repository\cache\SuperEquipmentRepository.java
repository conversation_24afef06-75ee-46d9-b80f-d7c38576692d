package com.fedex.mets.repository.cache;

import com.fedex.mets.entity.cache.SuperEquipment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SuperEquipmentRepository extends JpaRepository<SuperEquipment, String> {

    @Query(value="select * from SUPER_EQUIPMENT where acn=:acn",nativeQuery = true)
    public SuperEquipment getSuperEquipmentByAcn(String acn);
    @Query(value = "SELECT ACN, CURRENT_GATE from SUPER_EQUIPMENT", nativeQuery = true)
    public String getCurrentGateDetails();

    @Query(value = "SELECT ACN, CURRENT_GATE from SUPER_EQUIPMENT", nativeQuery = true)
    public List<Object[]> getCurrentGateListDetails();

    @Query(value = "SELECT ACN, CURRENT_GATE from SUPER_EQUIPMENT WHERE ACN=:acn", nativeQuery = true)
    public String getCurrentGate(@Param("acn") String acn);

    @Query(value = "SELECT CURRENT_ETIC_TEXT, OPERATIONAL_STATUS,COMMENTS from SUPER_EQUIPMENT WHERE ACN=:acn", nativeQuery = true)
    public String getSuperUpdateRequiredDetails(@Param("acn") String acn);

}
