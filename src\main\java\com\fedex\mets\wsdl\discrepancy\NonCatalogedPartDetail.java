
package com.fedex.mets.wsdl.discrepancy;

import java.math.BigDecimal;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for NonCatalogedPartDetail complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="NonCatalogedPartDetail">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="nonCatalogedOid" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="description" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="keyword" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="catalogedNoun" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="catalogedNbr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="figureId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="itemId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "NonCatalogedPartDetail", propOrder = {
    "nonCatalogedOid",
    "description",
    "keyword",
    "catalogedNoun",
    "catalogedNbr",
    "figureId",
    "itemId"
})
public class NonCatalogedPartDetail {

    @XmlElement(required = true)
    protected BigDecimal nonCatalogedOid;
    @XmlElement(required = true)
    protected String description;
    @XmlElement(required = true)
    protected String keyword;
    @XmlElement(required = true)
    protected String catalogedNoun;
    @XmlElement(required = true)
    protected String catalogedNbr;
    @XmlElement(required = true)
    protected String figureId;
    @XmlElement(required = true)
    protected String itemId;

    /**
     * Gets the value of the nonCatalogedOid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getNonCatalogedOid() {
        return nonCatalogedOid;
    }

    /**
     * Sets the value of the nonCatalogedOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setNonCatalogedOid(BigDecimal value) {
        this.nonCatalogedOid = value;
    }

    /**
     * Gets the value of the description property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDescription() {
        return description;
    }

    /**
     * Sets the value of the description property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDescription(String value) {
        this.description = value;
    }

    /**
     * Gets the value of the keyword property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKeyword() {
        return keyword;
    }

    /**
     * Sets the value of the keyword property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKeyword(String value) {
        this.keyword = value;
    }

    /**
     * Gets the value of the catalogedNoun property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCatalogedNoun() {
        return catalogedNoun;
    }

    /**
     * Sets the value of the catalogedNoun property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCatalogedNoun(String value) {
        this.catalogedNoun = value;
    }

    /**
     * Gets the value of the catalogedNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCatalogedNbr() {
        return catalogedNbr;
    }

    /**
     * Sets the value of the catalogedNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCatalogedNbr(String value) {
        this.catalogedNbr = value;
    }

    /**
     * Gets the value of the figureId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFigureId() {
        return figureId;
    }

    /**
     * Sets the value of the figureId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFigureId(String value) {
        this.figureId = value;
    }

    /**
     * Gets the value of the itemId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getItemId() {
        return itemId;
    }

    /**
     * Sets the value of the itemId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setItemId(String value) {
        this.itemId = value;
    }

}
