package com.fedex.mets.service.retrieval;

import com.fedex.mets.config.MachSoapClientConfig;
import com.fedex.mets.config.OktaTokenGenService;
import com.fedex.mets.data.ServiceWarningDays;
import com.fedex.mets.util.IServerConstants;
import com.fedex.mets.util.ServerDateHelper;
import com.fedex.mets.wsdl.aicraftStatus.GetServiceWarningDaysRequest;
import com.fedex.mets.wsdl.aicraftStatus.GetServiceWarningDaysResponse;
import com.fedex.mets.wsdl.discrepancy.AuthSourceSysType;
import com.fedex.mets.wsdl.discrepancy.SessionType;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

@SuppressWarnings("unused")
@Service
@RequiredArgsConstructor
public class WarningDaysService {

    private static final Logger logger = LoggerFactory.getLogger(DiscrepancyDetailService.class);

    @Autowired
    private MachSoapClientConfig wb;

    @Autowired
    private OktaTokenGenService oktaTokenGenService;




    @SuppressWarnings("unchecked")
    public ServiceWarningDays getServiceWarningDays(
            String acn,
            String userId) {

        logger.info("Getting serviceWarningDays details for Aircraft >" + acn);
        ServiceWarningDays serviceWarningDays = new ServiceWarningDays();
        try {
            String tokenId = this.oktaTokenGenService.generateToken();
            GetServiceWarningDaysResponse response = getWarningDays(
                    acn, userId, tokenId);

            if (response != null && response.getServiceWarningDays() != null && response.getServiceWarningDays().size() > 0) {
                System.out.println("The formatted date:"+convertTimeFormat(response.getServiceWarningDays().stream().filter(
                        s -> s.getType().value().equalsIgnoreCase("Security Chk")).map(val -> String.valueOf(val.getExpiryDate())).findFirst().orElse(null)));
                serviceWarningDays.setServiceChk(convertTimeFormat(response.getServiceWarningDays().stream().filter(
                        s -> s.getType().value().equalsIgnoreCase("Service Chk")).map(val -> String.valueOf(val.getExpiryDate())).findFirst().orElse(null)));
                serviceWarningDays.setTireDaily(convertTimeFormat(response.getServiceWarningDays().stream().filter(
                        s -> s.getType().value().equalsIgnoreCase("Tire/Daily")).map(val -> String.valueOf(val.getExpiryDate())).findFirst().orElse(null)));
                serviceWarningDays.setSecurityChk(convertTimeFormat(response.getServiceWarningDays().stream().filter(
                        s -> s.getType().value().equalsIgnoreCase("Security Chk")).map(val -> String.valueOf(val.getExpiryDate())).findFirst().orElse(null)));
            } else {
                logger.info(IServerConstants.ERROR, "No data found for the given ACN");
            }
        } catch (Exception e) {
            logger.warn(
                    "ERROR MetsRetrieval Servlet getServiceWarningDays() e >> "
                            + e.getMessage(), e);
        }
        return serviceWarningDays;
    }


    public GetServiceWarningDaysResponse getWarningDays(
            String acn, String userId, String tokenId) throws Exception {

        logger.info("______getServiceWarningDaysRequest for acn:" + acn);

        GetServiceWarningDaysRequest getServiceWarningDaysRequest = new GetServiceWarningDaysRequest();

        SessionType sessionType = new SessionType();
        sessionType.setUserId(userId);
        sessionType.setToken(tokenId);
        sessionType.setAuthSourceSysName(AuthSourceSysType.LDAP);
        getServiceWarningDaysRequest.setAcn(acn);
        getServiceWarningDaysRequest.setSession(sessionType);

        GetServiceWarningDaysResponse resp = (GetServiceWarningDaysResponse) wb.app1webServiceTemplate().marshalSendAndReceive(getServiceWarningDaysRequest);
        return resp;

    }

    public static String convertTimeFormat(String inputTime) {
        try {
            OffsetDateTime offsetDateTime = OffsetDateTime.parse(inputTime);
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("ddMMMyy HH:mm", Locale.ENGLISH);
            return offsetDateTime.format(outputFormatter);

        } catch (Exception e) {
            logger.error("Error parsing or formatting the time: " + e.getMessage());
            return null;
        }
    }

}
