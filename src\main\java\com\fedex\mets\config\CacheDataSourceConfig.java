package com.fedex.mets.config;

import com.fedex.mets.util.DecryptionUtil;
import jakarta.persistence.EntityManagerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        basePackages = "com.fedex.mets.repository.cache", // Repositories for Cache
        entityManagerFactoryRef = "cacheEntityManagerFactory",
        transactionManagerRef = "cacheTransactionManager"
)
public class CacheDataSourceConfig {


    @Value("${spring.datasource.cache.password}")
    private String encryptedPassword;

    @Value("${spring.datasource.cache.jdbc-url}")
    private String url;

    @Value("${spring.datasource.cache.username}")
    private String username;

    @Value("${spring.datasource.cache.driver-class-name}")
    private String driverClassName;

    @Bean(name = "cacheDataSource")
    public DataSource cacheDataSource() throws Exception {
        String decryptedPassword = DecryptionUtil.decrypt(encryptedPassword, "z76yf8ScxNFLZMbxC1YVRQ==");
        return DataSourceBuilder.create().username(username).url(url).driverClassName(driverClassName)
                .password(decryptedPassword).build();
    }

    @Bean(name = "cacheEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean cacheEntityManagerFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("cacheDataSource") DataSource dataSource) {
        return builder
                .dataSource(dataSource)
                .packages("com.fedex.mets.entity.cache") // Entities for Cache
                .persistenceUnit("cache") // Persistence unit name for Cache
                .build();
    }

    @Bean(name = "cacheTransactionManager")
    public PlatformTransactionManager cacheTransactionManager(
            @Qualifier("cacheEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        return new JpaTransactionManager(entityManagerFactory);
    }
}
