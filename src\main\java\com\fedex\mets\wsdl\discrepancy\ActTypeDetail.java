
package com.fedex.mets.wsdl.discrepancy;

import java.math.BigDecimal;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ActTypeDetail complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ActTypeDetail">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="userUpdateType" type="{http://www.fedex.com/airops/schemas/EnumTypes.xsd}UserUpdateTypeEnum"/>
 *         &lt;element name="DscrpActionPtTlOid" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="actTypeOid" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="tailSpecificPartOid" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="mfrPartNbr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="companyPartNbr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="desc" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="noun" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="qtyRequired" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="confirmedIctNbrs" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="createdMsn" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="msnIctStatus" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="reservationStatus" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *         &lt;element name="reqFlag" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="reviewNotes" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="actionDetail" type="{http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd}ActionUsedDetail"/>
 *         &lt;element name="nonCatalogedPartDetail" type="{http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd}NonCatalogedPartDetail"/>
 *         &lt;element name="iPCEffective" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="isFanBlade" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="bladeWeight" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ActTypeDetail", propOrder = {
    "userUpdateType",
    "dscrpActionPtTlOid",
    "actTypeOid",
    "tailSpecificPartOid",
    "mfrPartNbr",
    "companyPartNbr",
    "desc",
    "noun",
    "qtyRequired",
    "confirmedIctNbrs",
    "createdMsn",
    "msnIctStatus",
    "reservationStatus",
    "reqFlag",
    "reviewNotes",
    "actionDetail",
    "nonCatalogedPartDetail",
    "ipcEffective",
    "isFanBlade",
    "bladeWeight"
})
public class ActTypeDetail {

    @XmlElement(required = true)
    protected UserUpdateTypeEnum userUpdateType;
    @XmlElement(name = "DscrpActionPtTlOid", required = true)
    protected BigDecimal dscrpActionPtTlOid;
    @XmlElement(required = true)
    protected BigDecimal actTypeOid;
    @XmlElement(required = true)
    protected BigDecimal tailSpecificPartOid;
    @XmlElement(required = true)
    protected String mfrPartNbr;
    @XmlElement(required = true)
    protected String companyPartNbr;
    @XmlElement(required = true)
    protected String desc;
    @XmlElement(required = true)
    protected String noun;
    @XmlElement(required = true)
    protected BigDecimal qtyRequired;
    @XmlElement(required = true)
    protected String confirmedIctNbrs;
    @XmlElement(required = true)
    protected String createdMsn;
    @XmlElement(required = true)
    protected String msnIctStatus;
    protected int reservationStatus;
    @XmlElement(required = true)
    protected String reqFlag;
    @XmlElement(required = true)
    protected String reviewNotes;
    @XmlElement(required = true)
    protected ActionUsedDetail actionDetail;
    @XmlElement(required = true)
    protected NonCatalogedPartDetail nonCatalogedPartDetail;
    @XmlElement(name = "iPCEffective", defaultValue = "false")
    protected boolean ipcEffective;
    protected boolean isFanBlade;
    @XmlElement(required = true)
    protected String bladeWeight;

    /**
     * Gets the value of the userUpdateType property.
     * 
     * @return
     *     possible object is
     *     {@link UserUpdateTypeEnum }
     *     
     */
    public UserUpdateTypeEnum getUserUpdateType() {
        return userUpdateType;
    }

    /**
     * Sets the value of the userUpdateType property.
     * 
     * @param value
     *     allowed object is
     *     {@link UserUpdateTypeEnum }
     *     
     */
    public void setUserUpdateType(UserUpdateTypeEnum value) {
        this.userUpdateType = value;
    }

    /**
     * Gets the value of the dscrpActionPtTlOid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getDscrpActionPtTlOid() {
        return dscrpActionPtTlOid;
    }

    /**
     * Sets the value of the dscrpActionPtTlOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setDscrpActionPtTlOid(BigDecimal value) {
        this.dscrpActionPtTlOid = value;
    }

    /**
     * Gets the value of the actTypeOid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getActTypeOid() {
        return actTypeOid;
    }

    /**
     * Sets the value of the actTypeOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setActTypeOid(BigDecimal value) {
        this.actTypeOid = value;
    }

    /**
     * Gets the value of the tailSpecificPartOid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTailSpecificPartOid() {
        return tailSpecificPartOid;
    }

    /**
     * Sets the value of the tailSpecificPartOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTailSpecificPartOid(BigDecimal value) {
        this.tailSpecificPartOid = value;
    }

    /**
     * Gets the value of the mfrPartNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMfrPartNbr() {
        return mfrPartNbr;
    }

    /**
     * Sets the value of the mfrPartNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMfrPartNbr(String value) {
        this.mfrPartNbr = value;
    }

    /**
     * Gets the value of the companyPartNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCompanyPartNbr() {
        return companyPartNbr;
    }

    /**
     * Sets the value of the companyPartNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCompanyPartNbr(String value) {
        this.companyPartNbr = value;
    }

    /**
     * Gets the value of the desc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDesc() {
        return desc;
    }

    /**
     * Sets the value of the desc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDesc(String value) {
        this.desc = value;
    }

    /**
     * Gets the value of the noun property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNoun() {
        return noun;
    }

    /**
     * Sets the value of the noun property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNoun(String value) {
        this.noun = value;
    }

    /**
     * Gets the value of the qtyRequired property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getQtyRequired() {
        return qtyRequired;
    }

    /**
     * Sets the value of the qtyRequired property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setQtyRequired(BigDecimal value) {
        this.qtyRequired = value;
    }

    /**
     * Gets the value of the confirmedIctNbrs property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getConfirmedIctNbrs() {
        return confirmedIctNbrs;
    }

    /**
     * Sets the value of the confirmedIctNbrs property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setConfirmedIctNbrs(String value) {
        this.confirmedIctNbrs = value;
    }

    /**
     * Gets the value of the createdMsn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCreatedMsn() {
        return createdMsn;
    }

    /**
     * Sets the value of the createdMsn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCreatedMsn(String value) {
        this.createdMsn = value;
    }

    /**
     * Gets the value of the msnIctStatus property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMsnIctStatus() {
        return msnIctStatus;
    }

    /**
     * Sets the value of the msnIctStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMsnIctStatus(String value) {
        this.msnIctStatus = value;
    }

    /**
     * Gets the value of the reservationStatus property.
     * 
     */
    public int getReservationStatus() {
        return reservationStatus;
    }

    /**
     * Sets the value of the reservationStatus property.
     * 
     */
    public void setReservationStatus(int value) {
        this.reservationStatus = value;
    }

    /**
     * Gets the value of the reqFlag property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReqFlag() {
        return reqFlag;
    }

    /**
     * Sets the value of the reqFlag property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReqFlag(String value) {
        this.reqFlag = value;
    }

    /**
     * Gets the value of the reviewNotes property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReviewNotes() {
        return reviewNotes;
    }

    /**
     * Sets the value of the reviewNotes property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReviewNotes(String value) {
        this.reviewNotes = value;
    }

    /**
     * Gets the value of the actionDetail property.
     * 
     * @return
     *     possible object is
     *     {@link ActionUsedDetail }
     *     
     */
    public ActionUsedDetail getActionDetail() {
        return actionDetail;
    }

    /**
     * Sets the value of the actionDetail property.
     * 
     * @param value
     *     allowed object is
     *     {@link ActionUsedDetail }
     *     
     */
    public void setActionDetail(ActionUsedDetail value) {
        this.actionDetail = value;
    }

    /**
     * Gets the value of the nonCatalogedPartDetail property.
     * 
     * @return
     *     possible object is
     *     {@link NonCatalogedPartDetail }
     *     
     */
    public NonCatalogedPartDetail getNonCatalogedPartDetail() {
        return nonCatalogedPartDetail;
    }

    /**
     * Sets the value of the nonCatalogedPartDetail property.
     * 
     * @param value
     *     allowed object is
     *     {@link NonCatalogedPartDetail }
     *     
     */
    public void setNonCatalogedPartDetail(NonCatalogedPartDetail value) {
        this.nonCatalogedPartDetail = value;
    }

    /**
     * Gets the value of the ipcEffective property.
     * 
     */
    public boolean isIPCEffective() {
        return ipcEffective;
    }

    /**
     * Sets the value of the ipcEffective property.
     * 
     */
    public void setIPCEffective(boolean value) {
        this.ipcEffective = value;
    }

    /**
     * Gets the value of the isFanBlade property.
     * 
     */
    public boolean isIsFanBlade() {
        return isFanBlade;
    }

    /**
     * Sets the value of the isFanBlade property.
     * 
     */
    public void setIsFanBlade(boolean value) {
        this.isFanBlade = value;
    }

    /**
     * Gets the value of the bladeWeight property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBladeWeight() {
        return bladeWeight;
    }

    /**
     * Sets the value of the bladeWeight property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBladeWeight(String value) {
        this.bladeWeight = value;
    }

}
