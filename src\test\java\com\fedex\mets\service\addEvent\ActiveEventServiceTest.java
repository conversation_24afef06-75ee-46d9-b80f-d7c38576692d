package com.fedex.mets.service.addEvent;

import com.fedex.mets.dao.ActiveEvents;
import com.fedex.mets.dao.AircraftBean;
import com.fedex.mets.dao.UnReviewedEvents;
import com.fedex.mets.data.ActiveEventResults;
import com.fedex.mets.data.ListViewData;
import com.fedex.mets.repository.mets.EventsRepository;
import com.fedex.mets.util.RvDBHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.rmi.RemoteException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

public class ActiveEventServiceTest {

    @Mock
    private EventsRepository eventsRepository;

    @Mock
    private RvDBHelper rvDBHelper;

    @InjectMocks
    private ActiveEventService activeEventService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testFindActiveEvents_Success() throws RemoteException, SQLException {
        String acn = "ACN123";
        List<ActiveEvents> activeEvents = new ArrayList<>();
        ActiveEvents event = new ActiveEvents();
        event.setEventId(1);
        event.setAcn(acn);
        event.setType("NOTE");
        activeEvents.add(event);

        when(eventsRepository.findActiveEvents(anyString())).thenReturn(activeEvents);
        when(rvDBHelper.getAircraftRecordsFromRampview(anyString())).thenReturn(new AircraftBean());

        ActiveEventResults result = activeEventService.findActiveEvents(acn);

        assertEquals(1, result.getActiveEvents().size());
        assertEquals(1, result.getActiveEvents().get(0).getEventID());
    }

    @Test
    public void testFindActiveEvents_Exception() throws RemoteException {
        String acn = "ACN123";

        doThrow(new RuntimeException("Internal server error")).when(eventsRepository).findActiveEvents(anyString());

        try {
            activeEventService.findActiveEvents(acn);
        } catch (RemoteException e) {
            assertEquals("Internal server error", e.getMessage());
        }
    }

    @Test
    public void testFindUnReviewedEvents_Success() throws RemoteException {
        String acn = "ACN123";
        List<UnReviewedEvents> unReviewedEvents = new ArrayList<>();
        UnReviewedEvents event = new UnReviewedEvents();
        event.setEventId(1);
        event.setAcn(acn);
        event.setType("OOS");
        event.setRequestStatus("S");
        unReviewedEvents.add(event);

        when(eventsRepository.findUnReviewedEvents(anyString())).thenReturn(unReviewedEvents);

        List<ListViewData> result = activeEventService.findUnReviewedEvents(acn);

        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getEventID());
    }



    @Test
    public void testFindUnReviewedEvents_Exception() throws RemoteException {
        String acn = "ACN123";

        doThrow(new RuntimeException("Internal server error")).when(eventsRepository).findUnReviewedEvents(anyString());

        try {
            activeEventService.findUnReviewedEvents(acn);
        } catch (RemoteException e) {
            assertEquals("Internal server error", e.getMessage());
        }
    }
}
