package com.fedex.mets.aspect;

import com.fedex.mets.config.RequirePermission;
import jakarta.servlet.http.HttpServletRequest;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Map;
import java.util.Optional;
import java.util.Set;

@Aspect
@Component
public class AuthorizationAspect {

    Logger log= LoggerFactory.getLogger("AuthorizationAspect");

    @SuppressWarnings("unchecked")
    @Around("@annotation(requirePermission)")
    public Object checkUserPermission(ProceedingJoinPoint joinPoint, RequirePermission requirePermission) throws Throwable{
        ServletRequestAttributes requestAttributes= (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if(requestAttributes==null){
            throw new SecurityException("Request attributes could not be retrieved.");
        }
        HttpServletRequest request= requestAttributes.getRequest();

        Map<String, Set<Character>> userPermissions= Optional.ofNullable((Map<String, Set<Character>>) request.getAttribute("userPermissions"))
                .orElseThrow(()-> new SecurityException("Permissions could not be loaded."));
        String transaction= requirePermission.transaction();
        char requiredAction= requirePermission.action();


        if (requiredAction != 'B' || transaction.equals("METS")) {
            log.info("required actions is not of type C");
            if (!userPermissions.getOrDefault(transaction, Set.of()).contains(requiredAction)) {
                throw new AccessDeniedException("User does not have permission for " + transaction + " with action " + requiredAction);
            }
            return joinPoint.proceed();
        }
        //TODO NEED TO ADD FEW MORE CHECKS GOING FURTHER
        throw new AccessDeniedException("User is not authorized to modify this entity.");
    }
}
