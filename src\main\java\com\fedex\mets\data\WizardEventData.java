package com.fedex.mets.data;

import com.fedex.mets.entity.mets.EventMsns;
import com.fedex.mets.entity.mets.EventTfNotes;
import com.fedex.mets.entity.mets.EventTimers;
import lombok.*;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Vector;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
public class WizardEventData implements Serializable{		
	
	public int		eventId;

	//Client will have to set the ChangeType basing on what values have been changed from the existing data if a pending request is there in the database
	//the server will return the changeType from the database if any changes to status/comment/eitc is made the changeType value should be modified accordingly.
	//e.g. the sum of the changes on Card 3 with New Status="4"; New ETIC ="2" and "New Comment" = "1" 
	public int		changeType=0;

	//Client will have to set the ChangeType basing on what values have been changed while reviewing a request that is there in the database
	//the server will return the changeType from the database if any changes to status/comment/eitc is made the changeType value should be modified accordingly.
	//e.g. the sum of the changes on Card 3 with New Status="4"; New ETIC ="2" and "New Comment" = "1" 
	public int		reviewChangeType=0;
	
	public String	groupId;
	public String	groupTitle;//this is same as empDepartment.	
	public String	accessLevel;
	public String	ACN;
	public String	aircraftType;//Type on Client presentation.
	public String	eventType;//event type being added eg. OOS, TRK, Note or DOA
	public String	station;	
	public String	startDate;
	public String	startTime;
	public String	startDateTime;//concatenated START Date and Time in the format (yyyy-MM-dd hh:mm:ss)	
	public String	status;

	//this would be for the client to decide what value should be defaulted in the Status drop down 
	//IMPORTANT if the Event type being added is TRK or NOTE there will be no drop down boxes and the Client will have to display the value of defaultStatus.
	public String	defaultStatus;			
	public String	eticDate;
	public String	eticTime;
	public String	eticDateTime;//concatenated ETIC Date and Time in the format (yyyy-MM-dd hh:mm:ss)
	public String	eticInfo;
	public String	defaultEticInfo;//this would be for the client to decide what value should be defaulted in the EticInfo 
	public String	eticComment;
	
	public String	affectedFlightNumber;
	public String	affectedFlightDate;// client will receive a string in the format yyyyMMdd and display ddMMMyy, when updating/inserting the record, string format the client should be passing to the server is (yyyy-MM-dd 00:00:00)
	public String	affectedFlightLegNumber;
	
	//the below inbound flight details are not displayed any where on the client, but are inserted into Event_flt_info when an affected flight information is saved.
	public String	inboundFlightNumber;
	public String	inboundFlightDate;//client will receive a string in the format yyyyMMdd and display ddMMMyy, when updating/inserting the record, string format the client should be passing to the server is (yyyy-MM-dd 00:00:00)
	public String	inboundFlightLegNumber;
	
	public Vector	contactInfoOwnerVector=new Vector();//this is for the client to display valid list of Owners from the database.
	public String	contactInfoOwner;//the owner value selected on the client to add a record.
	public String	contactInfoCrew;
	public String	contactInfoContact;

	/*
	IMPORTANT this value is returned from the Server if AC_OWNER_GROUP_ID exists in the database. Client will have to show this value as Owner if a value returned.
	**/
	public String	defaultOwner;

	public boolean	addTubFile;
	public boolean	addLinkedDiscrepancy;
	
	public String	userId;
	public String	employeeName;
	public String	tokenId;//used to retrieve the MSN details from MAXI server. Client has to set this value while closing the Event.

	//outbound destination and ETA for adding a DOA Event
	public String	destination;
	public String	estimatedArrivalDate;
	public String	estimatedArrivalTime;
	public String	estimatedArrivalDateTime;//concatenated ETA Date and Time in the format (yyyy-MM-dd hh:mm:ss) added on 01/14/2003 for the new column in EVENT_DOA Table
	public String	additionalDescription;
	public String	equipmentType;//this variable is used for searching the Flights in FORTE ODS.
	public String	newStatus;//new status value the user wants to be changed to.

	//proposed new ETIC details.
	public String	newEticDate;
	public String	newEticTime;
	public String	newEticDateTime;//concatenated NEW ETIC Date and Time in the format (yyyy-MM-dd hh:mm:ss)
	public String	newEticInfo;	
	public String	newEticComment;

	public String	changeEventMessage;//this string is to hold the message from the server when card 1, 2 are displayed on the client.
	public String	changeRequestLastUpdated;//string value to hold the last Updated Date time for the record.
	public String	changeRequestCreatedDateTime;//string value to hold the createdDateTime for the change Request record.
	public String	requestStatus;//request Status in the CHANGE_REQUEST Table mostly used by the server, incase client needs the request status.
	public String	changeRequestNewStatus;//string value to hold the New_Status in the Change Request table for the record.


	public String	eventEndDate;
	public String	eventEndTime;
	public String	eventEndDateTime;//concatenated Event END Date and Time in the format (yyyy-MM-dd hh:mm:ss)
	public String	alertType;//variable declared for the Server.

	/*
	Server will return any active Timer for the Event and the list of timers. Client will have to decide if the The user will have to start or stop a Timer. 
	if there is an active timer for the Event the startDateTime will be set on the server side along with the timer name, timer id, last updated time and createdtime.
	**/	
	//this string holds the active TimerId.
	public String	activeTimerId;
	//this string holds the new TimerId value that the client intends to START while Converting an event.
	public String	timerId;
	//this is just for the display on the clinet if there is an active timer, need not be updated while starting or stoping a timer.
	public String	timerName;

	//The Server will set this value for display on client if there is any active timer.
	public String	activeTimerStartDateTime;
	//String value that holds the last updated time of an active timer in the format (yyyy-MM-dd hh:mm:ss){used while stopping a timer}
	public String	timerLastUpdated;
	//String value that holds the created Date Time of an active timer in the format (yyyy-MM-dd hh:mm:ss){used while stopping a timer}
	public String	timerCreatedDateTime;
	//Client will have to set this value if a new Timer is started, concatenated Timer Start Date and Time in the format (yyyy-MM-dd hh:mm:ss)
	public String	timerStartDateTime;
	//Client will have to set this value if a new Timer is started, concatenated Timer Stop Date and Time in the format (yyyy-MM-dd hh:mm:ss)
	// **** {Take the StartDateTime and decrement the value by a minute} **** .
	public String	timerStopDateTime;

	public String[] tubFileNote;	

	public boolean	isDOAEvent=false;//this variable is for determining if the Event type being added is a DOA Event.
	public boolean	isNewComment=false;	//this variable is set to true on the Client if the user had modified the comment while converting the event.
	public boolean	isInTransit=false;//this variable is set to true on the Server side if current Leg is in Transit.
	public boolean	checkFlightRequired;
	public boolean	submitChange=false;//this is for the client to set if the user wants to submit the change to SUPER.
	//this variable is set to true on the Client if there was an affected flight before converting and had been removed while converting the event.
	public boolean	isAffectedFlightDeleted=false;

	//for the server to set if the Request type in CHANGE_REQUEST table is pending for confiramtion and also for the client to decide for the NEXT Step.
	public boolean	isPendingRequest=false;
	public boolean	eticEnteredInError=false;//this initially is set to false but modified on client;
	public boolean  modifyPendingEvent=true;//this initially is set to true but modified on client;
	public boolean  cancelPendingEvent=false;//this initially is set to false but modified on client;

	//the below three boolean values should be changed according to the modifications on client.
	//if the user modifies any of the following set the value TRUE.
	public boolean	statusModified=false;
	public boolean	eticModified=false;
	public boolean	commentModified=false;
	public boolean 	ostModified=false;
	public boolean	eticRsnCdModified=false;
	public boolean 	eticRsnCommentModified=false;
	
	//The server will indicate if the Reporting categories will have to displayed to the Client, Also the Client can decide the same with the values it already holds from List view or Detail view
	public boolean  displayReportingCategories=false;
	public boolean  startNIWTimer=false;//To start a NIW Timer, this falg is set on the Client side only.
	public boolean  isDOAComplied=false;//this boolean is to decide if the DOA Maintenance Complied with should be shown on the Client.
	public boolean	doaCompliedMaintenance=false;//this is for the Client to set true or false to update the server.

	//the following variable is declared for the Client to decide if the Reporting Categories should be displayed
	public boolean	isReportingCategoryApplicable=false;

	//the Two vectors will be set in the Server one for linked and the other for unlinked. 
	//IMPORTANT while closing the event, all the modifications(linking and unlinking the discrepancies) should be contained in the discrepancyVector.
	public List<EventDiscrepancyListData>	discrepancyList=new ArrayList();//Vector used to hold the EventDiscrepancyListData.
	public Vector	linkedDiscrepancyVector=new Vector();//Vector used to hold the Linked Discrepancies. EventDiscrepancyListData
	public Vector	reportingCategoriesVector=new Vector();//Vector used to hold the ReportingCategoriesData.
	public List<ReportCategoriesKeyValueData>	reportingCategoriesKeys=new ArrayList<>();//List used to hold the Reporting categories Key values updated on client.
	public List<String> tfNotesList;//List used to hold the TFNotes Data Object updated on client.
	public List<EventTimers>	niwTimers=new ArrayList<>();//List used to hold the NIW Timers Data Object.
	public Vector	newStatusVector=new Vector();//vector holding the diff statuses e.g. DWN, AOG etc etc,	
	public Vector	statusVector=new Vector();//this is for the client to display a valid list of Status from the database.

	public List<EventMsns>	msnData=new ArrayList<>();//Vector used to hold the TFNotes Data Object updated on client.

	public java.sql.Timestamp convertedDateTime;//only used on the server side while Converting an Event.
	public java.sql.Timestamp createdDateTime;//only used on the server side while inserting the record.
	public java.sql.Timestamp reviewedDateTime;//only used on the server side while updating the record.
	public java.sql.Timestamp changedDateTime;//only used on the server side while updating the record.
	public java.sql.Timestamp closedDateTime;//only used on the server side while updating the record.
	public java.sql.Timestamp cancelledDateTime;//only used on the server side while updating the record.

	//data variable declared on 01/31/03 to hold the MGR_Notes for an Event. Currently only used while Duty Manager Reviewing an Event.
	public String		managerNote;

	public boolean	superUpdateRequired=false;//this variable declared for Server side to figure out if SUPER Update is required.

	public String		serverError;//this variable declared for Server side to track the error and send back to the client.

	public boolean	overrideRequest=false;//this variable is set on Client to Request the Server if Over riding of current values in change Request is required.(05-02-03)

	public int		overrideEventId =0;

	public boolean	continueAddingEvent=false;//this variable is set on Client to Request the Server if Over riding of current values in change Request is required.(05-05-03)

	public String	acnRegistrationNumber;//added on 05-14-2003 for updating PCS tables.

	public boolean	showTubFileNotes=false;//added on 05-19-2003 for Client.

	public boolean	showDiscrepancyCard=false;//added on 05-20-2003 for Client.

	public String	empDepartment;//added on 05-27-2003 Server would retrieve the EMP DEPT ID based on USER_ID from MARS database while inserting the tub file note.

	public boolean	addNewEvent = false;//added on 10-07-2003 for Client to indicate continue adding new event(10/27/03 release).
	public boolean	reopenEvent = false;//added on 10-07-2003 for Client to indicate continue adding new event(10/27/03 release).
	
	public String	resMgrId;//added on 06-29-2012, Server would retrieve the EMP DEPT ID based on USER_ID from MARS database while inserting the tub file note.
	
	public String	memDeskContact;//added 01/26/2016, MEM desk lead for MOCC Orange Man project, requested by Ebben Raves
	
	public String 	OST;// added on 09-07-2012, MOCC effort to reduce OOS times by assigning to OST desk as indicated by flag
	public String 	newOST;
	
	public String 	eticRsnCd;
	public String 	newEticRsnCd;
	
	public String 	eticRsnComment;
	public String 	newEticRsnComment;
}	