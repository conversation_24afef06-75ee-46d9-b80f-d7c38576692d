package com.fedex.mets.service.addEvent;

import com.fedex.mets.entity.mss.ForteLeg;
import com.fedex.mets.repository.mss.ForteLegRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class FlightDetailsService {
    private static final Logger logger = LoggerFactory.getLogger(FlightDetailsService.class);

    @Autowired
    private ForteLegRepository forteLegRepository;
    /**
     * The following getFlightDetails() is used to retrieve the Flight details for a particular flightNumber, flightDate, flightLegNumber.
     * @params String flightNumber, flightDate, flightLegNumber.
     * @return resultList.
     */
    public List<Object> getFlightDetails(
            String flightNumber,
            String flightLegNumber,
            String flightDate)
            {
        List<Object> resultList = new ArrayList<>();

        ForteLeg flightDetailList = null;
                //declared to hold the Flight details
                List<Object> ListFlightDetail = new ArrayList<>(); //declared to hold the Flight Details
        List<Object> deLayTimeList = new ArrayList<>(); //declared to hold the Delay Times
                List<Object> delayCodeList = new ArrayList<>(); //declared to hold the Delay Codes

        logger.info("In the getFlightDetails() method.");
        logger.info("flightNumber ========================" + flightNumber);
        logger.info("flightDate ========================" + flightDate);
        logger.info("flightLegNumber ========================" + flightLegNumber);

        if (flightNumber != null
                && flightDate != null
                && flightDate.trim().length() > 0
                && flightLegNumber != null) {
            try {

                flightDetailList = forteLegRepository.getFlightDetails(flightNumber, flightDate, flightLegNumber);
                if(flightDetailList!= null) {
                    logger.info("rsForte======== record found in Forte_leg table.");
                    ListFlightDetail.add(flightDetailList.getFltNbr());
                    ListFlightDetail.add(flightDetailList.getFltZuluDate());
                    ListFlightDetail.add(flightDetailList.getLegNbr());
                    ListFlightDetail.add(flightDetailList.getTailNbr());
                    ListFlightDetail.add(flightDetailList.getIataDestCd());
                    ListFlightDetail.add(flightDetailList.getLegStatDesc());
                    ListFlightDetail.add(flightDetailList.getLegTypeCd());
                    ListFlightDetail.add(flightDetailList.getSchedOutZuluTmstp());
                    ListFlightDetail.add(flightDetailList.getActlOutZuluTmstp());
                    ListFlightDetail.add(flightDetailList.getTotDelayQty());

                    deLayTimeList.add(flightDetailList.getDelayQty1());
                    deLayTimeList.add(flightDetailList.getDelayQty2());
                    deLayTimeList.add(flightDetailList.getDelayQty3());
                    deLayTimeList.add(flightDetailList.getDelayQty4());
                    deLayTimeList.add(flightDetailList.getDelayQty5());
                    deLayTimeList.add(flightDetailList.getDelayQty6());
                    deLayTimeList.add(flightDetailList.getDelayQty7());
                    deLayTimeList.add(flightDetailList.getDelayQty8());
                    deLayTimeList.add(flightDetailList.getDelayQty9());
                    deLayTimeList.add(flightDetailList.getDelayQty10());

                    delayCodeList.add(flightDetailList.getDelayCd1());
                    delayCodeList.add(flightDetailList.getDelayCd2());
                    delayCodeList.add(flightDetailList.getDelayCd3());
                    delayCodeList.add(flightDetailList.getDelayCd4());
                    delayCodeList.add(flightDetailList.getDelayCd5());
                    delayCodeList.add(flightDetailList.getDelayCd6());
                    delayCodeList.add(flightDetailList.getDelayCd7());
                    delayCodeList.add(flightDetailList.getDelayCd8());
                    delayCodeList.add(flightDetailList.getDelayCd9());
                    delayCodeList.add(flightDetailList.getDelayCd10());

                    ListFlightDetail.add(flightDetailList.getIataOrigCd());
                    ListFlightDetail.add(flightDetailList.getSchedInZuluTmstp());
                    ListFlightDetail.add(flightDetailList.getActlInZuluTmstp());

                    resultList.add(ListFlightDetail);
                    resultList.add(deLayTimeList);
                    resultList.add(delayCodeList);

                } else {
                    logger.info("rsForte======== record not found in Forte_leg table.");
                    resultList=null;
                }

            } catch (Exception forte) {
                logger.warn(
                        "ERROR Forte Retreive getFlightDetails() exception >> "
                                + forte.getMessage());
            }
        }
        return resultList;
    }


}
