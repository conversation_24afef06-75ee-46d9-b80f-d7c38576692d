package com.fedex.mets.service.update;

import com.fedex.mets.data.MetsEventUpdateEntity;
import com.fedex.mets.entity.mets.EventMsns;
import com.fedex.mets.repository.mets.EventMsnsRepository;
import com.fedex.mets.util.IServerConstants;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class EventMsnsUpdateService {
    private static final Logger logger = LoggerFactory.getLogger(EventMsnsUpdateService.class);

    @Autowired
    private EventMsnsRepository eventMsnsRepo;


    public Map<String, Object> updateMsns(MetsEventUpdateEntity request, Map<String, Object> hashMap,
                                          List<EventMsns> msnData) throws Exception {
        List<EventMsns> elements = new ArrayList<>();
        return hashMap;
    }


}
