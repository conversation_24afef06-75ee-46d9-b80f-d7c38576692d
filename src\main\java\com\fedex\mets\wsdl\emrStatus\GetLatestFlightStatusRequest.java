
package com.fedex.mets.wsdl.emrStatus;

import java.util.ArrayList;
import java.util.List;

import jakarta.xml.bind.annotation.*;



/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;extension base="{http://www.fedex.com/airops/schemas/Common.xsd}GenericRequest">
 *       &lt;sequence>
 *         &lt;element name="acns" type="{http://fedex.com/airops/emr/jaxws/services}acnType" maxOccurs="unbounded"/>
 *         &lt;element name="isGetPersistedStatus" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "acns",
    "isGetPersistedStatus"
})
@XmlRootElement(name = "getLatestFlightStatusRequest",namespace = "http://fedex.com/airops/emr/jaxws/services")
public class GetLatestFlightStatusRequest
    extends GenericRequest
{

    @XmlElement(required = true,namespace = "http://fedex.com/airops/emr/jaxws/services")
    protected List<String> acns;

    protected boolean isGetPersistedStatus;

    /**
     * Gets the value of the acns property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the acns property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getAcns().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link String }
     * 
     * 
     */
    public List<String> getAcns() {
        if (acns == null) {
            acns = new ArrayList<String>();
        }
        return this.acns;
    }

    /**
     * Gets the value of the isGetPersistedStatus property.
     * 
     */
    public boolean isIsGetPersistedStatus() {
        return isGetPersistedStatus;
    }

    /**
     * Sets the value of the isGetPersistedStatus property.
     * 
     */
    public void setIsGetPersistedStatus(boolean value) {
        this.isGetPersistedStatus = value;
    }

}
