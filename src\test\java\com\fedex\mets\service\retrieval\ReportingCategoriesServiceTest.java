package com.fedex.mets.service.retrieval;

import com.fedex.mets.dao.ReportCategoriesActiveKeyValueData;
import com.fedex.mets.dao.ReportCategoryKeyValues;
import com.fedex.mets.dao.ReportingCategoryValues;
import com.fedex.mets.dto.ReportCatgResponse;
import com.fedex.mets.repository.mets.EventRepCatgRepository;
import com.fedex.mets.repository.mets.EventsRepository;
import com.fedex.mets.repository.mets.GroupDictRepository;
import com.fedex.mets.repository.mets.RptCatgLevel1Repository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

public class ReportingCategoriesServiceTest {

    @Mock
    private EventsRepository eventsRepository;

    @Mock
    private GroupDictRepository groupDictRepository;

    @Mock
    private RptCatgLevel1Repository rptCatgRepository;

    @Mock
    private EventRepCatgRepository eventRepCatgRepository;

    @InjectMocks
    private ReportingCategoriesService reportingCategoriesService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testGetReportCategories_Success() throws Exception {
        String eventId = "123";
        String accessLevel = "level1";
        List<ReportingCategoryValues> mockReportCategories = new ArrayList<>();
        List<ReportCategoriesActiveKeyValueData> mockReportCategoriesKeyValues = new ArrayList<>();
        List<List<?>> mockElements = Arrays.asList(mockReportCategories, mockReportCategoriesKeyValues);

        when(reportingCategoriesService.getReportingCategories(eventId, accessLevel)).thenReturn(mockElements);

        ReportCatgResponse response = reportingCategoriesService.getReportCategories(eventId, accessLevel);


        assertNotNull(response);
        assertEquals(mockReportCategories, response.getReportingCategories());
        assertEquals(mockElements, response.getReportingCategoryKeys());
    }

    @Test
    public void testGetReportingCategoriesByEventType_Success() throws Exception {
        String eventType = "type1";
        String accessLevel = "level1";
        List<ReportingCategoryValues> mockReportCategories = new ArrayList<>();

        when(groupDictRepository.getGroupId(accessLevel)).thenReturn("group1");
        when(rptCatgRepository.getRepCategories("group1", eventType)).thenReturn(mockReportCategories);

        ReportCatgResponse response = reportingCategoriesService.getReportingCategoriesByEventType(eventType, accessLevel);


        assertNotNull(response);
        assertEquals(mockReportCategories, response.getReportingCategories());
    }

    @Test
    public void testGetReportingCategories_Success() throws Exception {
        String eventId = "123";
        String accessLevel = "level1";
        List<ReportingCategoryValues> mockReportCategories = new ArrayList<>();
        List<ReportCategoryKeyValues> mockReportCategoriesKeyValues = new ArrayList<>();
        List<List<?>> mockResultList = Arrays.asList(mockReportCategories, mockReportCategoriesKeyValues);

        when(eventsRepository.getEventType(Integer.parseInt(eventId))).thenReturn("eventType1");
        when(groupDictRepository.getGroupId(accessLevel)).thenReturn("group1");
        when(rptCatgRepository.getRepCategories("group1", "eventType1")).thenReturn(mockReportCategories);
        when(reportingCategoriesService.getReportCategoriesKeyValues(eventId, "group1")).thenReturn(mockReportCategoriesKeyValues);


        List<List<?>> result = reportingCategoriesService.getReportingCategories(eventId, accessLevel);


        assertNotNull(result);
        assertEquals(mockResultList, result);
    }

    @Test
    public void testGetReportCategoriesKeyValues_Success() throws Exception {
        String eventId = "123";
        String groupId = "group1";
        List<ReportCategoryKeyValues> mockReportCategoriesKeyValues = new ArrayList<>();

        when(eventRepCatgRepository.getRepCategoryKeyValues(eventId,groupId)).thenReturn(mockReportCategoriesKeyValues);

        List<ReportCategoryKeyValues> result = reportingCategoriesService.getReportCategoriesKeyValues(eventId, groupId);

        assertNotNull(result);
        assertEquals(mockReportCategoriesKeyValues, result);
    }

}
