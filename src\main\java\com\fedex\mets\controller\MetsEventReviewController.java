package com.fedex.mets.controller;

import com.fedex.mets.data.WizardEventData;
import com.fedex.mets.dto.MetsResponse;
import com.fedex.mets.service.reviewEvent.EventReviewService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;

@CrossOrigin(origins = "*") // Allows all origins
@RestController
@RequestMapping("/api/mets")
@Tag(name = "EVENT REVIEW ALERT", description = "Endpoint for reviewing change of an event.")
public class MetsEventReviewController {
    private static final Logger logger = LoggerFactory.getLogger(MetsEventReviewController.class);

    @Autowired
    private EventReviewService eventReviewService;

    @Operation(summary = "Review an event.", description = "Reviewing change of an event in METS system.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully reviewed the event."),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @PostMapping(path = "/reviewEvent")
    public ResponseEntity<MetsResponse> reviewEvent(@RequestBody WizardEventData request) throws Exception {
        MetsResponse reviewEventResponse = new MetsResponse();
        HashMap hashmap = new HashMap();
        String userId = request.getUserId();
        String tokenId = request.getTokenId();
        try {
            if (request != null) {
                if (!userId.isEmpty() && !tokenId.isEmpty()) {
                    logger.info(".....calling the reviewEvent");
                    hashmap = eventReviewService.reviewEvent(request);
                    if (hashmap != null) {
                        reviewEventResponse.setData(hashmap);
                    } else {
                        return ResponseEntity.status(500).build();
                    }
                } else {
                    logger.info("One or more essential parameters are missing from the request.");
                }
            }
            return ResponseEntity.ok(reviewEventResponse);
        } catch (IllegalArgumentException e) {
            logger.error("Invalid input for review Event: Wizard Event: {}. Error: {}", request, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error reviewing an event", e);
            return ResponseEntity.status(500).build();
        }
    }
}
