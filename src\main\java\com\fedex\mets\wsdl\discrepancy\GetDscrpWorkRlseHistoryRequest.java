
package com.fedex.mets.wsdl.discrepancy;

import java.math.BigDecimal;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;extension base="{http://www.fedex.com/airops/schemas/Common.xsd}GenericRequest">
 *       &lt;sequence>
 *         &lt;element name="aircraftNbr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ataNbr" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="dscrpNbr" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "aircraftNbr",
    "ataNbr",
    "dscrpNbr"
})
@XmlRootElement(name = "getDscrpWorkRlseHistoryRequest",namespace = "http:///www.fedex.com/airops/schemas/Mach")
public class GetDscrpWorkRlseHistoryRequest
    extends GenericRequest
{

    @XmlElement(required = true)
    protected String aircraftNbr;
    @XmlElement(required = true)
    protected BigDecimal ataNbr;
    @XmlElement(required = true)
    protected BigDecimal dscrpNbr;

    /**
     * Gets the value of the aircraftNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAircraftNbr() {
        return aircraftNbr;
    }

    /**
     * Sets the value of the aircraftNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAircraftNbr(String value) {
        this.aircraftNbr = value;
    }

    /**
     * Gets the value of the ataNbr property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAtaNbr() {
        return ataNbr;
    }

    /**
     * Sets the value of the ataNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAtaNbr(BigDecimal value) {
        this.ataNbr = value;
    }

    /**
     * Gets the value of the dscrpNbr property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getDscrpNbr() {
        return dscrpNbr;
    }

    /**
     * Sets the value of the dscrpNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setDscrpNbr(BigDecimal value) {
        this.dscrpNbr = value;
    }

}
