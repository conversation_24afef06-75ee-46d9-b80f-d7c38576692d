//package com.fedex.mets.controller;
//
//import org.hamcrest.Matchers;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
//import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
//import org.springframework.boot.test.mock.mockito.MockBean;
//import org.springframework.http.MediaType;
//import org.mockito.ArgumentMatchers;
//
//import org.mockito.Mockito;
//
//import java.util.HashMap;
//import java.util.Map;
//
//import org.springframework.test.web.servlet.MockMvc;
//import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
//import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
//
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.fedex.mets.data.EventNIWTimersDataEntity;
//import com.fedex.mets.data.MetsEventUpdateEntity;
//import com.fedex.mets.service.update.EventDiscrepanciesUpdateService;
//import com.fedex.mets.service.update.MetsAddWLMTFNotesService;
//import com.fedex.mets.service.update.MetsNIWTimerDetailsService;
//import com.fedex.mets.service.update.MetsSendMailService;
//import com.fedex.mets.service.update.MetsSetEventDOAInfoService;
//import com.fedex.mets.service.update.MetsSetFlightEticDetailService;
//import com.fedex.mets.service.update.MetsSetNIWTimerFromWLMService;
//import com.fedex.mets.service.update.MetsUpdateDOAEventDetailService;
//import com.fedex.mets.service.update.MetsUpdateEventDetailService;
//import com.fedex.mets.service.update.MetsValidateStartDateTimeService;
//import com.fedex.mets.service.update.ReportingCategoriesUpdateService;
//import com.fedex.mets.service.update.TFNotesUpdateService;
//
//@WebMvcTest(MetsUpdateController.class)
//@AutoConfigureMockMvc(addFilters = false)
//public class MetsUpdateControllerTests {
//
//    @Autowired
//    private MockMvc mockMvc;
//
//    @Autowired
//    private ObjectMapper objectMapper;
//
//    @MockBean
//    private MetsNIWTimerDetailsService metsniwtimerdetailservice;
//
//    @MockBean
//    private TFNotesUpdateService metstfnotesservice;
//
//    @MockBean
//    private EventDiscrepanciesUpdateService addeventdiscrepanciesservice;
//
//    @MockBean
//    private MetsSetFlightEticDetailService metssetflighteticdetailservice;
//
//    @MockBean
//    private MetsAddWLMTFNotesService metsaddwlmtfnotesservice;
//
//    @MockBean
//    private ReportingCategoriesUpdateService metseditreportingcategoriesservice;
//
//    @MockBean
//    private MetsSendMailService metssendmailservice;
//
//    @MockBean
//    private MetsSetEventDOAInfoService metsseteventdoainfoservice;
//
//    @MockBean
//    private MetsSetNIWTimerFromWLMService metssetniwtimerfromwlmservice;
//
//    @MockBean
//    private MetsUpdateDOAEventDetailService metsupdatedoaeventdetailservice;
//
//    @MockBean
//    private MetsUpdateEventDetailService metsupdateeventdetailservice;
//
//    @MockBean
//    private MetsValidateStartDateTimeService metsvalidatestartdatetimeservice;
//
//    String nullModeError = "MODE is null which is a required parameter for the Server to process the request.";
//
//    String niwTimersMissingParamError = "One of the required parameters is not passed to the Server.";
//
//    @Test
//    void testNullMode() throws Exception {
//
//    	MetsEventUpdateEntity testPayload = new MetsEventUpdateEntity();
//    	testPayload.setMode(null);
//
//    	String testPayloadString = objectMapper.writeValueAsString(testPayload);
//
//    	mockMvc.perform(MockMvcRequestBuilders.post("/api/mets/update").contentType(MediaType.APPLICATION_JSON).content(testPayloadString))
//    			.andExpect(MockMvcResultMatchers.status().isOk()).andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
//    			.andExpect(MockMvcResultMatchers.jsonPath("$.ERROR").value(nullModeError));
//    }
//
//    @Test
//    void testRequiredParamMissingNiwTimers() throws Exception {
//
//    	MetsEventUpdateEntity testPayload = new MetsEventUpdateEntity();
//    	testPayload.setMode("NIW_TIMERS");
//    	testPayload.setFlag("EDIT");
//
//    	String testPayloadString = objectMapper.writeValueAsString(testPayload);
//
//    	mockMvc.perform(MockMvcRequestBuilders.post("/api/mets/update").contentType(MediaType.APPLICATION_JSON).content(testPayloadString))
//    			.andExpect(MockMvcResultMatchers.status().isOk()).andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
//    			.andExpect(MockMvcResultMatchers.jsonPath("$.ERROR").value(niwTimersMissingParamError));
//
//    	testPayload.setFlag("DELETE");
//
//    	testPayloadString = objectMapper.writeValueAsString(testPayload);
//
//    	mockMvc.perform(MockMvcRequestBuilders.post("/api/mets/update").contentType(MediaType.APPLICATION_JSON).content(testPayloadString))
//    			.andExpect(MockMvcResultMatchers.status().isOk()).andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
//    			.andExpect(MockMvcResultMatchers.jsonPath("$.ERROR").value(niwTimersMissingParamError));
//
//    	testPayload.setFlag("ADD");
//
//    	testPayloadString = objectMapper.writeValueAsString(testPayload);
//
//    	mockMvc.perform(MockMvcRequestBuilders.post("/api/mets/update").contentType(MediaType.APPLICATION_JSON).content(testPayloadString))
//    			.andExpect(MockMvcResultMatchers.status().isOk()).andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
//    			.andExpect(MockMvcResultMatchers.jsonPath("$.ERROR").value(niwTimersMissingParamError));
//
//    	testPayload.setFlag("TEST");
//
//    	testPayloadString = objectMapper.writeValueAsString(testPayload);
//
//    	mockMvc.perform(MockMvcRequestBuilders.post("/api/mets/update").contentType(MediaType.APPLICATION_JSON).content(testPayloadString))
//    			.andExpect(MockMvcResultMatchers.status().isOk()).andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
//    			.andExpect(MockMvcResultMatchers.jsonPath("$.ERROR").value(niwTimersMissingParamError));
//    }
//
//    @Test
//    void testNiwTimersEdit() throws Exception {
//
//    	MetsEventUpdateEntity testPayload = new MetsEventUpdateEntity();
//    	testPayload.setMode("NIW_TIMERS");
//    	testPayload.setFlag("EDIT");
//    	testPayload.setUser_id("1");
//    	testPayload.setToken_id("1");
//
//    	EventNIWTimersDataEntity data = new EventNIWTimersDataEntity();
//    	data.setEventId(21357);
//    	data.setTimerId("15");
//    	data.setTimerStartDate("2003-09-01 03:30:31");
//    	data.setTimerStopDate("2003-09-02 05:22:21");
//    	data.setCreationDateTime("2003-08-30 03:30:31");
//
//    	testPayload.setNiw_timer_data(data);
//
//    	String testPayloadString = objectMapper.writeValueAsString(testPayload);
//
//    	Map<String, Object> mockResponse = new HashMap<>();
//        mockResponse.put("field1", "value1");
//        mockResponse.put("field2", "value2");
//        mockResponse.put("field3", "value3");
//        mockResponse.put("field4", "value4");
//        mockResponse.put("field5", "value5");
//
//        Mockito.when(metsniwtimerdetailservice.editNIWTimerDetails(ArgumentMatchers.any(MetsEventUpdateEntity.class),
//                ArgumentMatchers.anyMap(), ArgumentMatchers.any(EventNIWTimersDataEntity.class),ArgumentMatchers.any())).thenReturn(mockResponse);
//
//    	mockMvc.perform(MockMvcRequestBuilders.post("/api/mets/update").contentType(MediaType.APPLICATION_JSON).content(testPayloadString))
//    			.andExpect(MockMvcResultMatchers.status().isOk()).andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
//    			.andExpect(MockMvcResultMatchers.jsonPath("$.*", Matchers.hasSize(Matchers.greaterThan(4))));
//
//    	Mockito.verify(metsniwtimerdetailservice, Mockito.times(1)).editNIWTimerDetails(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any(),ArgumentMatchers.anyBoolean());
//    }
//
//    @Test
//    void testNiwTimersDelete() throws Exception {
//
//    	MetsEventUpdateEntity testPayload = new MetsEventUpdateEntity();
//    	testPayload.setMode("NIW_TIMERS");
//    	testPayload.setFlag("DELETE");
//    	testPayload.setUser_id("1");
//    	testPayload.setToken_id("1");
//
//    	EventNIWTimersDataEntity data = new EventNIWTimersDataEntity();
//    	data.setEventId(21358);
//    	data.setTimerId("16");
//    	data.setCreationDateTime("2003-08-30 03:30:31");
//
//    	testPayload.setNiw_timer_data(data);
//
//    	String testPayloadString = objectMapper.writeValueAsString(testPayload);
//
//    	Map<String, Object> mockResponse = new HashMap<>();
//        mockResponse.put("field1", "value1");
//        mockResponse.put("field2", "value2");
//        mockResponse.put("field3", "value3");
//        mockResponse.put("field4", "value4");
//
//        Mockito.when(metsniwtimerdetailservice.deleteNIWTimerDetails(ArgumentMatchers.any(MetsEventUpdateEntity.class),
//                ArgumentMatchers.anyMap(), ArgumentMatchers.any(EventNIWTimersDataEntity.class),
//                ArgumentMatchers.anyString(), ArgumentMatchers.anyString(),ArgumentMatchers.anyBoolean())).thenReturn(mockResponse);
//
//    	mockMvc.perform(MockMvcRequestBuilders.post("/api/mets/update").contentType(MediaType.APPLICATION_JSON).content(testPayloadString))
//    			.andExpect(MockMvcResultMatchers.status().isOk()).andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
//    			.andExpect(MockMvcResultMatchers.jsonPath("$.*", Matchers.hasSize(Matchers.greaterThan(3))));
//
//    	Mockito.verify(metsniwtimerdetailservice, Mockito.times(1)).deleteNIWTimerDetails(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any(),ArgumentMatchers.anyBoolean());
//
//    }
//
//    @Test
//    void testNiwTimersAdd() throws Exception {
//
//    	MetsEventUpdateEntity testPayload = new MetsEventUpdateEntity();
//    	testPayload.setMode("NIW_TIMERS");
//    	testPayload.setFlag("ADD");
//    	testPayload.setUser_id("1");
//    	testPayload.setToken_id("1");
//
//    	EventNIWTimersDataEntity data = new EventNIWTimersDataEntity();
//    	data.setEventId(21357);
//    	data.setTimerId("15");
//    	data.setTimerStartDate("2003-09-01 03:30:31");
//    	data.setTimerStopDate("2003-09-02 05:22:21");
//
//    	testPayload.setNiw_timer_data(data);
//
//    	String testPayloadString = objectMapper.writeValueAsString(testPayload);
//
//    	Map<String, Object> mockResponse = new HashMap<>();
//        mockResponse.put("field1", "value1");
//        mockResponse.put("field2", "value2");
//        mockResponse.put("field3", "value3");
//        mockResponse.put("field4", "value4");
//        mockResponse.put("field5", "value5");
//
//        Mockito.when(metsniwtimerdetailservice.addNIWTimerDetails(ArgumentMatchers.any(MetsEventUpdateEntity.class),
//                ArgumentMatchers.anyMap(), ArgumentMatchers.any(EventNIWTimersDataEntity.class),ArgumentMatchers.anyBoolean()
//               )).thenReturn(mockResponse);
//
//    	mockMvc.perform(MockMvcRequestBuilders.post("/api/mets/update").contentType(MediaType.APPLICATION_JSON).content(testPayloadString))
//    			.andExpect(MockMvcResultMatchers.status().isOk()).andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
//    			.andExpect(MockMvcResultMatchers.jsonPath("$.*", Matchers.hasSize(Matchers.greaterThan(4))));
//
//    	Mockito.verify(metsniwtimerdetailservice, Mockito.times(1)).addNIWTimerDetails(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any(),ArgumentMatchers.anyBoolean());
//
//    }
//
//    @Test
//    void testNiwTimersSetTimers() throws Exception {
//
//    	MetsEventUpdateEntity testPayload = new MetsEventUpdateEntity();
//    	testPayload.setMode("NIW_TIMERS");
//    	testPayload.setFlag("START");
//    	testPayload.setEvent_id("21357");
//    	testPayload.setFlag("START");
//    	testPayload.setTimer_id("15");
//    	testPayload.setUser_id("1");
//    	testPayload.setToken_id("1");
//
//    	EventNIWTimersDataEntity data = new EventNIWTimersDataEntity();
//    	data.setEventId(21357);
//    	data.setTimerId("15");
//
//    	testPayload.setNiw_timer_data(data);
//
//    	String testPayloadString = objectMapper.writeValueAsString(testPayload);
//
//    	Map<String, Object> mockResponse = new HashMap<>();
//        mockResponse.put("field1", "value1");
//        mockResponse.put("field2", "value2");
//        mockResponse.put("field3", "value3");
//
//        Mockito.when(metsniwtimerdetailservice.setNIWTimerDetails(ArgumentMatchers.any(MetsEventUpdateEntity.class),
//                ArgumentMatchers.anyMap(), ArgumentMatchers.anyString(), ArgumentMatchers.anyString(), ArgumentMatchers.anyString(),ArgumentMatchers.anyBoolean()
//                )).thenReturn(mockResponse);
//
//    	mockMvc.perform(MockMvcRequestBuilders.post("/api/mets/update").contentType(MediaType.APPLICATION_JSON).content(testPayloadString))
//    			.andExpect(MockMvcResultMatchers.status().isOk()).andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON))
//    			.andExpect(MockMvcResultMatchers.jsonPath("$.*", Matchers.hasSize(Matchers.greaterThan(2))));
//
//    	Mockito.verify(metsniwtimerdetailservice, Mockito.times(1)).setNIWTimerDetails(ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any(),ArgumentMatchers.anyBoolean());
//
//    }
//
//}
