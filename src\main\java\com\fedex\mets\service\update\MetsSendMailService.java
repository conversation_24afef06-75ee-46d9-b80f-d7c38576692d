package com.fedex.mets.service.update;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.fedex.mets.data.EventEmailDataEntity;
import com.fedex.mets.data.MetsEventUpdateEntity;

@Service
public class MetsSendMailService {
	
	public Map<String, Object> sendMail(MetsEventUpdateEntity request, Map<String, Object> hashMap, 
			EventEmailDataEntity eventEmailData, String userId, String tokenId) throws Exception {
		
		return null;
	}

}
