
package com.fedex.mets.wsdl.msn.shortagenotice;

import java.util.ArrayList;
import java.util.List;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;



/**
 * <p>Java class for shortageNoticeSearchCriteriaType complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="shortageNoticeSearchCriteriaType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="columnInfoTypeList" type="{http://fedex.com/airops/maxi/services/jaxws}columnInfoType" maxOccurs="unbounded"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "shortageNoticeSearchCriteriaType", namespace = "http://fedex.com/airops/maxi/services/jaxws/shortagenotice", propOrder = {
    "columnInfoTypeList"
})
public class ShortageNoticeSearchCriteriaType {

    @XmlElement(namespace = "http://fedex.com/airops/maxi/services/jaxws/shortagenotice",required = true)
    protected List<ColumnInfoType> columnInfoTypeList;

    /**
     * Gets the value of the columnInfoTypeList property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the columnInfoTypeList property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getColumnInfoTypeList().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link ColumnInfoType }
     * 
     * 
     */
    public List<ColumnInfoType> getColumnInfoTypeList() {
        if (columnInfoTypeList == null) {
            columnInfoTypeList = new ArrayList<ColumnInfoType>();
        }
        return this.columnInfoTypeList;
    }

}
