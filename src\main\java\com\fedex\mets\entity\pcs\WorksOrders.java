package com.fedex.mets.entity.pcs;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "WORKS_ORDERS")
public class WorksOrders {
    @Id
    @Column(name = "WO_NO", nullable = false)
    private Long woNo;

    @Column(name = "WO_TYPE", length = 3)
    private String woType;

    @Column(name = "WO_STATUS", length = 1)
    private String woStatus;

    @Column(name = "CUST_CD", length = 6)
    private String custCd;

    @Column(name = "WO_LAST_SEQ_NO")
    private Integer woLastSeqNo;

    @Column(name = "WO_DESC", length = 30)
    private String woDesc;

    @Column(name = "WORK_CENTRE_CD", length = 6)
    private String workCentreCd;

    @Column(name = "COMP_WO_WORKSHEET_REF", length = 10)
    private String compWoWorksheetRef;

    @Column(name = "ULT_CUST_CD", length = 6)
    private String ultCustCd;

    @Column(name = "AC_REGN", length = 10)
    private String acRegn;

    @Column(name = "WO_FILE_REF", length = 15)
    private String woFileRef;

    @Column(name = "WO_RAISE_DT")
    private Timestamp woRaiseDt;

    @Column(name = "WO_FINALISED_DT")
    private Timestamp woFinalisedDt;

    @Column(name = "PARENT_SO_NO")
    private Integer parentSoNo;

    @Column(name = "PARENT_WO_NO")
    private Long parentWoNo;

    @Column(name = "AC_WO_SCHEDULE_ID", length = 15)
    private String acWoScheduleId;

    @Column(name = "WO_DUP_CARDS_FLAG", length = 1)
    private String woDupCardsFlag;

    @Column(name = "NOMINAL_CD", length = 10)
    private String nominalCd;

    @Column(name = "WO_AUTO_BOOK_ON_FLAG", length = 1)
    private String woAutoBookOnFlag;

    @Column(name = "WO_MATL_PRE_LOAD_FLAG", length = 1)
    private String woMatlPreLoadFlag;

    @Column(name = "WO_MM_REVISION")
    private Integer woMmRevision;

    @Column(name = "WO_AMS_REVISION")
    private Integer woAmsRevision;

    @Column(name = "PART_NO", length = 25)
    private String partNo;

    @Column(name = "PART_SRL_NO", length = 15)
    private String partSrlNo;

    @Column(name = "COMP_WO_PART_QTY", precision = 11, scale = 2)
    private BigDecimal compWoPartQty;

    @Column(name = "COMP_WO_QUOTE_REQD_FLAG", length = 1)
    private String compWoQuoteReqdFlag;

    @Column(name = "COMP_WO_PART_AC_REGN", length = 10)
    private String compWoPartAcRegn;

    @Column(name = "COMP_WO_STRIP_REP_FLAG", length = 1)
    private String compWoStripRepFlag;

    @Column(name = "COMP_WO_STRIP_REP_REF", length = 15)
    private String compWoStripRepRef;

    @Column(name = "COMP_WO_STRIP_REP_REQD_DT")
    private Timestamp compWoStripRepReqdDt;

    @Column(name = "WO_WRNT_REP_FLAG", length = 1)
    private String woWrntRepFlag;

    @Column(name = "WO_WRNT_REP_REF", length = 15)
    private String woWrntRepRef;

    @Column(name = "WO_WRNT_REP_REQD_DT")
    private Timestamp woWrntRepReqdDt;

    @Column(name = "WO_CLOSE_STK_RETURN_FLAG", length = 1)
    private String woCloseStkReturnFlag;

    @Column(name = "WO_CLOSE_DT_TM_REQD")
    private Timestamp woCloseDtTmReqd;

    @Column(name = "WO_CERT_REL_DT")
    private Timestamp woCertRelDt;

    @Column(name = "WO_STK_IND", length = 1)
    private String woStkInd;

    @Column(name = "WO_NEXT_SHORT_NO")
    private Integer woNextShortNo;

    @Column(name = "ULT_WO_NO")
    private Long ultWoNo;

    @Column(name = "COMP_WO_GENERIC_PART_DESC", length = 25)
    private String compWoGenericPartDesc;

    @Column(name = "WO_PANEL_CREATION_IND", length = 1)
    private String woPanelCreationInd;

    @Column(name = "WO_REL_CERT_TYPE_CD_1", length = 3)
    private String woRelCertTypeCd1;

    @Column(name = "WO_REL_CERT_TYPE_CD_2", length = 3)
    private String woRelCertTypeCd2;

    @Column(name = "ULT_SO_NO")
    private Integer ultSoNo;

    @Column(name = "WO_CLOSED_DT_TM")
    private Timestamp woClosedDtTm;

    @Column(name = "WO_CERT_BAT_REF_NO", length = 8)
    private String woCertBatRefNo;

    @Column(name = "WO_CERT_REL_NO", length = 20)
    private String woCertRelNo;

    @Column(name = "WO_CERT_SIGNATORY", length = 30)
    private String woCertSignatory;

    @Column(name = "WO_DEF_CARD_CATG_CD", length = 3)
    private String woDefCardCatgCd;

    @Column(name = "SITE_CD", length = 3)
    private String siteCd;

    @Column(name = "WO_SITE_CD", length = 3)
    private String woSiteCd;

    @Column(name = "WO_SAVED_SITE_CD", length = 3)
    private String woSavedSiteCd;

    @Column(name = "WO_EVENT_TRANSFER_IND", length = 1)
    private String woEventTransferInd;

    @Column(name = "WO_PLANNED_START_Date")
    private Timestamp woPlannedStartDate;

    @Column(name = "WO_XPM_TASK_CREATED_FLAG", length = 1)
    private String woXpmTaskCreatedFlag;

    @Column(name = "CHECK_TYPE_CD", length = 10)
    private String checkTypeCd;

    @Column(name = "SCHEDULE_ID", length = 15)
    private String scheduleId;

    @Column(name = "VISIT_NO")
    private Integer visitNo;

    @Column(name = "WO_FIRST_BOOKING_DT_TM")
    private Timestamp woFirstBookingDtTm;

    @Column(name = "NEXT_MOD_CARD_NO")
    private Integer nextModCardNo;

    @Column(name = "REMOVAL_IND", length = 1)
    private String removalInd;

    @Column(name = "REMOVAL_REASON_CD", length = 3)
    private String removalReasonCd;

    @Column(name = "WRI_NO", length = 8)
    private String wriNo;

    @Column(name = "REQUIRED_RETURN_DT_TM")
    private Timestamp requiredReturnDtTm;

    @Column(name = "WO_PRINT_ACC_COPY", length = 1)
    private String woPrintAccCopy;

    @Column(name = "WO_DEF_UNEVAL_FLAG", length = 1)
    private String woDefUnevalFlag;

    @Column(name = "WO_ENGSTAND_TYPE", length = 3)
    private String woEngstandType;

    @Column(name = "WO_ENGSTAND_FLAG_NO")
    private Integer woEngstandFlagNo;

    @Column(name = "WO_ENG_CONFIG", length = 15)
    private String woEngConfig;

    @Column(name = "WO_ENG_INSTALL_DT")
    private Timestamp woEngInstallDt;

    @Column(name = "WO_ENG_RMKS", length = 60)
    private String woEngRmks;

    @Column(name = "WO_ENG_POS", length = 1)
    private String woEngPos;

    @Column(name = "WO_PRINT_PROGRESS_FORM", length = 1)
    private String woPrintProgressForm;

    @Column(name = "WO_ENG_CORR_DT")
    private Timestamp woEngCorrDt;

    @Column(name = "WO_ENG_LCN", length = 3)
    private String woEngLcn;

    @Column(name = "WO_ENG_ASSIGNED_AC", length = 10)
    private String woEngAssignedAc;

    @Column(name = "WO_ENG_CLASS_CD", length = 3)
    private String woEngClassCd;

    @Column(name = "CAPITAL_PROJECT_NO", length = 8)
    private String capitalProjectNo;

    @Column(name = "WO_CLOSED_EMP_ID")
    private Long woClosedEmpId;

    @Column(name = "WO_PRINT_PREV_REGS", length = 1)
    private String woPrintPrevRegs;

    @Column(name = "MAXI_CLOSED_DT_TM")
    private Timestamp maxiClosedDtTm;

    @Column(name = "MAXI_MESSAGE_STATUS", length = 1)
    private String maxiMessageStatus;

    @Column(name = "WO_AC_TOTAL_CYCLES")
    private Integer woAcTotalCycles;

    @Column(name = "WO_AC_TOTAL_HRS", precision = 9, scale = 2)
    private BigDecimal woAcTotalHrs;

    @Column(name = "WO_HAND_OVER_NOTES", length = 800)
    private String woHandOverNotes;

    @Column(name = "MAXI_FIRST_BOOKING_DT_TM")
    private Timestamp maxiFirstBookingDtTm;

    @Column(name = "WO_CONFIRM_MS_FLAG", length = 1)
    private String woConfirmMsFlag;

    @Column(name = "WO_PLANNED_SPAN")
    private Integer woPlannedSpan;

    @Column(name = "WO_ACTUAL_RTM_DT_TM")
    private Timestamp woActualRtmDtTm;

    @Column(name = "WO_PLANNED_RETURN_DT_TM")
    private Timestamp woPlannedReturnDtTm;

    @Column(name = "WO_ETIC_DT_TM")
    private Timestamp woEticDtTm;

    @Column(name = "WO_ACTUAL_RFM_DT_TM")
    private Timestamp woActualRfmDtTm;

    @Column(name = "WO_ETIC_TEXT", length = 25)
    private String woEticText;

    @Column(name = "WO_STORE_TRACKED_ITEMS", length = 1)
    private String woStoreTrackedItems;

    @Column(name = "WO_MAXI_STI_EMP_ID")
    private Long woMaxiStiEmpId;

    @Column(name = "AC_TYPE_CD", length = 10)
    private String acTypeCd;

    @Column(name = "ENG_MAXI_WO_NO")
    private Integer engMaxiWoNo;

    @Column(name = "VENDOR_CD", length = 3)
    private String vendorCd;

    @Column(name = "AT_VENDOR_DT")
    private Timestamp atVendorDt;

    @Column(name = "UMBRELLA_FLAG", length = 1)
    private String umbrellaFlag;

    @Column(name = "FIRST_SHIFT_DATE")
    private Timestamp firstShiftDate;

    @Column(name = "LABOUR_RATE", precision = 9, scale = 2)
    private BigDecimal labourRate;

    @Column(name = "HANDLING_PERCENT", precision = 5, scale = 2)
    private BigDecimal handlingPercent;

    @Column(name = "SITE_VENDOR_CD", length = 3)
    private String siteVendorCd;

    @Column(name = "ROW_ROW_TIMESTAMP")
    private Timestamp rowTimestamp;

    @Column(name = "CATCH_ALL_WO")
    private Integer catchAllWo;

    @Column(name = "HANDLING_CEILING", precision = 9, scale = 2)
    private BigDecimal handlingCeiling;

    @Column(name = "SPLIT_RATE", length = 1)
    private String splitRate;

    @Column(name = "ENG_LAB_RATE", precision = 9, scale = 2)
    private BigDecimal engLabRate;

    @Column(name = "MAINT_LAB_RATE", precision = 9, scale = 2)
    private BigDecimal maintLabRate;

    @Column(name = "LAST_TEMPLATE_APPLIED", length = 15)
    private String lastTemplateApplied;

    @Column(name = "WO_DEF_NR_MILESTONE", length = 3)
    private String woDefNrMilestone;

    @Column(name = "TEST_FLIGHT_WO", length = 1)
    private String testFlightWo;

    @Column(name = "INBOUND_FLIGHT", length = 15)
    private String inboundFlight;

    @Column(name = "OUTBOUND_FLIGHT", length = 15)
    private String outboundFlight;

    @Column(name = "DEFAULT_TEST_FLIGHT_HOURS")
    private Integer defaultTestFlightHours;

    @Column(name = "MFG_PART_NO", length = 30)
    private String mfgPartNo;

    @Column(name = "MILESTONE_CHECK_CD", length = 1)
    private String milestoneCheckCd;

    @Column(name = "ENG_REM_AC_REGN", length = 10)
    private String engRemAcRegn;

    @Column(name = "ENG_REM_AC_POSITION", length = 3)
    private String engRemAcPosition;

    @Column(name = "ENG_REM_DT")
    private Timestamp engRemDt;

    @Column(name = "ENG_REM_LOCN", length = 3)
    private String engRemLocn;

    @Column(name = "WO_DEF_START_HOUR")
    private Integer woDefStartHour;

    @Column(name = "ARRIVAL_DT_TM")
    private Timestamp arrivalDtTm;

    @Column(name = "ACCEPTED_FLAG", length = 1)
    private String acceptedFlag;

    @Column(name = "ACCEPTED_DT_TM")
    private Timestamp acceptedDtTm;

    @Column(name = "ORG_PLANNED_START_DATE")
    private Timestamp orgPlannedStartDate;

    @Column(name = "FEEDBACK", length = 1)
    private String feedback;

    @Column(name = "PANEL_CARD_STYLE")
    private Integer panelCardStyle;

    @Column(name = "PANEL_CARD_CREATE", length = 1)
    private String panelCardCreate;

    @Column(name = "ELECTRONIC_SIGN_OFFS", length = 1)
    private String electronicSignOffs;

    @Column(name = "TAIL_TYPE", length = 1)
    private String tailType;

    @Column(name = "VENDOR_AUDIT_SITE_CD", length = 3)
    private String vendorAuditSiteCd;

    @Column(name = "VENDOR_AUDIT_COMPLETION_DATE")
    private Timestamp vendorAuditCompletionDate;

    @Column(name = "WO_ETIC_REASON", length = 500)
    private String woEticReason;

    @Column(name = "PRIMARY_STRUCTURE", length = 1)
    private String primaryStructure;

    @Column(name = "ORIG_PLANNED_SPAN")
    private Integer origPlannedSpan;

    @Column(name = "ORIG_PLANNED_ETIC")
    private Timestamp origPlannedEtic;

    @Column(name = "ADAPT_POSITION", length = 3)
    private String adaptPosition;

    @Column(name = "WORKSCOPE_CD", length = 1)
    private String workscopeCd;

    @Column(name = "MJCS_PLANNER_EMP_ID")
    private Long mjcsPlannerEmpId;

    @Column(name = "MJCS_PLANNER_SIGN_DT_TM")
    private Timestamp mjcsPlannerSignDtTm;

    @Column(name = "MJCS_MANAGER_EMP_ID")
    private Long mjcsManagerEmpId;

    @Column(name = "MJCS_MANAGER_SIGN_DT_TM")
    private Timestamp mjcsManagerSignDtTm;

    @Column(name = "MJCS_QA_QC_EMP_ID")
    private Long mjcsQaQcEmpId;

    @Column(name = "MJCS_QA_QC_SIGN_DT_TM")
    private Timestamp mjcsQaQcSignDtTm;

    @Column(name = "SIGN_OFF_WATERMARK", length = 1)
    private String signOffWatermark;

    @Column(name = "HEAVY_CHECK_FLAG", length = 1)
    private String heavyCheckFlag;

    @Column(name = "LEAD_AMT_EMPID")
    private Long leadAmtEmpId;

    @Column(name = "MANAGER_EMPID")
    private Long managerEmpId;

    @Column(name = "TOUR_SHT_CREATED", length = 1)
    private String tourShtCreated;

    @Column(name = "PLANNED_MANPOWER")
    private Integer plannedManpower;

    @Column(name = "ACTUAL_MANPOWER")
    private Integer actualManpower;

    @Column(name = "PLANNER_NOTE", length = 4000)
    private String plannerNote;

    @Column(name = "MANL_CPI_DESCRN", length = 50)
    private String manlCpiDescrn;

    @Column(name = "MANL_CPI_MANHOURS", length = 8)
    private String manlCpiManhours;

    @Column(name = "MANL_CPI_SKILL", length = 3)
    private String manlCpiSkill;

    @Column(name = "MANL_CPI_ATA1")
    private Integer manlCpiAta1;

    @Column(name = "MANL_CPI_ATA2")
    private Integer manlCpiAta2;

    @Column(name = "FUEL_LOAD", length = 15)
    private String fuelLoad;

    @Column(name = "BALLAST", length = 15)
    private String ballast;

    @Column(name = "TEST_FLIGHT", length = 15)
    private String testFlight;

    @Column(name = "POWER_ON", length = 1)
    private String powerOn;

    @Column(name = "HYDRAULICS_ON", length = 1)
    private String hydraulicsOn;

    @Column(name = "TEMPLATE_ID", length = 15)
    private String templateId;

    @Column(name = "TEMPLATE_APPLIED_DT_TM")
    private Timestamp templateAppliedDtTm;

    @Column(name = "TEMPLATE_APPLIED_BY")
    private Long templateAppliedBy;

    @Column(name = "PREDOCK_SPAN")
    private Integer predockSpan;

    @Column(name = "WORKORDER_SPAN")
    private Integer workorderSpan;

    @Column(name = "POSTDOCK_SPAN")
    private Integer postdockSpan;

    @Column(name = "BALLAST_FLAG", length = 1)
    private String ballastFlag;

    @Column(name = "LAV_SVC_FLAG", length = 1)
    private String lavSvcFlag;

    @Column(name = "PLANNER_EMPID")
    private Long plannerEmpId;

    @Column(name = "PLANNER_TWO_EMPID")
    private Long plannerTwoEmpId;

    @Column(name = "LEAD_AMT_TWO_EMPID")
    private Long leadAmtTwoEmpId;

    @Column(name = "FIREWALL_HOUR", precision = 4, scale = 1)
    private BigDecimal firewallHour;

    @Column(name = "SHOW_FIREWALL", length = 1)
    private String showFirewall;

    @Column(name = "MAXI_REPORTED_DT_TM")
    private Timestamp maxiReportedDtTm;

    @Column(name = "MAXI_EXCEP_VIEWED_DT_TM")
    private Timestamp maxiExcepViewedDtTm;

    @Column(name = "CLEARDOWN_FLAG", length = 1)
    private String cleardownFlag;

    @Column(name = "SYSTEM_DRAWING", length = 20)
    private String systemDrawing;

    @Column(name = "PLACARD_DRAWING", length = 20)
    private String placardDrawing;

    @Column(name = "BURN_RATE", length = 4)
    private String burnRate;

    @Column(name = "MJCS_STATUS", length = 1)
    private String mjcsStatus;

    @Column(name = "WO_ACN", length = 6)
    private String woAcn;
}


