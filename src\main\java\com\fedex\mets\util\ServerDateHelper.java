package com.fedex.mets.util;

import lombok.experimental.UtilityClass;

import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.SimpleTimeZone;
import java.util.StringTokenizer;

@UtilityClass
public class ServerDateHelper {
    /**
     * Method to convert the String date format in the required date format.
     *
     * @ params String aDate(yyyy-MM-dd hh:mm:ss:SSS).
     * @ return String date(MM/dd/yyyy hh:mm:ss).
     */
    public static String getLookUpFormat(String dateString) {
        String month = "01", day = "01", year = "00", time = "00:00:00";

        StringBuffer sbuff = new StringBuffer("");
        if (dateString != null && dateString.trim().length() > 0) {
            StringTokenizer stok = new StringTokenizer(dateString, "-");
            if (stok.countTokens() == 3) {
                year = stok.nextToken();
                year = year.substring(2, 4);

                month = stok.nextToken();

                String tempStr = stok.nextToken();
                day = tempStr.substring(0, 2);

                time = tempStr.substring(2, tempStr.length());
                time = time.trim();
                time = time.substring(0, 8);
            }
        }

        sbuff.append(month);
        sbuff.append("/");
        sbuff.append(day);
        sbuff.append("/");
        sbuff.append(year);
        sbuff.append(" ");
        sbuff.append(time);

        return sbuff.toString();
    }

    /**
     * Method to convert the String date format in the required date format.
     *
     * @ params String aDate(yyyy-MM-dd hh:mm:ss:SSS).
     * @ return String date(MM/dd/yyyy hh:mm).
     */
    public static String getLookUpFormatHH_MM(String dateString) {
        String month = "01", day = "01", year = "00", time = "00:00";
        StringBuffer sbuff = new StringBuffer("");
        if (dateString != null && dateString.trim().length() > 0) {
            StringTokenizer stok = new StringTokenizer(dateString, "-");
            if (stok.countTokens() == 3) {
                year = stok.nextToken();
                year = year.substring(2, 4);

                month = stok.nextToken();

                String tempStr = stok.nextToken();
                day = tempStr.substring(0, 2);

                time = tempStr.substring(2, tempStr.length());
                time = time.trim();
                time = time.substring(0, 5);
            }
        }

        sbuff.append(month);
        sbuff.append("/");
        sbuff.append(day);
        sbuff.append("/");
        sbuff.append(year);
        sbuff.append(" ");
        sbuff.append(time);

        return sbuff.toString();
    }

    /**
     * Method to convert the data format in the required date format retreived from the database.
     *
     * @ params String aDate(yyyy-MM-dd hh:mm:ss:SSS).
     * @ return String date(yyyyMMdd).
     */
    public static String getDateFormat(String aDate) {
        StringTokenizer stok = new StringTokenizer(aDate, "-");

        String year = stok.nextToken();
        String month = stok.nextToken();
        String date = stok.nextToken();

        date = date.substring(0, 2);

        return year + month + date;
    }

    /**
     * Method to convert the data format in the required date format retreived from the database.
     *
     * @ params String aDate(yyyy-MM-dd hh:mm:ss:SSS).
     * @ return String date(ddMMMyy hh:mm).
     */
    public static String getFormattedDate(String aDate) {
        String time = "00:00";
        String tempDate = "";
        String tempMonth = "";
        String tempYear = "";
        StringBuffer sBuff = new StringBuffer("");
        StringTokenizer stok = new StringTokenizer(aDate, "-");
        if (stok.countTokens() == 3) {
            String year = stok.nextToken();
            String month = stok.nextToken();
            String date = stok.nextToken();

            tempYear = year.substring(2, 4);
            tempDate = date.substring(0, 2);
            time = date.substring(3, 8);

            if (month.equals("01"))
                tempMonth = "JAN";
            else if (month.equals("02"))
                tempMonth = "FEB";
            else if (month.equals("03"))
                tempMonth = "MAR";
            else if (month.equals("04"))
                tempMonth = "APR";
            else if (month.equals("05"))
                tempMonth = "MAY";
            else if (month.equals("06"))
                tempMonth = "JUN";
            else if (month.equals("07"))
                tempMonth = "JUL";
            else if (month.equals("08"))
                tempMonth = "AUG";
            else if (month.equals("09"))
                tempMonth = "SEP";
            else if (month.equals("10"))
                tempMonth = "OCT";
            else if (month.equals("11"))
                tempMonth = "NOV";
            else if (month.equals("12"))
                tempMonth = "DEC";
        }
        sBuff.append(tempDate);
        sBuff.append(tempMonth);
        sBuff.append(tempYear);
        sBuff.append(" ");
        sBuff.append(time);
        return tempDate + tempMonth + tempYear + " " + time;
    }

    /**
     * Method to get the current timestamp in the required date format.
     *
     * @ return String timestamp(yyyy-MM-dd HH:mm:s).
     */
    public static String getCurrentTimeStamp() {
        java.util.Date d = new java.util.Date();
        SimpleDateFormat sdFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdFormat.setTimeZone(new SimpleTimeZone(0, "UTC"));

        String currentTimeStamp = sdFormat.format(d);

        return currentTimeStamp;
    }

    /**
     * Method to get the current timestamp in the required date format. This
     * method is used to set the return data datetime value to be displayed in
     * the client.
     *
     * @ return String timestamp(ddMMMyy HH:mm:ss).
     */
    public static String getCurrentDateTimeStamp() {
        java.util.Date d = new java.util.Date();
        SimpleDateFormat sdFormat = new SimpleDateFormat("ddMMMyy HH:mm:ss");
        sdFormat.setTimeZone(new SimpleTimeZone(0, "UTC"));

        String currentTimeStamp = sdFormat.format(d);

        return currentTimeStamp;
    }

    /**
     * Method to convert the data format in the required date format retreived from the database.
     *
     * @ params String aDate(ddMMMdd hh:mm).
     * @ return String date(MM/dd/yy hh:mm).
     */
    public static String getFormattedDate_2(String aDate) {
        String day = "01", month = "01", year = "02", time = "00:00";
        StringBuffer sBuff = new StringBuffer("");
        if (aDate != null) {
            day = aDate.substring(0, 2);
            String tempMonth = aDate.substring(2, 5);
            year = aDate.substring(5, 7);
            time = aDate.substring(7, aDate.length());

            if (tempMonth.equalsIgnoreCase("JAN"))
                month = "01";
            else if (tempMonth.equalsIgnoreCase("FEB"))
                month = "02";
            else if (tempMonth.equalsIgnoreCase("MAR"))
                month = "03";
            else if (tempMonth.equalsIgnoreCase("APR"))
                month = "04";
            else if (tempMonth.equalsIgnoreCase("MAY"))
                month = "05";
            else if (tempMonth.equalsIgnoreCase("JUN"))
                month = "06";
            else if (tempMonth.equalsIgnoreCase("JUL"))
                month = "07";
            else if (tempMonth.equalsIgnoreCase("AUG"))
                month = "08";
            else if (tempMonth.equalsIgnoreCase("SEP"))
                month = "09";
            else if (tempMonth.equalsIgnoreCase("OCT"))
                month = "10";
            else if (tempMonth.equalsIgnoreCase("NOV"))
                month = "11";
            else if (tempMonth.equalsIgnoreCase("DEC"))
                month = "12";
        }

        sBuff.append(month);
        sBuff.append("/");
        sBuff.append(day);
        sBuff.append("/");
        sBuff.append(year);
        sBuff.append(" ");
        sBuff.append(time);

        return sBuff.toString();
    }

    /**
     * Method to convert the total minutes format in the required HH:MM format.
     *
     * @ params int delayTime.
     * @ return String HH:MM.
     */
    public static String getFormatedTime(int delayTime) {
        String hours = "", minutes = "";

        if (delayTime > 60) {
            int intHours = (delayTime / 60);
            int intMinutes = (delayTime % 60);

            if (intHours > 0 && intHours < 10) {
                hours = "0" + intHours;
            } else {
                hours = "" + intHours;
            }

            if (intMinutes > 0 && intMinutes < 10) {
                minutes = "0" + intMinutes;
            } else {
                minutes = "" + intMinutes;
            }

        } else {
            hours = "00";
            minutes = "" + delayTime;

            if (minutes.trim().length() == 1) {
                minutes = "0" + minutes;
            }

        }

        return hours + ":" + minutes;
    }

    /**
     * Method to get the convert the String format to Timestamp.
     *
     * @params String(yyyy - MM - dd HH : mm : ss)
     * @ return java.sql.timestamp(yyyy-MM-dd HH:mm:ss.SSS).
     */
    public static java.sql.Timestamp getConvertedTimestamp(String aDate) {
            java.util.Date tempConvertedDate = null;
            java.sql.Timestamp tempConvertedTimestamp = null;

            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");

            tempConvertedDate = dateFormat.parse(aDate, new ParsePosition(0));
            tempConvertedTimestamp =
                    new java.sql.Timestamp(tempConvertedDate.getTime());

            return tempConvertedTimestamp;

    }

    /**
     * Method to get the convert the String format to Timestamp.
     *
     * @params String(yyyy - MM - dd HH : mm : ss)
     * @ return java.sql.timestamp(yyyy-MM-dd HH:mm:ss.SSS).
     */
    public static java.sql.Timestamp getConvertedTimestampUTC(String aDate) {
        java.util.Date tempConvertedDate = null;
        java.sql.Timestamp tempConvertedTimestamp = null;

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        dateFormat.setTimeZone(new SimpleTimeZone(0, "UTC"));

        tempConvertedDate = dateFormat.parse(aDate, new ParsePosition(0));
        tempConvertedTimestamp =
                new java.sql.Timestamp(tempConvertedDate.getTime());

        return tempConvertedTimestamp;
    }

    /**
     * Method to get the convert the String format to SQL Date.
     *
     * @params String(yyyy - MM - dd HH : mm : ss)
     * @ return java.sql.timestamp(yyyy-MM-dd HH:mm:ss.SSS).
     */
    public static java.sql.Date getConvertedSQLDate(String aDate) {
        java.util.Date tempConvertedDate = null;
        java.sql.Date tempConvertedSQLDate = null;

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        tempConvertedDate = dateFormat.parse(aDate, new ParsePosition(0));
        tempConvertedSQLDate = new java.sql.Date(tempConvertedDate.getTime());

        return tempConvertedSQLDate;
    }

    /**
     * Method to get the current timestamp in the required Timestamp format.
     *
     * @ return java.sql.Timestamp (yyyy-MM-dd HH:mm:ss).
     */
    public static java.sql.Timestamp getTimeStamp() {
        java.util.Date date = new java.util.Date(), tempCurrentDate = null;
        java.sql.Timestamp tempCurrentTimestamp = null;

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        SimpleDateFormat sdFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdFormat.setTimeZone(new SimpleTimeZone(0, "UTC"));

        String strCurrentDate = sdFormat.format(date);

        tempCurrentDate = dateFormat.parse(strCurrentDate, new ParsePosition(0));

        tempCurrentTimestamp = new java.sql.Timestamp(tempCurrentDate.getTime());

        return tempCurrentTimestamp;
    }

    /**
     * Method to get the current Date in the required format.
     *
     * @ return java.sql.Date (yyyy-MM-dd HH:mm:ss) UTC.
     */
    public static java.util.Date getDate_UTC() {
        java.util.Date date = new java.util.Date(), tempCurrentDate = null;

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdFormat.setTimeZone(new SimpleTimeZone(0, "UTC"));

        String strCurrentDate = sdFormat.format(date);

        tempCurrentDate = dateFormat.parse(strCurrentDate, new ParsePosition(0));

        return tempCurrentDate;
    }

    /**
     * Method to convert the data format in the required date format retreived from the database.
     *
     * @ params String date(yyyyMMdd).
     * @ return String aDate(yyyy-MM-dd hh:mm:ss:SSS).
     */
    public static String getN8toTimestamp(String aDate) {
        StringBuffer sBuff = new StringBuffer("");
        String year = aDate.substring(0, 4);
        String month = aDate.substring(4, 6);
        String date = aDate.substring(6, 8);
        String hoursMinutes = "00:00:00.0";

        sBuff.append(year);
        sBuff.append("-");
        sBuff.append(month);
        sBuff.append("-");
        sBuff.append(date);
        sBuff.append(" ");
        sBuff.append(hoursMinutes);

        return year + "-" + month + "-" + date + " " + hoursMinutes;
    }


    /**
     * Method to convert the data format in the required date format retreived from the database.
     *
     * @ params String date(yyyyMMdd).
     * @ return String aDate(yyyy-MM-dd hh:mm:ss).
     */
    public static String getTimestampFormat(String aDate) {
        StringBuffer sBuff = new StringBuffer("");
        String year = aDate.substring(0, 4);
        String month = aDate.substring(4, 6);
        String date = aDate.substring(6, 8);
        String hoursMinutes = "00:00:00";

        sBuff.append(year);
        sBuff.append("-");
        sBuff.append(month);
        sBuff.append("-");
        sBuff.append(date);
        sBuff.append(" ");
        sBuff.append(hoursMinutes);

        return year + "-" + month + "-" + date + " " + hoursMinutes;
    }
}
