package com.fedex.mets.service.addEvent;

import com.fedex.mets.data.ActiveEventResults;
import com.fedex.mets.data.ListViewData;
import com.fedex.mets.data.WizardEventData;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.servlet.MockMvc;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ExtendWith(MockitoExtension .class)
public class EventEntryServiceTest {

        @InjectMocks
        private EventEntryService eventEntryService;

        @Mock
        private ActiveEventService activeEventService;

        @Mock
        private ValidateDoaEventService validateDoaEventService;

        @Mock
        private AddAircraftEventService addAircraftEventService;

        private WizardEventData testPayload=new WizardEventData();


        @Autowired
        private MockMvc mockMvc;

        @BeforeEach
        void dataSetUp() {
            testPayload.setUserId("1");
            testPayload.setTokenId("1");
            testPayload.setACN("215");
            testPayload.setAccessLevel("1");
            testPayload.setServerError("");
            testPayload.setAddNewEvent(true);

        }
        @Test
        void testfindActiveEvent() throws Exception {

            testPayload.setEventType("OOS");
            testPayload.setStatus("DWN");

            ActiveEventResults activeEventResponse = new ActiveEventResults();
            List<ListViewData> activeEvents = new ArrayList<>();

            List<ListViewData> overridenEvents=new ArrayList<>();
            activeEvents.add(new ListViewData(1));
            activeEvents.get(0).setType("OOS");
            activeEvents.get(0).setEventID(1);
            activeEventResponse.setOverridenEvents(overridenEvents);
            activeEventResponse.setActiveEvents(activeEvents);

            List<ListViewData> doaActiveEventList = new ArrayList<>();

            List<ListViewData> unReviewedEvents = new ArrayList<>();


            Mockito.when(activeEventService.findActiveEvents(ArgumentMatchers.anyString())).thenReturn(activeEventResponse);
            ActiveEventResults result = activeEventService.findActiveEvents(testPayload.getACN());
            Mockito.verify(activeEventService, Mockito.times(1)).findActiveEvents(ArgumentMatchers.anyString());

            Mockito.when(validateDoaEventService.validateDOAEvents(ArgumentMatchers.anyString())).thenReturn(doaActiveEventList);
            validateDoaEventService.validateDOAEvents("");
            Mockito.verify(validateDoaEventService, Mockito.times(1)).validateDOAEvents(ArgumentMatchers.anyString());

            Mockito.when(activeEventService.findUnReviewedEvents(ArgumentMatchers.anyString())).thenReturn(unReviewedEvents);
            activeEventService.findUnReviewedEvents("");
            Mockito.verify(activeEventService, Mockito.times(1)).findUnReviewedEvents(ArgumentMatchers.anyString());


            HashMap<String,Object> responseHashMap = new HashMap<>();
            responseHashMap.put("ERROR","Aircraft "+ testPayload.getACN()+" has active events requiring action and a "+testPayload.getEventType() +"cannot be added.\nPlease return.");
            responseHashMap.put("ACTION_REQUIRED_EVENTS", List.of(new ListViewData(1)));

            testPayload.setAddNewEvent(false);
            Map<String, Object> res = eventEntryService.addEvent(testPayload);

            Assertions.assertNotNull(res);
            Assertions.assertTrue(res.containsKey("ACTION_REQUIRED_EVENTS"));
        }


    @Test
    void testDoaActiveEvent() throws Exception {

        testPayload.setEventType("DOA");
        testPayload.setStatus("DOA");

        ActiveEventResults activeEventResponse = new ActiveEventResults();
        List<ListViewData> activeEvents = new ArrayList<>();
        activeEvents.add(new ListViewData(1));
        activeEvents.get(0).setEventID(1);

        List<ListViewData> overridenEvents=new ArrayList<>();

        activeEvents.get(0).setType("DOA");
        activeEventResponse.setOverridenEvents(overridenEvents);
        activeEventResponse.setActiveEvents(activeEvents);

        List<ListViewData> doaActiveEventList = new ArrayList<>();
        doaActiveEventList.add(new ListViewData(1));

        List<ListViewData> unReviewedEvents = new ArrayList<>();
        unReviewedEvents.add(new ListViewData(1));

        Mockito.when(activeEventService.findActiveEvents(ArgumentMatchers.anyString())).thenReturn(activeEventResponse);
        ActiveEventResults result = activeEventService.findActiveEvents(testPayload.getACN());
        Mockito.verify(activeEventService, Mockito.times(1)).findActiveEvents(ArgumentMatchers.anyString());

        Mockito.when(validateDoaEventService.validateDOAEvents(ArgumentMatchers.anyString())).thenReturn(doaActiveEventList);
        validateDoaEventService.validateDOAEvents("");
        Mockito.verify(validateDoaEventService, Mockito.times(1)).validateDOAEvents(ArgumentMatchers.anyString());

        Mockito.when(activeEventService.findUnReviewedEvents(ArgumentMatchers.anyString())).thenReturn(unReviewedEvents);
        activeEventService.findUnReviewedEvents("");
        Mockito.verify(activeEventService, Mockito.times(1)).findUnReviewedEvents(ArgumentMatchers.anyString());


        HashMap<String,Object> responseHashMap = new HashMap<>();
        responseHashMap.put("ERROR","Aircraft "+ testPayload.getACN()+" has active events requiring action and a "+testPayload.getEventType() +"cannot be added.\nPlease return.");
        responseHashMap.put("ACTION_REQUIRED_EVENTS", List.of(new ListViewData(1)));

        Map<String, Object> res = eventEntryService.addEvent(testPayload);

        Assertions.assertNotNull(res);
        Assertions.assertTrue(res.containsKey("ACTION_REQUIRED_EVENTS"));
    }

    @Test
    void testOverridableActiveEvent() throws Exception {

        testPayload.setEventType("OOS");
        testPayload.setStatus("DWN");

        ActiveEventResults activeEventResponse = new ActiveEventResults();
        List<ListViewData> activeEvents = new ArrayList<>();
        activeEvents.add(new ListViewData(1));

        List<ListViewData> overridenEvents=new ArrayList<>();
        overridenEvents.add(new ListViewData(1));

        activeEvents.get(0).setType("OOS");
        activeEventResponse.setOverridenEvents(overridenEvents);
        activeEventResponse.setActiveEvents(activeEvents);

        List<ListViewData> doaActiveEventList = new ArrayList<>();
        doaActiveEventList.add(new ListViewData(1));

        List<ListViewData> unReviewedEvents = new ArrayList<>();
        unReviewedEvents.add(new ListViewData(1));

        Mockito.when(activeEventService.findActiveEvents(ArgumentMatchers.anyString())).thenReturn(activeEventResponse);
        ActiveEventResults result = activeEventService.findActiveEvents(testPayload.getACN());
        Mockito.verify(activeEventService, Mockito.times(1)).findActiveEvents(ArgumentMatchers.anyString());


        HashMap<String,Object> responseHashMap = new HashMap<>();
        responseHashMap.put("ERROR","Aircraft "+ testPayload.getACN()+" has the following active event which has been called UP \nbut has not yet been confirmed from SUPER.\nDo you want to continue adding the new OOS event?");
        responseHashMap.put("OVERRIDABLE_EVENTS", List.of(new ListViewData(1)));

        Map<String, Object> res = eventEntryService.addEvent(testPayload);

        Assertions.assertNotNull(res);
        Assertions.assertTrue(res.containsKey("OVERRIDABLE_EVENTS"));
    }


    @Test
    void testAddingEvent() throws Exception {

        testPayload.setEventType("DOA");
        testPayload.setStatus("DOA");

        ActiveEventResults activeEventResponse = new ActiveEventResults();
        List<ListViewData> activeEvents = new ArrayList<>();
        activeEvents.add(new ListViewData(1));

        List<ListViewData> overridenEvents=new ArrayList<>();
        activeEvents.get(0).setType("OOS");
        activeEventResponse.setOverridenEvents(overridenEvents);
        activeEventResponse.setActiveEvents(activeEvents);

        Mockito.when(activeEventService.findActiveEvents(ArgumentMatchers.anyString())).thenReturn(activeEventResponse);
        ActiveEventResults result = activeEventService.findActiveEvents(testPayload.getACN());
        Mockito.verify(activeEventService, Mockito.times(1)).findActiveEvents(ArgumentMatchers.anyString());


        WizardEventData mockResponse = new WizardEventData();
        mockResponse.setEventId(1);

        Mockito.when(addAircraftEventService.addAircraftEvent(ArgumentMatchers.any())).thenReturn(mockResponse);
        addAircraftEventService.addAircraftEvent(testPayload);
        Mockito.verify(addAircraftEventService, Mockito.times(1)).addAircraftEvent(ArgumentMatchers.any());



        HashMap<String,Object> responseHashMap = new HashMap<>();
        responseHashMap.put("SUCCESS","The Event "+ testPayload.getEventId()+" was successfully added.");
        responseHashMap.put("ADDED_EVENT_DATA", List.of(new ListViewData(1)));

        Map<String, Object> res = eventEntryService.addEvent(testPayload);

        Assertions.assertNotNull(res);
        Assertions.assertTrue(res.containsKey("SUCCESS"));
    }

    @Test
    void testOverridingRequest() throws Exception {

        testPayload.setEventType("OOS");
        testPayload.setStatus("DWN");
        testPayload.setOverrideRequest(true);

        ActiveEventResults activeEventResponse = new ActiveEventResults();
        List<ListViewData> activeEvents = new ArrayList<>();
        activeEvents.add(new ListViewData(1));

        List<ListViewData> overridenEvents=new ArrayList<>();
        overridenEvents.add(new ListViewData(1));

        activeEvents.get(0).setType("OOS");
        activeEventResponse.setOverridenEvents(overridenEvents);
        activeEventResponse.setActiveEvents(activeEvents);

        Mockito.when(activeEventService.findActiveEvents(ArgumentMatchers.anyString())).thenReturn(activeEventResponse);
        ActiveEventResults result = activeEventService.findActiveEvents(testPayload.getACN());
        Mockito.verify(activeEventService, Mockito.times(1)).findActiveEvents(ArgumentMatchers.anyString());

        WizardEventData mockResponse = new WizardEventData();
        mockResponse.setEventId(1);

        Mockito.when(addAircraftEventService.addAircraftEvent(ArgumentMatchers.any())).thenReturn(mockResponse);
        addAircraftEventService.addAircraftEvent(testPayload);
        Mockito.verify(addAircraftEventService, Mockito.times(1)).addAircraftEvent(ArgumentMatchers.any());


        HashMap<String,Object> responseHashMap = new HashMap<>();
        responseHashMap.put("SUCCESS","The Event "+ testPayload.getEventId()+" was successfully added.");
        responseHashMap.put("WIZARD_EVENT_DATA", List.of(new ListViewData(1)));

        Map<String, Object> res = eventEntryService.addEvent(testPayload);

        Assertions.assertNotNull(res);
        Assertions.assertTrue(res.containsKey("SUCCESS"));
    }
    @Test
    void testOverridingRequestUnReviewedEvent_1() throws Exception {

        testPayload.setEventType("OOS");
        testPayload.setStatus("DWN");
        testPayload.setOverrideRequest(false);

        ActiveEventResults activeEventResponse = new ActiveEventResults();
        List<ListViewData> activeEvents = new ArrayList<>();


        List<ListViewData> overridenEvents=new ArrayList<>();
        activeEvents.add(new ListViewData(1));
        activeEvents.get(0).setType("OOS");
        overridenEvents.add(new ListViewData(1));
        overridenEvents.get(0).setEventID(1);
        activeEventResponse.setOverridenEvents(overridenEvents);
        activeEventResponse.setActiveEvents(activeEvents);

        List<ListViewData> doaActiveEventList = new ArrayList<>();

        List<ListViewData> unReviewedEvents = new ArrayList<>();
        unReviewedEvents.add(new ListViewData(1));


        Mockito.when(activeEventService.findActiveEvents(ArgumentMatchers.anyString())).thenReturn(activeEventResponse);
        ActiveEventResults result = activeEventService.findActiveEvents(testPayload.getACN());
        Mockito.verify(activeEventService, Mockito.times(1)).findActiveEvents(ArgumentMatchers.anyString());

        Mockito.when(validateDoaEventService.validateDOAEvents(ArgumentMatchers.anyString())).thenReturn(doaActiveEventList);
        validateDoaEventService.validateDOAEvents("");
        Mockito.verify(validateDoaEventService, Mockito.times(1)).validateDOAEvents(ArgumentMatchers.anyString());

        Mockito.when(activeEventService.findUnReviewedEvents(ArgumentMatchers.anyString())).thenReturn(unReviewedEvents);
        activeEventService.findUnReviewedEvents("");
        Mockito.verify(activeEventService, Mockito.times(1)).findUnReviewedEvents(ArgumentMatchers.anyString());


        HashMap<String,Object> responseHashMap = new HashMap<>();
        responseHashMap.put("ERROR","Aircraft "+ testPayload.getACN()+" has a closed"+testPayload.getEventType()+" Event requiring Duty Manager's Review");
        responseHashMap.put("UNREVIEWED_EVENTS", List.of(new ListViewData(1)));

        Map<String, Object> res = eventEntryService.addEvent(testPayload);

        Assertions.assertNotNull(res);
        Assertions.assertTrue(res.containsKey("UNREVIEWED_EVENTS"));
    }

    @Test
    void testOverridingDOAEvent_1() throws Exception {

        testPayload.setEventType("DOA");
        testPayload.setStatus("DOA");
        testPayload.setOverrideRequest(true);

        ActiveEventResults activeEventResponse = new ActiveEventResults();
        List<ListViewData> activeEvents = new ArrayList<>();
        activeEvents.add(new ListViewData(1));

        List<ListViewData> overridenEvents=new ArrayList<>();
        overridenEvents.add(new ListViewData(1));

        activeEvents.get(0).setType("OOS");
        activeEventResponse.setOverridenEvents(overridenEvents);
        activeEventResponse.setActiveEvents(activeEvents);

        Mockito.when(activeEventService.findActiveEvents(ArgumentMatchers.anyString())).thenReturn(activeEventResponse);
        ActiveEventResults result = activeEventService.findActiveEvents(testPayload.getACN());
        Mockito.verify(activeEventService, Mockito.times(1)).findActiveEvents(ArgumentMatchers.anyString());

        WizardEventData mockResponse = new WizardEventData();
        mockResponse.setEventId(1);

        Mockito.when(addAircraftEventService.addAircraftEvent(ArgumentMatchers.any())).thenReturn(mockResponse);
        addAircraftEventService.addAircraftEvent(testPayload);
        Mockito.verify(addAircraftEventService, Mockito.times(1)).addAircraftEvent(ArgumentMatchers.any());


        HashMap<String,Object> responseHashMap = new HashMap<>();
        responseHashMap.put("SUCCESS","The Event "+ testPayload.getEventId()+" was successfully added.");
        responseHashMap.put("WIZARD_EVENT_DATA", List.of(new ListViewData(1)));

        Map<String, Object> res = eventEntryService.addEvent(testPayload);
        Assertions.assertNotNull(res);
        Assertions.assertTrue(res.containsKey("SUCCESS"));
    }
    @Test
    void testOverridingDOAEvent_2() throws Exception {

        testPayload.setEventType("DOA");
        testPayload.setStatus("DOA");
        testPayload.setOverrideRequest(false);

        ActiveEventResults activeEventResponse = new ActiveEventResults();
        List<ListViewData> activeEvents = new ArrayList<>();
        activeEvents.add(new ListViewData(1));

        List<ListViewData> overridenEvents=new ArrayList<>();
        overridenEvents.add(new ListViewData(1));

        activeEvents.get(0).setType("OOS");
        activeEventResponse.setOverridenEvents(overridenEvents);
        activeEventResponse.setActiveEvents(activeEvents);

        Mockito.when(activeEventService.findActiveEvents(ArgumentMatchers.anyString())).thenReturn(activeEventResponse);
        ActiveEventResults result = activeEventService.findActiveEvents(testPayload.getACN());
        Mockito.verify(activeEventService, Mockito.times(1)).findActiveEvents(ArgumentMatchers.anyString());

        WizardEventData mockResponse = new WizardEventData();
        mockResponse.setEventId(1);

        Mockito.when(addAircraftEventService.addAircraftEvent(ArgumentMatchers.any())).thenReturn(mockResponse);
        addAircraftEventService.addAircraftEvent(testPayload);
        Mockito.verify(addAircraftEventService, Mockito.times(1)).addAircraftEvent(ArgumentMatchers.any());


        HashMap<String,Object> responseHashMap = new HashMap<>();
        responseHashMap.put("ERROR","Aircraft "+ testPayload.getACN()+" has the following active event which has been called UP \nbut has not yet been confirmed from SUPER.\nDo you want to continue adding the new OOS event?");
        responseHashMap.put("OVERRIDABLE_EVENTS", List.of(new ListViewData(1)));

        Map<String, Object> res = eventEntryService.addEvent(testPayload);

        Assertions.assertNotNull(res);
        Assertions.assertTrue(res.containsKey("OVERRIDABLE_EVENTS"));
    }

    @Test
    void testOverridingDOAEventError_1() throws Exception {

        testPayload.setEventType("DOA");
        testPayload.setStatus("DOA");
        testPayload.setOverrideRequest(true);

        ActiveEventResults activeEventResponse = new ActiveEventResults();
        List<ListViewData> activeEvents = new ArrayList<>();
        activeEvents.add(new ListViewData(1));

        List<ListViewData> overridenEvents=new ArrayList<>();
        overridenEvents.add(new ListViewData(1));

        activeEvents.get(0).setType("OOS");
        activeEventResponse.setOverridenEvents(overridenEvents);
        activeEventResponse.setActiveEvents(activeEvents);

        Mockito.when(activeEventService.findActiveEvents(ArgumentMatchers.anyString())).thenReturn(activeEventResponse);
        ActiveEventResults result = activeEventService.findActiveEvents(testPayload.getACN());
        Mockito.verify(activeEventService, Mockito.times(1)).findActiveEvents(ArgumentMatchers.anyString());

        WizardEventData mockResponse = new WizardEventData();
        mockResponse.setACN("215");
        mockResponse.setServerError("Server Errror");

        Mockito.when(addAircraftEventService.addAircraftEvent(ArgumentMatchers.any())).thenReturn(mockResponse);
        addAircraftEventService.addAircraftEvent(testPayload);
        Mockito.verify(addAircraftEventService, Mockito.times(1)).addAircraftEvent(ArgumentMatchers.any());


        HashMap<String,Object> responseHashMap = new HashMap<>();
        responseHashMap.put("ERROR",testPayload.getServerError());

        Map<String, Object> res = eventEntryService.addEvent(testPayload);

        Assertions.assertNotNull(res);
    }

}