
package com.fedex.mets.wsdl.msn.shortagenotice;

import com.fedex.mets.wsdl.msn.AuthSourceSysType;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for sessionType complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="sessionType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="userId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="token" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="authSourceSysName" type="{http://fedex.com/airops/maxi/services/jaxws}authSourceSysType"/>
 *         &lt;element name="userStation" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="userDepartment" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="printerId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "sessionType", propOrder = {
    "userId",
    "token",
    "authSourceSysName",
    "userStation",
    "userDepartment",
    "printerId"
})
public class SessionType {

    @XmlElement(required = true)
    protected String userId;
    @XmlElement(required = true)
    protected String token;
    @XmlElement(required = true)
    protected AuthSourceSysType authSourceSysName;
    @XmlElement(required = true)
    protected String userStation;
    @XmlElement(required = true)
    protected String userDepartment;
    @XmlElement(required = true)
    protected String printerId;

    /**
     * Gets the value of the userId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUserId() {
        return userId;
    }

    /**
     * Sets the value of the userId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUserId(String value) {
        this.userId = value;
    }

    /**
     * Gets the value of the token property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getToken() {
        return token;
    }

    /**
     * Sets the value of the token property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setToken(String value) {
        this.token = value;
    }

    /**
     * Gets the value of the authSourceSysName property.
     * 
     * @return
     *     possible object is
     *     {@link AuthSourceSysType }
     *     
     */
    public AuthSourceSysType getAuthSourceSysName() {
        return authSourceSysName;
    }

    /**
     * Sets the value of the authSourceSysName property.
     * 
     * @param value
     *     allowed object is
     *     {@link AuthSourceSysType }
     *     
     */
    public void setAuthSourceSysName(AuthSourceSysType value) {
        this.authSourceSysName = value;
    }

    /**
     * Gets the value of the userStation property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUserStation() {
        return userStation;
    }

    /**
     * Sets the value of the userStation property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUserStation(String value) {
        this.userStation = value;
    }

    /**
     * Gets the value of the userDepartment property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUserDepartment() {
        return userDepartment;
    }

    /**
     * Sets the value of the userDepartment property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUserDepartment(String value) {
        this.userDepartment = value;
    }

    /**
     * Gets the value of the printerId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPrinterId() {
        return printerId;
    }

    /**
     * Sets the value of the printerId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPrinterId(String value) {
        this.printerId = value;
    }

}
