package com.fedex.mets.repository.mets;

import com.fedex.mets.entity.mets.ChangeRequestHistory;
import com.fedex.mets.entity.mets.ChangeRequestHistoryPk;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;

@Repository
public interface ChangeRequestHistoryRepository extends JpaRepository<ChangeRequestHistory, ChangeRequestHistoryPk> {

    @Query(value = "select * from CHANGE_REQUEST_HIST where EVENT_ID=:eventId and ACN=:acn and CREATED_DT_TM=:dtTm",nativeQuery = true)
    public ChangeRequestHistory getChangeRequestHistory(@Param("eventId") int eventId, @Param("acn") String acn, @Param("dtTm") Timestamp dtTm);

    @Modifying
    @Transactional
    @Query(value = "update CHANGE_REQUEST_HIST SET ENTERED_IN_ERROR='Y' where EVENT_ID=:eventId and ACN=:acn and CREATED_DT_TM=:dtTm",nativeQuery = true)
    public void updateEnteredInErrorValue(@Param("eventId") int eventId, @Param("acn") String acn, @Param("dtTm") Timestamp dtTm);

    @Modifying
    @Transactional
    @Query(value = "update CHANGE_REQUEST_HIST SET REQUEST_STATUS='R',LAST_UPDATE_DT_TM=:dtTm where EVENT_ID=:eventId and ACN=:acn",nativeQuery = true)
    public void updateRequestStatus( @Param("dtTm") Timestamp dtTm,@Param("eventId") int eventId,@Param("acn") String acn);

    @Query(value = "select max(LAST_UPDATE_DT_TM) from CHANGE_REQUEST_HIST where EVENT_ID=:eventId",nativeQuery = true)
    public Timestamp getLastUpdateDtTm(@Param("eventId") int eventId);

    @Query(value = "select max(LAST_UPDATE_DT_TM) from CHANGE_REQUEST_HIST where EVENT_ID=:eventId and ACN=:acn",nativeQuery = true)
    public Timestamp getLastUpdateDtTmByEventIdAndAcn(@Param("eventId") int eventId, @Param("acn") String acn);

    @Query(value = "select CREATED_DT_TM from CHANGE_REQUEST_HIST where EVENT_ID=:eventId and ACN=:acn and LAST_UPDATE_DT_TM=:dtTm",nativeQuery = true)
    public Timestamp getCreatedDtTmByEventIdAndAcnAndLastUpdateDtTm(@Param("eventId") int eventId, @Param("acn") String acn, @Param("dtTm") Timestamp dtTm);


}
