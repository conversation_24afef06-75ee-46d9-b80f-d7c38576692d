
package com.fedex.mets.wsdl.discrepancy;

import java.math.BigDecimal;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;



/**
 * <p>Java class for dscrpMaintUpdtRefTag complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="dscrpMaintUpdtRefTag">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="dscrpMaintUpdtRefTagOid" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="userUpdateType" type="{http://www.fedex.com/airops/schemas/EnumTypes.xsd}UserUpdateTypeEnum"/>
 *         &lt;element name="tagCd" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="AddTm" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="AddUserId" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="UpdateProgramNm" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="UpdateTm" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="UpdateUserId" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "dscrpMaintUpdtRefTag", propOrder = {
    "dscrpMaintUpdtRefTagOid",
    "userUpdateType",
    "tagCd",
    "addTm",
    "addUserId",
    "updateProgramNm",
    "updateTm",
    "updateUserId"
})
public class DscrpMaintUpdtRefTag {

    protected BigDecimal dscrpMaintUpdtRefTagOid;
    @XmlElement(required = true)
    protected UserUpdateTypeEnum userUpdateType;
    protected String tagCd;
    @XmlElement(name = "AddTm")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar addTm;
    @XmlElement(name = "AddUserId")
    protected BigDecimal addUserId;
    @XmlElement(name = "UpdateProgramNm")
    protected String updateProgramNm;
    @XmlElement(name = "UpdateTm")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar updateTm;
    @XmlElement(name = "UpdateUserId")
    protected BigDecimal updateUserId;

    /**
     * Gets the value of the dscrpMaintUpdtRefTagOid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getDscrpMaintUpdtRefTagOid() {
        return dscrpMaintUpdtRefTagOid;
    }

    /**
     * Sets the value of the dscrpMaintUpdtRefTagOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setDscrpMaintUpdtRefTagOid(BigDecimal value) {
        this.dscrpMaintUpdtRefTagOid = value;
    }

    /**
     * Gets the value of the userUpdateType property.
     * 
     * @return
     *     possible object is
     *     {@link UserUpdateTypeEnum }
     *     
     */
    public UserUpdateTypeEnum getUserUpdateType() {
        return userUpdateType;
    }

    /**
     * Sets the value of the userUpdateType property.
     * 
     * @param value
     *     allowed object is
     *     {@link UserUpdateTypeEnum }
     *     
     */
    public void setUserUpdateType(UserUpdateTypeEnum value) {
        this.userUpdateType = value;
    }

    /**
     * Gets the value of the tagCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTagCd() {
        return tagCd;
    }

    /**
     * Sets the value of the tagCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTagCd(String value) {
        this.tagCd = value;
    }

    /**
     * Gets the value of the addTm property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getAddTm() {
        return addTm;
    }

    /**
     * Sets the value of the addTm property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setAddTm(XMLGregorianCalendar value) {
        this.addTm = value;
    }

    /**
     * Gets the value of the addUserId property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAddUserId() {
        return addUserId;
    }

    /**
     * Sets the value of the addUserId property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAddUserId(BigDecimal value) {
        this.addUserId = value;
    }

    /**
     * Gets the value of the updateProgramNm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUpdateProgramNm() {
        return updateProgramNm;
    }

    /**
     * Sets the value of the updateProgramNm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUpdateProgramNm(String value) {
        this.updateProgramNm = value;
    }

    /**
     * Gets the value of the updateTm property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getUpdateTm() {
        return updateTm;
    }

    /**
     * Sets the value of the updateTm property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setUpdateTm(XMLGregorianCalendar value) {
        this.updateTm = value;
    }

    /**
     * Gets the value of the updateUserId property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getUpdateUserId() {
        return updateUserId;
    }

    /**
     * Sets the value of the updateUserId property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setUpdateUserId(BigDecimal value) {
        this.updateUserId = value;
    }

}
