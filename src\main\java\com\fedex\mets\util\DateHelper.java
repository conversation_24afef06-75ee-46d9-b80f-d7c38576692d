package com.fedex.mets.util;
import java.util.GregorianCalendar;
import java.util.TimeZone;
public class DateHelper {
	public static boolean isValidN8Date(String theDate) {
		boolean valid = true;

		// first, validate the format
		if (theDate == null || theDate.trim().length() != 8) {
			valid = false;
		} else {
			for (int i = 0; i < theDate.length(); i++) {
				if (!Character.isDigit(theDate.charAt(i))) {
					valid = false;
					break;
				}
			}
		}
		// if valid format, check the validity of the date represented
		if (valid) {
			int month = Integer.parseInt(theDate.substring(4, 6));
			int day = Integer.parseInt(theDate.substring(6));
			int year = Integer.parseInt((theDate.substring(0, 4)));
			if (month == 1 || month == 3 || month == 5 || month == 7
					|| month == 8 || month == 10 || month == 12) {
				if (day < 1 || day > 31) {
					valid = false;
				}
			} else if (month == 4 || month == 6 || month == 9 || month == 11) {
				if (day < 1 || day > 30) {
					valid = false;
				}
			} else if (month == 2) {
				// is it a leap year?
				boolean isLeapYear = (year % 4 == 0);
				if (year % 100 == 0) {
					isLeapYear = (year % 400 == 0) ? true : false;
				}
				if (isLeapYear) {
					if (day < 1 || day > 29) {
						valid = false;
					}
				} else {
					if (day < 1 || day > 28) {
						valid = false;
					}
				}
			} else {
				valid = false;
			}
		}
		return valid;
	}

	/**
	 * Converts an N8 date (CCYYMMDD) to a GregorianCalendar object using Zulu
	 * Timezone.
	 *
	 * @param theDate
	 *            a String containing the N8 date to be converted
	 *
	 * @return GregorianCalendar an object representing the N8 date
	 */
	public static GregorianCalendar N8DateToGregorianCalendar(String theDate) {
		return N8DateToGregorianCalendarZulu(theDate);
	}

	/**
	 * Converts an N8 date (CCYYMMDD) to a GregorianCalendar object using Zulu
	 * Timezone
	 *
	 * @param theDate
	 *            a String containing the N8 date to be converted
	 *
	 * @return GregorianCalendar an object representing the N8 date
	 */
	public static GregorianCalendar N8DateToGregorianCalendarZulu(String theDate) {
		return N8DateToGregorianCalendar(theDate, TimeZone.getTimeZone("UTC"));
	}

	/**
	 * Converts an N8 date (CCYYMMDD) to a GregorianCalendar object using the
	 * specified Timezone.
	 *
	 * @param theDate
	 *            a String containing the N8 date to be converted
	 * @param timeZone
	 *            the required timezone for the return object (null for local)
	 *
	 * @return GregorianCalendar an object representing the N8 date
	 */
	public static GregorianCalendar N8DateToGregorianCalendar(String theDate,
			TimeZone timeZone) {
		GregorianCalendar cal;

		if (theDate != null && isValidN8Date(theDate)) {
			if (timeZone == null) {
				cal = new GregorianCalendar(Integer.parseInt(theDate.substring(
						0, 4)), Integer.parseInt(theDate.substring(4, 6)) - 1,
						Integer.parseInt(theDate.substring(6, 8)));
			} else {
				cal = new GregorianCalendar(timeZone);
				cal.clear();
				cal.set(Integer.parseInt(theDate.substring(0, 4)), Integer
						.parseInt(theDate.substring(4, 6)) - 1, Integer
						.parseInt(theDate.substring(6, 8)));
			}
		} else {
			cal = null;
		}
		return cal;
	}
}
