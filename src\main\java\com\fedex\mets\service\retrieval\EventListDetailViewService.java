package com.fedex.mets.service.retrieval;

import com.fedex.mets.dao.AircraftBean;
import com.fedex.mets.dao.EventDetailView;
import com.fedex.mets.data.DOADiscrepancyData;
import com.fedex.mets.data.DetailViewData;
import com.fedex.mets.data.FlightDepartureDetails;
import com.fedex.mets.dto.MetsRequest;
import com.fedex.mets.dto.MetsResponse;
import com.fedex.mets.entity.mets.EventMaxiDisc;
import com.fedex.mets.entity.mets.EventMsns;
import com.fedex.mets.entity.mets.EventTfNotes;
import com.fedex.mets.repository.cache.SuperEquipmentRepository;
import com.fedex.mets.repository.mars.StadeptDetailRepository;
import com.fedex.mets.repository.mets.*;
import com.fedex.mets.util.DateHelper;
import com.fedex.mets.util.IServerConstants;
import com.fedex.mets.util.RvDBHelper;
import com.fedex.mets.wsdl.msn.shortagenotice.GetShortageNoticeListResponse;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.Reader;
import java.rmi.RemoteException;
import java.sql.Clob;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

@SuppressWarnings("unchecked")
@Service
@RequiredArgsConstructor
public class EventListDetailViewService {

    private static final Logger logger = LoggerFactory.getLogger(EventListDetailViewService.class);

    @Autowired
    private EventTfNotesRepository tfNotesRepo;

    @Autowired
    private EventDiscrepanciesService eventDiscrepanciesService;

    @Autowired
    private GroupDictRepository groupDictRepository;

    @Autowired
    private EventsRepository eventRepository;
    @Autowired
    private EventDoaRepository eventDoaRepository;

    @Autowired
    private EventMaxiDiscRepository eventMaxiDiscRepository;

    @Autowired
    private EventTimersRepository eventTimersRepository;

    @Autowired
    private StadeptDetailRepository stadeptDetailRepository;

    @Autowired
    private SuperEquipmentRepository superEquipmentRepository;

    @Autowired
    private MsnDetailService msnDetailService;

    @Autowired
    private EventMsnsRepository eventMsnsRepository;

    @Autowired
    RvDBHelper rvDBHelper;

    /**
     * Private method to get the Detail List for a particular event.
     *
     * @return List of containing ListViewData Object.
     * @params ACN
     */
    public List<DetailViewData> getEventDetailView(
            String acn,String userId)
            throws Exception {
        List<DetailViewData> elements = new ArrayList<>();

        try {
            logger.info("==> before calling the getEventDetail()"+acn);
            elements =
                    getEventDetail(acn,userId);
        } catch (Exception e) {
            logger.warn(
                    "ERROR MetsRetrieval Servlet getEventDetailView() remote exception >> "
                            + e.getMessage());
        }
        return elements;
    }

    /**
     * Private method to get the TFNotes for a particular event.
     *
     * @return TFNotesData Object.
     * @params EventId.
     */
    public List<EventTfNotes> getTFNotes(
            String eventId) {
        List<EventTfNotes> elements = new ArrayList<>();
        try {
            logger.info("==> before calling the getTFNotes() for the eventId " + eventId);
            elements = tfNotesRepo.getAllTFNotes(Integer.parseInt(eventId));
        } catch (Exception e) {
            logger.warn(
                    "ERROR MetsRetrieval Servlet getTFNotes() remote exception >> "
                            + e.getMessage());
        }
        return elements;
    }


    /**
     * The following getEventDetail() is used to retreive the Active Event Details for an aircraft.
     *
     * @return List of eventDetails.
     * @params ACN, accessLevel, userId, tokenId
     */
    public List<DetailViewData> getEventDetail(
            String acn,String userId)
            throws Exception {
        List<DetailViewData> eventsDetail = new ArrayList<>();
        List<String> contactOwnerList = new ArrayList<>();
        int groupId = 0;

        //TODO//
        String accessLevel="80";


        try {
            String strGroupId = groupDictRepository.getGroupId(accessLevel);

            if (strGroupId != null) {
                try {
                    groupId = Integer.parseInt(strGroupId);
                } catch (Exception e) {
                    groupId = 0;
                }
            }

            List<String> strGroupTitle = groupDictRepository.getGroupTitleByACOwner();
            logger.info("strGroupTitle: " + strGroupTitle);
            for (String str : strGroupTitle) {
                contactOwnerList.add(str);
            }
            List<EventDetailView> eventDetail = new ArrayList<>();
            eventDetail = eventRepository.getDetailListView(acn);
            logger.info("Event Details:"+eventDetail.size());
            if(eventDetail!=null) {
                for (int i = 0; i < eventDetail.size(); i++) {
                    EventDetailView eventDetailView = eventDetail.get(i);
                    DetailViewData data = new DetailViewData();

                    data.setEventID(eventDetailView.getEventId());
                    data.setEventType(eventDetailView.getType());
                    data.setStartDateTimeUTC(String.valueOf(eventDetailView.getStartDateTime()));
                    data.setStartDateTime(String.valueOf(eventDetailView.getStartDateTime()));
                    data.setEndDateTime(String.valueOf(eventDetailView.getEndDateTime()));

                    //           ************ Duration Calculation *********************
                    if (data.getStartDateTime() != null && data.getStartDateTime().length() > 0) {
                        // Calculate the duration of the event (in days and hours:minutes
                        int intHour = 0, intMinute = 0;
                        Duration duration = null;
                        if (!(data.getEndDateTime().equalsIgnoreCase("null"))) {
                            if (data.getEndDateTime() != null) {
                                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                                String formattedStartString = data.getStartDateTime().substring(0, (data.getStartDateTime().length() - 2));
                                LocalDateTime startDate = LocalDateTime.parse(formattedStartString, formatter);
                                String formattedEndDateString = data.getEndDateTime().substring(0, (data.getEndDateTime().length() - 2));
                                LocalDateTime endDate = LocalDateTime.parse(formattedEndDateString, formatter);
                                duration = Duration.between(startDate, endDate);
                            }
                        } else {
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'")
                                    .withZone(ZoneId.of("UTC"));
                            String formattedStartString = data.getStartDateTime().substring(0, (data.getStartDateTime().length() - 2)) + 'Z';
                            StringBuilder sb = new StringBuilder(formattedStartString);
                            sb.setCharAt(10, 'T');

                            LocalDateTime startDate = LocalDateTime.parse(sb.toString(), formatter);
                            Instant currentInstant = Instant.now();
                            String zuluDate = formatter.format(currentInstant);
                            LocalDateTime endDate = LocalDateTime.parse(zuluDate, DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"));
                            duration = Duration.between(startDate, endDate);
                        }

                        // Convert duration to total days
                        long totalSeconds = duration.getSeconds();
                        long totalDays = totalSeconds / (24 * 60 * 60);
                        long remainingSeconds = totalSeconds % (24 * 60 * 60);

                        // Convert remaining seconds to hours and minutes
                        String h = "", m = "";
                        long hours = remainingSeconds / (60 * 60);
                        long minutes = (remainingSeconds % (60 * 60)) / 60;
                        h = Long.toString(hours).length() == 1 ? "0" + hours : Long.toString(hours);
                        m = Long.toString(minutes).length() == 1 ? "0" + minutes : Long.toString(minutes);
                        String durationData = totalDays + "D " + h + ":" + m;
                        data.setDuration(durationData);

                        // ************ Formatting StartDateTime *********************

                        String sDate, startTime;
                        if (String.valueOf(data.getStartDateTime()) != null && String.valueOf(data.getStartDateTime()).length() == 21) {
                            sDate = String.valueOf(data.getStartDateTime()).substring(0, 10);
                            sDate = sDate.replace("-", "");
                            startTime = String.valueOf(data.getStartDateTime()).substring(11, 16);
                            startTime = startTime.replace(":", "");

                            GregorianCalendar gStartDateTime;
                            gStartDateTime = DateHelper.N8DateToGregorianCalendar(sDate);
                            if (startTime != null && startTime.trim().length() > 0) {
                                intHour = Integer.parseInt(startTime.substring(0, 2));
                                intMinute = Integer.parseInt(startTime.substring(2, 4));
                            }
                            gStartDateTime.set(Calendar.HOUR, intHour);
                            gStartDateTime.set(Calendar.MINUTE, intMinute);

                            String startDateTimeDetails = new FlightDepartureDetails(gStartDateTime).getRenderingString();
                            data.setStartDateTime(startDateTimeDetails);
                        }
                    }
                    data.setEventACN(eventDetailView.getAcn());
                    data.setEventFleetDesc(eventDetailView.getFleetDesc());
                    data.setEventStation(eventDetailView.getStation());

                        /*
                        ############ IMPORTANT ###################################
                        Change in Logic : Status, ETIC & Comment should be the proposed values
                        rather than showing the old values on Client. - Change on 04/08/03

                        data.setEventStatus(rs.getString(8));
                        data.setEventEticDateTime(rs.getString(9));
                        data.setEventEticText(rs.getString(10));
                        data.setEventCurrentComment(rs.getString(11));
                        */

                    String strStatus = eventDetailView.getStatus();
                    String strEticDateTime = String.valueOf(eventDetailView.getEticDateTime());
                    if (strEticDateTime != null && strEticDateTime.length() == 21) {
                        SimpleDateFormat sdf = new SimpleDateFormat("MM-dd-yy HH:mm:ss");
                        String formattedDate = sdf.format(eventDetailView.getEticDateTime());
                        String date = formattedDate.substring(0, 8);
                        date = date.replace("-", "");
                        String time = formattedDate.substring(9, 14);
                        time = time.replace(":", "");
                        strEticDateTime = time + "/" + date;
                    }
                    String strEticText = eventDetailView.getEticText();
                    String strComment = (String) eventDetailView.getCurrentOrigComment();

                    data.setEventLastUpdateDateTime(String.valueOf(eventDetailView.getLastUpdateDateTime()));
                    data.setEventLastUpdatedBy(eventDetailView.getLastUpdatedBy());
                    data.setEventCreatedDateTime(String.valueOf(eventDetailView.getCreatedDateTime()));
                    data.setEventCreatedBy(eventDetailView.getCreatedBy());
                    data.setContact(eventDetailView.getPrimaryContact());
                    data.setAcOwnerGroupId(eventDetailView.getAcOwnerGroupId());
                    data.setEventOnwerGroupId(eventDetailView.getEventOwnerGroupId());

                    String requestStatus = eventDetailView.getRequestStatus();
                    int changeType = eventDetailView.getChangeType() != null ? eventDetailView.getChangeType() : 0;

                    data.setEventOriginalComment(eventDetailView.getOrigComment());

                    //            if(eventDetailView.getNewOldComment()!=null)
                    //            {
                    //                java.sql.Clob clobComment = (Clob) eventDetailView.getNewOldComment();
                    //
                    //                long lngLen = clobComment.length();
                    //                String strText = ((clobComment == null) ? "" : clobComment.getSubString(1, (int) lngLen));
                    //                data.setManagerNote(strText);
                    //            }

                    String strCancelled = eventDetailView.getCancelled();

                    if (strCancelled != null
                            && strCancelled.trim().equalsIgnoreCase("Y")) {
                        data.setEventCancelled(true);
                    }

                    data.setRequestStatus(requestStatus);
                    data.setChangeType(changeType);

                    data.setGroupId("" + groupId);

                    data.setEventActive(true);

                        /*
                        Setting the new values to Staus, ETIC & Comment to current Values for display on Client.
                        When there is a Submitted request for change in any of the above
                        Change on 04/08/03
                        **/
                    String strNewStatus = eventDetailView.getNewStatus();
                    String strNewEticText = eventDetailView.getNewEticText();
                    String strNewComment = (String) eventDetailView.getNewOldComment();
                    String strNewEticDateTime = String.valueOf(eventDetailView.getNewEticDtTm());
                    if (strNewEticDateTime != null && strNewEticDateTime.length() == 21) {
                        SimpleDateFormat sdf = new SimpleDateFormat("MM-dd-yy HH:mm:ss");
                        String formattedDate = sdf.format(eventDetailView.getNewEticDtTm());
                        String date = formattedDate.substring(0, 8);
                        date = date.replace("-", "");
                        String time = formattedDate.substring(9, 14);
                        time = time.replace(":", "");
                        strNewEticDateTime = time + "/" + date;
                    }

                    //changes to add responsible manager Id changes made 06/29/2012
                    data.setResMgrId(eventDetailView.getMemDeskContact());
                    Clob clob = (Clob) eventDetailView.getMgrNotes();
                    String clobToString = readClob(clob);
                    data.setManagerNote(clobToString);
                    data.setEventOST(eventDetailView.getOst());
                    data.setEventEticReasonCd(eventDetailView.getEticRsnCd());
                    data.setEventEticReasonComment(eventDetailView.getEticRsnComment());

                    data.setMemDeskContact(eventDetailView.getMemDeskContact());

                    if (requestStatus != null
                            && requestStatus.trim().equalsIgnoreCase("S")) {
                        data.setEventStatus(strNewStatus);
                        data.setEventEticDateTime(strNewEticDateTime);
                        data.setEventEticText(strNewEticText);
                        data.setEventCurrentComment(strNewComment);

                        data.setEventNewStatus(strNewStatus);

                    } else if (
                            requestStatus != null
                                    && requestStatus.trim().equalsIgnoreCase("U")) {
                        data.setEventStatus(strStatus);
                        data.setEventEticDateTime(strEticDateTime);
                        data.setEventEticText(strEticText);
                        data.setEventCurrentComment(strComment);

                        data.setEventNewStatus(strNewStatus);

                    } else if (
                            requestStatus != null
                                    && requestStatus.trim().equalsIgnoreCase("C")) {
                        data.setEventStatus(strNewStatus);
                        data.setEventEticDateTime(strNewEticDateTime);
                        data.setEventEticText(strNewEticText);
                        data.setEventCurrentComment(strNewComment);

                        data.setEventNewStatus(strNewStatus);

                    } else {
                        data.setEventStatus(strStatus);
                        data.setEventEticDateTime(strEticDateTime);
                        data.setEventEticText(strEticText);
                        data.setEventCurrentComment(strComment);

                        data.setEventNewStatus(strNewStatus);
                    }

                    //setting the owner List
                    data.setContactInfoOwnerList(contactOwnerList);
                    //get the current gate for the aircraft. added on 03/09/2011
                    //change made as OMNI_CAHCE is being separated.
                    try {
                        getCurrentGate(data);
                    } catch (Exception ee) {
                        logger.warn(" ERROR getEventDetail()>>" + ee.getMessage());
                        ee.printStackTrace();
                    }

                    //get the flight leg shcedule for the aircraft.
                    try {
                        getSchedule(data);
                    } catch (Exception ee) {
                        logger.warn(" ERROR getEventDetail()>>" + ee.getMessage());
                        ee.printStackTrace();
                    }

                    logger.info(
                            "After retreiving the eventsDetailList getSchedule" + data.getInboundDestination());

                    try {
                        int eventId = data.getEventID();
                        /*The following is to check the # of discrepancies linked to the Event**/
                        int discCount = 0;
                        List<DOADiscrepancyData> linkedDiscList = new ArrayList<>();
                        List<EventMaxiDisc> linkedDisct = eventMaxiDiscRepository.getEventMaxiDiscInfo(eventId);

                        if(linkedDisct!=null) {

                            for (EventMaxiDisc disc : linkedDisct) {
                                discCount = discCount + 1;

                                DOADiscrepancyData doaDiscData = new DOADiscrepancyData();

                                doaDiscData.setAta(disc.getEventMaxiDisckPk().getAta());
                                doaDiscData.setDiscrepancy(disc.getEventMaxiDisckPk().getDiscNum());

                                linkedDiscList.add(doaDiscData);
                            }
                        }

                        data.setNumberOfDiscrepancies("" + discCount);
                        data.setLinkedDiscList(linkedDiscList);

                        /*The following is to check if there are any active timers associated with the Event**/
                        int activeTimerCount = 0;
                        activeTimerCount = eventTimersRepository.getTimerCount(String.valueOf(eventId));
                        if (activeTimerCount > 0) {
                            data.setActiveTimer(true);
                        }
                        String acOwner = data.getAcOwnerGroupId();
                        String eventStation = data.getEventStation();
                        String tempDeptId = null;
                        logger.info(
                                "acOwner >> " + acOwner + "&& eventStation >> " + eventStation);

                        /* To retreive Group_Title from the Group_Dict table.**/
                        tempDeptId = groupDictRepository.getGroupTitle(acOwner);


                        logger.info(
                                " before setting the GROUP_TITLE "
                                        + tempDeptId
                                        + " to detail view object");
                        data.setAcOwnerGroupId(tempDeptId);
                    } catch (Exception indicator) {
                        logger.warn(
                                " ERROR getEventDetail() indicator>>" + indicator.getMessage());
                        //			indicator.printStackTrace();
                    }

                    logger.info(
                            "** after retreiving the Indicators && DOA && Discrepancy details. *** eventsDetailList size ****");

                    try {
                        String eventStation = data.getEventStation();
                        String tempDeptId = data.getAcOwnerGroupId();
                        logger.info(
                                "tempDeptId" + tempDeptId + " && eventStation" + eventStation);

                        /*
                         * To retreive the MOCC_MX_SPD_DIAL from STADEPTDETAIL from the Mars database
                         * based on  Mets.Event.STATION & Mets.Events.AC_OWNER_GROUP_ID
                         **/
                        String moccMxSpdDial = stadeptDetailRepository.getMoccMxSpdDial(eventStation, tempDeptId);
                        data.setMxSpeedDial(moccMxSpdDial);
                    } catch (Exception eNew) {
                        logger.warn(" ERROR getEventDetail() eNew >>" + eNew.getMessage());
                        eNew.printStackTrace();
                    }

                    try {
                        data = getMSNNumber(data, userId);
                    } catch (Exception exec) {
                        logger.warn(
                                " ERROR Event List Detail view getting Msn number exec ** >>" + exec.getMessage());
                        exec.printStackTrace();
                    }

                    try {
                        List<EventMsns> msns = eventMsnsRepository.getEventMsns(data.eventID);
                        data.setLinkedMsns(msns);
                    } catch (Exception exec) {
                        logger.warn(
                                " ERROR Event List Detail view linked msns exec ** >>" + exec.getMessage());
                        exec.printStackTrace();
                    }
                    eventsDetail.add(data);
                }
            }
            else{
                logger.info("No active event details found for acn:"+acn);
            }
            return eventsDetail;
        } catch (Exception e) {
            logger.warn(
                    " ERROR getEventDetail() >>" + e.getMessage());
            throw new RemoteException("Error in EventListDetailViewService", e);
        }
    }

    /**
     * The Following getMSNNumber() is used to set the Number of MSN's for the Aircraft Event.
     *
     * @return List of Events
     * @parans List of Events, userId, tokenId
     */
    private DetailViewData getMSNNumber(
            DetailViewData eventsDetail,
            String userId)
            throws RemoteException {
        try {
            GetShortageNoticeListResponse res=msnDetailService.getShortageNoticeListResponse(eventsDetail.getEventACN(),userId);
            EventMsns msn=new EventMsns();

            if(res.getStatus().getMessage()=="No Results found for the Search Criteria" || res==null || res.getShortageNoticeList()==null)
            {
                eventsDetail.setNumberOfMSN("0");
            }
            else
            {
                eventsDetail.setNumberOfMSN("" + res.getShortageNoticeList().size());
            }
        } catch (Exception exc) {
                logger.warn(
                        " ERROR getViewOnlyEventDetail() number of MSN >>"
                                + exc.getMessage());
            }

        return eventsDetail;
    }

    private void getCurrentGate(DetailViewData detailView) throws RemoteException {

        try {
            String gate = superEquipmentRepository.getCurrentGate(detailView.getEventACN());
            if(gate!=null)
            {
                String[] gateInfo = gate.split(",");

                if (detailView.getEventType().equalsIgnoreCase("TRK")
                        || detailView.getEventType().equalsIgnoreCase("OOS")
                        || detailView.getEventType().equalsIgnoreCase("NOTE")) {

                    detailView.setGate(gateInfo[1]);
                }
            }
        } catch (Exception se) {
            throw new RemoteException(
                    "Problem finding the Gates for list " + se.getMessage());
        }
    }


    /**
     * The following getSchedule() is used to retreive the Flight number and dates for an aircraft.
     *
     * @return List of events
     * @params List of events
     */
    public DetailViewData getSchedule(DetailViewData detailData) throws RemoteException {
        try {
            logger.info("Getting ACN Data from FOIS");
            AircraftBean acb = rvDBHelper.getAircraftRecordsFromRampview(detailData.getEventACN());
            if(acb!=null) {
                detailData.setEventACN(acb.getAcn());
                detailData.setEquipmentType(acb.getEquipType());
                detailData.setInboundFlightNumber(acb.getInboundFlight());
                detailData.setInboundFlightDate(acb.getInboundFlightDate());
                detailData.setInboundLegNumber(acb.getInboundFlightLeg());
                detailData.setInboundLegDate(acb.getInboundFlightLegDate());
                detailData.setInboundOrigination(acb.getInboundOrigination());
                detailData.setInboundFlightDepartureTime(acb.getInboundOutTime());
                detailData.setInboundDestination(acb.getInboundDestination());
                detailData.setInboundArrivalDate(acb.getInboundArrivalDate());
                detailData.setInboundArrivalTime(acb.getInboundArrivalTime());

                detailData.setOutboundFlightNumber(acb.getOutboundFlight());
                detailData.setOutboundFlightDate(acb.getOutboundFlightDate());
                detailData.setOutboundLegNumber(acb.getOutboundFlightLeg());
                detailData.setOutboundLegDate(acb.getOutboundFlightLegDate());
                detailData.setOutboundOrigination(acb.getOutboundOrigination());
                detailData.setOutboundFlightDepartureTime(acb.getOutboundOutTime());
                detailData.setOutboundDestination(acb.getOutboundDestination());
                detailData.setOutboundArrivalDate(acb.getOutboundArrivalDate());
                if (detailData.getEventType().trim().equals("NOTE")) {
                    String strCurrentStation = acb.getCurrentStation();
                    if (strCurrentStation != null
                            && strCurrentStation.trim().length() > 0) {
                        detailData.setEventStation(acb.getCurrentStation());
                    } else {
                        detailData.setEventStation(detailData.getInboundDestination());
                    }
                }
            }
            else{
                logger.info("No data received from rampview for acn:"+detailData.getEventACN());
            }

        } catch (Exception comparing) {
            logger.warn(
                    " ERROR getSchedule() comparing >>" + comparing.getMessage());
        }

        return detailData;
    }

    public String readClob(Clob clob) throws IOException, SQLException {

    // Read the CLOB data into a String

        StringBuilder sb = new StringBuilder();
        try (Reader reader = clob.getCharacterStream(1, (int) clob.length());

             BufferedReader bufferedReader = new BufferedReader(reader)) {

            String line;
            while ((line = bufferedReader.readLine()) != null) {

                sb.append(line);

            }

        }
        return sb.toString();

    }
}
