
package com.fedex.mets.wsdl.discrepancy;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for discrepancyQueryFilter complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="discrepancyQueryFilter">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="acn" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="acnFrom" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="acnTo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ataSeleted" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ataChapterSubChapter" type="{http://www.w3.org/2001/XMLSchema}decimal" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="ataFrom" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="ataTo" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="dscrpNbrs" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="dscrpNbrFrom" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="dscrpNbrTo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="status" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="fleetModelCd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="fleets" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="series" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="models" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="dscrpType" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="deferral" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="specifyDeferrals" type="{http://www.w3.org/2001/XMLSchema}decimal" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="deferralFrom" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="deferralTo" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="includeWorkscope" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="specifyWorkscopes" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="workscopeFrom" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="workscopeTo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="reportedBy" type="{http://www.w3.org/2001/XMLSchema}decimal" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="reportedByFrom" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="reportedByTo" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="enteredBy" type="{http://www.w3.org/2001/XMLSchema}decimal" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="enteredByFrom" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="enteredByTo" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="taskId" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="taskIdFrom" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="taskIdTo" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="reviewed" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="openDate" type="{http://www.w3.org/2001/XMLSchema}date" minOccurs="0"/>
 *         &lt;element name="fromOpenDate" type="{http://www.w3.org/2001/XMLSchema}date" minOccurs="0"/>
 *         &lt;element name="toOpenDate" type="{http://www.w3.org/2001/XMLSchema}date" minOccurs="0"/>
 *         &lt;element name="closedDate" type="{http://www.w3.org/2001/XMLSchema}date" minOccurs="0"/>
 *         &lt;element name="fromClosedDate" type="{http://www.w3.org/2001/XMLSchema}date" minOccurs="0"/>
 *         &lt;element name="toClosedDate" type="{http://www.w3.org/2001/XMLSchema}date" minOccurs="0"/>
 *         &lt;element name="includeMel" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="earlyAlertInd" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="inBoundPDIS" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="spanDays" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/>
 *         &lt;element name="modNbr" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="searchMaintTxt" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="getRstText" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="reviewActId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="getReviewActIdDscrp" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="sdrEvent" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="sdrEventReview" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="maintInfoTypes" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="filterTsiWatchDscrp" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="getAssignPartCounts" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="getBrokenLockDscrps" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="system" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="tfoaFlag" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="openStation" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="faultCodesFlag" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="tfoaCategory" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="rptModFlag" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "discrepancyQueryFilter", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", propOrder = {
    "acn",
    "acnFrom",
    "acnTo",
    "ataSeleted",
    "ataChapterSubChapter",
    "ataFrom",
    "ataTo",
    "dscrpNbrs",
    "dscrpNbrFrom",
    "dscrpNbrTo",
    "status",
    "fleetModelCd",
    "fleets",
    "series",
    "models",
    "dscrpType",
    "deferral",
    "specifyDeferrals",
    "deferralFrom",
    "deferralTo",
    "includeWorkscope",
    "specifyWorkscopes",
    "workscopeFrom",
    "workscopeTo",
    "reportedBy",
    "reportedByFrom",
    "reportedByTo",
    "enteredBy",
    "enteredByFrom",
    "enteredByTo",
    "taskId",
    "taskIdFrom",
    "taskIdTo",
    "reviewed",
    "openDate",
    "fromOpenDate",
    "toOpenDate",
    "closedDate",
    "fromClosedDate",
    "toClosedDate",
    "includeMel",
    "earlyAlertInd",
    "inBoundPDIS",
    "spanDays",
    "modNbr",
    "searchMaintTxt",
    "getRstText",
    "reviewActId",
    "getReviewActIdDscrp",
    "sdrEvent",
    "sdrEventReview",
    "maintInfoTypes",
    "filterTsiWatchDscrp",
    "getAssignPartCounts",
    "getBrokenLockDscrps",
    "system",
    "tfoaFlag",
    "openStation",
    "faultCodesFlag",
    "tfoaCategory",
    "rptModFlag"
})
public class DiscrepancyQueryFilter {

    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected List<String> acn;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String acnFrom;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String acnTo;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String ataSeleted;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected List<BigDecimal> ataChapterSubChapter;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal ataFrom;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal ataTo;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected List<String> dscrpNbrs;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String dscrpNbrFrom;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String dscrpNbrTo;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String status;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", required = true)
    protected String fleetModelCd;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected List<String> fleets;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected List<String> series;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected List<String> models;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected List<String> dscrpType;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String deferral;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected List<BigDecimal> specifyDeferrals;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal deferralFrom;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal deferralTo;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String includeWorkscope;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected List<String> specifyWorkscopes;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String workscopeFrom;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String workscopeTo;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected List<BigDecimal> reportedBy;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal reportedByFrom;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal reportedByTo;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected List<BigDecimal> enteredBy;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal enteredByFrom;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal enteredByTo;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected List<String> taskId;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String taskIdFrom;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String taskIdTo;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String reviewed;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar openDate;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar fromOpenDate;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar toOpenDate;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar closedDate;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar fromClosedDate;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar toClosedDate;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", defaultValue = "false")
    protected Boolean includeMel;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", defaultValue = "false")
    protected Boolean earlyAlertInd;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", defaultValue = "false")
    protected Boolean inBoundPDIS;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected Integer spanDays;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String modNbr;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", required = true)
    protected String searchMaintTxt;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected boolean getRstText;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", required = true)
    protected String reviewActId;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", defaultValue = "false")
    protected boolean getReviewActIdDscrp;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String sdrEvent;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String sdrEventReview;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected List<String> maintInfoTypes;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", defaultValue = "false")
    protected boolean filterTsiWatchDscrp;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", defaultValue = "false")
    protected boolean getAssignPartCounts;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected boolean getBrokenLockDscrps;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String system;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", required = true)
    protected String tfoaFlag;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", required = true)
    protected String openStation;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", required = true)
    protected String faultCodesFlag;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String tfoaCategory;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String rptModFlag;

    /**
     * Gets the value of the acn property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the acn property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getAcn().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link String }
     * 
     * 
     */
    public List<String> getAcn() {
        if (acn == null) {
            acn = new ArrayList<String>();
        }
        return this.acn;
    }

    /**
     * Gets the value of the acnFrom property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAcnFrom() {
        return acnFrom;
    }

    /**
     * Sets the value of the acnFrom property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAcnFrom(String value) {
        this.acnFrom = value;
    }

    /**
     * Gets the value of the acnTo property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAcnTo() {
        return acnTo;
    }

    /**
     * Sets the value of the acnTo property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAcnTo(String value) {
        this.acnTo = value;
    }

    /**
     * Gets the value of the ataSeleted property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAtaSeleted() {
        return ataSeleted;
    }

    /**
     * Sets the value of the ataSeleted property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAtaSeleted(String value) {
        this.ataSeleted = value;
    }

    /**
     * Gets the value of the ataChapterSubChapter property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the ataChapterSubChapter property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getAtaChapterSubChapter().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link BigDecimal }
     * 
     * 
     */
    public List<BigDecimal> getAtaChapterSubChapter() {
        if (ataChapterSubChapter == null) {
            ataChapterSubChapter = new ArrayList<BigDecimal>();
        }
        return this.ataChapterSubChapter;
    }

    /**
     * Gets the value of the ataFrom property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAtaFrom() {
        return ataFrom;
    }

    /**
     * Sets the value of the ataFrom property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAtaFrom(BigDecimal value) {
        this.ataFrom = value;
    }

    /**
     * Gets the value of the ataTo property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAtaTo() {
        return ataTo;
    }

    /**
     * Sets the value of the ataTo property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAtaTo(BigDecimal value) {
        this.ataTo = value;
    }

    /**
     * Gets the value of the dscrpNbrs property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the dscrpNbrs property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getDscrpNbrs().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link String }
     * 
     * 
     */
    public List<String> getDscrpNbrs() {
        if (dscrpNbrs == null) {
            dscrpNbrs = new ArrayList<String>();
        }
        return this.dscrpNbrs;
    }

    /**
     * Gets the value of the dscrpNbrFrom property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDscrpNbrFrom() {
        return dscrpNbrFrom;
    }

    /**
     * Sets the value of the dscrpNbrFrom property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDscrpNbrFrom(String value) {
        this.dscrpNbrFrom = value;
    }

    /**
     * Gets the value of the dscrpNbrTo property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDscrpNbrTo() {
        return dscrpNbrTo;
    }

    /**
     * Sets the value of the dscrpNbrTo property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDscrpNbrTo(String value) {
        this.dscrpNbrTo = value;
    }

    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStatus(String value) {
        this.status = value;
    }

    /**
     * Gets the value of the fleetModelCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFleetModelCd() {
        return fleetModelCd;
    }

    /**
     * Sets the value of the fleetModelCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFleetModelCd(String value) {
        this.fleetModelCd = value;
    }

    /**
     * Gets the value of the fleets property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the fleets property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getFleets().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link String }
     * 
     * 
     */
    public List<String> getFleets() {
        if (fleets == null) {
            fleets = new ArrayList<String>();
        }
        return this.fleets;
    }

    /**
     * Gets the value of the series property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the series property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getSeries().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link String }
     * 
     * 
     */
    public List<String> getSeries() {
        if (series == null) {
            series = new ArrayList<String>();
        }
        return this.series;
    }

    /**
     * Gets the value of the models property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the models property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getModels().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link String }
     * 
     * 
     */
    public List<String> getModels() {
        if (models == null) {
            models = new ArrayList<String>();
        }
        return this.models;
    }

    /**
     * Gets the value of the dscrpType property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the dscrpType property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getDscrpType().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link String }
     * 
     * 
     */
    public List<String> getDscrpType() {
        if (dscrpType == null) {
            dscrpType = new ArrayList<String>();
        }
        return this.dscrpType;
    }

    /**
     * Gets the value of the deferral property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDeferral() {
        return deferral;
    }

    /**
     * Sets the value of the deferral property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDeferral(String value) {
        this.deferral = value;
    }

    /**
     * Gets the value of the specifyDeferrals property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the specifyDeferrals property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getSpecifyDeferrals().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link BigDecimal }
     * 
     * 
     */
    public List<BigDecimal> getSpecifyDeferrals() {
        if (specifyDeferrals == null) {
            specifyDeferrals = new ArrayList<BigDecimal>();
        }
        return this.specifyDeferrals;
    }

    /**
     * Gets the value of the deferralFrom property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getDeferralFrom() {
        return deferralFrom;
    }

    /**
     * Sets the value of the deferralFrom property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setDeferralFrom(BigDecimal value) {
        this.deferralFrom = value;
    }

    /**
     * Gets the value of the deferralTo property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getDeferralTo() {
        return deferralTo;
    }

    /**
     * Sets the value of the deferralTo property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setDeferralTo(BigDecimal value) {
        this.deferralTo = value;
    }

    /**
     * Gets the value of the includeWorkscope property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIncludeWorkscope() {
        return includeWorkscope;
    }

    /**
     * Sets the value of the includeWorkscope property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIncludeWorkscope(String value) {
        this.includeWorkscope = value;
    }

    /**
     * Gets the value of the specifyWorkscopes property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the specifyWorkscopes property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getSpecifyWorkscopes().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link String }
     * 
     * 
     */
    public List<String> getSpecifyWorkscopes() {
        if (specifyWorkscopes == null) {
            specifyWorkscopes = new ArrayList<String>();
        }
        return this.specifyWorkscopes;
    }

    /**
     * Gets the value of the workscopeFrom property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWorkscopeFrom() {
        return workscopeFrom;
    }

    /**
     * Sets the value of the workscopeFrom property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWorkscopeFrom(String value) {
        this.workscopeFrom = value;
    }

    /**
     * Gets the value of the workscopeTo property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWorkscopeTo() {
        return workscopeTo;
    }

    /**
     * Sets the value of the workscopeTo property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWorkscopeTo(String value) {
        this.workscopeTo = value;
    }

    /**
     * Gets the value of the reportedBy property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the reportedBy property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getReportedBy().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link BigDecimal }
     * 
     * 
     */
    public List<BigDecimal> getReportedBy() {
        if (reportedBy == null) {
            reportedBy = new ArrayList<BigDecimal>();
        }
        return this.reportedBy;
    }

    /**
     * Gets the value of the reportedByFrom property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getReportedByFrom() {
        return reportedByFrom;
    }

    /**
     * Sets the value of the reportedByFrom property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setReportedByFrom(BigDecimal value) {
        this.reportedByFrom = value;
    }

    /**
     * Gets the value of the reportedByTo property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getReportedByTo() {
        return reportedByTo;
    }

    /**
     * Sets the value of the reportedByTo property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setReportedByTo(BigDecimal value) {
        this.reportedByTo = value;
    }

    /**
     * Gets the value of the enteredBy property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the enteredBy property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getEnteredBy().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link BigDecimal }
     * 
     * 
     */
    public List<BigDecimal> getEnteredBy() {
        if (enteredBy == null) {
            enteredBy = new ArrayList<BigDecimal>();
        }
        return this.enteredBy;
    }

    /**
     * Gets the value of the enteredByFrom property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getEnteredByFrom() {
        return enteredByFrom;
    }

    /**
     * Sets the value of the enteredByFrom property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setEnteredByFrom(BigDecimal value) {
        this.enteredByFrom = value;
    }

    /**
     * Gets the value of the enteredByTo property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getEnteredByTo() {
        return enteredByTo;
    }

    /**
     * Sets the value of the enteredByTo property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setEnteredByTo(BigDecimal value) {
        this.enteredByTo = value;
    }

    /**
     * Gets the value of the taskId property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the taskId property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getTaskId().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link String }
     * 
     * 
     */
    public List<String> getTaskId() {
        if (taskId == null) {
            taskId = new ArrayList<String>();
        }
        return this.taskId;
    }

    /**
     * Gets the value of the taskIdFrom property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTaskIdFrom() {
        return taskIdFrom;
    }

    /**
     * Sets the value of the taskIdFrom property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTaskIdFrom(String value) {
        this.taskIdFrom = value;
    }

    /**
     * Gets the value of the taskIdTo property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTaskIdTo() {
        return taskIdTo;
    }

    /**
     * Sets the value of the taskIdTo property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTaskIdTo(String value) {
        this.taskIdTo = value;
    }

    /**
     * Gets the value of the reviewed property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReviewed() {
        return reviewed;
    }

    /**
     * Sets the value of the reviewed property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReviewed(String value) {
        this.reviewed = value;
    }

    /**
     * Gets the value of the openDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getOpenDate() {
        return openDate;
    }

    /**
     * Sets the value of the openDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setOpenDate(XMLGregorianCalendar value) {
        this.openDate = value;
    }

    /**
     * Gets the value of the fromOpenDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getFromOpenDate() {
        return fromOpenDate;
    }

    /**
     * Sets the value of the fromOpenDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setFromOpenDate(XMLGregorianCalendar value) {
        this.fromOpenDate = value;
    }

    /**
     * Gets the value of the toOpenDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getToOpenDate() {
        return toOpenDate;
    }

    /**
     * Sets the value of the toOpenDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setToOpenDate(XMLGregorianCalendar value) {
        this.toOpenDate = value;
    }

    /**
     * Gets the value of the closedDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getClosedDate() {
        return closedDate;
    }

    /**
     * Sets the value of the closedDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setClosedDate(XMLGregorianCalendar value) {
        this.closedDate = value;
    }

    /**
     * Gets the value of the fromClosedDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getFromClosedDate() {
        return fromClosedDate;
    }

    /**
     * Sets the value of the fromClosedDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setFromClosedDate(XMLGregorianCalendar value) {
        this.fromClosedDate = value;
    }

    /**
     * Gets the value of the toClosedDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getToClosedDate() {
        return toClosedDate;
    }

    /**
     * Sets the value of the toClosedDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setToClosedDate(XMLGregorianCalendar value) {
        this.toClosedDate = value;
    }

    /**
     * Gets the value of the includeMel property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isIncludeMel() {
        return includeMel;
    }

    /**
     * Sets the value of the includeMel property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setIncludeMel(Boolean value) {
        this.includeMel = value;
    }

    /**
     * Gets the value of the earlyAlertInd property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isEarlyAlertInd() {
        return earlyAlertInd;
    }

    /**
     * Sets the value of the earlyAlertInd property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setEarlyAlertInd(Boolean value) {
        this.earlyAlertInd = value;
    }

    /**
     * Gets the value of the inBoundPDIS property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isInBoundPDIS() {
        return inBoundPDIS;
    }

    /**
     * Sets the value of the inBoundPDIS property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setInBoundPDIS(Boolean value) {
        this.inBoundPDIS = value;
    }

    /**
     * Gets the value of the spanDays property.
     * 
     * @return
     *     possible object is
     *     {@link Integer }
     *     
     */
    public Integer getSpanDays() {
        return spanDays;
    }

    /**
     * Sets the value of the spanDays property.
     * 
     * @param value
     *     allowed object is
     *     {@link Integer }
     *     
     */
    public void setSpanDays(Integer value) {
        this.spanDays = value;
    }

    /**
     * Gets the value of the modNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getModNbr() {
        return modNbr;
    }

    /**
     * Sets the value of the modNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setModNbr(String value) {
        this.modNbr = value;
    }

    /**
     * Gets the value of the searchMaintTxt property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSearchMaintTxt() {
        return searchMaintTxt;
    }

    /**
     * Sets the value of the searchMaintTxt property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSearchMaintTxt(String value) {
        this.searchMaintTxt = value;
    }

    /**
     * Gets the value of the getRstText property.
     * 
     */
    public boolean isGetRstText() {
        return getRstText;
    }

    /**
     * Sets the value of the getRstText property.
     * 
     */
    public void setGetRstText(boolean value) {
        this.getRstText = value;
    }

    /**
     * Gets the value of the reviewActId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReviewActId() {
        return reviewActId;
    }

    /**
     * Sets the value of the reviewActId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReviewActId(String value) {
        this.reviewActId = value;
    }

    /**
     * Gets the value of the getReviewActIdDscrp property.
     * 
     */
    public boolean isGetReviewActIdDscrp() {
        return getReviewActIdDscrp;
    }

    /**
     * Sets the value of the getReviewActIdDscrp property.
     * 
     */
    public void setGetReviewActIdDscrp(boolean value) {
        this.getReviewActIdDscrp = value;
    }

    /**
     * Gets the value of the sdrEvent property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSdrEvent() {
        return sdrEvent;
    }

    /**
     * Sets the value of the sdrEvent property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSdrEvent(String value) {
        this.sdrEvent = value;
    }

    /**
     * Gets the value of the sdrEventReview property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSdrEventReview() {
        return sdrEventReview;
    }

    /**
     * Sets the value of the sdrEventReview property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSdrEventReview(String value) {
        this.sdrEventReview = value;
    }

    /**
     * Gets the value of the maintInfoTypes property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the maintInfoTypes property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getMaintInfoTypes().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link String }
     * 
     * 
     */
    public List<String> getMaintInfoTypes() {
        if (maintInfoTypes == null) {
            maintInfoTypes = new ArrayList<String>();
        }
        return this.maintInfoTypes;
    }

    /**
     * Gets the value of the filterTsiWatchDscrp property.
     * 
     */
    public boolean isFilterTsiWatchDscrp() {
        return filterTsiWatchDscrp;
    }

    /**
     * Sets the value of the filterTsiWatchDscrp property.
     * 
     */
    public void setFilterTsiWatchDscrp(boolean value) {
        this.filterTsiWatchDscrp = value;
    }

    /**
     * Gets the value of the getAssignPartCounts property.
     * 
     */
    public boolean isGetAssignPartCounts() {
        return getAssignPartCounts;
    }

    /**
     * Sets the value of the getAssignPartCounts property.
     * 
     */
    public void setGetAssignPartCounts(boolean value) {
        this.getAssignPartCounts = value;
    }

    /**
     * Gets the value of the getBrokenLockDscrps property.
     * 
     */
    public boolean isGetBrokenLockDscrps() {
        return getBrokenLockDscrps;
    }

    /**
     * Sets the value of the getBrokenLockDscrps property.
     * 
     */
    public void setGetBrokenLockDscrps(boolean value) {
        this.getBrokenLockDscrps = value;
    }

    /**
     * Gets the value of the system property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSystem() {
        return system;
    }

    /**
     * Sets the value of the system property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSystem(String value) {
        this.system = value;
    }

    /**
     * Gets the value of the tfoaFlag property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTfoaFlag() {
        return tfoaFlag;
    }

    /**
     * Sets the value of the tfoaFlag property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTfoaFlag(String value) {
        this.tfoaFlag = value;
    }

    /**
     * Gets the value of the openStation property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOpenStation() {
        return openStation;
    }

    /**
     * Sets the value of the openStation property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOpenStation(String value) {
        this.openStation = value;
    }

    /**
     * Gets the value of the faultCodesFlag property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFaultCodesFlag() {
        return faultCodesFlag;
    }

    /**
     * Sets the value of the faultCodesFlag property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFaultCodesFlag(String value) {
        this.faultCodesFlag = value;
    }

    /**
     * Gets the value of the tfoaCategory property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTfoaCategory() {
        return tfoaCategory;
    }

    /**
     * Sets the value of the tfoaCategory property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTfoaCategory(String value) {
        this.tfoaCategory = value;
    }

    /**
     * Gets the value of the rptModFlag property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRptModFlag() {
        return rptModFlag;
    }

    /**
     * Sets the value of the rptModFlag property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRptModFlag(String value) {
        this.rptModFlag = value;
    }

}
