package com.fedex.mets.dao;

import jakarta.persistence.Column;
import lombok.*;

import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ActiveEvents {

    @Column(name = "EVENT_ID")
    public Integer eventId;

    @Column(name = "ACN")
    public String acn;

    @Column(name = "TYPE")
    public String type;

    @Column(name = "START_DT_TM")
    public Timestamp startDateTime;

    @Column(name = "STATION")
    public String station;

    @Column(name = "STATUS")
    public String status;

    public Object newOldComment;

    @Column(name = "NEW_STATUS")
    public String newStatus;

    @Column(name = "REQUEST_STATUS")
    public String requestStatus;

    @Column(name = "END_DT_TM")
    public Timestamp endDateTime;

}
