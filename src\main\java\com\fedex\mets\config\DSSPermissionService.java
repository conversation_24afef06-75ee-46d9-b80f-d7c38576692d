package com.fedex.mets.config;


import com.fedex.lhsd.mss.client.MssClient;
import com.fedex.lhsd.mss.security.data.TranPermission;
import com.fedex.lhsd.mss.security.data.TranPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class DSSPermissionService {
    private static final Logger log = LoggerFactory.getLogger(DSSPermissionService.class);
    @Autowired
    private Environment env;

    public Map<String, Set<Character>> fetchUserPermissions(String token,String userId) throws IOException, ClassNotFoundException {
        Map<String, String> permissions = new HashMap<>();
        log.info("Inside the GetPermissions");
        MssClient.setDefaultMssServletURL(env.getProperty("dss.url"));
        log.info("The Dss root url ****" + MssClient.getDefaultMssServletURL());
        Map map = MssClient.getPermissionsResults(
                "LDAP",
                token,
                userId,
                MssClient.REQ_ALL_MSS,
                "",
                "Test Function",
                null
        );
        if (map != null && !map.isEmpty()) {

            // If there were results
            // Get just the permissions map from the MssClient response
            TranPermissions perms = (TranPermissions) map.get(MssClient.BUF_PERMISSIONS);
            log.info("+++++getPermissions+++++++" + perms.get("METS"));
            TranPermission perm = perms.get("METS");
            String Actions = perm.getActions();
            permissions.put("METS", Actions);
        }
        return parsePermissions(permissions);
//        SimpleAuthConfigurationManager.setProxy(null);
//		DssApiHelper.setRoot("https://dss-l4.test.cloud.fedex.com/DSS/rest");
////        https://dssdev.ute.fedex.com:443/DSS/rest
//
//        List<String> apps = List.of("NGPSCTRLPNL");
//
//        RestDssRequestPermissions perms = new RestDssRequestPermissions();
//        perms.setRequestType(IRestMssConstants.REQ_APPLICATION_LIST);
//        perms.setRequestedCodes(apps);
//        perms.setToken(token);
//
//        RestDssResponse dssResp = DssApiHelper.requestPermissions(perms);
//        System.out.println("Permissions: " + dssResp.getPermissions() +"      "+dssResp.getUserName() +"   "+dssResp.getDssUser());
    }

    private Map<String, Set<Character>> parsePermissions(Map<String, String> permissions) {
        Map<String, Set<Character>> permissionMap = new HashMap<>();

        for (Map.Entry<String, String> entry : permissions.entrySet()) {
            String transaction = entry.getKey();
            log.info("transaction: " + transaction);
            String actions = entry.getValue(); // Example: "ABDM"
            System.out.println("The action is: " + actions);
            log.info("The action is: " + actions);
            permissionMap.put(transaction, actions.chars().mapToObj(c -> (char) c).collect(Collectors.toSet()));
        }
        return permissionMap;
    }
}