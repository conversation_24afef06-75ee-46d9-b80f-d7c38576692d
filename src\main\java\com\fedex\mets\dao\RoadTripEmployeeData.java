package com.fedex.mets.dao;
import jakarta.persistence.Column;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
public class RoadTripEmployeeData{
	@Column(name = "EVENT_ID", nullable = false)
	private Integer eventId;

	@Column(name = "SEQ_ID", nullable = false)
	private Integer seqId;

	@Column(name = "EMP_NUM", nullable = false)
	private String empNum;

	@Column(name = "EMP_NAME", nullable = false)
	private String empName;
}	