
package com.fedex.mets.wsdl.discrepancy;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;extension base="{http://www.fedex.com/airops/schemas/Common.xsd}GenericRequest">
 *       &lt;sequence>
 *         &lt;element name="acn" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="additionalDataReq" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "acn",
    "additionalDataReq"
})
@XmlRootElement(name = "getAcnAircraftRequest",namespace="http:///www.fedex.com/airops/schemas/Mach")
public class GetAcnAircraftRequest
    extends GenericRequest
{

    @XmlElement(required = true)
    protected String acn;
    @XmlElement(defaultValue = "false")
    protected boolean additionalDataReq;

    /**
     * Gets the value of the acn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAcn() {
        return acn;
    }

    /**
     * Sets the value of the acn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAcn(String value) {
        this.acn = value;
    }

    /**
     * Gets the value of the additionalDataReq property.
     * 
     */
    public boolean isAdditionalDataReq() {
        return additionalDataReq;
    }

    /**
     * Sets the value of the additionalDataReq property.
     * 
     */
    public void setAdditionalDataReq(boolean value) {
        this.additionalDataReq = value;
    }

}