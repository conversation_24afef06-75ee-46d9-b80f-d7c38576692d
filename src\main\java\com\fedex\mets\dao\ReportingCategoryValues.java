package com.fedex.mets.dao;

import jakarta.persistence.Column;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
public class ReportingCategoryValues {

//    public Object distinctlevel2Id;

    @Column(name = "DESCRIPTION")
    public String  description;

    @Column(name = "LEVEL_1_ID", nullable = false)
    public String level1Id;

    @Column(name = "LEVEL_1_NAME", nullable = false)
    public String level1Name;

    @Column(name = "LIST_ORDER")
    public Integer listOrder;

    @Column(name = "MULTISELECT")
    public String multiselect;

    @Column(name = "ADD_PROMPT")
    public String addPrompt;

    @Column(name = "CLOSE_PROMPT")
    public String closePrompt;

    @Column(name = "REQ_ADD_PROMPT")
    public String reqAddPrompt;

    @Column(name = "REQ_CLOSE_PROMPT")
    public String reqClosePrompt;

    @Column(name = "DESCRIPTION")
    public String desc;

    @Column(name = "LEVEL_2_NAME")
    public String level2Name;

    @Column(name = "LIST_ORDER")
    public Integer listOrderL2;

    @Column(name = "LEVEL_2_ID", nullable = false)
    public String level2Id;

}
