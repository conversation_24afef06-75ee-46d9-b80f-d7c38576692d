//package com.fedex.mets.service.update;
//
//import java.util.ArrayList;
//import java.util.Collections;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//import com.fedex.mets.entity.mets.EventTimersPk;
//import org.junit.jupiter.api.Assertions;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.ArgumentMatchers;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.mockito.junit.jupiter.MockitoExtension;
//
//import com.fedex.mets.data.EventNIWTimersDataEntity;
//import com.fedex.mets.data.MetsEventUpdateEntity;
//import com.fedex.mets.entity.mets.EventTimers;
//import com.fedex.mets.repository.NIWTimersUpdateRepository;
//import com.fedex.mets.service.retrieval.NIWTimersService;
//
//@ExtendWith(MockitoExtension.class)
//public class MetsNIWTimerDetailsServiceTests {
//
//    @Mock
//    private NIWTimersUpdateRepository niwtimersupdaterepository;
//
//    @Mock
//    private NIWTimersService nIWTimersService;
//
//    @Mock
//    private EventDiscrepanciesUpdateService eventDiscrepanciesUpdateService;
//
//    @InjectMocks
//    private MetsNIWTimerDetailsService metsNIWTimerDetailsService;
//
//    private MetsEventUpdateEntity request;
//
//    private EventNIWTimersDataEntity timerData;
//
//    @BeforeEach
//    void dataSetUp() {
//
//        request = new MetsEventUpdateEntity();
//
//        timerData = new EventNIWTimersDataEntity();
//
//        request.setMode("NIW_TIMERS");
//        request.setUser_id("1");
//        request.setToken_id("1");
//        request.setEvent_active(true);
//
//        timerData.setEventId(21357);
//        timerData.setTimerId("15");
//        timerData.setTimerStartDate("2023-01-01 00:00:00");
//        timerData.setTimerStopDate("2023-01-01 01:00:00");
//        timerData.setCreationDateTime("2023-01-01 00:00:00");
//
//        request.setNiw_timer_data(timerData);
//
//    }
//
//    @Test
//    void testEditNIWTimerDetails_Success() throws Exception {
//
//        request.setFlag("EDIT");
//
//        Map<String,Object> responseHashMap = new HashMap<>();
//        Mockito.when(niwtimersupdaterepository.findNIWTimerDetails(ArgumentMatchers.anyInt(),ArgumentMatchers.anyString(),
//                ArgumentMatchers.anyString(), ArgumentMatchers.anyString(), ArgumentMatchers.anyString())).thenReturn(Collections.emptyList());
//
//        Mockito.when(niwtimersupdaterepository.findLastUpdated(ArgumentMatchers.anyInt(), ArgumentMatchers.anyString(),
//                ArgumentMatchers.anyString(), ArgumentMatchers.anyString())).thenReturn(0);
//
//        Mockito.when(niwtimersupdaterepository.updateNIWTimerDetails(ArgumentMatchers.anyInt(), ArgumentMatchers.anyString(),
//                ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any()
//        )).thenReturn(true);
//
//        EventTimers mockEventTimers = new EventTimers();
//        EventTimersPk pk=new EventTimersPk();
//        pk.setEventId(21357);
//        mockEventTimers.setEventTimersPk(pk);
//        Mockito.when(nIWTimersService.getNIWTimerDetails(ArgumentMatchers.anyString(), ArgumentMatchers.anyString())).thenReturn((List<EventTimers>) mockEventTimers);
//
//        List<List<?>> detailElements = new ArrayList<>();
//        detailElements.add(new ArrayList<>());
//        detailElements.add(new ArrayList<>());
//        detailElements.add(new ArrayList<>());
//        Mockito.when(nIWTimersService.getNIWTimers(ArgumentMatchers.anyString())).thenReturn(detailElements);
//
//        Mockito.doNothing().when(eventDiscrepanciesUpdateService).publishEventUpdate(ArgumentMatchers.anyInt(), ArgumentMatchers.anyString());
//
//        Map<String, Object> result = metsNIWTimerDetailsService.editNIWTimerDetails(request, responseHashMap, timerData,request.isEvent_active());
//
//        Assertions.assertNotNull(result);
//        Assertions.assertTrue(result.containsKey("OVERLAP_TIMER_DETAILS"));
//        Assertions.assertTrue(result.containsKey("UPDATED_TIMER_DETAILS"));
//        Assertions.assertTrue(result.containsKey("NIW_TIMERS"));
//        Assertions.assertTrue(result.containsKey("EVENT_ACTIVE_NIW_TIMERS"));
//        Assertions.assertTrue(result.containsKey("EVENT_NIW_TIMERS"));
//
//        Mockito.verify(niwtimersupdaterepository, Mockito.times(1)).findNIWTimerDetails(ArgumentMatchers.anyInt(),ArgumentMatchers.anyString(),
//                ArgumentMatchers.anyString(), ArgumentMatchers.anyString(), ArgumentMatchers.anyString());
//
//        Mockito.verify(niwtimersupdaterepository, Mockito.times(1)).findLastUpdated(ArgumentMatchers.anyInt(), ArgumentMatchers.anyString(),
//                ArgumentMatchers.anyString(), ArgumentMatchers.anyString());
//
//        Mockito.verify(niwtimersupdaterepository, Mockito.times(1)).updateNIWTimerDetails(ArgumentMatchers.anyInt(), ArgumentMatchers.anyString(),
//                ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any());
//
//        Mockito.verify(nIWTimersService, Mockito.times(1)).getNIWTimerDetails(ArgumentMatchers.anyString(), ArgumentMatchers.anyString());
//
//        Mockito.verify(nIWTimersService, Mockito.times(1)).getNIWTimers(ArgumentMatchers.anyString());
//
//        Mockito.verify(eventDiscrepanciesUpdateService, Mockito.times(1)).publishEventUpdate(ArgumentMatchers.anyInt(), ArgumentMatchers.anyString());
//
//    }
//
//    @Test
//    void testEditNIWTimerDetails_OverlapFound() throws Exception {
//
//        request.setFlag("EDIT");
//
//        Map<String,Object> responseHashMap = new HashMap<>();
//        List<Object> existingTimerList = new ArrayList<>();
//        existingTimerList.add(new EventNIWTimersDataEntity());
//
//        Mockito.when(niwtimersupdaterepository.findNIWTimerDetails(ArgumentMatchers.anyInt(),ArgumentMatchers.anyString(),
//                ArgumentMatchers.anyString(), ArgumentMatchers.anyString(), ArgumentMatchers.anyString())).thenReturn(existingTimerList);
//
//        Map<String, Object> result = metsNIWTimerDetailsService.editNIWTimerDetails(request, responseHashMap, timerData, true);
//
//        Mockito.verify(niwtimersupdaterepository, Mockito.times(1)).findNIWTimerDetails(ArgumentMatchers.anyInt(),ArgumentMatchers.anyString(),
//                ArgumentMatchers.anyString(), ArgumentMatchers.anyString(), ArgumentMatchers.anyString());
//
//        Mockito.verify(niwtimersupdaterepository, Mockito.never()).findLastUpdated(ArgumentMatchers.anyInt(), ArgumentMatchers.anyString(),
//                ArgumentMatchers.anyString(), ArgumentMatchers.anyString());
//
//        Mockito.verify(niwtimersupdaterepository, Mockito.never()).updateNIWTimerDetails(ArgumentMatchers.anyInt(), ArgumentMatchers.anyString(),
//                ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any());
//
//        Mockito.verify(nIWTimersService, Mockito.never()).getNIWTimerDetails(ArgumentMatchers.anyString(), ArgumentMatchers.anyString());
//
//        Mockito.verify(nIWTimersService, Mockito.never()).getNIWTimers(ArgumentMatchers.anyString());
//
//        Mockito.verify(eventDiscrepanciesUpdateService, Mockito.never()).publishEventUpdate(ArgumentMatchers.anyInt(), ArgumentMatchers.anyString());
//
//    }
//
//    @Test
//    void testEditNIWTimerDetails_lastUpdateFound() throws Exception {
//
//        request.setFlag("EDIT");
//
//        Map<String,Object> responseHashMap = new HashMap<>();
//        Mockito.when(niwtimersupdaterepository.findNIWTimerDetails(ArgumentMatchers.anyInt(),ArgumentMatchers.anyString(),
//                ArgumentMatchers.anyString(), ArgumentMatchers.anyString(), ArgumentMatchers.anyString())).thenReturn(Collections.emptyList());
//
//        Mockito.when(niwtimersupdaterepository.findLastUpdated(ArgumentMatchers.anyInt(), ArgumentMatchers.anyString(),
//                ArgumentMatchers.anyString(), ArgumentMatchers.anyString())).thenReturn(1);
//
//        Map<String, Object> result = metsNIWTimerDetailsService.editNIWTimerDetails(request, responseHashMap, timerData, true);
//
//        Assertions.assertNotNull(result);
//        Assertions.assertTrue(result.containsKey("ERROR"));
//
//        Mockito.verify(niwtimersupdaterepository, Mockito.times(1)).findNIWTimerDetails(ArgumentMatchers.anyInt(),ArgumentMatchers.anyString(),
//                ArgumentMatchers.anyString(), ArgumentMatchers.anyString(), ArgumentMatchers.anyString());
//
//        Mockito.verify(niwtimersupdaterepository, Mockito.times(1)).findLastUpdated(ArgumentMatchers.anyInt(), ArgumentMatchers.anyString(),
//                ArgumentMatchers.anyString(), ArgumentMatchers.anyString());
//
//        Mockito.verify(niwtimersupdaterepository, Mockito.never()).updateNIWTimerDetails(ArgumentMatchers.anyInt(), ArgumentMatchers.anyString(),
//                ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any());
//
//        Mockito.verify(nIWTimersService, Mockito.never()).getNIWTimerDetails(ArgumentMatchers.anyString(), ArgumentMatchers.anyString());
//
//        Mockito.verify(nIWTimersService, Mockito.never()).getNIWTimers(ArgumentMatchers.anyString());
//
//        Mockito.verify(eventDiscrepanciesUpdateService, Mockito.never()).publishEventUpdate(ArgumentMatchers.anyInt(), ArgumentMatchers.anyString());
//
//    }
//
//    @Test
//    void testDeleteNIWTimerDetails_Success() throws Exception {
//
//        request.setFlag("DELETE");
//
//        Map<String,Object> responseHashMap = new HashMap<>();
//
//        Mockito.when(niwtimersupdaterepository.findLastUpdated(ArgumentMatchers.anyInt(), ArgumentMatchers.anyString(),
//                ArgumentMatchers.anyString(), ArgumentMatchers.anyString())).thenReturn(0);
//
//        Mockito.when(niwtimersupdaterepository.deleteNIWTimerDetails(ArgumentMatchers.anyInt(), ArgumentMatchers.anyString(),
//                ArgumentMatchers.any(), ArgumentMatchers.anyString(), ArgumentMatchers.anyString(), ArgumentMatchers.anyBoolean()
//        )).thenReturn(true);
//
//        EventTimers mockEventTimers = new EventTimers();
//        EventTimersPk pk=new EventTimersPk();
//        pk.setEventId(21357);
//        mockEventTimers.setEventTimersPk(pk);
//        Mockito.when(nIWTimersService.getNIWTimerDetails(ArgumentMatchers.anyString(), ArgumentMatchers.anyString())).thenReturn((List<EventTimers>) mockEventTimers);
//
//        List<List<?>> detailElements = new ArrayList<>();
//        detailElements.add(new ArrayList<>());
//        detailElements.add(new ArrayList<>());
//        detailElements.add(new ArrayList<>());
//        Mockito.when(nIWTimersService.getNIWTimers(ArgumentMatchers.anyString())).thenReturn(detailElements);
//
//        Mockito.doNothing().when(eventDiscrepanciesUpdateService).publishEventUpdate(ArgumentMatchers.anyInt(), ArgumentMatchers.anyString());
//
//        Map<String, Object> result = metsNIWTimerDetailsService.deleteNIWTimerDetails(request, responseHashMap, timerData, request.getUser_id(),request.getToken_id(),request.isEvent_active());
//
//        Assertions.assertNotNull(result);
//        Assertions.assertTrue(result.containsKey("UPDATED_TIMER_DETAILS"));
//        Assertions.assertTrue(result.containsKey("NIW_TIMERS"));
//        Assertions.assertTrue(result.containsKey("EVENT_ACTIVE_NIW_TIMERS"));
//        Assertions.assertTrue(result.containsKey("EVENT_NIW_TIMERS"));
//
//        Mockito.verify(niwtimersupdaterepository, Mockito.times(1)).findLastUpdated(ArgumentMatchers.anyInt(), ArgumentMatchers.anyString(),
//                ArgumentMatchers.anyString(), ArgumentMatchers.anyString());
//
//        Mockito.verify(niwtimersupdaterepository, Mockito.times(1)).deleteNIWTimerDetails(ArgumentMatchers.anyInt(), ArgumentMatchers.anyString(),
//                ArgumentMatchers.any(), ArgumentMatchers.anyString(),
//                ArgumentMatchers.anyString(), ArgumentMatchers.anyBoolean());
//
//        Mockito.verify(nIWTimersService, Mockito.times(1)).getNIWTimerDetails(ArgumentMatchers.anyString(), ArgumentMatchers.anyString());
//
//        Mockito.verify(nIWTimersService, Mockito.times(1)).getNIWTimers(ArgumentMatchers.anyString());
//
//        Mockito.verify(eventDiscrepanciesUpdateService, Mockito.times(1)).publishEventUpdate(ArgumentMatchers.anyInt(), ArgumentMatchers.anyString());
//
//    }
//
//    @Test
//    void testDeleteNIWTimerDetails_lastUpdateFound() throws Exception {
//
//        request.setFlag("DELETE");
//
//        Map<String,Object> responseHashMap = new HashMap<>();
//
//        Mockito.when(niwtimersupdaterepository.findLastUpdated(ArgumentMatchers.anyInt(), ArgumentMatchers.anyString(),
//                ArgumentMatchers.anyString(), ArgumentMatchers.anyString())).thenReturn(1);
//
//        Map<String, Object> result = metsNIWTimerDetailsService.deleteNIWTimerDetails(request, responseHashMap, timerData, "1", "1", true);
//
//        Assertions.assertNotNull(result);
//        Assertions.assertTrue(result.containsKey("ERROR"));
//
//        Mockito.verify(niwtimersupdaterepository, Mockito.times(1)).findLastUpdated(ArgumentMatchers.anyInt(), ArgumentMatchers.anyString(),
//                ArgumentMatchers.anyString(), ArgumentMatchers.anyString());
//
//        Mockito.verify(niwtimersupdaterepository, Mockito.never()).deleteNIWTimerDetails(ArgumentMatchers.anyInt(), ArgumentMatchers.anyString(),
//                ArgumentMatchers.any(), ArgumentMatchers.anyString(),
//                ArgumentMatchers.anyString(), ArgumentMatchers.anyBoolean());
//
//        Mockito.verify(nIWTimersService, Mockito.never()).getNIWTimerDetails(ArgumentMatchers.anyString(), ArgumentMatchers.anyString());
//
//        Mockito.verify(nIWTimersService, Mockito.never()).getNIWTimers(ArgumentMatchers.anyString());
//
//        Mockito.verify(eventDiscrepanciesUpdateService, Mockito.never()).publishEventUpdate(ArgumentMatchers.anyInt(), ArgumentMatchers.anyString());
//
//    }
//
//    @Test
//    void testAddNIWTimerDetails_Success() throws Exception {
//
//        request.setFlag("ADD");
//
//        Map<String,Object> responseHashMap = new HashMap<>();
//        Mockito.when(niwtimersupdaterepository.findNIWTimerDetails(ArgumentMatchers.anyInt(),ArgumentMatchers.anyString(),
//                ArgumentMatchers.any(), ArgumentMatchers.anyString(), ArgumentMatchers.anyString())).thenReturn(Collections.emptyList());
//
//        Mockito.when(niwtimersupdaterepository.addNIWTimerDetails(ArgumentMatchers.anyInt(), ArgumentMatchers.anyString(),
//                ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any()
//        )).thenReturn(true);
//
//        EventTimers mockEventTimers = new EventTimers();
//        EventTimersPk pk=new EventTimersPk();
//        pk.setEventId(21357);
//        mockEventTimers.setEventTimersPk(pk);
//        Mockito.when(nIWTimersService.getNIWTimerDetails(ArgumentMatchers.anyString(), ArgumentMatchers.anyString())).thenReturn((List<EventTimers>) mockEventTimers);
//
//        List<List<?>> detailElements = new ArrayList<>();
//        detailElements.add(new ArrayList<>());
//        detailElements.add(new ArrayList<>());
//        detailElements.add(new ArrayList<>());
//        Mockito.when(nIWTimersService.getNIWTimers(ArgumentMatchers.anyString())).thenReturn(detailElements);
//
//        Mockito.doNothing().when(eventDiscrepanciesUpdateService).publishEventUpdate(ArgumentMatchers.anyInt(), ArgumentMatchers.anyString());
//
//        Map<String, Object> result = metsNIWTimerDetailsService.addNIWTimerDetails(request, responseHashMap, timerData,true);
//
//        Assertions.assertNotNull(result);
//        Assertions.assertTrue(result.containsKey("OVERLAP_TIMER_DETAILS"));
//        Assertions.assertTrue(result.containsKey("UPDATED_TIMER_DETAILS"));
//        Assertions.assertTrue(result.containsKey("NIW_TIMERS"));
//        Assertions.assertTrue(result.containsKey("EVENT_ACTIVE_NIW_TIMERS"));
//        Assertions.assertTrue(result.containsKey("EVENT_NIW_TIMERS"));
//
//        Mockito.verify(niwtimersupdaterepository, Mockito.times(1)).findNIWTimerDetails(ArgumentMatchers.anyInt(),ArgumentMatchers.anyString(),
//                ArgumentMatchers.any(), ArgumentMatchers.anyString(), ArgumentMatchers.anyString());
//
//        Mockito.verify(niwtimersupdaterepository, Mockito.times(1)).addNIWTimerDetails(ArgumentMatchers.anyInt(), ArgumentMatchers.anyString(),
//                ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any());
//
//        Mockito.verify(nIWTimersService, Mockito.times(1)).getNIWTimerDetails(ArgumentMatchers.anyString(), ArgumentMatchers.anyString());
//
//        Mockito.verify(nIWTimersService, Mockito.times(1)).getNIWTimers(ArgumentMatchers.anyString());
//
//        Mockito.verify(eventDiscrepanciesUpdateService, Mockito.times(1)).publishEventUpdate(ArgumentMatchers.anyInt(), ArgumentMatchers.anyString());
//
//    }
//
//    @Test
//    void testAddNIWTimerDetails_OverlapFound() throws Exception {
//
//        request.setFlag("ADD");
//
//        Map<String,Object> responseHashMap = new HashMap<>();
//        List<Object> existingTimerList = new ArrayList<>();
//        existingTimerList.add(new EventNIWTimersDataEntity());
//
//        Mockito.when(niwtimersupdaterepository.findNIWTimerDetails(ArgumentMatchers.anyInt(),ArgumentMatchers.anyString(),
//                ArgumentMatchers.any(), ArgumentMatchers.anyString(), ArgumentMatchers.anyString())).thenReturn(existingTimerList);
//
//        Map<String, Object> result = metsNIWTimerDetailsService.addNIWTimerDetails(request, responseHashMap, timerData,true);
//
//        Mockito.verify(niwtimersupdaterepository, Mockito.times(1)).findNIWTimerDetails(ArgumentMatchers.anyInt(),ArgumentMatchers.anyString(),
//                ArgumentMatchers.any(), ArgumentMatchers.anyString(), ArgumentMatchers.anyString());
//
//        Mockito.verify(niwtimersupdaterepository, Mockito.never()).addNIWTimerDetails(ArgumentMatchers.anyInt(), ArgumentMatchers.anyString(),
//                ArgumentMatchers.any(), ArgumentMatchers.any(), ArgumentMatchers.any());
//
//        Mockito.verify(nIWTimersService, Mockito.never()).getNIWTimerDetails(ArgumentMatchers.anyString(), ArgumentMatchers.anyString());
//
//        Mockito.verify(nIWTimersService, Mockito.never()).getNIWTimers(ArgumentMatchers.anyString());
//
//        Mockito.verify(eventDiscrepanciesUpdateService, Mockito.never()).publishEventUpdate(ArgumentMatchers.anyInt(), ArgumentMatchers.anyString());
//
//    }
//
//    @Test
//    void testSetNIWTimerDetails_Success() throws Exception {
//
//        request.setFlag("START");
//
//        Map<String,Object> responseHashMap = new HashMap<>();
//        Mockito.when(niwtimersupdaterepository.setNIWTimerDetails(ArgumentMatchers.anyString(), ArgumentMatchers.anyString(),
//                ArgumentMatchers.anyString()
//        )).thenReturn(true);
//
//        List<List<?>> detailElements = new ArrayList<>();
//        detailElements.add(new ArrayList<>());
//        detailElements.add(new ArrayList<>());
//        detailElements.add(new ArrayList<>());
//        Mockito.when(nIWTimersService.getNIWTimers(ArgumentMatchers.anyString())).thenReturn(detailElements);
//
//        Mockito.doNothing().when(eventDiscrepanciesUpdateService).publishEventUpdate(ArgumentMatchers.anyInt(), ArgumentMatchers.anyString());
//
//        Map<String, Object> result = metsNIWTimerDetailsService.setNIWTimerDetails(request, responseHashMap, "21357", "15", "START",true);
//
//        Assertions.assertNotNull(result);
//        Assertions.assertTrue(result.containsKey("NIW_TIMERS"));
//        Assertions.assertTrue(result.containsKey("EVENT_ACTIVE_NIW_TIMERS"));
//        Assertions.assertTrue(result.containsKey("EVENT_NIW_TIMERS"));
//
//        Mockito.verify(niwtimersupdaterepository, Mockito.times(1)).setNIWTimerDetails(ArgumentMatchers.anyString(), ArgumentMatchers.anyString(),
//                ArgumentMatchers.anyString());
//
//        Mockito.verify(nIWTimersService, Mockito.times(1)).getNIWTimers(ArgumentMatchers.anyString());
//
//    }
//
//    @Test
//    void testSetNIWTimerDetails_Fail() throws Exception {
//
//        request.setFlag("START");
//
//        Map<String,Object> responseHashMap = new HashMap<>();
//        Mockito.when(niwtimersupdaterepository.setNIWTimerDetails(ArgumentMatchers.anyString(), ArgumentMatchers.anyString(),
//                ArgumentMatchers.anyString()
//        )).thenReturn(false);
//
//        Map<String, Object> result = metsNIWTimerDetailsService.setNIWTimerDetails(request, responseHashMap, "21357", "15", "START",true);
//
//        Mockito.verify(niwtimersupdaterepository, Mockito.times(1)).setNIWTimerDetails(ArgumentMatchers.anyString(), ArgumentMatchers.anyString(),
//                ArgumentMatchers.anyString());
//
//        Mockito.verify(nIWTimersService, Mockito.never()).getNIWTimers(ArgumentMatchers.anyString());
//
//    }
//
//}
