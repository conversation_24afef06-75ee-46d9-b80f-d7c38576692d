package com.fedex.mets.entity.mets;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Date;
import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "EVENT_DOA")
public class EventDoa {
    @Id
    @Column(name = "EVENT_ID")
    public Integer eventId;

    @Column(name = "CHK_FLT")
    public String checkFleet;

    @Column(name = "MAINT_CW")
    public String maintCW;

    @Column(name = "DOA_FLT_NUM")
    public String doaFleetNumber;

    @Column(name = "DOA_FLT_DATE")
    public Timestamp doaFleetDate;

    @Column(name = "DOA_FLT_LEG")
    public String doaFleetLeg;

    @Column(name = "DOA_FLT_COMMENTS")
    public String doaFleetComments;

    @Column(name = "CLOSED_BY")
    public String closedBy;

    @Column(name = "CLOSED_DT_TM")
    public Timestamp closedDtTm;

    @Column(name = "LAST_UPDATE_DT_TM")
    public Timestamp LastUpdtDtTm;

    @Column(name = "DOA_FLT_ETA")
    public Timestamp DoaFltEta;

    @Column(name="DOA_FLT_DEST")
    public String DoaFltDest;

}
