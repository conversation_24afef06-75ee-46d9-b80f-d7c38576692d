package com.fedex.mets.repository.mets;

import com.fedex.mets.dao.EventListView;
import com.fedex.mets.dao.FlightEticView;
import com.fedex.mets.entity.mets.EventFlightInfo;
import com.fedex.mets.entity.mets.EventFltInfoPk;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EventFlightInfoRepository extends JpaRepository<EventFlightInfo, EventFltInfoPk> {

    @Query(value="select count(*) from event_flt_info",nativeQuery = true)
    public int getEventFlightInfo();

    @Query(value = "Select * from EVENT_FLT_INFO where EVENT_ID=:eventId and FLT_FLAG='I'",nativeQuery = true)
    public EventFlightInfo findByEventIdAndFltFlagI(@Param("eventId") int eventId);

    @Query(value = "Select * from EVENT_FLT_INFO where EVENT_ID=:eventId and (FLT_FLAG='O' or FLT_FLAG='B')",nativeQuery = true)
    public EventFlightInfo findByEventIdAndFltFlagOAndFltFlagB(@Param("eventId") int eventId);

    @Query(value="select * from EVENT_FLT_INFO where EVENT_ID=:id and FLT_FLAG=:flag",nativeQuery = true)
    public EventFlightInfo getEventFlightInfo(@Param("id") int eventId,@Param("flag") Character flag);

    @Query(value="select * from EVENT_FLT_INFO where EVENT_ID=:id and FLT_FLAG=:flag and FLT_NUM=:fltNum and FLT_DATE=TO_DATE('03/19/2024','mm/dd/yy hh24:mi:ss') and FLT_LEG=:leg",nativeQuery = true)
    public EventFlightInfo getEventFlightInfoByFltNumAndEventIdAndFlag(@Param("id") int eventId,@Param("flag") Character flag,@Param("fltNum") String flightNumber,@Param("leg") String flightLeg);


    @Query("Select new com.fedex.mets.dao.FlightEticView(a.flightNumber, a.flightDate, a.flightLeg," +
            " a.flightACN, a.flightDestination,a.flightOrigin, a.flightStatus,a.flightType, a.flightSchedDeptDateTime, " +
            "a.flightActualDeptDateTime, a.flightTotalDelay, b.Description)\n" +
            " from EventFlightInfo a " +
            "LEFT JOIN FlightType b on a.flightType=b.Code\n" +
            "where a.eventFltInfoPk.eventId=:tempEventId\n"+
            "and (a.eventFltInfoPk.flightFlag='A' or a.eventFltInfoPk.flightFlag='B')")
    public FlightEticView getFligthEticData(@Param("tempEventId") int tempEventId);

}
