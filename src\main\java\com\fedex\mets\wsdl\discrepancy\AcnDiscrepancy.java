
package com.fedex.mets.wsdl.discrepancy;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for AcnDiscrepancy complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="AcnDiscrepancy">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="AcnDiscrepancyOid" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="AddProgramNm" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="AddSourceSysCd" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="AddTm" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="AddUserId" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="AircraftNbr" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="AtaNbr" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="AutoGeneratedWriCd" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="AutoGenAircraftDscrpOid" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="AutoGenEmpId" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="CanadianMelFlg" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="CatRelatedStatusFlg" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="efvsRelatedFlg" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="DeferralControlNbr" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="DiscrpDueDt" type="{http://www.w3.org/2001/XMLSchema}date" minOccurs="0"/>
 *         &lt;element name="DscrpInWorkFlg" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="DscrpNbr" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="discrepancyType" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="EarlyAlertFlg" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="EfvsAffectedAmtFlg" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="EnteredByUserId" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="FactTm" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="IntentCd" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="MaintActionRequiredFlg" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="MajorMinorRepairCd" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Mod" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ModPos" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="OilTrackingInWorkCd" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="OpenDt" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="OpenFlg" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="OpenItemFlg" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="OpenStationCd" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="PcsOverideUserId" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="PlannedItemCd" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="PlanOid" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="Rcn" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ReportedByUserId" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="RvsmEntryFlg" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="Serial" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="TsiDefOid" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="UpdateProgramNm" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="UpdateSourceSysCd" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="UpdateTm" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="UpdateUserId" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="WorkOrder" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="WorkOrderType" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ZeroTimeKey" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="s7ReleaseInd" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="images" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="dscrpNotes" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="dscrpMaintUpdt" type="{http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd}AcnDiscrepancyMaintUpdt" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="processControl" type="{http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd}DscrpProcessControl" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="dscrpActionId" type="{http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd}ActionIdDetail" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="logPageNbrs" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="assignPartCounts" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="modAssignLater" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="referenceNbr" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="acnDiscrepFaultCd" type="{http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd}acnDiscrepFaultCdType" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="tfoaEntry" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="repeatModFlg" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="tfoaCategory" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AcnDiscrepancy", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", propOrder = {
    "acnDiscrepancyOid",
    "addProgramNm",
    "addSourceSysCd",
    "addTm",
    "addUserId",
    "aircraftNbr",
    "ataNbr",
    "autoGeneratedWriCd",
    "autoGenAircraftDscrpOid",
    "autoGenEmpId",
    "canadianMelFlg",
    "catRelatedStatusFlg",
    "efvsRelatedFlg",
    "deferralControlNbr",
    "discrpDueDt",
    "dscrpInWorkFlg",
    "dscrpNbr",
    "discrepancyType",
    "earlyAlertFlg",
    "efvsAffectedAmtFlg",
    "enteredByUserId",
    "factTm",
    "intentCd",
    "maintActionRequiredFlg",
    "majorMinorRepairCd",
    "mod",
    "modPos",
    "oilTrackingInWorkCd",
    "openDt",
    "openFlg",
    "openItemFlg",
    "openStationCd",
    "pcsOverideUserId",
    "plannedItemCd",
    "planOid",
    "rcn",
    "reportedByUserId",
    "rvsmEntryFlg",
    "serial",
    "tsiDefOid",
    "updateProgramNm",
    "updateSourceSysCd",
    "updateTm",
    "updateUserId",
    "workOrder",
    "workOrderType",
    "zeroTimeKey",
    "s7ReleaseInd",
    "images",
    "dscrpNotes",
    "dscrpMaintUpdt",
    "processControl",
    "dscrpActionId",
    "logPageNbrs",
    "assignPartCounts",
    "modAssignLater",
    "referenceNbr",
    "acnDiscrepFaultCd",
    "tfoaEntry",
    "repeatModFlg",
    "tfoaCategory"
})
@XmlSeeAlso({
    AutoSchedAcnDiscrepancy.class
})
public class AcnDiscrepancy {

    @XmlElement(name = "AcnDiscrepancyOid", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal acnDiscrepancyOid;
    @XmlElement(name = "AddProgramNm", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String addProgramNm;
    @XmlElement(name = "AddSourceSysCd", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String addSourceSysCd;
    @XmlElement(name = "AddTm", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar addTm;
    @XmlElement(name = "AddUserId", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal addUserId;
    @XmlElement(name = "AircraftNbr", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String aircraftNbr;
    @XmlElement(name = "AtaNbr", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal ataNbr;
    @XmlElement(name = "AutoGeneratedWriCd", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String autoGeneratedWriCd;
    @XmlElement(name = "AutoGenAircraftDscrpOid", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal autoGenAircraftDscrpOid;
    @XmlElement(name = "AutoGenEmpId", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal autoGenEmpId;
    @XmlElement(name = "CanadianMelFlg", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String canadianMelFlg;
    @XmlElement(name = "CatRelatedStatusFlg", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String catRelatedStatusFlg;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String efvsRelatedFlg;
    @XmlElement(name = "DeferralControlNbr", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal deferralControlNbr;
    @XmlElement(name = "DiscrpDueDt", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar discrpDueDt;
    @XmlElement(name = "DscrpInWorkFlg", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String dscrpInWorkFlg;
    @XmlElement(name = "DscrpNbr", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String dscrpNbr;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String discrepancyType;
    @XmlElement(name = "EarlyAlertFlg", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String earlyAlertFlg;
    @XmlElement(name = "EfvsAffectedAmtFlg")
    protected String efvsAffectedAmtFlg;
    @XmlElement(name = "EnteredByUserId", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal enteredByUserId;
    @XmlElement(name = "FactTm", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar factTm;
    @XmlElement(name = "IntentCd", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String intentCd;
    @XmlElement(name = "MaintActionRequiredFlg", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String maintActionRequiredFlg;
    @XmlElement(name = "MajorMinorRepairCd", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String majorMinorRepairCd;
    @XmlElement(name = "Mod", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String mod;
    @XmlElement(name = "ModPos", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String modPos;
    @XmlElement(name = "OilTrackingInWorkCd", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String oilTrackingInWorkCd;
    @XmlElement(name = "OpenDt", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar openDt;
    @XmlElement(name = "OpenFlg", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String openFlg;
    @XmlElement(name = "OpenItemFlg", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String openItemFlg;
    @XmlElement(name = "OpenStationCd", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String openStationCd;
    @XmlElement(name = "PcsOverideUserId", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal pcsOverideUserId;
    @XmlElement(name = "PlannedItemCd", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String plannedItemCd;
    @XmlElement(name = "PlanOid", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal planOid;
    @XmlElement(name = "Rcn", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String rcn;
    @XmlElement(name = "ReportedByUserId", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal reportedByUserId;
    @XmlElement(name = "RvsmEntryFlg", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String rvsmEntryFlg;
    @XmlElement(name = "Serial", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String serial;
    @XmlElement(name = "TsiDefOid", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal tsiDefOid;
    @XmlElement(name = "UpdateProgramNm", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String updateProgramNm;
    @XmlElement(name = "UpdateSourceSysCd", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String updateSourceSysCd;
    @XmlElement(name = "UpdateTm", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar updateTm;
    @XmlElement(name = "UpdateUserId", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal updateUserId;
    @XmlElement(name = "WorkOrder", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String workOrder;
    @XmlElement(name = "WorkOrderType", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String workOrderType;
    @XmlElement(name = "ZeroTimeKey", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal zeroTimeKey;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String s7ReleaseInd;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected boolean images;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected boolean dscrpNotes;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected List<AcnDiscrepancyMaintUpdt> dscrpMaintUpdt;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected List<DscrpProcessControl> processControl;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected List<ActionIdDetail> dscrpActionId;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected List<String> logPageNbrs;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal assignPartCounts;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected boolean modAssignLater;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String referenceNbr;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected List<AcnDiscrepFaultCdType> acnDiscrepFaultCd;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", defaultValue = "false")
    protected boolean tfoaEntry;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String repeatModFlg;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal tfoaCategory;

    /**
     * Gets the value of the acnDiscrepancyOid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAcnDiscrepancyOid() {
        return acnDiscrepancyOid;
    }

    /**
     * Sets the value of the acnDiscrepancyOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAcnDiscrepancyOid(BigDecimal value) {
        this.acnDiscrepancyOid = value;
    }

    /**
     * Gets the value of the addProgramNm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAddProgramNm() {
        return addProgramNm;
    }

    /**
     * Sets the value of the addProgramNm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAddProgramNm(String value) {
        this.addProgramNm = value;
    }

    /**
     * Gets the value of the addSourceSysCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAddSourceSysCd() {
        return addSourceSysCd;
    }

    /**
     * Sets the value of the addSourceSysCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAddSourceSysCd(String value) {
        this.addSourceSysCd = value;
    }

    /**
     * Gets the value of the addTm property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getAddTm() {
        return addTm;
    }

    /**
     * Sets the value of the addTm property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setAddTm(XMLGregorianCalendar value) {
        this.addTm = value;
    }

    /**
     * Gets the value of the addUserId property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAddUserId() {
        return addUserId;
    }

    /**
     * Sets the value of the addUserId property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAddUserId(BigDecimal value) {
        this.addUserId = value;
    }

    /**
     * Gets the value of the aircraftNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAircraftNbr() {
        return aircraftNbr;
    }

    /**
     * Sets the value of the aircraftNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAircraftNbr(String value) {
        this.aircraftNbr = value;
    }

    /**
     * Gets the value of the ataNbr property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAtaNbr() {
        return ataNbr;
    }

    /**
     * Sets the value of the ataNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAtaNbr(BigDecimal value) {
        this.ataNbr = value;
    }

    /**
     * Gets the value of the autoGeneratedWriCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAutoGeneratedWriCd() {
        return autoGeneratedWriCd;
    }

    /**
     * Sets the value of the autoGeneratedWriCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAutoGeneratedWriCd(String value) {
        this.autoGeneratedWriCd = value;
    }

    /**
     * Gets the value of the autoGenAircraftDscrpOid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAutoGenAircraftDscrpOid() {
        return autoGenAircraftDscrpOid;
    }

    /**
     * Sets the value of the autoGenAircraftDscrpOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAutoGenAircraftDscrpOid(BigDecimal value) {
        this.autoGenAircraftDscrpOid = value;
    }

    /**
     * Gets the value of the autoGenEmpId property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAutoGenEmpId() {
        return autoGenEmpId;
    }

    /**
     * Sets the value of the autoGenEmpId property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAutoGenEmpId(BigDecimal value) {
        this.autoGenEmpId = value;
    }

    /**
     * Gets the value of the canadianMelFlg property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCanadianMelFlg() {
        return canadianMelFlg;
    }

    /**
     * Sets the value of the canadianMelFlg property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCanadianMelFlg(String value) {
        this.canadianMelFlg = value;
    }

    /**
     * Gets the value of the catRelatedStatusFlg property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCatRelatedStatusFlg() {
        return catRelatedStatusFlg;
    }

    /**
     * Sets the value of the catRelatedStatusFlg property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCatRelatedStatusFlg(String value) {
        this.catRelatedStatusFlg = value;
    }

    /**
     * Gets the value of the efvsRelatedFlg property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEfvsRelatedFlg() {
        return efvsRelatedFlg;
    }

    /**
     * Sets the value of the efvsRelatedFlg property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEfvsRelatedFlg(String value) {
        this.efvsRelatedFlg = value;
    }

    /**
     * Gets the value of the deferralControlNbr property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getDeferralControlNbr() {
        return deferralControlNbr;
    }

    /**
     * Sets the value of the deferralControlNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setDeferralControlNbr(BigDecimal value) {
        this.deferralControlNbr = value;
    }

    /**
     * Gets the value of the discrpDueDt property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDiscrpDueDt() {
        return discrpDueDt;
    }

    /**
     * Sets the value of the discrpDueDt property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDiscrpDueDt(XMLGregorianCalendar value) {
        this.discrpDueDt = value;
    }

    /**
     * Gets the value of the dscrpInWorkFlg property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDscrpInWorkFlg() {
        return dscrpInWorkFlg;
    }

    /**
     * Sets the value of the dscrpInWorkFlg property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDscrpInWorkFlg(String value) {
        this.dscrpInWorkFlg = value;
    }

    /**
     * Gets the value of the dscrpNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDscrpNbr() {
        return dscrpNbr;
    }

    /**
     * Sets the value of the dscrpNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDscrpNbr(String value) {
        this.dscrpNbr = value;
    }

    /**
     * Gets the value of the discrepancyType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDiscrepancyType() {
        return discrepancyType;
    }

    /**
     * Sets the value of the discrepancyType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDiscrepancyType(String value) {
        this.discrepancyType = value;
    }

    /**
     * Gets the value of the earlyAlertFlg property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEarlyAlertFlg() {
        return earlyAlertFlg;
    }

    /**
     * Sets the value of the earlyAlertFlg property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEarlyAlertFlg(String value) {
        this.earlyAlertFlg = value;
    }
    
    /**
     * Gets the value of the efvsAffectedAmtFlg property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEfvsAffectedAmtFlg() {
		return this.efvsAffectedAmtFlg;
	}
	
    /**
     * Sets the value of the efvsAffectedAmtFlg property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
	public void setEfvsAffectedAmtFlg(String efvsAffectedAmtFlg) {
		this.efvsAffectedAmtFlg = efvsAffectedAmtFlg;
	}

    /**
     * Gets the value of the enteredByUserId property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getEnteredByUserId() {
        return enteredByUserId;
    }

    /**
     * Sets the value of the enteredByUserId property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setEnteredByUserId(BigDecimal value) {
        this.enteredByUserId = value;
    }

    /**
     * Gets the value of the factTm property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getFactTm() {
        return factTm;
    }

    /**
     * Sets the value of the factTm property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setFactTm(XMLGregorianCalendar value) {
        this.factTm = value;
    }

    /**
     * Gets the value of the intentCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIntentCd() {
        return intentCd;
    }

    /**
     * Sets the value of the intentCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIntentCd(String value) {
        this.intentCd = value;
    }

    /**
     * Gets the value of the maintActionRequiredFlg property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMaintActionRequiredFlg() {
        return maintActionRequiredFlg;
    }

    /**
     * Sets the value of the maintActionRequiredFlg property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMaintActionRequiredFlg(String value) {
        this.maintActionRequiredFlg = value;
    }

    /**
     * Gets the value of the majorMinorRepairCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMajorMinorRepairCd() {
        return majorMinorRepairCd;
    }

    /**
     * Sets the value of the majorMinorRepairCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMajorMinorRepairCd(String value) {
        this.majorMinorRepairCd = value;
    }

    /**
     * Gets the value of the mod property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMod() {
        return mod;
    }

    /**
     * Sets the value of the mod property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMod(String value) {
        this.mod = value;
    }

    /**
     * Gets the value of the modPos property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getModPos() {
        return modPos;
    }

    /**
     * Sets the value of the modPos property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setModPos(String value) {
        this.modPos = value;
    }

    /**
     * Gets the value of the oilTrackingInWorkCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOilTrackingInWorkCd() {
        return oilTrackingInWorkCd;
    }

    /**
     * Sets the value of the oilTrackingInWorkCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOilTrackingInWorkCd(String value) {
        this.oilTrackingInWorkCd = value;
    }

    /**
     * Gets the value of the openDt property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getOpenDt() {
        return openDt;
    }

    /**
     * Sets the value of the openDt property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setOpenDt(XMLGregorianCalendar value) {
        this.openDt = value;
    }

    /**
     * Gets the value of the openFlg property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOpenFlg() {
        return openFlg;
    }

    /**
     * Sets the value of the openFlg property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOpenFlg(String value) {
        this.openFlg = value;
    }

    /**
     * Gets the value of the openItemFlg property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOpenItemFlg() {
        return openItemFlg;
    }

    /**
     * Sets the value of the openItemFlg property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOpenItemFlg(String value) {
        this.openItemFlg = value;
    }

    /**
     * Gets the value of the openStationCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOpenStationCd() {
        return openStationCd;
    }

    /**
     * Sets the value of the openStationCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOpenStationCd(String value) {
        this.openStationCd = value;
    }

    /**
     * Gets the value of the pcsOverideUserId property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getPcsOverideUserId() {
        return pcsOverideUserId;
    }

    /**
     * Sets the value of the pcsOverideUserId property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setPcsOverideUserId(BigDecimal value) {
        this.pcsOverideUserId = value;
    }

    /**
     * Gets the value of the plannedItemCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPlannedItemCd() {
        return plannedItemCd;
    }

    /**
     * Sets the value of the plannedItemCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPlannedItemCd(String value) {
        this.plannedItemCd = value;
    }

    /**
     * Gets the value of the planOid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getPlanOid() {
        return planOid;
    }

    /**
     * Sets the value of the planOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setPlanOid(BigDecimal value) {
        this.planOid = value;
    }

    /**
     * Gets the value of the rcn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRcn() {
        return rcn;
    }

    /**
     * Sets the value of the rcn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRcn(String value) {
        this.rcn = value;
    }

    /**
     * Gets the value of the reportedByUserId property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getReportedByUserId() {
        return reportedByUserId;
    }

    /**
     * Sets the value of the reportedByUserId property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setReportedByUserId(BigDecimal value) {
        this.reportedByUserId = value;
    }

    /**
     * Gets the value of the rvsmEntryFlg property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRvsmEntryFlg() {
        return rvsmEntryFlg;
    }

    /**
     * Sets the value of the rvsmEntryFlg property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRvsmEntryFlg(String value) {
        this.rvsmEntryFlg = value;
    }

    /**
     * Gets the value of the serial property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSerial() {
        return serial;
    }

    /**
     * Sets the value of the serial property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSerial(String value) {
        this.serial = value;
    }

    /**
     * Gets the value of the tsiDefOid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTsiDefOid() {
        return tsiDefOid;
    }

    /**
     * Sets the value of the tsiDefOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTsiDefOid(BigDecimal value) {
        this.tsiDefOid = value;
    }

    /**
     * Gets the value of the updateProgramNm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUpdateProgramNm() {
        return updateProgramNm;
    }

    /**
     * Sets the value of the updateProgramNm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUpdateProgramNm(String value) {
        this.updateProgramNm = value;
    }

    /**
     * Gets the value of the updateSourceSysCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUpdateSourceSysCd() {
        return updateSourceSysCd;
    }

    /**
     * Sets the value of the updateSourceSysCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUpdateSourceSysCd(String value) {
        this.updateSourceSysCd = value;
    }

    /**
     * Gets the value of the updateTm property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getUpdateTm() {
        return updateTm;
    }

    /**
     * Sets the value of the updateTm property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setUpdateTm(XMLGregorianCalendar value) {
        this.updateTm = value;
    }

    /**
     * Gets the value of the updateUserId property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getUpdateUserId() {
        return updateUserId;
    }

    /**
     * Sets the value of the updateUserId property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setUpdateUserId(BigDecimal value) {
        this.updateUserId = value;
    }

    /**
     * Gets the value of the workOrder property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWorkOrder() {
        return workOrder;
    }

    /**
     * Sets the value of the workOrder property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWorkOrder(String value) {
        this.workOrder = value;
    }

    /**
     * Gets the value of the workOrderType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWorkOrderType() {
        return workOrderType;
    }

    /**
     * Sets the value of the workOrderType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWorkOrderType(String value) {
        this.workOrderType = value;
    }

    /**
     * Gets the value of the zeroTimeKey property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getZeroTimeKey() {
        return zeroTimeKey;
    }

    /**
     * Sets the value of the zeroTimeKey property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setZeroTimeKey(BigDecimal value) {
        this.zeroTimeKey = value;
    }

    /**
     * Gets the value of the s7ReleaseInd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getS7ReleaseInd() {
        return s7ReleaseInd;
    }

    /**
     * Sets the value of the s7ReleaseInd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setS7ReleaseInd(String value) {
        this.s7ReleaseInd = value;
    }

    /**
     * Gets the value of the images property.
     * 
     */
    public boolean isImages() {
        return images;
    }

    /**
     * Sets the value of the images property.
     * 
     */
    public void setImages(boolean value) {
        this.images = value;
    }

    /**
     * Gets the value of the dscrpNotes property.
     * 
     */
    public boolean isDscrpNotes() {
        return dscrpNotes;
    }

    /**
     * Sets the value of the dscrpNotes property.
     * 
     */
    public void setDscrpNotes(boolean value) {
        this.dscrpNotes = value;
    }

    /**
     * Gets the value of the dscrpMaintUpdt property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the dscrpMaintUpdt property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getDscrpMaintUpdt().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link AcnDiscrepancyMaintUpdt }
     * 
     * 
     */
    public List<AcnDiscrepancyMaintUpdt> getDscrpMaintUpdt() {
        if (dscrpMaintUpdt == null) {
            dscrpMaintUpdt = new ArrayList<AcnDiscrepancyMaintUpdt>();
        }
        return this.dscrpMaintUpdt;
    }

    /**
     * Gets the value of the processControl property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the processControl property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getProcessControl().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link DscrpProcessControl }
     * 
     * 
     */
    public List<DscrpProcessControl> getProcessControl() {
        if (processControl == null) {
            processControl = new ArrayList<DscrpProcessControl>();
        }
        return this.processControl;
    }

    /**
     * Gets the value of the dscrpActionId property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the dscrpActionId property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getDscrpActionId().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link ActionIdDetail }
     * 
     * 
     */
    public List<ActionIdDetail> getDscrpActionId() {
        if (dscrpActionId == null) {
            dscrpActionId = new ArrayList<ActionIdDetail>();
        }
        return this.dscrpActionId;
    }

    /**
     * Gets the value of the logPageNbrs property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the logPageNbrs property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getLogPageNbrs().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link String }
     * 
     * 
     */
    public List<String> getLogPageNbrs() {
        if (logPageNbrs == null) {
            logPageNbrs = new ArrayList<String>();
        }
        return this.logPageNbrs;
    }

    /**
     * Gets the value of the assignPartCounts property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAssignPartCounts() {
        return assignPartCounts;
    }

    /**
     * Sets the value of the assignPartCounts property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAssignPartCounts(BigDecimal value) {
        this.assignPartCounts = value;
    }

    /**
     * Gets the value of the modAssignLater property.
     * 
     */
    public boolean isModAssignLater() {
        return modAssignLater;
    }

    /**
     * Sets the value of the modAssignLater property.
     * 
     */
    public void setModAssignLater(boolean value) {
        this.modAssignLater = value;
    }

    /**
     * Gets the value of the referenceNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReferenceNbr() {
        return referenceNbr;
    }

    /**
     * Sets the value of the referenceNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReferenceNbr(String value) {
        this.referenceNbr = value;
    }

    /**
     * Gets the value of the acnDiscrepFaultCd property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the acnDiscrepFaultCd property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getAcnDiscrepFaultCd().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link AcnDiscrepFaultCdType }
     * 
     * 
     */
    public List<AcnDiscrepFaultCdType> getAcnDiscrepFaultCd() {
        if (acnDiscrepFaultCd == null) {
            acnDiscrepFaultCd = new ArrayList<AcnDiscrepFaultCdType>();
        }
        return this.acnDiscrepFaultCd;
    }

    /**
     * Gets the value of the tfoaEntry property.
     * 
     */
    public boolean isTfoaEntry() {
        return tfoaEntry;
    }

    /**
     * Sets the value of the tfoaEntry property.
     * 
     */
    public void setTfoaEntry(boolean value) {
        this.tfoaEntry = value;
    }

    /**
     * Gets the value of the repeatModFlg property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRepeatModFlg() {
        return repeatModFlg;
    }

    /**
     * Sets the value of the repeatModFlg property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRepeatModFlg(String value) {
        this.repeatModFlg = value;
    }

    /**
     * Gets the value of the tfoaCategory property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTfoaCategory() {
        return tfoaCategory;
    }

    /**
     * Sets the value of the tfoaCategory property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTfoaCategory(BigDecimal value) {
        this.tfoaCategory = value;
    }

}
