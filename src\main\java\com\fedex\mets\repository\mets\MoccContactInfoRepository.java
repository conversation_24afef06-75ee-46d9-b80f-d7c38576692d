package com.fedex.mets.repository.mets;

import com.fedex.mets.entity.mets.MoccContactInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MoccContactInfoRepository extends JpaRepository<MoccContactInfo, String> {

    @Query(value="Select HOURS_PHONE from MOCC_CONTACT_INFO order by AC_MFG",nativeQuery = true)
    public List<String> getHoursPhone();
}
