package com.fedex.mets.repository.mets;

import com.fedex.mets.entity.mets.EventMaxiDisc;
import com.fedex.mets.entity.mets.EventMaxiDiscPk;
import com.fedex.mets.entity.mets.Events;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EventMaxiDiscRepository extends JpaRepository<EventMaxiDisc, EventMaxiDiscPk> {
    @Query(value="select * from EVENT_MAXI_DISC where EVENT_ID=:id",nativeQuery = true)
    public List<EventMaxiDisc> getEventMaxiDiscInfo(@Param("id") int id);
    @Query(value="select * from EVENT_MAXI_DISC where EVENT_ID=:eventId AND ATA=:ata AND DISC_NUM=:discNumber",nativeQuery = true)
    public EventMaxiDisc findMaxiDiscByEventIdAndAta(@Param("eventId") int eventId, @Param("ata") String ata, @Param("discNumber") int discNumber);

}
