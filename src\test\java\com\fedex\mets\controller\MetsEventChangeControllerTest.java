package com.fedex.mets.controller;

import com.fedex.mets.data.WizardEventData;
import com.fedex.mets.dto.MetsResponse;
import com.fedex.mets.service.changeEvent.EventChangeService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

public class MetsEventChangeControllerTest {

    @Mock
    private EventChangeService eventChangeService;

    @InjectMocks
    private MetsEventChangeController metsEventChangeController;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testChangeEvent_Success() throws Exception {
        WizardEventData request = new WizardEventData();
        request.setUserId("User1");
        request.setTokenId("Token1");
        HashMap<String, Object> responseMap = new HashMap<>();
        responseMap.put("key", "value");

        when(eventChangeService.changeEvent(any(WizardEventData.class), any(String.class), any(String.class))).thenReturn(responseMap);

        ResponseEntity<MetsResponse> response = metsEventChangeController.changeEvent(request);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(responseMap, response.getBody().getData());
    }

    @Test
    public void testChangeEvent_InvalidInput() throws Exception {
        WizardEventData request = new WizardEventData();
        request.setUserId("User1");
        request.setTokenId("Token1");

        doThrow(new IllegalArgumentException("Invalid input")).when(eventChangeService).changeEvent(any(WizardEventData.class), any(String.class), any(String.class));

        ResponseEntity<MetsResponse> response = metsEventChangeController.changeEvent(request);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    }

    @Test
    public void testChangeEvent_InternalServerError() throws Exception {
        WizardEventData request = new WizardEventData();
        request.setUserId("User1");
        request.setTokenId("Token1");

        doThrow(new RuntimeException("Internal server error")).when(eventChangeService).changeEvent(any(WizardEventData.class), any(String.class), any(String.class));

        ResponseEntity<MetsResponse> response = metsEventChangeController.changeEvent(request);

        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
    }
}
