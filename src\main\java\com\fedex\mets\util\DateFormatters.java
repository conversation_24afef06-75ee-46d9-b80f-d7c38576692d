package com.fedex.mets.util;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.TimeZone;

public class DateFormatters {
  private static final TimeZone GMT = TimeZone.getTimeZone("GMT");
  
  private SimpleDateFormat sdf_HHmmWithCol = new SimpleDateFormat("HH:mm");
  
  private SimpleDateFormat sdf_HHmm = new SimpleDateFormat("HHmm");
  
  private SimpleDateFormat sdf_ddMMMyy = new SimpleDateFormat("ddMMMyy");
  
  private SimpleDateFormat sdf_ddMMMyy_HH_mm = new SimpleDateFormat("ddMMMyy HH:mm");
  
  private SimpleDateFormat sdf_yyyyMMdd = new SimpleDateFormat("yyyyMMdd");
  
  private SimpleDateFormat sdf_yyyyMMdd_HHmm = new SimpleDateFormat("yyyyMMdd HHmm");
  
  private HashMap<String, SimpleDateFormat> formatters = new HashMap<String, SimpleDateFormat>();
  
  private static DateFormatters instanceGMT = null;
  
  private DateFormatters(boolean isGMT) {
    if (isGMT) {
      this.sdf_HHmmWithCol.setTimeZone(GMT);
      this.sdf_HHmm.setTimeZone(GMT);
      this.sdf_ddMMMyy.setTimeZone(GMT);
      this.sdf_ddMMMyy_HH_mm.setTimeZone(GMT);
      this.sdf_yyyyMMdd.setTimeZone(GMT);
      this.sdf_yyyyMMdd_HHmm.setTimeZone(GMT);
    } 
    this.formatters.put("HH:mm", this.sdf_HHmmWithCol);
    this.formatters.put("HHmm", this.sdf_HHmm);
    this.formatters.put("sdf_ddMMMyy", this.sdf_ddMMMyy);
    this.formatters.put("ddMMMyy HHmm", this.sdf_ddMMMyy_HH_mm);
    this.formatters.put("yyyyMMdd", this.sdf_yyyyMMdd);
    this.formatters.put("yyyyMMdd HHmm", this.sdf_yyyyMMdd_HHmm);
  }
  public static DateFormatters getInstanceGMT() {
    if (instanceGMT == null)
      instanceGMT = new DateFormatters(true); 
    return instanceGMT;
  }
  
  public String formatddMMMyy(Date date) {
    return this.sdf_ddMMMyy.format(date);
  }

  
  public String format(Date date, String pattern, boolean isGMT) throws IllegalArgumentException {
    SimpleDateFormat sdf = getFormatter(pattern, isGMT);
    return sdf.format(date);
  }

  private SimpleDateFormat getFormatter(String pattern, boolean isGMT) throws IllegalArgumentException {
    SimpleDateFormat retVal = this.formatters.get(pattern);
    if (retVal == null) {
      retVal = new SimpleDateFormat(pattern);
      if (isGMT)
        retVal.setTimeZone(GMT); 
      this.formatters.put(pattern, retVal);
    } 
    return retVal;
  }
}
