package com.fedex.mets.util;

public class LogpagesLineFormat extends BaseLineFormat{
	
	protected void init(){
		sfd = new SentenceFormatDef("Logpages", 80, 5, 3);
		sfd.setMultiLine(true);
		addLogpageHeader();
		addLogpageValues();
	}
	
	private void addLogpageHeader() {
		FieldFormatDef ffd = new FieldFormatDef("Logpage:",  9);
		ffd.setValue("Logpages:");
		sfd.getFields().add(ffd);
	}

	private void addLogpageValues() {
		FieldFormatDef ffd = new FieldFormatDef("Logpages",  9, true);
		sfd.getFields().add(ffd);
	}
}

