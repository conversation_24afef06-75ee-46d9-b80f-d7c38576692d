package com.fedex.mets.util;

import com.fedex.mets.config.RampviewDataSourceConfig;
import com.fedex.mets.dao.AircraftBean;
import com.fedex.mets.data.ListViewData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


@Service
public class RvDBHelper {

    public static final String ACN_CD = "ACN_CD";
    public static final String AIRCRAFT_REG = "AIRCRAFT_REG";
    public static final String IS_CHARTER = "IS_CHARTER";
    public static final String MX_STATUS_CD = "MX_STATUS_CD";
    public static final String LAUNCH_STATUS_CD = "LAUNCH_STATUS_CD";
    public static final String CAT_STATUS_CD = "CAT_STATUS_CD";
    public static final String CURR_STATION_CD = "CURR_STATION_CD";
    public static final String CURR_GATE = "CURR_GATE";
    public static final String OOOI_STATUS_CD = "OOOI_STATUS_CD";
    public static final String LOCATION = "LOCATION";
    public static final String CURR_FLT_DT = "CURR_FLT_DT";
    public static final String CURR_FLT_NBR = "CURR_FLT_NBR";
    public static final String CURR_FLT_LEG = "CURR_FLT_LEG";
    public static final String CURR_FLT_LEG_DT = "CURR_FLT_LEG_DT";
    public static final String CURR_ORIGIN = "CURR_ORIGIN";
    public static final String CURR_DEST = "CURR_DEST";
    public static final String CURR_DEPARTURE_GATE = "CURR_DEPARTURE_GATE";
    public static final String CURR_OUT_DTM = "CURR_OUT_DTM";
    public static final String CURR_GROUND_START = "CURR_GROUND_START";
    public static final String CURR_GROUND_END = "CURR_GROUND_END";
    public static final String PREV_FLT_DT = "PREV_FLT_DT";
    public static final String PREV_FLT_NBR = "PREV_FLT_NBR";
    public static final String PREV_FLT_LEG = "PREV_FLT_LEG";
    public static final String PREV_FLT_LEG_DT = "PREV_FLT_LEG_DT";
    public static final String PREV_ORIGIN = "PREV_ORIGIN";
    public static final String PREV_DEPARTURE_GATE = "PREV_DEPARTURE_GATE";
    public static final String PREV_DEST = "PREV_DEST";
    public static final String PREV_ARRIVAL_GATE = "PREV_ARRIVAL_GATE";
    public static final String PREV_OUT_DTM = "PREV_OUT_DTM";
    public static final String PREV_GROUND_START = "PREV_GROUND_START";
    public static final String PREV_GROUND_END = "PREV_GROUND_END";
    public static final String GROUND_TIME = "GROUND_TIME";
    public static final String REMAINING_PREV_GROUND_TIME = "REMAINING_PREV_GROUND_TIME";
    public static final String SYSDATE_UTC = "SYSDATE_UTC";
    public static final String NEXT_FLT_DT = "NEXT_FLT_DT";
    public static final String NEXT_FLT_NBR = "NEXT_FLT_NBR";
    public static final String NEXT_FLT_LEG = "NEXT_FLT_LEG";
    public static final String NEXT_FLT_LEG_DT = "NEXT_FLT_LEG_DT";
    public static final String NEXT_ORIGIN = "NEXT_ORIGIN";
    public static final String NEXT_DEPARTURE_GATE = "NEXT_DEPARTURE_GATE";
    public static final String NEXT_DEST = "NEXT_DEST";
    public static final String NEXT_ARRIVAL_GATE = "NEXT_ARRIVAL_GATE";
    public static final String NEXT_IN_DTM = "NEXT_IN_DTM";
    public static final String NEXT_GROUND_START = "NEXT_GROUND_START";
    public static final String NEXT_GROUND_END = "NEXT_GROUND_END";
    public static final String NEXT_GROUND_TIME = "NEXT_GROUND_TIME";
    public static final String EQUIPEMENT_TYPE = "EQUIPMENT_TYPE";
    public static final String SELECT_ALL_FLIGHT_STATUS =
            "SELECT a.acn_cd, a.Aircraft_Reg, a.is_charter, a.mx_status_cd, a.launch_status_cd, a.cat_status_cd,a.equipment_Type"
                    + "   ,a.Curr_Station_Cd, a.Curr_Gate, a.Oooi_Status_Cd "
                    + "   ,case when a.Oooi_Status_Cd is null and a.Curr_Station_Cd is not null then 'At Station' "
                    + "	 	  when a.Oooi_Status_Cd is not null then 'Departed' "
                    + "	 	  else 'Unknown' "
                    + "	  end as location "
                    + "   ,TO_CHAR(a.curr_flt_dt, 'YYYYMMDD') as curr_flt_dt"
                    + "   ,a.curr_flt_nbr "
                    + "   ,a.curr_flt_leg "
                    + "   ,TO_CHAR(trunc(f_curr.sch_departure_dtm), 'YYYYMMDD') as curr_flt_leg_dt "
                    + "   ,f_curr.origin_station_cd as curr_origin, f_curr.destination_station_cd as curr_dest "
                    + "   ,f_curr.departure_gate as curr_departure_gate "
                    + "   ,TO_CHAR(coalesce(f_curr.block_out_dtm, f_curr.est_departure_dtm, f_curr.sch_departure_dtm), 'HH24MI') as curr_out_dtm "
                    + "   ,TO_CHAR(f_prev.flt_dt, 'YYYYMMDD') as prev_flt_dt "
                    + "   ,f_prev.flt_nbr as prev_flt_nbr "
                    + "   ,f_prev.flt_leg as prev_flt_leg "
                    + "   ,TO_CHAR(trunc(f_prev.sch_departure_dtm), 'YYYYMMDD') as prev_flt_leg_dt "
                    + "   ,f_prev.origin_station_cd as prev_origin "
                    + "   ,f_prev.departure_gate as prev_departure_gate "
                    + "   ,f_prev.destination_station_cd as prev_dest "
                    + "   ,f_prev.arrival_gate as prev_arrival_gate "
                    + "   ,TO_CHAR(coalesce(f_prev.block_out_dtm, f_prev.est_departure_dtm, f_prev.sch_departure_dtm), 'HH24MI') as prev_out_dtm "
                    + "   ,TO_CHAR(coalesce(f_prev.block_in_dtm, f_prev.est_arrival_dtm, f_prev.sch_arrival_dtm), 'YYYYMMDD HH24MI') as curr_ground_start "
                    + "   ,coalesce(f_curr.block_out_dtm, f_curr.est_departure_dtm, f_curr.sch_departure_dtm) as curr_ground_end "
                    + "   ,round ((coalesce(f_curr.block_out_dtm, f_curr.est_departure_dtm, f_curr.sch_departure_dtm) "
                    + "        - coalesce(f_prev.block_in_dtm, f_prev.est_arrival_dtm, f_prev.sch_arrival_dtm)) * 1440, 1) as ground_time "
                    + "   ,round ((coalesce(f_curr.block_out_dtm, f_curr.est_departure_dtm, f_curr.sch_departure_dtm) "
                    + "        - rv_utils_pkg.SYSDATE_UTC) * 1440, 1) as remaining_ground_time "
                    + "   ,rv_utils_pkg.SYSDATE_UTC as SYSDATE_UTC "
                    + "   ,TO_CHAR(f_next.flt_dt, 'YYYYMMDD') as next_flt_dt "
                    + "   ,f_next.flt_nbr as next_flt_nbr "
                    + "   ,f_next.flt_leg as next_flt_leg "
                    + "   ,TO_CHAR(trunc(f_next.sch_departure_dtm), 'YYYYMMDD') as next_flt_leg_dt "
                    + "   ,f_curr.destination_station_cd as next_origin "
                    + "   ,nvl(a.curr_gate, f_curr.departure_gate) as next_departure_gate "
                    + "   ,f_next.destination_station_cd as next_dest "
                    + "   ,f_next.arrival_gate as next_arrival_gate "
                    + "   ,TO_CHAR(coalesce(f_next.block_in_dtm, f_next.est_arrival_dtm, f_next.sch_arrival_dtm), 'YYYYMMDD HH24MI') as next_in_dtm "
                    + "   ,TO_CHAR(coalesce(f_curr.block_in_dtm, f_curr.est_arrival_dtm, f_curr.sch_arrival_dtm), 'YYYYMMDD HH24MI') as next_ground_start "
                    + "   ,TO_CHAR(coalesce(f_next.block_out_dtm, f_next.est_departure_dtm, f_next.sch_departure_dtm), 'HH24MI') as next_ground_end "
                    + "   ,round ((coalesce(f_next.block_out_dtm, f_next.est_departure_dtm, f_next.sch_departure_dtm) "
                    + "        - coalesce(f_curr.block_in_dtm, f_curr.est_arrival_dtm, f_curr.sch_arrival_dtm)) * 1440, 1) as next_ground_time "
                    + " FROM RV_AIRCRAFT a "
                    + "   ,RV_FLIGHT_LEGS f_prev "
                    + "   ,RV_FLIGHT_LEGS f_curr "
                    + "   ,RV_FLIGHT_LEGS f_next "
                    + " WHERE NVL(a.is_inactive, 'N') <> 'Y' "
                    + " and nvl(a.mx_status_cd, 0) <> 999 "
                    + " and NVL(a.is_charter, 'N') <> 'Y' "
                    + " AND a.Last_Block_In_Flt_Dt = f_prev.flt_dt (+) "
                    + " AND a.Last_Block_In_Flt_Nbr = f_prev.flt_nbr (+) "
                    + " AND a.Last_Block_In_Flt_Leg_Nbr = f_prev.flt_leg (+) "
                    + " AND a.curr_flt_dt = f_curr.flt_dt (+) "
                    + " AND a.curr_flt_nbr = f_curr.flt_nbr (+) "
                    + " AND a.curr_flt_leg = f_curr.flt_leg (+) "
                    + " AND f_curr.next_flt_dt = f_next.flt_dt (+) "
                    + " AND f_curr.next_flt_nbr = f_next.flt_nbr (+) "
                    + " AND f_curr.next_flt_leg = f_next.flt_leg (+) ";
    public static final String SINGLE_ACN_ONLY = " AND a.acn_cd=?";
    private static final Logger logger = LoggerFactory.getLogger(RvDBHelper.class);
    public static String ALL_ACN = "ALL_ACN";
//    private static JdbcTemplate rvJdbcTemplate;
    @Autowired
	RampviewDataSourceConfig rampviewDataSourceConfig;

//	@Autowired
//	private static JdbcTemplate jdbcTemplate;

//	@Autowired
//	RampviewDataSourceConfig rampviewDataSourceConfig;

//	@GetMapping(path ="/test")
//	public void test() throws MalformedURLException, SQLException {
//		String sql = "select acn_cd from RV_AIRCRAFT";
//		Connection con=rampviewDataSourceConfig.rampviewDataSource().getConnection();
//		PreparedStatement statement = con.prepareStatement(sql);
//		statement.setFetchSize(1000);
//		ResultSet rs = statement.executeQuery();
//		System.out.println("Connection established......"+rs);
////        jdbcTemplate.setFetchSize(1000);
////        return jdbcTemplate.queryForList(sql, String.class);
//
//	}

//    public RvDBHelper(
//            @Qualifier("rvJdbcTemplate") JdbcTemplate rvJdbcTemplate,
//            @Qualifier("metsJdbcTemplate") JdbcTemplate metsJdbcTemplate
//    ) {
//        RvDBHelper.rvJdbcTemplate = rvJdbcTemplate;
//    }

    public List<AircraftBean> getNewAircraftRecordsFromRampview(String acn, List<ListViewData> list) throws SQLException {

        logger.info("Retrieving Aircraft from RAMPVIEW");
        logger.info("ACN: " + acn);
        List<String> acnLs = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            acnLs.add(list.get(i).ACN);
        }
        String formattedString = acnLs.stream()
                .map(s -> "'" + s + "'")
                .collect(Collectors.joining(",", "(", ")"));

        ArrayList<AircraftBean> acnList = new ArrayList<AircraftBean>();

        PreparedStatement statement = null;
        ResultSet rs = null;

        Connection conn = null;
        try {

            try {
                conn = rampviewDataSourceConfig.rampviewDataSource().getConnection();

            } catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }

            if (conn != null) {

                String query = SELECT_ALL_FLIGHT_STATUS + "AND a.acn_cd in " + formattedString;

                if (!acn.equals(ALL_ACN)) {
                    logger.info("Single acn. Appending query");
                    query += SINGLE_ACN_ONLY;
                }

                statement = conn.prepareStatement(query);

                if (!acn.equals(ALL_ACN)) {
                    logger.info("Single ACN: " + acn + " setting parameter in query");
                    statement.setString(1, acn);
                }
                statement.setFetchSize(1000);
                rs = statement.executeQuery();
                logger.info("Statement Executed for SELECT ALL FLIGHT STATUS FROM RV FOR ACN: " + acn);
                while (rs.next()) {

                    AircraftBean ac = new AircraftBean();

                    // populate the common fields first
                    ac.setAcn(rs.getString(ACN_CD));
                    ac.setRegistrationNumber(rs.getString(AIRCRAFT_REG));
                    ac.setCatStatus(rs.getString(CAT_STATUS_CD));
                    setGroundDateTime(ac, rs.getInt(GROUND_TIME));

                    if (rs.getString(LOCATION).equals("Departed")) {
                        // if departed, populate certain fields
                        ac.setInTransit("Y");
                        ac.setInboundFlight(rs.getString(CURR_FLT_NBR));
                        ac.setInboundFlightDate(rs.getString(CURR_FLT_DT));
                        ac.setInboundFlightLeg(getFormattedFlightLeg(rs.getString(CURR_FLT_LEG)));
                        ac.setInboundFlightLegDate(rs.getString(CURR_FLT_LEG_DT));
                        ac.setInboundOrigination(rs.getString(CURR_ORIGIN));
                        ac.setInboundOutTime(rs.getString(CURR_OUT_DTM));
                        ac.setInboundDestination(rs.getString(CURR_DEST));
                        setInboundArrivalDateTime(ac, rs.getString(NEXT_GROUND_START));

                        ac.setOutboundFlight(rs.getString(NEXT_FLT_NBR));
                        ac.setOutboundFlightDate(rs.getString(NEXT_FLT_DT));
                        ac.setOutboundFlightLeg(getFormattedFlightLeg(rs.getString(NEXT_FLT_LEG)));
                        ac.setOutboundFlightLegDate(rs.getString(NEXT_FLT_LEG_DT));
                        ac.setOutboundOrigination(rs.getString(NEXT_ORIGIN));
                        ac.setOutboundOutTime(rs.getString(NEXT_GROUND_END));
                        ac.setOutboundDestination(rs.getString(NEXT_DEST));
                        setOutboundArrivalDateTime(ac, rs.getString(NEXT_IN_DTM));

                    } else {
                        // if not departed, use the other fields

                        ac.setInTransit(" ");
                        ac.setCurrentStation(rs.getString(CURR_STATION_CD));
                        logger.info("Current Station: " + ac.getCurrentStation());
                        ac.setInboundFlight(rs.getString(PREV_FLT_NBR));
                        ac.setInboundFlightDate(rs.getString(PREV_FLT_DT));
                        ac.setInboundFlightLeg(getFormattedFlightLeg(rs.getString(PREV_FLT_LEG)));
                        ac.setInboundFlightLegDate(rs.getString(PREV_FLT_LEG_DT));
                        ac.setInboundOrigination(rs.getString(PREV_ORIGIN));
                        ac.setInboundOutTime(rs.getString(PREV_OUT_DTM));
                        ac.setInboundDestination(rs.getString(PREV_DEST));
                        setInboundArrivalDateTime(ac, rs.getString(CURR_GROUND_START));
                        ac.setCurrentGate(rs.getString(CURR_GATE));
                        ac.setOutboundFlight(rs.getString(CURR_FLT_NBR));
                        ac.setOutboundFlightDate(rs.getString(CURR_FLT_DT));
                        ac.setOutboundFlightLeg(getFormattedFlightLeg(rs.getString(CURR_FLT_LEG)));
                        ac.setOutboundFlightLegDate(rs.getString(CURR_FLT_LEG_DT));
                        ac.setOutboundOrigination(rs.getString(CURR_ORIGIN));
                        ac.setOutboundOutTime(rs.getString(CURR_OUT_DTM));
                        ac.setOutboundDestination(rs.getString(CURR_DEST));
                        setOutboundArrivalDateTime(ac, rs.getString(NEXT_GROUND_START));

                    }

                    //logger.info("Adding AircraftBean: " + ac.toString());
                    acnList.add(ac);
                }
            } else {
                logger.error("DATABASE CONNECTION WAS NULL");
                throw new SQLException("DATABASE CONNECTION WAS NULL");
            }
        } finally {
            try {
                if (rs != null)
                    rs.close();
                if (statement != null)
                    statement.close();
                if (conn != null)
                    conn.close();
            } catch (Exception f) {
                logger.warn(
                        " ERROR Cancel Event cancelEvent()  close connection>>"
                                + f.getMessage());
            }
        }


        return acnList;
    }

    public AircraftBean getAircraftRecordsFromRampview(String acn) throws SQLException {

        logger.info("Retrieving Aircraft from RAMPVIEW");
        logger.info("ACN: " + acn);

        PreparedStatement statement = null;
        ResultSet rs = null;

        AircraftBean ac = new AircraftBean();
        Connection conn = null;
        try {

            try {
                conn = rampviewDataSourceConfig.rampviewDataSource().getConnection();

            } catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }

            if (conn != null) {

                String query = SELECT_ALL_FLIGHT_STATUS + "AND a.acn_cd =" + "'" + acn + "'";
                logger.info("Single acn. Appending query" + query);
                statement = conn.prepareStatement(query);
                statement.setFetchSize(1000);
                rs = statement.executeQuery();
                logger.info("Statement Executed for SELECT ALL FLIGHT STATUS FROM RV FOR ACN: " + acn);
                while (rs.next()) {

                    // populate the common fields first
                    ac.setAcn(rs.getString(ACN_CD));
                    logger.info("Retrieved Details of ACN:" + ac.getAcn());
                    ac.setRegistrationNumber(rs.getString(AIRCRAFT_REG));
                    ac.setCatStatus(rs.getString(CAT_STATUS_CD));
                    ac.setEquipType(rs.getString(EQUIPEMENT_TYPE));
                    setGroundDateTime(ac, rs.getInt(GROUND_TIME));
                    logger.info("Ground Time: " + ac.getTotalGroundTime() + "RegistrationNumber: " + ac.getRegistrationNumber());
                    logger.info("Location is Departed? : " + rs.getString(LOCATION));
                    if (rs.getString(LOCATION).equals("Departed")) {
                        // if departed, populate certain fields
                        ac.setInTransit("Y");
                        ac.setInboundFlight(rs.getString(CURR_FLT_NBR));
                        ac.setInboundFlightDate(rs.getString(CURR_FLT_DT));
                        ac.setInboundFlightLeg(getFormattedFlightLeg(rs.getString(CURR_FLT_LEG)));
                        ac.setInboundFlightLegDate(rs.getString(CURR_FLT_LEG_DT));
                        ac.setInboundOrigination(rs.getString(CURR_ORIGIN));
                        ac.setInboundOutTime(rs.getString(CURR_OUT_DTM));
                        ac.setInboundDestination(rs.getString(CURR_DEST));
                        setInboundArrivalDateTime(ac, rs.getString(NEXT_GROUND_START));

                        ac.setOutboundFlight(rs.getString(NEXT_FLT_NBR));
                        ac.setOutboundFlightDate(rs.getString(NEXT_FLT_DT));
                        ac.setOutboundFlightLeg(getFormattedFlightLeg(rs.getString(NEXT_FLT_LEG)));
                        ac.setOutboundFlightLegDate(rs.getString(NEXT_FLT_LEG_DT));
                        ac.setOutboundOrigination(rs.getString(NEXT_ORIGIN));
                        ac.setOutboundOutTime(rs.getString(NEXT_GROUND_END));
                        ac.setOutboundDestination(rs.getString(NEXT_DEST));
                        setOutboundArrivalDateTime(ac, rs.getString(NEXT_IN_DTM));

                    } else {
                        // if not departed, use the other fields

                        ac.setInTransit(" ");
                        ac.setCurrentStation(rs.getString(CURR_STATION_CD));
                        ac.setInboundFlight(rs.getString(PREV_FLT_NBR));
                        ac.setInboundFlightDate(rs.getString(PREV_FLT_DT));
                        ac.setInboundFlightLeg(getFormattedFlightLeg(rs.getString(PREV_FLT_LEG)));
                        ac.setInboundFlightLegDate(rs.getString(PREV_FLT_LEG_DT));
                        ac.setInboundOrigination(rs.getString(PREV_ORIGIN));
                        ac.setInboundOutTime(rs.getString(PREV_OUT_DTM));
                        ac.setInboundDestination(rs.getString(PREV_DEST));
                        setInboundArrivalDateTime(ac, rs.getString(CURR_GROUND_START));
                        ac.setCurrentGate(rs.getString(CURR_GATE));
                        ac.setOutboundFlight(rs.getString(CURR_FLT_NBR));
                        ac.setOutboundFlightDate(rs.getString(CURR_FLT_DT));
                        ac.setOutboundFlightLeg(getFormattedFlightLeg(rs.getString(CURR_FLT_LEG)));
                        ac.setOutboundFlightLegDate(rs.getString(CURR_FLT_LEG_DT));
                        ac.setOutboundOrigination(rs.getString(CURR_ORIGIN));
                        ac.setOutboundOutTime(rs.getString(CURR_OUT_DTM));
                        ac.setOutboundDestination(rs.getString(CURR_DEST));
                        setOutboundArrivalDateTime(ac, rs.getString(NEXT_GROUND_START));

                        ac.setEquipType(rs.getString(EQUIPEMENT_TYPE));


                    }

                    //logger.info("Adding AircraftBean: " + ac.toString());
                }
            } else {
                logger.error("DATABASE CONNECTION WAS NULL");
                throw new SQLException("DATABASE CONNECTION WAS NULL");
            }
        } finally {
            try {
                if (rs != null)
                    rs.close();
                if (statement != null)
                    statement.close();
                if (conn != null)
                    conn.close();
            } catch (Exception f) {
                logger.warn(
                        " ERROR Cancel Event cancelEvent()  close connection>>"
                                + f.getMessage());
            }
        }


        return ac;
    }

    public static void setGroundDateTime(AircraftBean ac, int totalGroundTimeInMinutes) {
        int groundDays = totalGroundTimeInMinutes / 24 / 60;
        int groundHours = (totalGroundTimeInMinutes - (groundDays * 24 * 60)) / 60;
        int groundMinutes = totalGroundTimeInMinutes % 60;
        String groundMinutesStr = new DecimalFormat("00").format(groundMinutes);

        String groundTimeStr = "??:??";
        if (groundHours > 0) {
            groundTimeStr = groundHours + ":" + groundMinutesStr;
        } else if (groundMinutes > 0) {
            groundTimeStr = ":" + groundMinutesStr;
        }

        ac.setTotalGroundDays(groundDays + "");
        ac.setTotalGroundTime(groundTimeStr);
    }

    public static void setInboundArrivalDateTime(AircraftBean ac, String dateTime) {
        if (ac != null && dateTime != null) {
            if (dateTime.length() == 13) {
                ac.setInboundArrivalDate(dateTime.substring(0, 8));
                ac.setInboundArrivalTime(dateTime.substring(9));

            }
        }
    }

    public static void setOutboundArrivalDateTime(AircraftBean ac, String dateTime) {
        if (ac != null && dateTime != null) {
            if (dateTime.length() == 13) {
                ac.setOutboundArrivalDate(dateTime.substring(0, 8));
                ac.setOutboundArrivalTime(dateTime.substring(9));

            }
        }
    }

    public static String getFormattedFlightLeg(String fltLeg) {
        if (fltLeg != null) {
            while (fltLeg.length() < 2) {
                fltLeg = "0" + fltLeg;
            }
        }
        return fltLeg;
    }

}
