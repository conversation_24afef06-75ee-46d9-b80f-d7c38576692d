package com.fedex.mets.entity.rampview;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;

@Entity
@Table(name="RV_AIRCRAFT")
@AllArgsConstructor(access = AccessLevel.PROTECTED)
@NoArgsConstructor
@Getter
@Setter
public class RvAircraft {

	@Id
	@Column(name="ACN_CD")
	private String acn;
	@Column(name="FLEET_SERIES")
	private String fleet;
	@Column(name="FUEL_ACTL_ON_BOARD_LBS_QTY")
	private int fuelOnBoard;

}
