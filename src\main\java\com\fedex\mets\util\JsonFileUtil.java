package com.fedex.mets.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * Utility class for JSON file operations
 */
@Component
public class JsonFileUtil {
    private static final Logger logger = LoggerFactory.getLogger(JsonFileUtil.class);
    private final ObjectMapper objectMapper;
    
    public JsonFileUtil(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }
    
    /**
     * Save an object to a JSON file
     * 
     * @param filePath the path to save the file
     * @param data the data to save
     * @return true if successful, false otherwise
     */
    public boolean saveToJsonFile(String filePath, Object data) {
        try {
            // Ensure directory exists
            File file = new File(filePath);
            file.getParentFile().mkdirs();
            
            // Write to file
            objectMapper.writerWithDefaultPrettyPrinter().writeValue(file, data);
            logger.info("Successfully saved data to JSON file: {}", filePath);
            return true;
        } catch (IOException e) {
            logger.error("Failed to save data to JSON file: {}", filePath, e);
            return false;
        }
    }
    
    /**
     * Read an object from a JSON file
     * 
     * @param filePath the path to read the file from
     * @param valueType the class of the object to read
     * @return the object read from the file, or null if an error occurred
     */
    public <T> T readFromJsonFile(String filePath, Class<T> valueType) {
        try {
            if (!Files.exists(Paths.get(filePath))) {
                logger.warn("JSON file does not exist: {}", filePath);
                return null;
            }
            
            return objectMapper.readValue(new File(filePath), valueType);
        } catch (IOException e) {
            logger.error("Failed to read data from JSON file: {}", filePath, e);
            return null;
        }
    }
}
