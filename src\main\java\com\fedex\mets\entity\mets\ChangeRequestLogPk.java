package com.fedex.mets.entity.mets;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Embeddable
public class ChangeRequestLogPk {

    @Column(name = "EVENT_ID")
    private Integer eventId;

    @Column(name = "CREATED_DT_TM")
    private Timestamp createdDtTm;

    @Column(name = "STATUS_CHANGED_DT_TM")
    private Timestamp statusChangedDtTm;
}
