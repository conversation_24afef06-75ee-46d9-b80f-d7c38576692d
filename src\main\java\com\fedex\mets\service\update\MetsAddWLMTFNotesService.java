package com.fedex.mets.service.update;

import com.fedex.mets.data.EventTfNotesDto;
import com.fedex.mets.data.MetsEventUpdateEntity;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class MetsAddWLMTFNotesService {

    public Map<String, Object> addWLMTFNotes(MetsEventUpdateEntity request, Map<String, Object> hashMap,
                                             String acn, EventTfNotesDto tfNotesData, String userId, String tokenId) throws Exception {

        return null;
    }

}
