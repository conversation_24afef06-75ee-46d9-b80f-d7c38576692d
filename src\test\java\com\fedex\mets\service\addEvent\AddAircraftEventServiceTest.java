package com.fedex.mets.service.addEvent;

import com.fedex.mets.data.WizardEventData;
import com.fedex.mets.entity.mets.Events;
import com.fedex.mets.repository.cache.SuperEquipmentRepository;
import com.fedex.mets.repository.mets.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.rmi.RemoteException;
import java.sql.Timestamp;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public class AddAircraftEventServiceTest {

    @Mock
    private SuperEquipmentRepository superEquipmentRepository;

    @Mock
    private EventsRepository eventsRepository;

    @Mock
    private ChangeRequestRepository changeRequestRepository;

    @Mock
    private EventDictRepository eventDictRepository;

    @Mock
    private GroupDictRepository groupDictRepository;

    @Mock
    private EventTypeHistoryRepository eventTypeHistoryRepository;

    @Mock
    private FlightDetailsService flightDetailsService;

    @Mock
    private EventFlightInfoRepository flightInfoRepository;

    @Mock
    private EventFlightDelaysRepository eventFlightDelaysRepository;

    @Mock
    private EventTfNotesRepository eventTfNotesRepository;

    @Mock
    private EventTimersRepository eventTimersRepository;

    @Mock
    private EventRepCatgRepository eventRepCatgRepository;

    @Mock
    private EventMaxiDiscRepository eventMaxiDiscRepository;

    @Mock
    private EventMaxiDwningItmRepository eventMaxiDwningItmRepository;

    @Mock
    private EventDoaRepository eventDoaRepository;

    @Mock
    private EventMsnsRepository eventMsnsRepository;

    @Mock
    private ChangeRequestLogRepository changeRequestLogRepository;

    @InjectMocks
    private AddAircraftEventService addAircraftEventService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testAddAircraftEvent_Success() throws Exception {
        WizardEventData wizardEventData = new WizardEventData();
        wizardEventData.setEventId(1);
        wizardEventData.setACN("ACN123");
        wizardEventData.setEventType("OOS");

        when(eventsRepository.getMaxEvent()).thenReturn(0);
        when(eventsRepository.save(any(Events.class))).thenReturn(new Events());

        WizardEventData result = addAircraftEventService.addAircraftEvent(wizardEventData);

        assertEquals(1, result.getEventId());
        verify(eventsRepository, times(1)).save(any(Events.class));
    }

    @Test
    public void testAddAircraftEvent_Exception() throws Exception {
        WizardEventData wizardEventData = new WizardEventData();

        doThrow(new RuntimeException("Internal server error")).when(eventsRepository).getMaxEvent();

        try {
            addAircraftEventService.addAircraftEvent(wizardEventData);
        } catch (RemoteException e) {
            assertEquals("Internal server error", e.getMessage());
        }
    }

    @Test
    public void testAddEvent_Success() throws Exception {
        WizardEventData wizardEventData = new WizardEventData();
        wizardEventData.setEventId(1);
        wizardEventData.setACN("ACN123");
        wizardEventData.setEventType("OOS");
        wizardEventData.setCreatedDateTime(new Timestamp(System.currentTimeMillis()));

        when(eventsRepository.getMaxEvent()).thenReturn(0);
        when(eventsRepository.save(any(Events.class))).thenReturn(new Events());

        WizardEventData result = addAircraftEventService.addEvent(wizardEventData);

        assertEquals(1, result.getEventId());
        verify(eventsRepository, times(1)).save(any(Events.class));
    }

    @Test
    public void testAddEvent_Exception() throws Exception {
        WizardEventData wizardEventData = new WizardEventData();

        doThrow(new RuntimeException("Internal server error")).when(eventsRepository).getMaxEvent();

        try {
            addAircraftEventService.addEvent(wizardEventData);
        } catch (Exception e) {
            assertEquals("Internal server error", e.getMessage());
        }
    }
}
