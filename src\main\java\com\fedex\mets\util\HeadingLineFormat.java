package com.fedex.mets.util;

public class HeadingLineFormat extends BaseLineFormat {
	
	static int line = 80;
	
	public HeadingLineFormat() {
	}
	
	protected void init(){
		sfd = new SentenceFormatDef("Heading", line, 0, 27);
		addType();
		addAcn();
		addAta();
		addNbr();
		addOpenDate();
		addOpenStation();
		addStatus();
		addReview();
	}
	
	private void addType() {
		FieldFormatDef ffd = new FieldFormatDef("EntryType",  6);
		sfd.getFields().add(ffd);
		
	}

	private void addAcn() {
		FieldFormatDef ffd = new FieldFormatDef("ACN",  8);
		sfd.getFields().add(ffd);
	}

	private void addAta() {
		FieldFormatDef ffd = new FieldFormatDef("ATA",  7);
		sfd.getFields().add(ffd);
	}


	private void addNbr() {
		FieldFormatDef ffd = new FieldFormatDef("DiscrpNbr",  9);
		sfd.getFields().add(ffd);
	}

	private void addOpenDate() {
		FieldFormatDef ffd = new FieldFormatDef("OpenDate",  9);
		sfd.getFields().add(ffd);
	}

	private void addOpenStation() {
		FieldFormatDef ffd = new FieldFormatDef("OpenStation",  5);
		sfd.getFields().add(ffd);
	}

	private void addStatus() {
		FieldFormatDef ffd = new FieldFormatDef("Status",  6);
		sfd.getFields().add(ffd);
	}

	private void addReview() {
		FieldFormatDef ffd = new FieldFormatDef("Review",  1);
		sfd.getFields().add(ffd);
	}

}

