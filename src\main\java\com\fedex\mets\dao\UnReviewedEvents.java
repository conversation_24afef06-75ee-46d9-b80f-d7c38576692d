package com.fedex.mets.dao;

import jakarta.persistence.Column;
import lombok.*;

import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class UnReviewedEvents {
    @Column(name = "EVENT_ID")
    private Integer eventId;

    @Column(name = "ACN")
    private String acn;

    @Column(name = "TYPE")
    private String type;


    @Column(name = "START_DT_TM")
    private Timestamp startDateTime;

    @Column(name = "STATION")
    private String station;

    @Column(name = "STATUS")
    private String status;

    @Column(name = "CUR_COMMENT")
    private String curComment;

    @Column(name = "REQUEST_STATUS", nullable = false)
    private String requestStatus;
}
