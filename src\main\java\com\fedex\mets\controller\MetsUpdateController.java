package com.fedex.mets.controller;

import com.fedex.mets.data.*;
import com.fedex.mets.entity.mets.EventMsns;
import com.fedex.mets.entity.mets.EventTimers;
import com.fedex.mets.service.update.*;
import com.fedex.mets.util.IServerConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


//@CrossOrigin(origins = "*")
@RestController
@RequestMapping("/api/mets/update")
@Tag(name = "Mets Update", description = "Endpoints for managing Mets updates")
public class MetsUpdateController {
    private static final Logger logger = LoggerFactory.getLogger(MetsUpdateController.class);

    @Autowired
    private MetsNIWTimerDetailsService metsniwtimerdetailservice;

    @Autowired
    private TFNotesUpdateService metstfnotesservice;

    @Autowired
    private EventDiscrepanciesUpdateService addeventdiscrepanciesservice;

    @Autowired
    private MetsSetFlightEticDetailService metssetflighteticdetailservice;

    @Autowired
    private ReportingCategoriesUpdateService metseditreportingcategoriesservice;

    @Autowired
    private MetsSetEventDOAInfoService metsseteventdoainfoservice;

    @Autowired
    private MetsUpdateDOAEventDetailService metsupdatedoaeventdetailservice;
    @Autowired
    private MetsUpdateEventDetailService metsupdateeventdetailservice;

    @Autowired
    private EventMsnsUpdateService eventMsnsUpdateService;

    Map<String, Object> responseHashMap = new HashMap<String, Object>();

    private String flag = "";
    private String userId="";
    private String tokenId="";
    private Boolean eventActive=true;

    @Operation(summary = "Update Tub File Notes", description = "Adding/Editing tubfile Notes in mets the system.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully updated tubFileNotes"),
            @ApiResponse(responseCode = "400", description = "Invalid input provided", content = @Content),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @PostMapping("/tfnotes")
    public ResponseEntity<Map<String, Object>> tfNotesUpdate(@RequestBody MetsEventUpdateEntity request) throws Exception {
        try{
            EventTfNotesDto tfNotesDto=null;
            tfNotesDto=request.getTf_notes_data();
            flag=request.getFlag();
            eventActive=request.isEvent_active();
            if (tfNotesDto != null) {
                logger.info("....calling the addTFNotes");
                responseHashMap = metstfnotesservice.TFNotes(flag, request, responseHashMap, tfNotesDto,eventActive);

            } else {
                logger.info("One of the required parameters is not passed to the Server.");
            }
            return ResponseEntity.ok(responseHashMap);
        }
        catch (IllegalArgumentException e) {
            logger.error("Invalid input for updating tub File Notes: {}. Error: {}", request, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error updating tub File notes:{}", e);
            return ResponseEntity.status(500).build();
        }

    }

    @Operation(summary = "Update event discrepanies", description = "Adding or deleting linked/downing discrepancies in mets the system.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully updated discrepancies"),
            @ApiResponse(responseCode = "400", description = "Invalid input provided", content = @Content),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @PostMapping("/discrepancy")
    public ResponseEntity<Map<String, Object>> discUpdate(@RequestBody MetsEventUpdateEntity request) throws Exception {
        try{
            //TODO
            String accessLevel="80";
            List<EventDiscrepancyListData> discrepancyListFromClient = new ArrayList<>();
            discrepancyListFromClient=request.getEvent_discrepancy_data();
            userId = request.getUser_id();
            tokenId="";
            flag=request.getFlag();
            eventActive=request.isEvent_active();
            logger.info("discrepancyListFromClient size ====> " + discrepancyListFromClient.size());
            if (discrepancyListFromClient != null && discrepancyListFromClient.size() > 0
                    && accessLevel != null) {

                logger.info("....calling the addEventDiscrepancies");
                responseHashMap = addeventdiscrepanciesservice.addEventDiscrepancies(request, responseHashMap, discrepancyListFromClient, accessLevel,userId,tokenId,eventActive);

            } else {
                logger.info( "One of the required parameters is not passed to the Server.");
            }
            return ResponseEntity.ok(responseHashMap);
        }
        catch (IllegalArgumentException e) {
            logger.error("Invalid input for updating Discrepancies: {}. Error: {}", request, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error updating Discrepancies:{}", e);
            return ResponseEntity.status(500).build();
        }

    }

    @Operation(summary = "Update reporting Categories", description = "Updating reporting Categories in mets the system.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully updated reporting categories"),
            @ApiResponse(responseCode = "400", description = "Invalid input provided", content = @Content),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @PostMapping("/rpt-catg")
    public ResponseEntity<Map<String, Object>> rptCatgUpdate(@RequestBody MetsEventUpdateEntity request) throws Exception {
        try{
            //TODO
            String accessLevel="80";
            List<ReportCategoriesKeyValueData> reportingCategoriesFromClient = new ArrayList<>();
            reportingCategoriesFromClient=request.getReport_categories_data();
            userId = request.getUser_id();
            tokenId="";
            eventActive=request.isEvent_active();
            Map<String, Object> responseHashMap = new HashMap<String, Object>();
            logger.info("reportingCategoriesFromClient size ====> " + reportingCategoriesFromClient.size());
            if (accessLevel != null && userId != null && tokenId != null) {

                if (reportingCategoriesFromClient.size() == 0) {
                    logger.info("Please have the Reporting Categories expanded in order to update the values.");
                } else {
                    logger.info("....calling the editReportingCategories");
                    responseHashMap = metseditreportingcategoriesservice.editReportingCategories(request, responseHashMap, reportingCategoriesFromClient, accessLevel, userId, tokenId,eventActive);
                }

            } else {
                logger.info( "One of the required parameters is not passed to the Server.");
            }
            return ResponseEntity.ok(responseHashMap);
        }
        catch (IllegalArgumentException e) {
            logger.error("Invalid input for updating Reporting Categories: {}. Error: {}", request, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error updating reporting categories:{}", e);
            return ResponseEntity.status(500).build();
        }
    }


    @Operation(summary = "Update NIW Timers", description = "Add/Edit/Delete NIW Timers in mets the system.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully updated NIW Timers"),
            @ApiResponse(responseCode = "400", description = "Invalid input provided", content = @Content),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @PostMapping("/niw-timers")
    public ResponseEntity<Map<String, Object>> niwTimersUpdate(@RequestBody MetsEventUpdateEntity request) throws Exception {
        try{
            EventTimers eventNIWTimerData = null;
            eventNIWTimerData = request.getNiw_timer_data();
            userId = request.getUser_id();
            String eventId = request.getEvent_id();
            String timerId = request.getTimer_id();
            flag=request.getFlag();
            String tokenId="";
            eventActive=request.isEvent_active();
            if (flag.equalsIgnoreCase(IServerConstants.EDIT)) {

                if (eventNIWTimerData != null && userId != null && tokenId != null) {

                    logger.info("....calling the editNIWTimerDetails");
                    responseHashMap = metsniwtimerdetailservice.editNIWTimerDetails(request, responseHashMap, eventNIWTimerData,eventActive);

                } else {
                    logger.info("One of the required parameters is not passed to the Server.");
                }

            } else if (flag.equalsIgnoreCase(IServerConstants.DELETE)) {
                if (eventNIWTimerData != null && userId != null && tokenId != null) {

                    logger.info("....calling the deleteNIWTimerDetails");
                    responseHashMap = metsniwtimerdetailservice.deleteNIWTimerDetails(request, responseHashMap, eventNIWTimerData, userId, tokenId,eventActive);

                } else {
                    logger.info( "One of the required parameters is not passed to the Server.");
                }

            } else if (flag.equalsIgnoreCase(IServerConstants.ADD)) {

                if (eventNIWTimerData != null && userId != null && tokenId != null) {

                    logger.info("....calling the addNIWTimerDetails");
                    responseHashMap = metsniwtimerdetailservice.addNIWTimerDetails(request, responseHashMap, eventNIWTimerData,eventActive);

                } else {
                    logger.info( "One of the required parameters is not passed to the Server.");
                }

            } else {

                if (eventId != null && flag != null && timerId != null && userId != null && tokenId != null) {

                    logger.info("....calling the setNIWTimerDetails");
                    responseHashMap = metsniwtimerdetailservice.setNIWTimerDetails(request, responseHashMap, eventId, timerId, flag,eventActive);

                } else {
                    logger.info( "One of the required parameters is not passed to the Server.");
                }

            }
            return ResponseEntity.ok(responseHashMap);
        }
        catch (IllegalArgumentException e) {
            logger.error("Invalid input for updating Reporting Categories: {}. Error: {}", request, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error updating reporting categories:{}", e);
            return ResponseEntity.status(500).build();
        }
    }

    @Operation(summary = "Update Event Detais", description = "Updating all event details in mets the system.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully updated event details"),
            @ApiResponse(responseCode = "400", description = "Invalid input provided", content = @Content),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @PostMapping("/event_detail")
    public ResponseEntity<Map<String, Object>> eventDetail(@RequestBody DetailViewDataEntity request) throws Exception {
        try{
            DetailViewDataEntity detailViewData = null;
            detailViewData = request;
            String accessLevel="80";
            //TODO need to update the DetailViewDataEntity
            Boolean commentUpdated  = true;
            userId="49941";

            if (detailViewData != null && accessLevel != null
                    && userId != null && tokenId != null) {

                if (detailViewData.getEventType() != null && detailViewData.getEventType().equalsIgnoreCase("DOA")) {

                    logger.info("....calling the updateDOAEventDetail");
                    DOADataEntity eventDOAData = detailViewData.getDoaData();
                    responseHashMap = metsupdatedoaeventdetailservice.updateDOAEventDetail( responseHashMap, detailViewData, eventDOAData, commentUpdated, accessLevel, userId, tokenId);

                } else if (detailViewData.getEventType() != null) {

                    logger.info("....calling the updateEventDetail");
                    responseHashMap = metsupdateeventdetailservice.updateEventDetail(responseHashMap, detailViewData, accessLevel, userId, tokenId);

                } else {
                    logger.info("....Detail View Data is not passed to the Server.");
                }

            } else {
                logger.info("One of the required parameters is not passed to the Server.");
            }
            return ResponseEntity.ok(responseHashMap);
        }
        catch (IllegalArgumentException e) {
            logger.error("Invalid input for updating event details: {}. Error: {}", request, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error updating event details:{}", e);
            return ResponseEntity.status(500).build();
        }
    }

    @Operation(summary = "Update Event DOA", description = "Updating event doa details in mets the system.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully updated event doa details."),
            @ApiResponse(responseCode = "400", description = "Invalid input provided", content = @Content),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @PostMapping("/event_Doa")
    public ResponseEntity<Map<String, Object>> doaUpdate(@RequestBody MetsEventUpdateEntity request) throws Exception {
        DOADataEntity doaData = null;
        doaData = request.getEvent_doa_data();
        flag=request.getFlag();
        userId=request.getUser_id();
        try{
            if (flag != null && doaData != null && userId != null && tokenId != null) {
                logger.info("....calling the setEventDOAInfo");
                responseHashMap = metsseteventdoainfoservice.setEventDOAInfo(request, responseHashMap, doaData, flag,userId, eventActive);

            } else {
                logger.info( "One of the required parameters is not passed to the Server.");
            }
            return ResponseEntity.ok(responseHashMap);
        }
        catch (IllegalArgumentException e) {
            logger.error("Invalid input for updating event doa info: {}. Error: {}", request, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error updating event doa info:{}", e);
            return ResponseEntity.status(500).build();
        }
    }

    @Operation(summary = "Update Event Flight/Etic", description = "Updating event FLIGHT/ETIC  details in mets the system.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully updated flight/etic  details."),
            @ApiResponse(responseCode = "400", description = "Invalid input provided", content = @Content),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @PostMapping("/flt-etic")
    public ResponseEntity<Map<String, Object>> fltEticUpdate(@RequestBody MetsEventUpdateEntity request) throws Exception {
        EventFlightEticDataEntity eventFlightEticData = null;
        eventFlightEticData = request.getFlight_etic_data();
        flag=request.getFlag();
        userId=request.getUser_id();
        try{
            if (eventFlightEticData != null && flag != null
                    && userId != null && tokenId != null) {

                logger.info("....calling the SetFlightEticDetail");
                responseHashMap = metssetflighteticdetailservice.setFlightEticDetail(request, responseHashMap, eventFlightEticData, flag, userId, tokenId, eventActive);
            } else {
                logger.info("One of the required parameters is not passed to the Server.");
            }
            return ResponseEntity.ok(responseHashMap);
        }
        catch (IllegalArgumentException e) {
            logger.error("Invalid input for updating event flt/etic info: {}. Error: {}", request, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error updating event flt/etic info:{}", e);
            return ResponseEntity.status(500).build();
        }
    }

    @Operation(summary = "Update Event Msns", description = "Updating event msn details in mets the system.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully updated msn  details."),
            @ApiResponse(responseCode = "400", description = "Invalid input provided", content = @Content),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @PostMapping("/msns")
    public ResponseEntity<Map<String, Object>> msnUpdate(@RequestBody MetsEventUpdateEntity request) throws Exception {
        List<EventMsns> eventMsns = null;
        eventMsns = request.getEvent_msns_data();
        flag=request.getFlag();
        userId=request.getUser_id();
        Map<String, Object> responseHashMap = new HashMap<String, Object>();
        try{
            if (eventMsns != null && flag != null
                    && userId != null) {

                logger.info("....calling the UPDATE msns");
                responseHashMap = eventMsnsUpdateService.updateMsns(request, responseHashMap, eventMsns);
            } else {
                logger.info("One of the required parameters is not passed to the Server.");
            }
            return ResponseEntity.ok(responseHashMap);
        }
        catch (IllegalArgumentException e) {
            logger.error("Invalid input for updating event msns info: {}. Error: {}", request, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error updating event flt/etic info:{}", e);
            return ResponseEntity.status(500).build();
        }
    }

}
