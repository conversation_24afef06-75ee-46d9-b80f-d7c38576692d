package com.fedex.mets.service.retrieval;

import com.fedex.mets.repository.rampview.RvRegionsRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

public class MOCCRegionServiceTest {

    @Mock
    private RvRegionsRepository rvRegionsRepository;

    @InjectMocks
    private MOCCRegionService moccRegionService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }
        @Test
        public void testGetMOCCRegions_Success() throws Exception {
            List<String> regions = new ArrayList<>();
            regions.add("Region1");
            regions.add("Region2");

            when(rvRegionsRepository.getRegions()).thenReturn(regions);

            List<String> result = moccRegionService.getMOCCRegions();

            assertEquals(2, result.size());
            assertEquals("Region1", result.get(0));
            assertEquals("Region2", result.get(1));
        }

        @Test
        public void testGetMOCCRegions_Exception() {
            doThrow(new RuntimeException("Internal server error")).when(rvRegionsRepository).getRegions();

            try {
                moccRegionService.getMOCCRegions();
            } catch (Exception e) {
                assertEquals("Problem finding the Regions list Internal server error", e.getMessage());
            }
        }

        @Test
        public void testGetMOCCRegionStations_Success() throws Exception {
            String regionDescription = "Region1";
            List<String> stations = new ArrayList<>();
            stations.add("Station1");
            stations.add("Station2");

            when(rvRegionsRepository.getRegionStations(regionDescription)).thenReturn(stations);

            List<String> result = moccRegionService.getMOCCRegionStations(regionDescription);

            assertEquals(2, result.size());
            assertEquals("Station1", result.get(0));
            assertEquals("Station2", result.get(1));
        }

        @Test
        public void testGetMOCCRegionStations_Exception() {
            String regionDescription = "Region1";

            doThrow(new RuntimeException("Internal server error")).when(rvRegionsRepository).getRegionStations(regionDescription);

            try {
                moccRegionService.getMOCCRegionStations(regionDescription);
            } catch (Exception e) {
                assertEquals("Problem finding the Regions Stations list Internal server error", e.getMessage());
            }
        }

    }
