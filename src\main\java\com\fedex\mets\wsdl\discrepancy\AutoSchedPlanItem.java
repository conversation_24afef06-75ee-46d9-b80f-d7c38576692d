
package com.fedex.mets.wsdl.discrepancy;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for autoSchedPlanItem complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="autoSchedPlanItem">
 *   &lt;complexContent>
 *     &lt;extension base="{http:///www.fedex.com/airops/schemas/Mach}PlannedItemAsgmtType">
 *       &lt;sequence>
 *         &lt;element name="validAssignment" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="validStartTm" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="currentGroundTmWarning" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "autoSchedPlanItem", propOrder = {
    "validAssignment",
    "validStartTm",
    "currentGroundTmWarning"
})
public class AutoSchedPlanItem
    extends PlannedItemAsgmtType
{

    protected boolean validAssignment;
    protected boolean validStartTm;
    protected boolean currentGroundTmWarning;

    /**
     * Gets the value of the validAssignment property.
     * 
     */
    public boolean isValidAssignment() {
        return validAssignment;
    }

    /**
     * Sets the value of the validAssignment property.
     * 
     */
    public void setValidAssignment(boolean value) {
        this.validAssignment = value;
    }

    /**
     * Gets the value of the validStartTm property.
     * 
     */
    public boolean isValidStartTm() {
        return validStartTm;
    }

    /**
     * Sets the value of the validStartTm property.
     * 
     */
    public void setValidStartTm(boolean value) {
        this.validStartTm = value;
    }

    /**
     * Gets the value of the currentGroundTmWarning property.
     * 
     */
    public boolean isCurrentGroundTmWarning() {
        return currentGroundTmWarning;
    }

    /**
     * Sets the value of the currentGroundTmWarning property.
     * 
     */
    public void setCurrentGroundTmWarning(boolean value) {
        this.currentGroundTmWarning = value;
    }

}
