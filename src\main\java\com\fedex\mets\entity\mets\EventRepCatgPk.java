package com.fedex.mets.entity.mets;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Embeddable
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EventRepCatgPk implements Serializable {

    @Column(name = "EVENT_ID", nullable = false)
    private Integer eventId;

    @Column(name = "LEVEL_1_ID", nullable = false)
    private String level1Id;

    @Column(name = "LEVEL_2_ID", nullable = false)
    private String level2Id;
}
