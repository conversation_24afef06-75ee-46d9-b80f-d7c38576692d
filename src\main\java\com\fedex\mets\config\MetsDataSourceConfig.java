package com.fedex.mets.config;

import com.fedex.mets.util.DecryptionUtil;
import jakarta.persistence.EntityManagerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        basePackages = "com.fedex.mets.repository.mets", // Repositories for Mets
        entityManagerFactoryRef = "metsEntityManagerFactory",
        transactionManagerRef = "metsTransactionManager"
)
public class MetsDataSourceConfig {

    @Value("${spring.datasource.mets.password}")
    private String encryptedPassword;

    @Value("${spring.datasource.mets.jdbc-url}")
    private String url;

    @Value("${spring.datasource.mets.username}")
    private String username;

    @Value("${spring.datasource.mets.driver-class-name}")
    private String driverClassName;

    @Bean(name = "metsDataSource")
    @Primary
    public DataSource metsDataSource() throws Exception {
        String decryptedPassword = DecryptionUtil.decrypt(encryptedPassword, "z76yf8ScxNFLZMbxC1YVRQ==");
        return DataSourceBuilder.create().username(username).url(url).driverClassName(driverClassName)
        .password(decryptedPassword).build();
    }

    @Bean(name = "metsEntityManagerFactory")
    @Primary
    public LocalContainerEntityManagerFactoryBean metsEntityManagerFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("metsDataSource") DataSource dataSource) {
        return builder
                .dataSource(dataSource)
                .packages("com.fedex.mets.entity.mets") // Entities for Mets
                .persistenceUnit("mets") // Persistence unit name for Mets
                .build();
    }

    @Bean(name = "metsTransactionManager")
    @Primary
    public PlatformTransactionManager metsTransactionManager(
            @Qualifier("metsEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        return new JpaTransactionManager(entityManagerFactory);
    }
}
