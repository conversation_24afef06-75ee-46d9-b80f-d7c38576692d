
package com.fedex.mets.wsdl.discrepancy;

import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;extension base="{http://www.fedex.com/airops/schemas/Common.xsd}GenericResponse">
 *       &lt;sequence>
 *         &lt;element name="dscrpMaintUpdates" type="{http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd}AcnDiscrepancyMaintUpdt" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="attachedLogpageNbrs" type="{http:///www.fedex.com/airops/schemas/Mach}DscrpAttachedLogpageNbrs" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "dscrpMaintUpdates",
    "attachedLogpageNbrs"
})
@XmlRootElement(name = "getAllMaintUpdateResponse",namespace = "http:///www.fedex.com/airops/schemas/Mach")
public class GetAllMaintUpdateResponse
    extends GenericResponse
{

    protected List<AcnDiscrepancyMaintUpdt> dscrpMaintUpdates;
    protected List<DscrpAttachedLogpageNbrs> attachedLogpageNbrs;

    /**
     * Gets the value of the dscrpMaintUpdates property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the dscrpMaintUpdates property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getDscrpMaintUpdates().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link AcnDiscrepancyMaintUpdt }
     * 
     * 
     */
    public List<AcnDiscrepancyMaintUpdt> getDscrpMaintUpdates() {
        if (dscrpMaintUpdates == null) {
            dscrpMaintUpdates = new ArrayList<AcnDiscrepancyMaintUpdt>();
        }
        return this.dscrpMaintUpdates;
    }

    /**
     * Gets the value of the attachedLogpageNbrs property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the attachedLogpageNbrs property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getAttachedLogpageNbrs().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link DscrpAttachedLogpageNbrs }
     * 
     * 
     */
    public List<DscrpAttachedLogpageNbrs> getAttachedLogpageNbrs() {
        if (attachedLogpageNbrs == null) {
            attachedLogpageNbrs = new ArrayList<DscrpAttachedLogpageNbrs>();
        }
        return this.attachedLogpageNbrs;
    }

}
