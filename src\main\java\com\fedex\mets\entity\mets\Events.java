package com.fedex.mets.entity.mets;

import jakarta.persistence.*;
import lombok.*;

import java.sql.Clob;
import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Entity
@Table(name="EVENTS")
public class Events {

        @Id
        @Column(name = "EVENT_ID")
        private Integer eventId;

        @Column(name = "TYPE")
        private String type;

        @Column(name = "START_DT_TM")
        private Timestamp startDateTime;

        @Column(name = "END_DT_TM")
        private Timestamp endDateTime;

        @Column(name = "ACN")
        private String acn;

        @Column(name = "FLEET_DESC")
        private String fleetDesc;

        @Column(name = "STATION")
        private String station;

        @Column(name = "STATUS")
        private String status;

        @Column(name = "ETIC_DT_TM")
        private Timestamp eticDateTime;

        @Column(name = "ETIC_TEXT")
        private String eticText;

        @Column(name = "ORIG_COMMENT")
        private String origComment;

        @Column(name = "AC_OWNER_GROUP_ID")
        private String acOwnerGroupId;

        @Column(name = "CUR_COMMENT")
        private String curComment;

        @Column(name = "LAST_UPDATE_DT_TM")
        private Timestamp lastUpdateDateTime;

        @Column(name = "LAST_UPDATE_BY")
        private String lastUpdatedBy;

        @Column(name = "CREATED_DT_TM")
        private Timestamp createdDateTime;

        @Column(name = "CREATED_BY")
        private String createdBy;

        @Column(name = "CREATED_BY_GROUP_ID")
        private String createdByGroupId;

        @Column(name = "ETIC_INITIAL")
        private Timestamp eticInitial;

        @Column(name = "ETIC_NUM")
        private int eticNumber;

        @Column(name = "CANCELLED")
        private String cancelled;

        @Column(name = "ACTIVE_EVENT")
        private String activeEvent;

        @Column(name = "EVENT_OWNER_GROUP_ID")
        private String eventOwnerGroupId;

        @Column(name = "PRIMARY_CONTACT")
        private String primaryContact;

        @Lob
        @Column(name = "MGR_NOTES")
        private Clob mgrNotes;

//        @Column(name = "RES_MGR_ID")
//        private String resMgrId;

        @Column(name="FEDEXID_NBR")
        private String fedexIdNumber;

        @Column(name = "OST")
        private String ost;

        @Column(name = "ETIC_RSN_CD")
        private String eticRsnCd;

        @Column(name = "ETIC_RSN_COMMENT")
        private String eticRsnComment;

        @Column(name = "MEM_DESK_CONTACT")
        private String memDeskContact;

}
