package com.fedex.mets.repository.mets;

import com.fedex.mets.entity.mets.EventDoa;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EventDoaRepository extends JpaRepository<EventDoa, Integer> {

    @Query(value = " select * from EVENT_DOA where EVENT_ID=:eventId", nativeQuery = true)
    public EventDoa getEventDOAInfo(@Param("eventId") int eventId);

}
