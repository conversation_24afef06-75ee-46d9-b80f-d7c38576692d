package com.fedex.mets.service.retrieval;

import com.fedex.mets.data.DOAData;
import com.fedex.mets.data.DOADiscrepancyData;
import com.fedex.mets.data.EventDiscrepancyList;
import com.fedex.mets.dto.MetsRequest;
import com.fedex.mets.entity.mets.EventDoa;
import com.fedex.mets.entity.mets.EventMaxiDisc;
import com.fedex.mets.entity.mets.Events;
import com.fedex.mets.repository.mets.EventDoaRepository;
import com.fedex.mets.repository.mets.EventMaxiDiscRepository;
import com.fedex.mets.repository.mets.EventsRepository;
import com.fedex.mets.util.IServerConstants;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Service
@RequiredArgsConstructor
public class EventDoaService {
    private static final Logger logger = LoggerFactory.getLogger(EventDoaService.class);

    @Autowired
    private EventDoaRepository eventDoaRepository;

    @Autowired
    private EventDiscrepanciesService eventDiscrepanciesService;

    @Autowired
    private EventsRepository eventsRepository;

    @Autowired
    private EventMaxiDiscRepository eventMaxiDiscRepository;

    /**
     * Private method to get the DOA DETAILS for a particular event.
     *
     * @return hashTable containing DOADetail Object.
     * @params EventId.
     */
    public HashMap getEventDOAInfo(
            MetsRequest request,
            HashMap hashmap,
            String eventId,
            String userId) {
        DOAData elements = new DOAData();

        try {
            logger.info("==> before calling the getEventDOAInfo()");
            elements = getEventDOAInfo(eventId, userId);

            hashmap.put(IServerConstants.DOA_DETAILS, elements);
        } catch (Exception e) {
            logger.warn(
                    "ERROR MetsRetrieval Servlet getEventDOAInfo() remote exception >> "
                            + e.getMessage());
            hashmap.put(IServerConstants.ERROR, e.getMessage());
        }
        return hashmap;
    }


    /**
     * The following getEventDOAInfo() is used to retreive the Event DOA data for a particular event.
     *
     * @return List of EventDOA data.
     * @params String eventId , int sequenceNumber
     */
    public DOAData getEventDOAInfo(String eventId, String userId) {
        DOAData data = new DOAData();
        String acn = "",
                ata = "";
        try {

            EventDoa doaElements = eventDoaRepository.getEventDOAInfo(Integer.parseInt(eventId));


            data.setEventId(doaElements.getEventId());
            data.setFlightNumber(doaElements.getDoaFleetNumber());
            data.setFlightDate(String.valueOf(doaElements.getDoaFleetDate()));
            data.setFlightLegNumber(doaElements.getDoaFleetLeg());
            data.setAdditionalDescription(doaElements.getDoaFleetComments());

            String checkFlight = doaElements.getCheckFleet();
            if (checkFlight != null && checkFlight.equalsIgnoreCase("Y")) {
                data.setCheckFlightRequrired(true);
            }
            data.setClosedBy(doaElements.getClosedBy());
            data.setClosedAt(String.valueOf(doaElements.getClosedDtTm()));

            String maintCrew = doaElements.getMaintCW();
            if (maintCrew != null && maintCrew.equalsIgnoreCase("Y")) {
                data.setMaintenanceCrew(true);
            }

            data.setLastUpdated(String.valueOf(doaElements.getLastUpdtDtTm()));

            data.setDestination(doaElements.getDoaFltDest());
            data.setEstimatedTimeOfArrival(doaElements.getDoaFltDest());

        } catch (Exception ee) {
            logger.warn("ERROR getEventDOAInfo() >> " + ee.getMessage());
        }

        try {

            Events eventElements = eventsRepository.getEventsByEventId(data.getEventId());


            data.setDoaOriginator(eventElements.getCreatedBy());
            data.setCreatedAt(String.valueOf(eventElements.getCreatedDateTime()));

            String eventType = eventElements.getType();
            if (eventType.equalsIgnoreCase("DOA")) {
                data.setComment(eventElements.getCurComment());
            } else if (eventType.equalsIgnoreCase("OOS")) {
                data.setComment(eventElements.getOrigComment());
            }

            acn = eventElements.getAcn();


            List<DOADiscrepancyData> discValues = new ArrayList<>();
            // List to hold the discrepancy values.

            List<EventMaxiDisc> maxiElement = eventMaxiDiscRepository.getEventMaxiDiscInfo(data.getEventId());
            DOADiscrepancyData discData = new DOADiscrepancyData();
            if (maxiElement.size()>0) {
                for (EventMaxiDisc maxi:maxiElement)
                {
                    ata = maxi.getEventMaxiDisckPk().getAta();
                    String strATA = "";
                    if (ata.length() == 4)
                        strATA = ata.substring(0, 2) + "-" + ata.substring(2, 4);

                    discData.setAta(strATA);
                    discData.setDiscrepancy(maxi.getEventMaxiDisckPk().getDiscNum());
                    discData.setEventType(maxi.getType());

                    List<EventDiscrepancyList> discs = eventDiscrepanciesService
                            .getDOAAircraftDscrps(acn, ata,
                                    discData.getDiscrepancy(), userId);
                    if (discs.size() == 1) {
                        discData.setDiscrepancyText(discs.get(0).getText());
                    } else {
                        String text1 = "Data not available";
                        String[] omdisDiscText = {text1};
                        discData.setDiscrepancyText(omdisDiscText);
                    }

                    discValues.add(discData);
                }
            }
            data.setDiscVector(discValues);

        } catch (Exception e) {
            logger.warn("ERROR getEventDOAInfo() >> " + e.getMessage());
            e.printStackTrace();
        }
        return data;
    }


}
