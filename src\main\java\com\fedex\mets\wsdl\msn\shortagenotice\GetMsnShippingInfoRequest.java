
package com.fedex.mets.wsdl.msn.shortagenotice;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="session" type="{http://fedex.com/airops/maxi/services/jaxws}sessionType"/>
 *         &lt;element name="header" type="{http://fedex.com/airops/maxi/services/jaxws}HeaderType"/>
 *         &lt;element name="msn" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "session",
    "header",
    "msn"
})
@XmlRootElement(name = "getMsnShippingInfoRequest", namespace = "http://fedex.com/airops/maxi/services/jaxws/shortagenotice")
public class GetMsnShippingInfoRequest {

    @XmlElement(namespace = "http://fedex.com/airops/maxi/services/jaxws/shortagenotice", required = true)
    protected SessionType session;
    @XmlElement(namespace = "http://fedex.com/airops/maxi/services/jaxws/shortagenotice", required = true)
    protected HeaderType header;
    @XmlElement(namespace = "http://fedex.com/airops/maxi/services/jaxws/shortagenotice", required = true)
    protected String msn;

    /**
     * Gets the value of the session property.
     * 
     * @return
     *     possible object is
     *     {@link SessionType }
     *     
     */
    public SessionType getSession() {
        return session;
    }

    /**
     * Sets the value of the session property.
     * 
     * @param value
     *     allowed object is
     *     {@link SessionType }
     *     
     */
    public void setSession(SessionType value) {
        this.session = value;
    }

    /**
     * Gets the value of the header property.
     * 
     * @return
     *     possible object is
     *     {@link HeaderType }
     *     
     */
    public HeaderType getHeader() {
        return header;
    }

    /**
     * Sets the value of the header property.
     * 
     * @param value
     *     allowed object is
     *     {@link HeaderType }
     *     
     */
    public void setHeader(HeaderType value) {
        this.header = value;
    }

    /**
     * Gets the value of the msn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMsn() {
        return msn;
    }

    /**
     * Sets the value of the msn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMsn(String value) {
        this.msn = value;
    }

}
