package com.fedex.mets.service.addEvent;

import com.fedex.mets.dao.ActiveEvents;
import com.fedex.mets.dao.AircraftBean;
import com.fedex.mets.dao.UnReviewedEvents;
import com.fedex.mets.data.ActiveEventResults;
import com.fedex.mets.data.ListViewData;
import com.fedex.mets.repository.mets.EventsRepository;
import com.fedex.mets.util.RvDBHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.rmi.RemoteException;
import java.util.ArrayList;
import java.util.List;

@Service
public class ActiveEventService {

    private static final Logger logger = LoggerFactory.getLogger(ActiveEventService.class);

    @Autowired
    private EventsRepository eventsRepository;

    @Autowired
    RvDBHelper rvDBHelper;

    /**
     * The following findActiveEvents() is used to find if there are any active Events for a particular ACN.
     * @params String acn.
     * @return List of Active Events Data Object.
     */
    public ActiveEventResults findActiveEvents(String acn) throws RemoteException {
        List<ListViewData> activeEventsList = new ArrayList<>(),
                overridableEventsList = new ArrayList<>();
        ActiveEventResults  resultList = new ActiveEventResults();
        List<ActiveEvents> activeList = new ArrayList<>();
        try {
            activeList = eventsRepository.findActiveEvents(acn);
            for(ActiveEvents eventListView : activeList) {
                ListViewData data = new ListViewData();

                data.setEventID(eventListView.getEventId());
                data.setACN(eventListView.getAcn());
                data.setType(eventListView.getType());
                data.setStartDateTime(String.valueOf(eventListView.getStartDateTime()));
                data.setStation(eventListView.getStation());
                data.setStatus(eventListView.getStatus());
                data.setCurComment((String) eventListView.getNewOldComment());

                if (data.getType() != null && data.getType().equals("NOTE")) {
                    try {
                        String strStation = "",
                                strCurrentStation = "",
                                strInTransit = "",
                                strInboundDestination = "";

                        logger.info("@@@@ Calling RvDBHelper for ACN: " + data.getACN());
                        AircraftBean acb = rvDBHelper.getAircraftRecordsFromRampview(data.getACN());
                        strCurrentStation = acb.getCurrentStation();
                        strInTransit = acb.getInTransit();
                        strInboundDestination = acb.getInboundDestination();

                        if ( strInTransit != null &&strInTransit.trim().length() > 0) {
                            if (strInTransit.equalsIgnoreCase("Y"))
                                strStation = strInboundDestination;
                        } else {
                            strStation = strCurrentStation;
                        }

                        data.setStation(strStation);
                    } catch (Exception e) {
                        logger.info(
                                "ERROR ADD Event findActiveEvents() e " + e.getMessage());
                    }
                }
                activeEventsList.add(data);
                String strStatus = eventListView.getStatus();
                String strRequestStatus= eventListView.getRequestStatus();
                data.setRequestStatus(strRequestStatus);
                data.setEndDateTime(String.valueOf(eventListView.getEndDateTime()));

                if (strStatus != null && strStatus.trim().equalsIgnoreCase("UP")) {
                    if (strRequestStatus != null
                            && strRequestStatus.trim().equalsIgnoreCase("S")) {
                        ListViewData overridableEventsData = new ListViewData();

                        overridableEventsData.setEventID(data.getEventID());
                        overridableEventsData.setACN(data.getACN());
                        overridableEventsData.setType(data.getType());
                        overridableEventsData.setStartDateTime(data.getStartDateTime());
                        overridableEventsData.setStation(data.getStation());
                        overridableEventsData.setStatus(data.getStatus());
                        overridableEventsData.setCurComment(data.getCurComment());

                        if (overridableEventsData.getType() != null
                                && overridableEventsData.getType().equals("NOTE")) {
                            try {
                                String strStation = "",
                                        strCurrentStation = "",
                                        strInTransit = "",
                                        strInboundDestination = "";

                                logger.info("@@@@ Calling RvDBHelper for ACN: " + data.getACN());
                                AircraftBean acb = rvDBHelper.getAircraftRecordsFromRampview(data.getACN());

                                strCurrentStation = acb.getCurrentStation();// rsCache.getString(1);
                                strInTransit = acb.getInTransit();// rsCache.getString(2);
                                strInboundDestination = acb.getInboundDestination();//.getString(3);


                                if (strInTransit != null && strInTransit.trim().length() > 0) {
                                    if (strInTransit.equalsIgnoreCase("Y"))
                                        strStation = strInboundDestination;
                                } else {
                                    strStation = strCurrentStation;
                                }

                                overridableEventsData.setStation(strStation);
                            } catch (Exception e) {
                                logger.warn(
                                        "ERROR ADD Event findActiveEvents() e 1" + e.getMessage());
                            }
                        }
                        overridableEventsList.add(overridableEventsData);
                    }
                }
            }
        } catch (Exception e) {
            logger.warn(
                    "ERROR ADD Event findActiveEvents() e 2" + e.getMessage());
        }
        resultList.setActiveEvents(activeEventsList);
        resultList.setOverridenEvents(overridableEventsList);

        return resultList;
    }

    /**
     * The following findUnReviewedEvents() is used to find if there are any closed OOS events requiring Duty Mgr review for a particular ACN.
     * @params String acn.
     * @return List of UnReviewed Events Data Object.
     */
    public List<ListViewData> findUnReviewedEvents(String acn) throws RemoteException {
        List<ListViewData> unReviewedEventsList = new ArrayList<>();
        try {
            List<UnReviewedEvents> unReviewedEvents = eventsRepository.findUnReviewedEvents(acn);

            for (UnReviewedEvents eventListView : unReviewedEvents){
                ListViewData data = new ListViewData();

                data.setEventID(eventListView.getEventId());
                data.setACN(eventListView.getAcn());
                data.setType(eventListView.getType());
                data.setStartDateTime(String.valueOf(eventListView.getStartDateTime()));
                data.setStation(eventListView.getStation());
                data.setStatus(eventListView.getStatus());
                data.setCurComment(eventListView.getCurComment());

                String strRequestStatus = eventListView.getRequestStatus();

                if (strRequestStatus != null && strRequestStatus.trim().length() > 0)
                    unReviewedEventsList.add(data);

            }
        } catch (Exception e) {
            logger.warn(
                    "ERROR ADD Event findUnReviewedEvents() e " + e.getMessage());
        }
        return unReviewedEventsList;
    }


}
