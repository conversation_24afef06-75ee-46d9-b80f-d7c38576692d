
package com.fedex.mets.wsdl.flightSearch;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;
import com.fedex.mets.wsdl.discrepancy.GenericRequest;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;extension base="{http://www.fedex.com/airops/schemas/Common.xsd}GenericRequest">
 *       &lt;sequence>
 *         &lt;element name="aircraftNbr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="getLastGroundLeg" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "aircraftNbr",
    "getLastGroundLeg"
})
@XmlRootElement(name = "getCurrentFlightLegRequest",namespace="http:///www.fedex.com/airops/schemas/Mach")
public class GetCurrentFlightLegRequest
    extends GenericRequest
{

    @XmlElement(required = true)
    protected String aircraftNbr;
    @XmlElement(defaultValue = "false")
    protected boolean getLastGroundLeg;

    /**
     * Gets the value of the aircraftNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAircraftNbr() {
        return aircraftNbr;
    }

    /**
     * Sets the value of the aircraftNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAircraftNbr(String value) {
        this.aircraftNbr = value;
    }

    /**
     * Gets the value of the getLastGroundLeg property.
     * 
     */
    public boolean isGetLastGroundLeg() {
        return getLastGroundLeg;
    }

    /**
     * Sets the value of the getLastGroundLeg property.
     * 
     */
    public void setGetLastGroundLeg(boolean value) {
        this.getLastGroundLeg = value;
    }

}
