package com.fedex.mets.repository.mets;

import com.fedex.mets.dao.EventListView;
import com.fedex.mets.dao.ReportingCategoryValues;
import com.fedex.mets.data.ReportCategoriesData;
import com.fedex.mets.entity.mets.RptCatgLevel1;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RptCatgLevel1Repository extends JpaRepository<RptCatgLevel1, String> {

    @Query("select new com.fedex.mets.dao.ReportingCategoryValues(a.description, a.level1Id," +
            " a.level1Name, a.listOrder," +
            " a.multiselect, a.addPrompt, a.closePrompt, a.reqAddPrompt,a.reqClosePrompt," +
            " b.desc,b.level2Name,b.listOrderL2, b.level2Id) \n" +
            " from RptCatgLevel1 a LEFT JOIN RptCatgLevel2 b ON a.level1Id = b.level1Id \n" +
            " LEFT JOIN RptCatg1Events c on a.level1Id= c.level1Id where a.groupId=:groupId \n" +
            " and a.activeCatg='Y' and b.activeCatg='Y' and c.type =:type \n" +
            " and c.flag = 'Y' order by a.listOrder, b.listOrderL2")
    public List<ReportingCategoryValues> getRepCategories(@Param("groupId") String groupId, @Param("type") String type);

    @Query("select new com.fedex.mets.dao.ReportingCategoryValues(distinct(b.level2Id) as distinctlevel2Id,a.description, a.level1Id," +
            " a.level1Name, a.listOrder," +
            " a.multiselect, a.addPrompt, a.closePrompt, a.reqAddPrompt,a.reqClosePrompt," +
            " b.desc,b.level2Name,b.listOrderL2, b.level2Id) \n" +
            " from RptCatgLevel1 a LEFT JOIN RptCatgLevel2 b ON a.level1Id = b.level1Id \n" +
            " LEFT JOIN RptCatg1Events c on a.level1Id= c.level1Id where \n" +
            " a.activeCatg='Y' and b.activeCatg='Y' and c.type =:type \n" +
            " and c.flag = 'Y' order by a.listOrder, b.listOrderL2")
    public List<ReportingCategoryValues> getRepCategories(@Param("type") String type);

}
