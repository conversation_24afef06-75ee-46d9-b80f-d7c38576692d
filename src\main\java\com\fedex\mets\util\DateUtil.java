package com.fedex.mets.util;

import javax.xml.datatype.XMLGregorianCalendar;
import java.util.Date;

public class DateUtil {
  
  private static final DateFormatters dfGMT = DateFormatters.getInstanceGMT();

  public static final String formatGMT(XMLGregorianCalendar xmlDate, String pattern) throws IllegalArgumentException {
    if (xmlDate == null)
      return ""; 
    return formatGMT(xmlDate.toGregorianCalendar().getTime(), pattern);
  }
  
  public static final String formatGMT(Date date, String pattern) throws IllegalArgumentException {
    return dfGMT.format(date, pattern, true);
  }

  public static final String formatddMMMyyGMT(XMLGregorianCalendar xmlDate) {
    if (xmlDate == null)
      return ""; 
    return formatddMMMyyGMT(xmlDate.toGregorianCalendar().getTime());
  }
  
  public static final String formatddMMMyyGMT(Date aDate) {
    if (aDate == null)
      return "";
    return dfGMT.formatddMMMyy(aDate);
  }
}
