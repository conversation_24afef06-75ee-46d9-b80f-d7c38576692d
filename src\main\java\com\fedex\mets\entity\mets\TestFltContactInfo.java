package com.fedex.mets.entity.mets;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "TEST_FLT_CONTACT_INFO")
public class TestFltContactInfo {

    @Id
    @Column(name = "EMAIL")
    private String email;

    @Column(name = "OFFICE_PHONE")
    private String officePhone;

    @Column(name = "AFTER_HOURS_PHONE")
    private String afterHoursPhone;
}