package com.fedex.mets.service.retrieval;

import com.fedex.mets.config.MachSoapClientConfig;
import com.fedex.mets.config.OktaTokenGenService;
import com.fedex.mets.dao.AcnCacheDetail;
import com.fedex.mets.util.JsonFileUtil;
import com.fedex.mets.wsdl.acnCache.CacheDataTypeEnum;
import com.fedex.mets.wsdl.acnCache.GetCacheDataResponse;
import com.fedex.mets.wsdl.discrepancy.AcnAircraftDetail;
import com.fedex.mets.wsdl.discrepancy.ResultType;
import com.fedex.mets.wsdl.discrepancy.SessionType;

import java.net.MalformedURLException;
import jakarta.xml.soap.SOAPException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.quality.Strictness;

import static org.mockito.Mockito.lenient;
import org.springframework.ws.client.core.WebServiceTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AcnCacheServiceTest {

    @Mock
    private MachSoapClientConfig machSoapClientConfig;

    @Mock
    private WebServiceTemplate webServiceTemplate;

    @Mock
    private OktaTokenGenService oktaTokenGenService;

    @Mock
    private JsonFileUtil jsonFileUtil;

    @InjectMocks
    private AcnCacheService acnCacheService;

    private GetCacheDataResponse mockResponse;
    private HashMap<String, List<?>> expectedCacheData;

    @BeforeEach
    void setUp() throws MalformedURLException, SOAPException {
        // Set up the WebServiceTemplate mock - use lenient to avoid unnecessary stubbing errors
        lenient().when(machSoapClientConfig.app1webServiceTemplate()).thenReturn(webServiceTemplate);
        // Mock the Okta token generation - use lenient to avoid unnecessary stubbing errors
        lenient().when(oktaTokenGenService.generateToken()).thenReturn("mock-token");

        mockResponse = new GetCacheDataResponse();

        ResultType resultType = new ResultType();
        resultType.setSuccessful(true);
        mockResponse.setStatus(resultType);

        List<AcnAircraftDetail> acnAircraftDetails = new ArrayList<>();

        AcnAircraftDetail detail1 = new AcnAircraftDetail();
        detail1.setAcn("ACN123");
        detail1.setFleetModelCd("B777");
        detail1.setStatus("A"); // Active status
        acnAircraftDetails.add(detail1);

        AcnAircraftDetail detail2 = new AcnAircraftDetail();
        detail2.setAcn("ACN456");
        detail2.setFleetModelCd("B767");
        detail2.setStatus("A"); // Active status
        acnAircraftDetails.add(detail2);

        mockResponse.getAcnAircraftDetails().addAll(acnAircraftDetails);

        expectedCacheData = new HashMap<>();
        List<AcnCacheDetail> acnDetailList = new ArrayList<>();

        AcnCacheDetail acnDetail1 = new AcnCacheDetail();
        acnDetail1.setAcn("ACN123");
        acnDetail1.setFleetCode("B777");
        acnDetail1.setStatus("A");
        acnDetailList.add(acnDetail1);

        AcnCacheDetail acnDetail2 = new AcnCacheDetail();
        acnDetail2.setAcn("ACN456");
        acnDetail2.setFleetCode("B767");
        acnDetail2.setStatus("A");
        acnDetailList.add(acnDetail2);

        expectedCacheData.put(AcnCacheService.ACN_CACHE_DETAIL_KEY, acnDetailList);
    }

    @Test
    void testUpdateAcnCache() throws Exception {
        when(webServiceTemplate.marshalSendAndReceive(any())).thenReturn(mockResponse);

        when(jsonFileUtil.saveToJsonFile(eq(AcnCacheService.ACN_CACHE_FILE_PATH), any())).thenReturn(true);

        acnCacheService.updateAcnCache();

        verify(oktaTokenGenService, times(1)).generateToken();
        verify(machSoapClientConfig, times(1)).app1webServiceTemplate();
        verify(jsonFileUtil, times(1)).saveToJsonFile(eq(AcnCacheService.ACN_CACHE_FILE_PATH), any());
    }

    @Test
    void testGetAcnCacheDetail() throws Exception {
        when(webServiceTemplate.marshalSendAndReceive(any())).thenReturn(mockResponse);

        HashMap<String, List<?>> result = acnCacheService.getAcnCacheDetail();

        assertNotNull(result);
        assertTrue(result.containsKey(AcnCacheService.ACN_CACHE_DETAIL_KEY));

        List<?> acnDetailList = result.get(AcnCacheService.ACN_CACHE_DETAIL_KEY);
        assertNotNull(acnDetailList);
        assertEquals(2, acnDetailList.size());

        verify(oktaTokenGenService, times(1)).generateToken();
        verify(machSoapClientConfig, times(1)).app1webServiceTemplate();
    }

    @Test
    void testGetAcnCacheDetail_Exception() throws Exception {
        when(webServiceTemplate.marshalSendAndReceive(any())).thenThrow(new RuntimeException("Test exception"));

        Exception exception = assertThrows(Exception.class, () -> {
            acnCacheService.getAcnCacheDetail();
        });

        assertEquals("Test exception", exception.getMessage());

        verify(oktaTokenGenService, times(1)).generateToken();
        verify(machSoapClientConfig, times(1)).app1webServiceTemplate();
    }

    @Test
    void testGetAcnCacheFromFile() {
        when(jsonFileUtil.readFromJsonFile(eq(AcnCacheService.ACN_CACHE_FILE_PATH), eq(HashMap.class))).thenReturn(expectedCacheData);

        HashMap<String, List<?>> result = acnCacheService.getAcnCacheFromFile();

        assertNotNull(result);
        assertEquals(expectedCacheData, result);

        verify(jsonFileUtil, times(1)).readFromJsonFile(eq(AcnCacheService.ACN_CACHE_FILE_PATH), eq(HashMap.class));
    }

    @Test
    void testGetAcnCacheFromFile_NullResponse() {
        when(jsonFileUtil.readFromJsonFile(eq(AcnCacheService.ACN_CACHE_FILE_PATH), eq(HashMap.class))).thenReturn(null);

        HashMap<String, List<?>> result = acnCacheService.getAcnCacheFromFile();

        assertNull(result);
        verify(jsonFileUtil, times(1)).readFromJsonFile(eq(AcnCacheService.ACN_CACHE_FILE_PATH), eq(HashMap.class));
    }
}
