
package com.fedex.mets.wsdl.discrepancy;

import java.math.BigDecimal;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for detailDiscrepancyQueryFilter complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="detailDiscrepancyQueryFilter">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="aircraftNbr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ataNbr" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="actionOid" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="getImage" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="noImageData" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="discrepancyNbr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="oid" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "detailDiscrepancyQueryFilter", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", propOrder = {
    "aircraftNbr",
    "ataNbr",
    "actionOid",
    "getImage",
    "noImageData",
    "discrepancyNbr",
    "oid"
})
public class DetailDiscrepancyQueryFilter {

    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", required = true)
    protected String aircraftNbr;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", required = true)
    protected BigDecimal ataNbr;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", required = true)
    protected BigDecimal actionOid;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", defaultValue = "true")
    protected boolean getImage;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected boolean noImageData;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", required = true)
    protected String discrepancyNbr;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", required = true)
    protected BigDecimal oid;

    /**
     * Gets the value of the aircraftNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAircraftNbr() {
        return aircraftNbr;
    }

    /**
     * Sets the value of the aircraftNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAircraftNbr(String value) {
        this.aircraftNbr = value;
    }

    /**
     * Gets the value of the ataNbr property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAtaNbr() {
        return ataNbr;
    }

    /**
     * Sets the value of the ataNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAtaNbr(BigDecimal value) {
        this.ataNbr = value;
    }

    /**
     * Gets the value of the actionOid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getActionOid() {
        return actionOid;
    }

    /**
     * Sets the value of the actionOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setActionOid(BigDecimal value) {
        this.actionOid = value;
    }

    /**
     * Gets the value of the getImage property.
     * 
     */
    public boolean isGetImage() {
        return getImage;
    }

    /**
     * Sets the value of the getImage property.
     * 
     */
    public void setGetImage(boolean value) {
        this.getImage = value;
    }

    /**
     * Gets the value of the noImageData property.
     * 
     */
    public boolean isNoImageData() {
        return noImageData;
    }

    /**
     * Sets the value of the noImageData property.
     * 
     */
    public void setNoImageData(boolean value) {
        this.noImageData = value;
    }

    /**
     * Gets the value of the discrepancyNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDiscrepancyNbr() {
        return discrepancyNbr;
    }

    /**
     * Sets the value of the discrepancyNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDiscrepancyNbr(String value) {
        this.discrepancyNbr = value;
    }

    /**
     * Gets the value of the oid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getOid() {
        return oid;
    }

    /**
     * Sets the value of the oid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setOid(BigDecimal value) {
        this.oid = value;
    }

}
