
package com.fedex.mets.wsdl.discrepancy;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for AcnDiscrepancyMaintUpdt complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="AcnDiscrepancyMaintUpdt">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="acnDiscrepancyOid" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="acnDscrpMaintUpdtOid" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="actionIdOid" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="AddUserId" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="addDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="AutoGenText" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="CatRelatedMsg" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="EnteredByUserId" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="EtopsAtaSupReviewId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="EtopsAtaSupUserId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="EtopsPartSupReviewId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="EtopsPartSupUserId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="LogpageNbr" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="maintInfoTypeCd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="maintUpdateType" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="MaintUpdateText" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="MajorRepairFlg" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ReportedByUserId" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="ReviewFlg" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="RiiByReviewKey" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="RiiByUserId" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="RptByReviewKey" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="TextProtectedFlg" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="WorkDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="WorkStationCd" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="maintUpdtRef" type="{http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd}AcnDiscrepMaintUpdtRef" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="maintPrcsControl" type="{http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd}DscrpProcessControl" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="plannedItemInfoText" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="workedOnAcnMinutes" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="workedOffAcnMinutes" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AcnDiscrepancyMaintUpdt", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", propOrder = {
    "acnDiscrepancyOid",
    "acnDscrpMaintUpdtOid",
    "actionIdOid",
    "addUserId",
    "addDate",
    "autoGenText",
    "catRelatedMsg",
    "enteredByUserId",
    "etopsAtaSupReviewId",
    "etopsAtaSupUserId",
    "etopsPartSupReviewId",
    "etopsPartSupUserId",
    "logpageNbr",
    "maintInfoTypeCd",
    "maintUpdateType",
    "maintUpdateText",
    "majorRepairFlg",
    "reportedByUserId",
    "reviewFlg",
    "riiByReviewKey",
    "riiByUserId",
    "rptByReviewKey",
    "textProtectedFlg",
    "workDate",
    "workStationCd",
    "maintUpdtRef",
    "maintPrcsControl",
    "plannedItemInfoText",
    "workedOnAcnMinutes",
    "workedOffAcnMinutes"
})
public class AcnDiscrepancyMaintUpdt {

    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal acnDiscrepancyOid;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal acnDscrpMaintUpdtOid;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal actionIdOid;
    @XmlElement(name = "AddUserId", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal addUserId;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar addDate;
    @XmlElement(name = "AutoGenText", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String autoGenText;
    @XmlElement(name = "CatRelatedMsg", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String catRelatedMsg;
    @XmlElement(name = "EnteredByUserId", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal enteredByUserId;
    @XmlElement(name = "EtopsAtaSupReviewId", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String etopsAtaSupReviewId;
    @XmlElement(name = "EtopsAtaSupUserId", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String etopsAtaSupUserId;
    @XmlElement(name = "EtopsPartSupReviewId", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String etopsPartSupReviewId;
    @XmlElement(name = "EtopsPartSupUserId", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String etopsPartSupUserId;
    @XmlElement(name = "LogpageNbr", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal logpageNbr;
    @XmlElement(name = "maintInfoTypeCd", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String maintInfoTypeCd;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", required = true)
    protected String maintUpdateType;
    @XmlElement(name = "MaintUpdateText", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String maintUpdateText;
    @XmlElement(name = "MajorRepairFlg", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String majorRepairFlg;
    @XmlElement(name = "ReportedByUserId", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal reportedByUserId;
    @XmlElement(name = "ReviewFlg", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String reviewFlg;
    @XmlElement(name = "RiiByReviewKey", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String riiByReviewKey;
    @XmlElement(name = "RiiByUserId", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal riiByUserId;
    @XmlElement(name = "RptByReviewKey", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String rptByReviewKey;
    @XmlElement(name = "TextProtectedFlg", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String textProtectedFlg;
    @XmlElement(name = "WorkDate", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar workDate;
    @XmlElement(name = "WorkStationCd", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String workStationCd;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected List<AcnDiscrepMaintUpdtRef> maintUpdtRef;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected List<DscrpProcessControl> maintPrcsControl;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String plannedItemInfoText;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", required = true)
    protected BigDecimal workedOnAcnMinutes;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", required = true)
    protected BigDecimal workedOffAcnMinutes;

    /**
     * Gets the value of the acnDiscrepancyOid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAcnDiscrepancyOid() {
        return acnDiscrepancyOid;
    }

    /**
     * Sets the value of the acnDiscrepancyOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAcnDiscrepancyOid(BigDecimal value) {
        this.acnDiscrepancyOid = value;
    }

    /**
     * Gets the value of the acnDscrpMaintUpdtOid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAcnDscrpMaintUpdtOid() {
        return acnDscrpMaintUpdtOid;
    }

    /**
     * Sets the value of the acnDscrpMaintUpdtOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAcnDscrpMaintUpdtOid(BigDecimal value) {
        this.acnDscrpMaintUpdtOid = value;
    }

    /**
     * Gets the value of the actionIdOid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getActionIdOid() {
        return actionIdOid;
    }

    /**
     * Sets the value of the actionIdOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setActionIdOid(BigDecimal value) {
        this.actionIdOid = value;
    }

    /**
     * Gets the value of the addUserId property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAddUserId() {
        return addUserId;
    }

    /**
     * Sets the value of the addUserId property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAddUserId(BigDecimal value) {
        this.addUserId = value;
    }

    /**
     * Gets the value of the addDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getAddDate() {
        return addDate;
    }

    /**
     * Sets the value of the addDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setAddDate(XMLGregorianCalendar value) {
        this.addDate = value;
    }

    /**
     * Gets the value of the autoGenText property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAutoGenText() {
        return autoGenText;
    }

    /**
     * Sets the value of the autoGenText property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAutoGenText(String value) {
        this.autoGenText = value;
    }

    /**
     * Gets the value of the catRelatedMsg property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCatRelatedMsg() {
        return catRelatedMsg;
    }

    /**
     * Sets the value of the catRelatedMsg property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCatRelatedMsg(String value) {
        this.catRelatedMsg = value;
    }

    /**
     * Gets the value of the enteredByUserId property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getEnteredByUserId() {
        return enteredByUserId;
    }

    /**
     * Sets the value of the enteredByUserId property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setEnteredByUserId(BigDecimal value) {
        this.enteredByUserId = value;
    }

    /**
     * Gets the value of the etopsAtaSupReviewId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEtopsAtaSupReviewId() {
        return etopsAtaSupReviewId;
    }

    /**
     * Sets the value of the etopsAtaSupReviewId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEtopsAtaSupReviewId(String value) {
        this.etopsAtaSupReviewId = value;
    }

    /**
     * Gets the value of the etopsAtaSupUserId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEtopsAtaSupUserId() {
        return etopsAtaSupUserId;
    }

    /**
     * Sets the value of the etopsAtaSupUserId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEtopsAtaSupUserId(String value) {
        this.etopsAtaSupUserId = value;
    }

    /**
     * Gets the value of the etopsPartSupReviewId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEtopsPartSupReviewId() {
        return etopsPartSupReviewId;
    }

    /**
     * Sets the value of the etopsPartSupReviewId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEtopsPartSupReviewId(String value) {
        this.etopsPartSupReviewId = value;
    }

    /**
     * Gets the value of the etopsPartSupUserId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEtopsPartSupUserId() {
        return etopsPartSupUserId;
    }

    /**
     * Sets the value of the etopsPartSupUserId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEtopsPartSupUserId(String value) {
        this.etopsPartSupUserId = value;
    }

    /**
     * Gets the value of the logpageNbr property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getLogpageNbr() {
        return logpageNbr;
    }

    /**
     * Sets the value of the logpageNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setLogpageNbr(BigDecimal value) {
        this.logpageNbr = value;
    }

    /**
     * Gets the value of the maintInfoTypeCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMaintInfoTypeCd() {
        return maintInfoTypeCd;
    }

    /**
     * Sets the value of the maintInfoTypeCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMaintInfoTypeCd(String value) {
        this.maintInfoTypeCd = value;
    }

    /**
     * Gets the value of the maintUpdateType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMaintUpdateType() {
        return maintUpdateType;
    }

    /**
     * Sets the value of the maintUpdateType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMaintUpdateType(String value) {
        this.maintUpdateType = value;
    }

    /**
     * Gets the value of the maintUpdateText property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMaintUpdateText() {
        return maintUpdateText;
    }

    /**
     * Sets the value of the maintUpdateText property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMaintUpdateText(String value) {
        this.maintUpdateText = value;
    }

    /**
     * Gets the value of the majorRepairFlg property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMajorRepairFlg() {
        return majorRepairFlg;
    }

    /**
     * Sets the value of the majorRepairFlg property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMajorRepairFlg(String value) {
        this.majorRepairFlg = value;
    }

    /**
     * Gets the value of the reportedByUserId property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getReportedByUserId() {
        return reportedByUserId;
    }

    /**
     * Sets the value of the reportedByUserId property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setReportedByUserId(BigDecimal value) {
        this.reportedByUserId = value;
    }

    /**
     * Gets the value of the reviewFlg property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReviewFlg() {
        return reviewFlg;
    }

    /**
     * Sets the value of the reviewFlg property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReviewFlg(String value) {
        this.reviewFlg = value;
    }

    /**
     * Gets the value of the riiByReviewKey property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRiiByReviewKey() {
        return riiByReviewKey;
    }

    /**
     * Sets the value of the riiByReviewKey property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRiiByReviewKey(String value) {
        this.riiByReviewKey = value;
    }

    /**
     * Gets the value of the riiByUserId property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getRiiByUserId() {
        return riiByUserId;
    }

    /**
     * Sets the value of the riiByUserId property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setRiiByUserId(BigDecimal value) {
        this.riiByUserId = value;
    }

    /**
     * Gets the value of the rptByReviewKey property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRptByReviewKey() {
        return rptByReviewKey;
    }

    /**
     * Sets the value of the rptByReviewKey property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRptByReviewKey(String value) {
        this.rptByReviewKey = value;
    }

    /**
     * Gets the value of the textProtectedFlg property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTextProtectedFlg() {
        return textProtectedFlg;
    }

    /**
     * Sets the value of the textProtectedFlg property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTextProtectedFlg(String value) {
        this.textProtectedFlg = value;
    }

    /**
     * Gets the value of the workDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getWorkDate() {
        return workDate;
    }

    /**
     * Sets the value of the workDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setWorkDate(XMLGregorianCalendar value) {
        this.workDate = value;
    }

    /**
     * Gets the value of the workStationCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWorkStationCd() {
        return workStationCd;
    }

    /**
     * Sets the value of the workStationCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWorkStationCd(String value) {
        this.workStationCd = value;
    }

    /**
     * Gets the value of the maintUpdtRef property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the maintUpdtRef property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getMaintUpdtRef().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link AcnDiscrepMaintUpdtRef }
     * 
     * 
     */
    public List<AcnDiscrepMaintUpdtRef> getMaintUpdtRef() {
        if (maintUpdtRef == null) {
            maintUpdtRef = new ArrayList<AcnDiscrepMaintUpdtRef>();
        }
        return this.maintUpdtRef;
    }

    /**
     * Gets the value of the maintPrcsControl property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the maintPrcsControl property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getMaintPrcsControl().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link DscrpProcessControl }
     * 
     * 
     */
    public List<DscrpProcessControl> getMaintPrcsControl() {
        if (maintPrcsControl == null) {
            maintPrcsControl = new ArrayList<DscrpProcessControl>();
        }
        return this.maintPrcsControl;
    }

    /**
     * Gets the value of the plannedItemInfoText property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPlannedItemInfoText() {
        return plannedItemInfoText;
    }

    /**
     * Sets the value of the plannedItemInfoText property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPlannedItemInfoText(String value) {
        this.plannedItemInfoText = value;
    }

    /**
     * Gets the value of the workedOnAcnMinutes property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getWorkedOnAcnMinutes() {
        return workedOnAcnMinutes;
    }

    /**
     * Sets the value of the workedOnAcnMinutes property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setWorkedOnAcnMinutes(BigDecimal value) {
        this.workedOnAcnMinutes = value;
    }

    /**
     * Gets the value of the workedOffAcnMinutes property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getWorkedOffAcnMinutes() {
        return workedOffAcnMinutes;
    }

    /**
     * Sets the value of the workedOffAcnMinutes property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setWorkedOffAcnMinutes(BigDecimal value) {
        this.workedOffAcnMinutes = value;
    }

}
