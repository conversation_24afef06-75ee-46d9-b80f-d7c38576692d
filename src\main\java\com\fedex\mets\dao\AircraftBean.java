package com.fedex.mets.dao;

import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class AircraftBean {
	private String acn;
	private String registrationNumber;
	private String fleet;
	private String series;
	private String trunkFlag;
	private String status;
	private String currentGate;
	private String etic;
	private String inTransit;
	private String equipType;
	private int melCount;
	private int cdlCount;
	private boolean isHalfLife;
	private String currentStation;
	private String inboundFlight;
	private String inboundFlightDate;
	private String inboundFlightLeg;
	private String inboundFlightLegDate;
	private String inboundOrigination;
	private String inboundOutTime;
	private String inboundDestination;
	private String inboundArrivalDate;
	private String inboundArrivalTime;
	private String inboundShort;
	private String outboundFlight;
	private String outboundFlightDate;
	private String outboundFlightLeg;
	private String outboundFlightLegDate;
	private String outboundOrigination;
	private String outboundOutTime;
	private String outboundDestination;
	private String outboundArrivalDate;
	private String outboundArrivalTime;
	private String outboundShort;
	private String totalGroundDays;
	private String totalGroundTime;
	private long totalGroundTimeInMinutes;
	private String remGroundTimeHours;
	private long remGroundTimeInMillies;
	private String catStatus;
	private String comments;
	private String workOrder;
	private boolean isVendor;
	private boolean isOST;
	private String openAta;
	private String openDisc;
	private String openReferenceNumber;
	private String openCategory;
	private String openDue;
	private String openDueTrack;
	private String openType;
	private String openDate;
	private String openStation;
}
