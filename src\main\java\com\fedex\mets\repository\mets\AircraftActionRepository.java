package com.fedex.mets.repository.mets;

import com.fedex.mets.entity.mets.AircraftAction;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface AircraftActionRepository extends JpaRepository<AircraftAction, Integer> {

    @Modifying
    @Transactional
    @Query(value = "delete from AIRCRAFT_ACTION where ACN=:acn", nativeQuery = true)
    public void deleteRecordByAcn(@Param("acn") String acn);
}
