package com.fedex.mets.config;


import com.fedex.mets.util.DecryptionUtil;
import jakarta.persistence.EntityManagerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        basePackages = "com.fedex.mets.repository.pcs", // Repositories for PCS
        entityManagerFactoryRef = "pcsEntityManagerFactory",
        transactionManagerRef = "pcsTransactionManager"
)
public class PcsDataSourceConfig {


    @Value("${spring.datasource.pcs.password}")
    private String encryptedPassword;

    @Value("${spring.datasource.pcs.jdbc-url}")
    private String url;

    @Value("${spring.datasource.pcs.username}")
    private String username;

    @Value("${spring.datasource.pcs.driver-class-name}")
    private String driverClassName;

    @Bean(name = "pcsDataSource")
    public DataSource pcsDataSource() throws Exception {
        String decryptedPassword = DecryptionUtil.decrypt(encryptedPassword, "z76yf8ScxNFLZMbxC1YVRQ==");
        return DataSourceBuilder.create().username(username).url(url).driverClassName(driverClassName)
                .password(decryptedPassword).build();
    }

    @Bean(name = "pcsEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean pcsEntityManagerFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("pcsDataSource") DataSource dataSource) {
        return builder
                .dataSource(dataSource)
                .packages("com.fedex.mets.entity.pcs") // Entities for pcs
                .persistenceUnit("pcs") // Persistence unit name for pcs
                .build();
    }

    @Bean(name = "pcsTransactionManager")
    public PlatformTransactionManager pcsTransactionManager(
            @Qualifier("pcsEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        return new JpaTransactionManager(entityManagerFactory);
    }
}
