package com.fedex.mets.repository.mets;

import com.fedex.mets.entity.mets.EventMsns;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EventMsnsRepository extends JpaRepository<EventMsns,Long> {
    @Query(value = "select * from event_msns where EVENT_ID=:eventId", nativeQuery = true)
    public List<EventMsns> getEventMsns(@Param("eventId") int eventId);
}
