package com.fedex.mets.util;

public class UpdateLineFormat extends BaseLineFormat{

	static int line = 80;
	
	@Override
	protected void init(){
		sfd = new SentenceFormatDef("Update Line", line, 5, 49);
		
		addUpdtType();
		addDateTime();
		addStation();
		addReview();
	}
	
	private void addUpdtType() {
		FieldFormatDef ffd = new FieldFormatDef("Update Type",  5);
		sfd.getFields().add(ffd);
	}

	private void addDateTime() {
		FieldFormatDef ffd = new FieldFormatDef("Date Time",  16, true);
		sfd.getFields().add(ffd);
	}

	private void addStation() {
		FieldFormatDef ffd = new FieldFormatDef("Station",  4);
		sfd.getFields().add(ffd);
	}

	private void addReview() {
		FieldFormatDef ffd = new FieldFormatDef("Review",  1);
		sfd.getFields().add(ffd);
	}

}

