package com.fedex.mets.data;


import java.util.Calendar;
import java.util.TimeZone;
import java.util.GregorianCalendar;
import java.text.SimpleDateFormat;

public class FlightDepartureDetails extends GregorianCalendar {
    private Calendar m_fltTime;
    private boolean m_isBold;

    public FlightDepartureDetails(Calendar flightTime){

        super(TimeZone.getTimeZone("UTC"));
        if( flightTime != null ){
              setTime(flightTime.getTime());
        }
        m_fltTime = flightTime;
    }

    public void setFlightDate(Calendar fltTime ){
        m_fltTime = fltTime;
    }
    public Calendar getFlightDate(){
        return m_fltTime;
    }

    public String getRenderingString(){
        StringBuffer buffer = new StringBuffer();
        if( m_fltTime != null ){
            SimpleDateFormat formatter = new SimpleDateFormat("MM/dd HH:mm");
            formatter.setTimeZone(TimeZone.getTimeZone("UTC"));
            buffer.append(formatter.format(m_fltTime.getTime()));
        }
        return buffer.toString();
    }

    public void setIsBold( boolean isBold){
        m_isBold = isBold;
    }

    public boolean isBold(){
        return m_isBold;
    }

}