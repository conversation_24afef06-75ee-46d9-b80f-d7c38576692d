package com.fedex.mets.repository.mets;

import com.fedex.mets.entity.mets.EventFlightInfo;
import com.fedex.mets.entity.mets.EventMaxiDwningItm;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EventMaxiDwningItmRepository extends JpaRepository<EventMaxiDwningItm,Long> {

    @Query(value="select * from EVENT_MAXI_DWNING_ITM where EVENT_ID=:id",nativeQuery = true)
    public EventMaxiDwningItm getEventMaxiDwningItemByEventId(@Param("id") int eventId);


}
