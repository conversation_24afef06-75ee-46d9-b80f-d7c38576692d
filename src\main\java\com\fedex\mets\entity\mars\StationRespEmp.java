package com.fedex.mets.entity.mars;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "STATION_RESP_EMP")
public class StationRespEmp {

    @Id
    @Column(name = "STA_ID", nullable = false)
    private String staId;

    @Column(name = "DEPT_ID", nullable = false)
    private String deptId;

    @Column(name = "RESP_EMPNUM", nullable = false)
    private String respEmpnum;

    @Column(name = "COMMENTS")
    private String comments;

    @Column(name = "PRIMARY_FLAG")
    private Character primaryFlag;
}
