package com.fedex.mets.service.retrieval;

import com.fedex.mets.entity.ldap.User;
import com.fedex.mets.repository.ldap.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class UserService {
    @Autowired
    private UserRepository userRepository;

    public Optional<User> getUserByUid(String uid) {
        return userRepository.findByUid(uid);
    }
}
