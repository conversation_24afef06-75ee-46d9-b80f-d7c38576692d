package com.fedex.mets.entity.mets;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "aircraft_action")
public class AircraftAction {

    @Id
    @Column(name = "ACN", nullable = false, length = 6)
    private String acn;

    @Column(name = "TYPE", length = 4)
    private String type;

    @Column(name = "EMP_NUM", length = 10)
    private String empNum;

    @Column(name = "EMP_NAME", length = 30)
    private String empName;

    @Column(name = "EMP_DEPARTMENT", length = 25)
    private String empDepartment;

    @Column(name = "LAST_UPDATE_DT_TM")
    private Timestamp lastUpdateDtTm;
}