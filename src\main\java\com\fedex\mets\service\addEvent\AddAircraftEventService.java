package com.fedex.mets.service.addEvent;

import com.fedex.mets.data.ReportCategoriesKeyValueData;
import com.fedex.mets.data.WizardEventData;
import com.fedex.mets.data.EventDiscrepancyListData;
import com.fedex.mets.entity.mets.*;
import com.fedex.mets.repository.cache.SuperEquipmentRepository;
import com.fedex.mets.repository.mets.*;
import com.fedex.mets.util.ServerDateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import com.fedex.mets.util.ETICHelper;

import java.rmi.RemoteException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

@Service
public class AddAircraftEventService {
    private static final Logger logger = LoggerFactory.getLogger(AddAircraftEventService.class);

    @Autowired
    private SuperEquipmentRepository superEquipmentRepository;

    @Autowired
    private EventsRepository eventsRepository;

    @Autowired
    private ChangeRequestRepository changeRequestRepository;

    @Autowired
    private EventDictRepository eventDictRepository;

    @Autowired
    private GroupDictRepository groupDictRepository;

    @Autowired
    private EventTypeHistoryRepository eventTypeHistoryRepository;

    @Autowired
    private FlightDetailsService flightDetailsService;

    @Autowired
    private EventFlightInfoRepository flightInfoRepository;

    @Autowired
    private  EventFlightDelaysRepository eventFlightDelaysRepository;
    
    @Autowired
    private EventTfNotesRepository eventTfNotesRepository;

    @Autowired
    private EventTimersRepository eventTimersRepository;

    @Autowired
    private  EventRepCatgRepository eventRepCatgRepository;

    @Autowired
    private EventMaxiDiscRepository eventMaxiDiscRepository;

    @Autowired
    private EventMaxiDwningItmRepository eventMaxiDwningItmRepository;

    @Autowired
    private  EventDoaRepository eventDoaRepository;

    @Autowired
    private EventMsnsRepository eventMsnsRepository;

    @Autowired
    private ChangeRequestLogRepository changeRequestLogRepository;

    @Autowired
    private ChangeRequestHistoryRepository changeRequestHistoryRepository;

    @Autowired
    private SimpMessagingTemplate messagingTemplate;


    public WizardEventData addAircraftEvent(WizardEventData wizardEventData)
            throws RemoteException {
        logger.info("****** Proceeding with adding the Event ******");
            try {
                wizardEventData = addEvent(wizardEventData);
                logger.info("****** new event added for ACN - came here ******"+wizardEventData.getACN());
                messagingTemplate.convertAndSend("/topic/notifications", "New Event Created for ACN : "+wizardEventData.getACN());
            } catch (Exception exec) {
                throw new RemoteException(exec.getMessage());
            }

            //TODO: Later implement the following code

//            if (wizardEventData.getServerError() == null) {
//                if (wizardEventData.getEventId() > 0) {
//                    //Added the condition for access level(Should call Super only when MOCC adds anew event)
//
//                    String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
//
//                    if (wizardEventData.getAccessLevel().equals("90")
//                            && wizardEventData.getSuperUpdateRequired()) {
//                        logger.info("Before requesting Super Update");
//                        try {
//                            boolean superUpdated =
//                                    updateSuper(wizardEventData);
//                        } catch (Exception supUpdate) {
//                            logger.info(IServerConstants.ERROR + supUpdate.getMessage());
//                        }
//                    }
//
//                    /*New logic added on 10-16-2003 to delete the record from Action Table after completing the transaction on the ACN.
//                     **/
//                    try {
//                        boolean isTranscationComplete = false;
//                        EventVerifySessionHome eventVerifySessionHome = null;
//                        EventVerifySession eventVerifySession = null;
//
//                        try {
//                            eventVerifySessionHome =
//                                    (EventVerifySessionHome) getInitialContext().lookup(
//                                            "EventVerifySession");
//                        } catch (NamingException ne) {
//                            logger.info(IServerConstants.ERROR + ne.getMessage());
//                        } catch (Exception e) {
//                            logger.info(IServerConstants.ERROR + e.getMessage());
//                        }
//
//                        if (eventVerifySessionHome != null) {
//                            try {
//                                eventVerifySession = eventVerifySessionHome.create();
//                                isTranscationComplete =
//                                        eventVerifySession.deleteEventAction(
//                                                0,
//                                                wizardEventData.getACN(),
//                                                wizardEventData.getEventType());
//                            } catch (Exception eventVerification) {
//                                logger.info(
//                                        IServerConstants.ERROR + eventVerification.getMessage());
//                            }
//
//                            logger.info(
//                                    " is Event Action record Deleted >> " + isTranscationComplete);
//                        }
//                    } catch (Exception verify) {
//                        logger.info(IServerConstants.ERROR + verify.getMessage());
//                    }
//
//                    try {
//                        eventWizardSession.publishEvent(
//                                wizardEventData.getEventId(),
//                                IServerConstants.ADD_EVENT);
//                    } catch (Exception pub) {
//                        logger.info(IServerConstants.ERROR + pub.getMessage());
//                    }
//
//                    logger.info("  ");
//                    logger.info(" ************************************* ");
//                    logger.info(
//                            " wizardEventData.getOverrideRequest() "
//                                    + wizardEventData.getOverrideRequest());
//                    logger.info(
//                            " wizardEventData.getAddNewEvent() "
//                                    + wizardEventData.getAddNewEvent());
//                    logger.info(
//                            " wizardEventData.getOverrideEventId() "
//                                    + wizardEventData.getOverrideEventId());
//                    if ((wizardEventData.getOverrideRequest()
//                            || wizardEventData.getAddNewEvent())
//                            && wizardEventData.getOverrideEventId() > 0) {
//                        logger.info(" Publishing the CLOSE EVENT ");
//                        eventWizardSession.publishEvent(
//                                wizardEventData.getOverrideEventId(),
//                                IServerConstants.CLOSE_EVENT);
//                    }
//
//                    //the following is to publish the TF_Notes Object and Text Message when an Event is added
//                    boolean isTFNotesPublished =
//                            publishEventUpdate(wizardEventData.getEventId());
//                    logger.info(" isTFNotesPublished >> " + isTFNotesPublished);
//
//                } else {
//                    throw new RemoteException("The Event could not be added to the Database.");
//                }
//            } else {
//                throw new RemoteException(wizardEventData.getServerError());
//            }

        return wizardEventData;
    }

    /*################################## START ADD EVENT ##########################################################################**/
    /**
     * The following addEvent() is used to add the Event and the related data to the database for a particular aircraft.
     * @params WizardEventData wizardData.
     * @return int EventId. Changed return type to wizardData
     */
    public WizardEventData addEvent(WizardEventData wizardData) {
        /*First Step before doing any thing would be to Check the Security Level of the User trying to ADD the Event.**/
        try {
//            Vector acessFlagVector = null;

            String strUserId = wizardData.getUserId();
            String strTokenId = wizardData.getTokenId();
            String strTransactionId = "METS";

            if (strUserId != null && strTokenId != null) {
                try{
                    //#####################TODO: implement access level functionality
//                    acessFlagVector =
//                            SecurityHelper.getAccessFlags(
//                                    strUserId,
//                                    strTokenId,
//                                    strTransactionId);
                }catch(Exception e){
                    logger.warn(
                            "ERROR EventWizaredEvent addEvent() SecurityHelper.getAccessFlags exception "
                                    + e.getMessage());
                    throw new RemoteException("512");
                }
            }
            //#####################TODO: continuation
            //for all Adding of Events check access flag 1 is 80 or 90 else throw exception
//            if (acessFlagVector != null) {
//                String firstElement = (String) acessFlagVector.firstElement();
//
//                if (firstElement.trim().equals("SUCCESS")) {
//                    String strSecurityAccess = (String) acessFlagVector.get(1);
//
//                    if (strSecurityAccess.trim().equals("80")
//                            || strSecurityAccess.trim().equals("90")) {
//                        logger.info("User " + strUserId + " has access to Add an Event.");
//                    } else {
//                        logger.info(
//                                "User " + strUserId + " does not have access to Add an Event.");
//                        wizardData.setServerError(
//                                "User does not have permission/access to Add an Event");
//                        return wizardData;
//                    }
//                } else {
//                    if (firstElement.trim().equals("512")) {
//                        wizardData.setServerError(firstElement);
//                        return wizardData;
//                    } else {
//                        String strSecurityAccessError =
//                                (String) acessFlagVector.get(1);
//
//                        wizardData.setServerError(strSecurityAccessError);
//                        return wizardData;
//                    }
//                }
//            } else if (acessFlagVector == null) {
//                logger.info(
//                        "User "
//                                + strUserId
//                                + " does not have permission/access to Add an Event");
//                wizardData.setServerError(
//                        "User does not have permission/access to Add an Event");
//                return wizardData;
//            }
//       #####################TODO-end
        } catch (Exception security) {
            logger.warn(
                    "ERROR ADD Event addEvent()  security exception "
                            + security.getMessage());
        }

        int eventId = 0;
        ChangeRequest changeRequest = null;
        Events events = new Events();

        java.sql.Timestamp createdDateTimeStamp = null;
        java.sql.Timestamp startDateTimeStamp = null, eticDateTimeStamp = null;

        String strStartDateTime = wizardData.getStartDateTime();
        String strEticDateTime = wizardData.getEticDateTime();

        logger.info("strEticDateTime >>>>>>>>>>>>>> " + strEticDateTime);
        logger.info("strStartDateTime  from client  -------" + strStartDateTime);

        try {
            if (strStartDateTime != null) {
                startDateTimeStamp =
                        ServerDateHelper.getConvertedTimestamp(strStartDateTime);
                logger.info(
                        "startDateTimeStamp  after converting -------" + startDateTimeStamp);
            }

            if (strEticDateTime != null) {
                eticDateTimeStamp =
                        ServerDateHelper.getConvertedTimestamp(strEticDateTime);
                wizardData.setEticDateTime(String.valueOf(eticDateTimeStamp));
                logger.info(
                        "eticDateTimeStamp  after converting -------" + eticDateTimeStamp);
            }

            createdDateTimeStamp = ServerDateHelper.getTimeStamp();
            logger.info("createdDateTimeStamp >>>>>>>>>>>>>> " + createdDateTimeStamp);

            wizardData.setCreatedDateTime(createdDateTimeStamp);

        } catch (Exception convert) {
            logger.warn(
                    "ERROR ADD Event addEvent()  convert time stamp exception "
                            + convert.getMessage());
            wizardData.setServerError(
                    "The Date Time String could not converted to TimeStamp variable."
                            + convert.getMessage());
            return wizardData;
        }
        try {
            logger.info(".........Getting Max Event Id");
            eventId= eventsRepository.getMaxEvent();
            eventId = eventId + 1;
            logger.info("Incremented eventId====== " + eventId);
            wizardData.setEventId(eventId);
            logger.info("Next Event ID to be added added:"+eventId);
        } catch (Exception e) {
            logger.warn(
                    "ERROR ADD Event addEvent()  increment event id exception "
                            + e.getMessage());
            wizardData.setServerError(
                    "Event Id could not be generated to add the Event.");
            return wizardData;
        }
        try {
                /*
				To Override an Existing Event that has been called UP and did not receive any confirmation
				back from SUPER, and the User has selected to Override the Event. In this case mark the Active Event as IN_ACTIVE and delete the record
				from Change Request table.
				**/
                if (wizardData.isOverrideRequest()
                        && wizardData.getOverrideEventId() > 0) {
                    try {
                        logger.info(".....Getting existing event");
                        events = eventsRepository.getEventsByEventId(wizardData.getOverrideEventId());
                    } catch (Exception pk) {
                        logger.warn(
                                "ERROR ADD Event addEvent()  overrode event finder exception "
                                        + pk.getMessage());
                    }
                    if (events != null) {
                        try {
                            logger.info("updating status for existing event"+wizardData.getOverrideEventId());
                            eventsRepository.updateActiveEventStatus("N",wizardData.getOverrideEventId());
                        } catch (Exception overridingEvent) {
                            logger.warn(
                                    "ERROR ADD Event addEvent()  overridingEvent exception "
                                            + overridingEvent.getMessage());
                            wizardData.setServerError(
                                    "Could not update the Record in the Events Table."
                                            + overridingEvent.getMessage());
                            return wizardData;
                        }
                        try {
                            logger.info("...deleting record from change request table.");
                            changeRequestRepository.deleteChangeRequestRecord(wizardData.getACN());
                        } catch (Exception overridingChangeRequest) {
                            logger.warn(
                                    "ERROR ADD Event addEvent()  overridingChangeRequest exception "
                                            + overridingChangeRequest.getMessage());
                            wizardData.setServerError(
                                    "Could not remove the Record from Change Request Table."
                                            + overridingChangeRequest.getMessage());
                            return wizardData;
                        }
                    }
                }

                /*
				To Continue adding an Event while an Event that has been called UP exists,
				and the User has selected to Continue adding the Event. In this case mark the Active Event as IN_ACTIVE.
				**/
                if (wizardData.isAddNewEvent()
                        && wizardData.getOverrideEventId() > 0) {

                    try {
                        events = eventsRepository.getEventsByEventId(wizardData.getOverrideEventId());
                    } catch (Exception pk) {
                        logger.warn(
                                "ERROR ADD Event addEvent()  AddNewEvent event finder exception "
                                        + pk.getMessage());
                    }

                    if (events != null) {
                        try {
                            logger.info(
                                    "setting the Event as inactive for Event ID ----->"
                                            + events.getEventId());
                            eventsRepository.updateActiveEventStatus("N",wizardData.getOverrideEventId());
                        } catch (Exception overridingEvent) {
                            logger.warn(
                                    "ERROR ADD Event addEvent()  overridingEvent exception "
                                            + overridingEvent.getMessage());
                            wizardData.setServerError(
                                    "Could not update the Record in the Events Table."
                                            + overridingEvent.getMessage());
                            return wizardData;
                        }
                        try {
                            changeRequestRepository.deleteChangeRequestRecord(wizardData.getACN());
                        } catch (Exception overridingChangeRequest) {
                            logger.warn(
                                    "ERROR ADD Event addEvent()  overridingChangeRequest exception "
                                            + overridingChangeRequest.getMessage());
                        }
                    }
                }

                Events eventsData = new Events();
                try {
                    eventsData =
                            generateEventData(
                                    wizardData,
                                    eticDateTimeStamp,
                                    startDateTimeStamp);
                } catch (Exception generate) {
                    logger.warn(
                            "ERROR Add Event generate ---- " + generate.getMessage());
                }

                wizardData.setEticInfo(eventsData.getEticText());

                if (eventsData != null) {
                    eventsRepository.save(eventsData);
                    logger.info(
                            "After creating a record in the Events table. event Id----->"
                                    + eventId);
                } else {
                    wizardData.setServerError(
                            "Could not insert the Record into Events Table, for ACN "
                                    + wizardData.getACN());
                    return wizardData;
                }

        } catch (Exception rem) {
            logger.warn(
                    "ERROR ADD Event addEvent() exception "
                            + rem.getMessage());
            wizardData.setServerError(
                    "Could not insert the Record into Events Table." + rem.getMessage());
            return wizardData;
        }

        /*The following is to create a new Record in the EventTypeHistory table.**/
        try {
            if (events != null) {
                EventTypeHistory eventTypeHistoryData = new EventTypeHistory();

                eventTypeHistoryData.setEventId(wizardData.getEventId());
                eventTypeHistoryData.setChangeDateTime(wizardData.getCreatedDateTime());
                eventTypeHistoryData.setNewType(wizardData.getEventType());
                eventTypeHistoryData.setChangedBy(wizardData.getUserId());

                eventTypeHistoryRepository.save(eventTypeHistoryData);
                logger.info(
                        "Event Type History table updated with event Id----->"
                                + eventId);

            }
        } catch (Exception eventHistory) {
            logger.warn(
                    "ERROR ADD Event addEvent()  eventHistory exception "
                            + eventHistory.getMessage());
            wizardData.setServerError(
                    "Could not add Event History for the event "
                            + wizardData.getEventId()
                            + "\n"
                            + eventHistory.getMessage());
            return wizardData;
        }

        /*The following is to add the affected Outbound flight Info to the database.**/
        if (events != null) {
            logger.info("**********Adding affected Outbound flight Info to the database");
            logger.info(
                    "wizardData.getAffectedFlightNumber()"
                            + wizardData.getAffectedFlightNumber());
            logger.info(
                    "wizardData.getAffectedFlightDate()"
                            + wizardData.getAffectedFlightDate());
            logger.info(
                    "wizardData.getAffectedFlightLegNumber()"
                            + wizardData.getAffectedFlightLegNumber());
            logger.info("*********************");
            if (wizardData.getAffectedFlightNumber() != null
                    && wizardData.getAffectedFlightNumber().trim().length() > 0
                    && wizardData.getAffectedFlightDate() != null
                    && wizardData.getAffectedFlightDate().trim().length() > 0
                    && wizardData.getAffectedFlightLegNumber() != null
                    && wizardData.getAffectedFlightLegNumber().trim().length() > 0) {

                if (!wizardData.isDOAEvent()) {
                    boolean affectedFlight = addAffectedFlight(wizardData);

                    if (!affectedFlight) {
                        logger.info(
                                "Could not add Affected Flight for event "
                                        + wizardData.getEventId());
                        wizardData.setServerError(
                                "Could not add Affected Flight for event "
                                        + wizardData.getEventId());
                        return wizardData;
                    }
                }
            }

            if (wizardData.getInboundFlightNumber() != null
                    && wizardData.getInboundFlightNumber().trim().length() > 0
                    && wizardData.getInboundFlightDate() != null
                    && wizardData.getInboundFlightDate().trim().length() > 0
                    && wizardData.getInboundFlightLegNumber() != null
                    && wizardData.getInboundFlightLegNumber().trim().length() > 0) {

                logger.info("*********************");
                logger.info(
                        "wizardData.getInboundFlightNumber()"
                                + wizardData.getInboundFlightNumber());
                logger.info(
                        "wizardData.getInboundFlightDate()"
                                + wizardData.getInboundFlightDate());
                logger.info(
                        "wizardData.getInboundFlightLegNumber()"
                                + wizardData.getInboundFlightLegNumber());
                logger.info("*********************");
                /*The following is to add the affected Inbound flight Info to the database.**/
                if (wizardData.getInboundFlightNumber() != null
                        && wizardData.getInboundFlightDate() != null
                        && wizardData.getInboundFlightDate().trim().length() > 0
                        && wizardData.getInboundFlightLegNumber() != null) {

                    if (!wizardData.isDOAEvent()) {
                        boolean inboundFlight = addInboundFlight(wizardData);

                        if (!inboundFlight) {
                            logger.info(
                                    "Could not add Inbound Flight for event "
                                            + wizardData.getEventId());
                            wizardData.setServerError(
                                    "Could not add Inbound Flight for event "
                                            + wizardData.getEventId());
                            return wizardData;
                        }
                    }
                }
            }
        }

        /*The follwing is to add a TF_Notes to the database.**/
        try {
            if (events != null) {

                logger.info(
                        "before adding the TF_Notes for the event "
                                + wizardData.getEventId());
                boolean tfNotesAdded = addTFNotes(wizardData);

                if (!tfNotesAdded) {
                    wizardData.setServerError(
                            "Could not add TF_Notes for event " + wizardData.getEventId());
                    return wizardData;
                }
            }
        } catch (Exception tfNote) {
            logger.warn(
                    "ERROR ADD Event addEvent()  tfNote exception " + tfNote.getMessage());
        }
        try {
            //the following is to insert a record in to the Change Request table if the access level of the user is 90
            //and the event type added is 'OOS'
            int activeEvents = 0;
            boolean deleteChangeRequestRecord = false;
            if (wizardData.getEventType().trim().equalsIgnoreCase("DOA")) {
                try {
                    int tempEventId = 0;
                    String strStatus = "";

                    String result=eventsRepository.getActiveOOS(wizardData.getACN());
                    if(result !=null)
                    {
                        String[] details=result.split(",");
                        tempEventId=Integer.parseInt(details[0]);
                        strStatus=details[1];
                        if (tempEventId > 0) {
                            if (strStatus != null && strStatus.trim().equals("UP")) {
                                deleteChangeRequestRecord = true;
                            } else {
                                activeEvents = activeEvents + 1;
                            }
                        }
                    }
                    /*
					The following is added on 02-27-04 to Override an Existing Event that has been called UP, and the User trying to add a DOA Event for the ACN.
					In this case mark the Active Event as IN_ACTIVE and delete the record from Change Request table if any.
					**/
                    if (deleteChangeRequestRecord) {
                            try {
                                logger.info(
                                        "As Status for ACN is UP Event is marked inactive for Event Id "
                                                + tempEventId);
                                eventsRepository.updateActiveEventStatus("N",tempEventId);
                            } catch (Exception overridingEvent) {
                                logger.warn(
                                        "ERROR ADD Event addEvent()  overridingEvent exception "
                                                + overridingEvent.getMessage());
                                wizardData.setServerError(
                                        "Could not update the Record in the Events Table."
                                                + overridingEvent.getMessage());
                                return wizardData;
                            }
                            try {
                                if (changeRequest != null) {
                                    changeRequestRepository.deleteChangeRequestRecord(wizardData.getACN());
                                    logger.info(
                                            "As Status for ACN is UP Event is deleted from the change Request for Event Id "
                                                    + tempEventId);
                                }
                            } catch (Exception overridingChangeRequest) {
                                logger.warn(
                                        "ERROR ADD Event addEvent()  overridingChangeRequest exception "
                                                + overridingChangeRequest.getMessage());
                                wizardData.setServerError(
                                        "Could not remove the Record from Change Request Table."
                                                + overridingChangeRequest.getMessage());
                                return wizardData;
                            }

                            wizardData.setAddNewEvent(true);
                            wizardData.setOverrideEventId(tempEventId);
                    }
                    /*END of new Logic**/
                } catch (Exception e) {
                    logger.warn(
                            "ERROR ADD Event addEvent()  doa read exception " + e.getMessage());
                }
            }

            if (events != null && activeEvents == 0) {
                logger.info(
                        "before adding the Change Request for the event "
                                + wizardData.getEventId()
                                + " as Access level of the user is "
                                + wizardData.getAccessLevel());

                if (wizardData.getEventType().trim().equals("OOS")
                        || wizardData.getEventType().trim().equals("DOA")) {
                    int existingOOSRecords = 0;
                    try {

                        String result=changeRequestRepository.getChangeRequestStatus(wizardData.getACN());
                        if(result!=null) {
                            String[] rs = result.split(",");
                            String strRequestStatus = rs[0];
                            String strNewStatus = rs[1];

                            if (strRequestStatus != null
                                    && strRequestStatus.trim().equals("S")) {
                                if (strNewStatus != null && strNewStatus.trim().equals("UP")) {
                                    logger.info("As Request status is S in change Request and new status UP let the user proceed with adding new record.");
                                } else {
                                    logger.info("As Request status is S in change Request with newStatus as other than UP Adding new record is not allowed.");
                                    existingOOSRecords = existingOOSRecords + 1;
                                }
                            } else if (
                                    strRequestStatus != null
                                            && strRequestStatus.trim().equals("C")) {
                                if (strNewStatus != null && strNewStatus.trim().equals("UP")) {
                                    if ((wizardData.isOverrideRequest()
                                            || wizardData.isAddNewEvent())
                                            && wizardData.getOverrideEventId() > 0) {
                                        logger.info("As Request status is C in change Request with newStatus as UP && User requested to Add / Reopen the Event New Event Should now be allowed>>>>>>");
                                        deleteChangeRequestRecord = true;
                                    } else {
                                        logger.info("As Request status is C in change Request with newStatus as UP Adding new record is not allowed");
                                        existingOOSRecords = existingOOSRecords + 1;
                                    }
                                } else {
                                    if (wizardData.getEventType().trim().equals("OOS")) {
                                        logger.info("As Request status is C in change Request and new status other than UP let the user proceed with adding new record");
                                    } else if (wizardData.getEventType().trim().equals("DOA")) {
                                        logger.info("RequestStatus is C and NewStatus other than UP Meaning OOS Event & User cannot send DOA Status to SUPER.");
                                        existingOOSRecords = existingOOSRecords + 1;
                                    }
                                }
                            } else if (
                                    strRequestStatus != null
                                            && strRequestStatus.trim().equals("U")) {
                                logger.info("As Request status is U in change Request Adding new record is not allowed======");
                                existingOOSRecords = existingOOSRecords + 1;
                            }
                        }
                    } catch (Exception e) {
                        logger.warn(
                                "ERROR ADD Event addEvent()  select request status exception "
                                        + e.getMessage());
                    }
                    if (existingOOSRecords == 0) {
                        if (deleteChangeRequestRecord) {
                            int overwriteEventId = changeRequestRepository.getEventIdByAcn(wizardData.getACN());

                            logger.info(
                                    "As Request status is C for UP Event deleting the change Request for Event Id "
                                            + overwriteEventId
                                            + " and Adding new record");
                            wizardData.setAddNewEvent(true);
                            wizardData.setOverrideEventId(overwriteEventId);
                            changeRequestRepository.deleteChangeRequestRecord(wizardData.getACN());
                        }

                        try {
                            changeRequest =
                                    generateChangeRequestData(wizardData, eticDateTimeStamp);
                                if (changeRequest != null) {
                                    changeRequestRepository.save(changeRequest);
                                    logger.info(
                                            "After creating a record in the Change Request table. event Id----->"
                                                    + eventId);
                                    wizardData.setSuperUpdateRequired(true);
                                } else if (changeRequest != null) {
                                    wizardData.setSuperUpdateRequired(true);
                                }
                        } catch (Exception changeReq) {
                            logger.warn(
                                    "ERROR ADD Event addEvent() changeReqexception exception "
                                            + changeReq.getMessage());
                            wizardData.setServerError(
                                    "Could not add Change Request for the event "
                                            + wizardData.getEventId()
                                            + "\n"
                                            + changeReq.getMessage());
                            return wizardData;
                        }

                        try {
                            ChangeRequestHistory changeRequestHistoryData =
                                    generateChangeRequestHistoryData(wizardData, eticDateTimeStamp);
                                    changeRequestHistoryRepository.save(changeRequestHistoryData);
                                logger.info(
                                        "After creating a record in the Change Request History table. event Id----->"
                                                + eventId);
                        } catch (Exception changeReqHist) {
                            logger.warn(
                                    "ERROR ADD Event addEvent() changeReqHist exception "
                                            + changeReqHist.getMessage());
                            wizardData.setServerError(
                                    "Could not add Change Request Hisotry for the event "
                                            + wizardData.getEventId()
                                            + "\n"
                                            + changeReqHist.getMessage());
                            return wizardData;
                        }

                        try {
                            ChangeRequestLog changeRequestLogData =
                                    generateChangeRequestLogData(wizardData);
                                        changeRequestLogRepository.save(changeRequestLogData);
                                        logger.info(
                                                "After creating a record in the Change Request Log table. event Id----->"
                                                        + eventId);
                        } catch (Exception changeReqHist) {
                            logger.warn(
                                    "ERROR ADD Event addEvent() changeReq exception "
                                            + changeReqHist.getMessage());
                            //							changeReqHist.printStackTrace();
                            wizardData.setServerError(
                                    "Could not add Change Request  for the event "
                                            + wizardData.getEventId()
                                            + "\n"
                                            + changeReqHist.getMessage());
                            return wizardData;
                        }
                    }
                }
            }
        } catch (Exception chRequest) {
            logger.warn(
                    "ERROR ADD Event addEvent() chRequest exception "
                            + chRequest.getMessage());
            //			chRequest.printStackTrace();
            wizardData.setServerError(
                    "Could not add Change Request for the event "
                            + wizardData.getEventId()
                            + " "
                            + chRequest.getMessage());
            return wizardData;
        }

        //the following is to add Reporting categories edited by the client.
        try {
            if (events != null
                    && wizardData.getReportingCategoriesKeys() != null
                    && wizardData.getReportingCategoriesKeys().size() > 0) {
                boolean resultFromBean = false;
                java.sql.Timestamp createdDateTime = wizardData.getCreatedDateTime();
                try {
                    for (int rep = 0;
                         rep < wizardData.getReportingCategoriesKeys().size();
                         rep++) {

                        ReportCategoriesKeyValueData data =
                                (ReportCategoriesKeyValueData) wizardData
                                        .getReportingCategoriesKeys()
                                        .get(rep);

                        data.setEventId(wizardData.getEventId());

                        boolean modified = data.getIsModified();

                        if (modified) {
                            resultFromBean = setReportingCategories(data, createdDateTime);
                        }
                    }
                } catch (Exception exec) {
                    logger.warn(
                            "ERROR ADD Event addEvent() repcategories exec exception "
                                    + exec.getMessage());
                }
            }
        } catch (Exception repCategories) {
            logger.warn(
                    "ERROR ADD Event addEvent() repCategories exception "
                            + repCategories.getMessage());
            wizardData.setServerError(
                    "Could not add Reporting Categories for the event "
                            + wizardData.getEventId()
                            + "\n"
                            + repCategories.getMessage());
            return wizardData;
        }

        //the following is to add a Linked Discrepancy from the client
        try {
            if (events != null
                    && wizardData.getDiscrepancyList() != null
                    && wizardData.getDiscrepancyList().size() > 0) {
                boolean result = false;

                try {
                    for (int disc = 0;
                         disc < wizardData.getDiscrepancyList().size();
                         disc++) {
                        EventDiscrepancyListData data =
                                (EventDiscrepancyListData) wizardData
                                        .getDiscrepancyList()
                                        .get(
                                                disc);

                        data.setEventId(wizardData.getEventId());

                        boolean modified = data.isModified();
                        boolean linkedDiscrepancy = data.isLink();

                        data.setEventType(wizardData.getEventType());

                        if (modified) {
                            if (linkedDiscrepancy) {
                                result = addEventDiscrepancyData(data);
                            }
                        }
                    }
                } catch (Exception exec) {
                    logger.warn(
                            "ERROR ADD Event addEvent() Discrepancy exec exception "
                                    + exec.getMessage());
                    wizardData.setServerError(
                            "Could not add Linked Discrepancies for the event "
                                    + wizardData.getEventId()
                                    + " "
                                    + exec.getMessage());
                    return wizardData;
                }
            }
        } catch (Exception discrepnacy) {
            logger.warn(
                    "ERROR ADD Event addEvent() Discrepancy exception "
                            + discrepnacy.getMessage());
        }

        //The following is to add the DOA Details to the EVENT_DOA Table.
        try {
            if (events != null && wizardData.isDOAEvent()) {

                boolean doaAdded = false;

                try {
                    doaAdded = addDOAEvent(wizardData);
                } catch (Exception e) {
                    logger.warn(
                            "ERROR Add Event adding doa event ---- " + e.getMessage());
                }
            }
        } catch (Exception doa) {
            logger.warn(
                    "ERROR ADD Event addEvent() doa exception " + doa.getMessage());
        }

        //the following is to Start a NIW Timer started by the client.
        try {
            if (events != null && wizardData.getTimerId() != null) {
                if (wizardData.getTimerStartDateTime() != null) {
                    boolean resultFromBean = false;
                    String strEventId = "" + wizardData.getEventId();
                    String strTimerId = wizardData.getTimerId();
                    String timerStartDateTime = wizardData.getTimerStartDateTime();

                    logger.info(
                            "before starting the NIW timer for Event Id "
                                    + wizardData.getEventId()
                                    + " and Timer id "
                                    + wizardData.getTimerId());
                    resultFromBean =
                            startNIWTimer(
                                    strEventId,
                                    strTimerId,
                                    timerStartDateTime,
                                    wizardData.getCreatedDateTime());
                    logger.info("Timer Id Started >>>>>>" + resultFromBean);
                }
            }
        } catch (Exception timers) {
            logger.warn(
                    "ERROR ADD Event addEvent() timers exception " + timers.getMessage());
            //			timers.printStackTrace();
            wizardData.setServerError(
                    "Could not Start the Timer"
                            + wizardData.getTimerId()
                            + " for the event "
                            + wizardData.getEventId()
                            + "\n"
                            + timers.getMessage());
            return wizardData;
        }
        //the following is to add a msn from the client
        try {
            if (events != null
                    && wizardData.getMsnData() != null
                    && wizardData.getMsnData().size() > 0) {
                boolean result = false;

                try {
                    for (int msn = 0;
                         msn < wizardData.getMsnData().size();
                         msn++) {
                        EventMsns data =
                                (EventMsns) wizardData
                                        .getMsnData()
                                        .get(msn);
                        data.setEventId(Long.valueOf(wizardData.getEventId()));
                        if(data!=null)
                        {
                            addMsn(data);
                        }
                    }
                } catch (Exception exec) {
                    logger.warn(
                            "ERROR ADD Event addEvent() add MSN exception "
                                    + exec.getMessage());
                    wizardData.setServerError(
                            "Could not add MSN for the event "
                                    + wizardData.getEventId()
                                    + " "
                                    + exec.getMessage());
                    return wizardData;
                }
            }
        } catch (Exception msn) {
            logger.warn(
                    "ERROR ADD Event addEvent() Discrepancy exception "
                            + msn.getMessage());
        }
        return wizardData;
    }

    /*################################## END of ADD EVENT ##########################################################################**/

    /**
     * The following updateSuper() is used to Send a Request to update the SUPER.
     * @params WizardEventData wizardData.
     * @return boolean superUpdated.
     */
    public boolean updateSuper(WizardEventData wizardData)
            throws RemoteException {
        boolean isSuperUpdated = false;
        boolean isSuperUpdateRequired = false;

        try {
            isSuperUpdateRequired = superUpdateRequired(wizardData);
        } catch (Exception updateRequired) {
            logger.warn(
                    "ERROR ADD Event updateSuper() isSuperUpdateRequired exception "
                            + updateRequired.getMessage());
        }

    //TODO: Later implement the following code

//        try {
//            if (isSuperUpdateRequired) {
//                String strSuperOmniHost =
//                        ConfigHelper.getProperty("SUPER_OMNI_HOST_NAME");
//                logger.info("strSuperOmniHost >>" + strSuperOmniHost);
//
//                HttpMessage httpMessage =
//                        new HttpMessage(new java.net.URL(strSuperOmniHost));
//                SendBuffer sendBuffer = new SendBuffer();
//                sendBuffer.putValue(
//                        MetsGDIConstants.PARM_COMMAND_TO_EXECUTE,
//                        new Integer(MetsGDIConstants.TRANS_UPDT_OPSTATUS));
//                sendBuffer.putValue(
//                        MetsGDIConstants.PARM_STATION,
//                        wizardData.getStation());
//                sendBuffer.putValue(MetsGDIConstants.PARM_NAME, wizardData.getACN());
//                sendBuffer.putValue(
//                        MetsGDIConstants.PARM_EMPLOYEE_NUMBER,
//                        wizardData.getUserId());
//
//                if (wizardData.getEticDateTime() == null
//                        && wizardData.getEticInfo() != null) {
//                    logger.info("ETIC TEXT sending to Super >>" + wizardData.getEticInfo());
//                    sendBuffer.putValue(
//                            MetsGDIConstants.PARM_ETIC_TEXT,
//                            wizardData.getEticInfo());
//                }
//                if (wizardData.getEticDateTime() != null
//                        && wizardData.getEticInfo() != null) {
//                    String eticText =
//                            ETICHelper.getETICFormat(
//                                    wizardData.getEticDateTime(),
//                                    wizardData.getEticInfo());
//                    logger.info("ETIC TEXT sending to Super >>" + eticText);
//                    sendBuffer.putValue(MetsGDIConstants.PARM_ETIC_TEXT, eticText);
//                }
//
//                if (wizardData.getEticDateTime() != null
//                        && wizardData.getEticInfo() == null) {
//                    logger.info(
//                            "ETIC Date  sending to Super ETICHelper.getETICGregorianCalendar(wizardData.getEticDateTime()) >>"
//                                    + ETICHelper.getETICGregorianCalendar(
//                                    wizardData.getEticDateTime()));
//                    sendBuffer.putValue(
//                            MetsGDIConstants.PARM_ETIC_DATE,
//                            ETICHelper.getETICGregorianCalendar(wizardData.getEticDateTime()));
//                }
//
//                sendBuffer.putValue(
//                        MetsGDIConstants.PARM_OPERATIONAL_STATUS,
//                        wizardData.getStatus());
//                sendBuffer.putValue(
//                        MetsGDIConstants.PARM_GENERAL_COMMENTS,
//                        wizardData.getEticComment());
//
//                logger.info("before Requesting for Super Update >>>> ");
//                Object obj = httpMessage.execute(sendBuffer);
//                logger.info(" After super server is called");
//
//                ReceiveBuffer receiveBuffer = (ReceiveBuffer) obj;
//
//                char gdiReturnCode = receiveBuffer.getReturnCode();
//                logger.info("gdiReturnCode...." + gdiReturnCode);
//                if (gdiReturnCode == 'E') {
//                    logger.info(
//                            "Error Text from GDI Server...." + receiveBuffer.getErrorText());
//                }
//
//                Boolean _superUpdated =
//                        (Boolean) receiveBuffer.get(MetsGDIConstants.RET_RESULTS);
//
//                isSuperUpdated = _superUpdated.booleanValue();
//            } else {
//				/*As SUPER_EQUIPMENT data and Change Request Data matches, Auto Confirmation should take place.
//				Update Change Request Table, Events table and insert a record in TF_Notes table.**/
//
//                isSuperUpdated = updateMetsDataRecords(wizardData);
//                logger.info("is Auto Confirmation DONE >>" + isSuperUpdated);
//            }
//        } catch (Exception superUpdate) {
//            logger.warn(
//                    "ERROR ADD Event updateSuper() superUpdate exception "
//                            + superUpdate.getMessage());
//        }
        logger.info("Requested for Super Update >>>> " + isSuperUpdated);
        return isSuperUpdated;
    }

    private boolean superUpdateRequired(WizardEventData wizardData)
            throws RemoteException {
        logger.info("== compareSuperEquipmentData().. ==>");
        boolean updateRequired = false;

        java.sql.Timestamp eticDateTime = null, eticDateTimeStamp = null;
        String strEticText = "", strOperationalStatus = "", strComment = "";
        String strEticDateTime = wizardData.getEticDateTime();
        String strEticTextToSuper = "";

        if (strEticDateTime != null) {
            eticDateTimeStamp =
                    ServerDateHelper.getConvertedTimestamp(strEticDateTime);
        }

        if (wizardData.getEticDateTime() == null
                && wizardData.getEticInfo() != null) {
            strEticTextToSuper = wizardData.getEticInfo();
        }
        if (wizardData.getEticDateTime() != null
                && wizardData.getEticInfo() != null) {
            strEticTextToSuper =
                    ETICHelper.getETICFormat(
                            wizardData.getEticDateTime(),
                            wizardData.getEticInfo());
        }else if (wizardData.getEticDateTime() != null
                && wizardData.getEticInfo() == null){
            strEticTextToSuper =
                    ETICHelper.getETICFormat(
                            wizardData.getEticDateTime(),
                            "");
        }
        try {
            String data=superEquipmentRepository.getSuperUpdateRequiredDetails(wizardData.getACN());
            String[] dataInfo = data.split(",");
            strEticText = dataInfo[0];
            strOperationalStatus = dataInfo[1];
            strComment = dataInfo[2];
        } catch (Exception equipment) {
            logger.warn(
                    "ERROR ADD Event superUpdateRequired() equipment exception "
                            + equipment.getMessage());
            throw new RemoteException(equipment.getMessage());
        }

        logger.info(" Super_Equipment >> eticDateTime.." + eticDateTime);
        logger.info(" Super_Equipment >> strEticText.." + strEticText);
        logger.info(
                " Super_Equipment >> strOperationalStatus.." + strOperationalStatus);
        logger.info(" Super_Equipment >> strComment.." + strComment);
        logger.info(" ");
        logger.info(" wizardData >> strEticDateTime.." + strEticDateTime);
        logger.info(" wizardData >> strEticTextToSuper.." + strEticTextToSuper);
        logger.info(" wizardData >> getStatus.." + wizardData.getStatus());
        logger.info(
                " wizardData >> wizardData.getEticComment().."
                        + wizardData.getEticComment());

        if (strOperationalStatus != null
                && strOperationalStatus.trim().length() > 0) {
            if (!strOperationalStatus.equalsIgnoreCase(wizardData.getStatus())) {
                logger.info(" .. 1 ..");
                logger.info(" As Status does not match SUPER_EQUIPMENT update is required");
                updateRequired = true;
            }

            if (strComment != null
                    && !(strComment.trim().equals(wizardData.getEticComment()))) {
                logger.info(" .. 2 ..");
                logger.info(" As Comment does not match SUPER_EQUIPMENT update is required");
                updateRequired = true;
            }

            if (eticDateTime != null && !(eticDateTime.equals(eticDateTimeStamp))) {
                logger.info(" .. 3 ..");
                logger.info(" As eticDateTime does not match SUPER_EQUIPMENT update is required");
                updateRequired = true;
                //			}else if(eticDateTime==null){

                //           Changed the above condition because there can be conditions where
                //           the user does not change the date but changes the info to a 2 char
                //           value e.g. "WA"
            } else {
                if (strEticText != null
                        && !(strEticText.trim().equalsIgnoreCase(strEticTextToSuper))) {
                    logger.info(" .. 4 ..");
                    logger.info(" As strEticTextToSuper does not match SUPER_EQUIPMENT update is required");
                    updateRequired = true;
                } else if (
                        strEticText == null
                                && (strEticTextToSuper != null
                                && strEticTextToSuper.trim().length() > 0)) {
                    logger.info(" .. 5 ..");
                    logger.info(" As strEticText is null && strEticTextToSuper does not match SUPER_EQUIPMENT update is required");
                    updateRequired = true;
                }
            }
        }else if (strOperationalStatus == null
                || strOperationalStatus.trim().length() == 0){
            //new condition added so the update can be sent to super even if a record is not found.
            logger.info(" .. 6 ..");
            logger.info(" As no record found in SUPER_EQUIPMENT update is required");
            updateRequired = true;
        }

        logger.info(" SUPER_EQUIPMENT update is required --> " + updateRequired);

        return updateRequired;
    }

    /**
     private method to generate the Events Data to be inserted in to the Events table.
     @ params	WizardEventData wizardData, java.sql.Timestamp eticDateTimeStamp, java.sql.Timestamp startDateTimeStamp
     @ return	EventsData
     */
    private Events generateEventData(
            WizardEventData wizardData,
            java.sql.Timestamp eticDateTimeStamp,
            java.sql.Timestamp startDateTimeStamp) {
        Events eventsData = null;
        try {
            eventsData = new Events();

            eventsData.setEventId(wizardData.getEventId());
            eventsData.setType(wizardData.getEventType());

            if (startDateTimeStamp != null) {
                eventsData.setStartDateTime(startDateTimeStamp);
            } else {
                eventsData.setStartDateTime(wizardData.getCreatedDateTime());
            }

            eventsData.setAcn(wizardData.getACN());
            eventsData.setFleetDesc(wizardData.getAircraftType());
            eventsData.setStation(wizardData.getStation());
            eventsData.setLastUpdateDateTime(wizardData.getCreatedDateTime());
            eventsData.setLastUpdatedBy(wizardData.getUserId());
            eventsData.setCreatedDateTime(wizardData.getCreatedDateTime());
            eventsData.setCreatedByGroupId(wizardData.getGroupId());
            eventsData.setCreatedBy(wizardData.getUserId());
            eventsData.setPrimaryContact(wizardData.getContactInfoContact());
            eventsData.setEticNumber(0);
            //as the record is being created for first time ETIC_NUM == 0
            eventsData.setCancelled("N");
            eventsData.setActiveEvent("Y");

            /*
             * Changes made in Database to store responsible manager Id
             * */
            eventsData.setFedexIdNumber(wizardData.getResMgrId());
            eventsData.setMemDeskContact(wizardData.getMemDeskContact());

            if (!wizardData.getEventType().trim().equals("NOTE"))
                eventsData.setStatus(wizardData.getStatus());

            if (eticDateTimeStamp != null)
                eventsData.setEticDateTime(eticDateTimeStamp);

            if (wizardData.getEventType().trim().equals("DOA")) {
                try {
                    String strEticInfo = "";
                    strEticInfo=eventDictRepository.getSuperEtic(wizardData.getEventType());
                    eventsData.setEticText(strEticInfo);
                    wizardData.setEticInfo(strEticInfo);
                } catch (Exception eventOwner) {
                    logger.warn(
                            "ERROR ADD Event generateEventData() eventOwner exception "
                                    + eventOwner.getMessage());
                }
            } else {
                if (wizardData.getEticInfo() != null)
                    eventsData.setEticText(wizardData.getEticInfo().trim());
            }

            if (wizardData.getEticComment() != null)
                eventsData.setCurComment(wizardData.getEticComment().trim());

            if (wizardData.getOST() != null)
                eventsData.setOst(wizardData.getOST().trim());

            if(wizardData.getEticRsnCd() != null) {
                eventsData.setEticRsnCd(wizardData.getEticRsnCd().trim());
            }

            if(wizardData.getEticRsnComment() != null) {
                eventsData.setEticRsnComment(wizardData.getEticRsnComment().trim());
            }

            if (eticDateTimeStamp != null && wizardData.getEticInfo() == null)
                eventsData.setEticInitial(eticDateTimeStamp);

            String strGroupId = null;
            /*To retreive the Group_id for the AC_Owner based on the ContactInfo Onwer selected.**/
            try {
                strGroupId=groupDictRepository.getGroupIdByGroupTitle(wizardData.getContactInfoOwner());
                //insert the value only if not null;
                if (strGroupId != null) {
                    eventsData.setAcOwnerGroupId(strGroupId);
                    //re-initialize to null
                    strGroupId = null;
                }
            } catch (Exception acOwner) {
                logger.warn(
                        "ERROR ADD Event generateEventData() acOwner exception "
                                + acOwner.getMessage());
            }

            //if the Event_Type is OOS then insert the Group_Id with Access_Level = 90 else Group_Id of the user.
            if (wizardData.getEventType().trim().equalsIgnoreCase("OOS")) {
                try {
                    strGroupId=groupDictRepository.getGroupId(wizardData.getAccessLevel());
                    //insert the value only if not null;
                    if (strGroupId != null)
                        eventsData.setEventOwnerGroupId(strGroupId);
                } catch (Exception eventOwner) {
                    logger.warn(
                            "ERROR ADD Event generateEventData() eventOwner exception "
                                    + eventOwner.getMessage());
                }
            } else {
                eventsData.setEventOwnerGroupId(wizardData.getGroupId());
            }
        } catch (Exception generate) {
            logger.warn(
                    "ERROR ADD Event generateEventData() generate exception "
                            + generate.getMessage());
            eventsData = null;
        }
        return eventsData;
    }

    /**
     The following method is a private/sub method to support the addEvent method, to add a affected Outbound Flight detail to the database.
     @ params WizardData Object
     @ return boolean result
     */
    private boolean addAffectedFlight(WizardEventData wizardData) {
        logger.info("....in the addAffectedFlight() method to add Affected Flight");
        boolean result = false;
        try {
                String strFlightNumber = "",
                        strFlightLegNumber = "",
                        strFlightACN = "",
                        strFlightDestination = "",
                        strFlightStatus = "",
                        strFlightType = "",
                        strTotalDelay = "",
                        strFlightOrigniation = "";

                List<Object> ListFlightData = new ArrayList<>(),
                        delayTimerData = new ArrayList<>(),
                        delayCodeData = new ArrayList<>();
                List<Object> forteResultsList = new ArrayList<>();

                java.sql.Timestamp schDepartureTime = null,
                        actDepartureTime = null,
                        schArrivalTime = null,
                        actArrivalTime = null;
                Timestamp dateFlightDate = null;

                if (wizardData.getAffectedFlightNumber() != null
                        && wizardData.getAffectedFlightDate() != null
                        && wizardData.getAffectedFlightDate().trim().length() > 0
                        && wizardData.getAffectedFlightLegNumber() != null) {
                    try {
                        String strLookupDate =
                                ServerDateHelper.getLookUpFormat(
                                        wizardData.getAffectedFlightDate());

                       strFlightNumber= wizardData.getAffectedFlightNumber();
                       strFlightLegNumber= wizardData.getAffectedFlightLegNumber();
                        forteResultsList=flightDetailsService.getFlightDetails(strFlightNumber, strFlightLegNumber, strLookupDate);

                            if (forteResultsList != null
                                    && forteResultsList.size() == 3) {
                                ListFlightData = (List<Object>) forteResultsList.get(0);
                                delayTimerData = (List<Object>) forteResultsList.get(1);
                                delayCodeData = (List<Object>) forteResultsList.get(2);
                            }

                        logger.info("ListFlightData size ==> " + ListFlightData.size());

                        if (ListFlightData != null && ListFlightData.size() >= 12) {
                            strFlightNumber = (String) ListFlightData.get(0);
                            dateFlightDate = Timestamp.valueOf(ServerDateHelper.getTimestampFormat(String.valueOf((Timestamp)(ListFlightData.get(1)))));
                            strFlightLegNumber = (String) ListFlightData.get(2);
                            strFlightACN = (String) ListFlightData.get(3);
                            strFlightDestination = (String) ListFlightData.get(4);
                            strFlightStatus = (String) ListFlightData.get(5);
                            strFlightType = (String) ListFlightData.get(6);
                            schDepartureTime =
                                    Timestamp.valueOf(ServerDateHelper.getTimestampFormat(String.valueOf((Timestamp)(ListFlightData.get(7)))));
                            actDepartureTime =
                                    Timestamp.valueOf(ServerDateHelper.getTimestampFormat(String.valueOf((Timestamp)(ListFlightData.get(8)))));
                            strTotalDelay = (String) ListFlightData.get(9);

                            strFlightOrigniation = (String) ListFlightData.get(10);
                            schArrivalTime =
                                    Timestamp.valueOf(ServerDateHelper.getTimestampFormat(String.valueOf((Timestamp)(ListFlightData.get(11)))));
                            actArrivalTime =
                                    Timestamp.valueOf(ServerDateHelper.getTimestampFormat(String.valueOf((Timestamp)(ListFlightData.get(12)))));
                        }
                    } catch (Exception e) {
                        logger.warn(
                                "ERROR ADD Event addAffectedFlight() exception "
                                        + e.getMessage());
                    }

                    if (strFlightNumber == null || strFlightNumber.trim().length() == 0)
                        strFlightNumber = wizardData.getAffectedFlightNumber();

                    if (strFlightLegNumber == null
                            || strFlightLegNumber.trim().length() == 0)
                        strFlightLegNumber = wizardData.getAffectedFlightLegNumber();

                    if (dateFlightDate == null)
                        dateFlightDate = Timestamp.valueOf(ServerDateHelper.getTimestampFormat(wizardData.getAffectedFlightDate()));

                    try {
                        if (strFlightNumber != null
                                && strFlightNumber.trim().length() > 0) {
                            EventFlightInfo newData = new EventFlightInfo();

                            EventFltInfoPk pk=new EventFltInfoPk();
                            pk.setEventId(wizardData.getEventId());
                            pk.setFlightFlag('A');

                            newData.setEventFltInfoPk(pk);
                            newData.setFlightNumber(strFlightNumber);

                            if (dateFlightDate != null)
                                newData.setFlightDate(dateFlightDate);

                            newData.setFlightLeg(strFlightLegNumber);
                            newData.setFlightACN(strFlightACN);
                            newData.setFlightDestination(strFlightDestination);
                            newData.setFlightStatus(strFlightStatus);
                            newData.setFlightType(strFlightType);

                            if (schDepartureTime != null)
                                newData.setFlightSchedDeptDateTime(schDepartureTime);

                            if (actDepartureTime != null)
                                newData.setFlightActualDeptDateTime(actDepartureTime);

                            if (schArrivalTime != null)
                                newData.setFlightSchedArrivalDateTime(schArrivalTime);

                            if (actArrivalTime != null)
                                newData.setFlightActualArrivalDateTime(actArrivalTime);

                            newData.setFlightOrigin(strFlightOrigniation);

                            newData.setFlightType("A");

                            if (strTotalDelay != null && strTotalDelay.trim().length() > 0) {
                                int totalDelayTime = Integer.parseInt(strTotalDelay.trim());
                                newData.setFlightTotalDelay(totalDelayTime);
                            }
                            flightInfoRepository.save(newData);
                            logger.info("After creating a record in the Event_Flight_Info table..");
                        }
                        result = true;
                    } catch (Exception e) {
                        logger.warn(
                                "ERROR ADD Event addAffectedFlight() create exception "
                                        + e.getMessage());
                    }

                    /*
					to update the Event_flt_delays table...
					*/
                    try {
                            logger.info("before updating the Event_flt_delays table..");
                            if(delayCodeData!=null) {
                                for (int i = 0; i < delayCodeData.size(); i++) {
                                    EventFlightDelays eventFlightDelay = null;
                                    if (delayCodeData.get(i) != null
                                            && delayTimerData.get(i) != null) {
                                        String tempDelayCode = (String) delayCodeData.get(i);
                                        String tempDelayTime = (String) delayTimerData.get(i);

                                        if (tempDelayCode.trim().length() > 0) {
                                            try {
                                                if (tempDelayTime.trim().length() > 0) {
                                                    logger.info("before updating the existing record in Event_flt_delays table..");
                                                    int delayTime = Integer.parseInt(tempDelayTime);
                                                    eventFlightDelaysRepository.updateDelayTime(wizardData.getEventId(), tempDelayCode, delayTime);
                                                    result = true;

                                                } else if (
                                                        eventFlightDelay == null
                                                                && tempDelayTime.trim().length() > 0) {
                                                    logger.info("before creating a new record in Event_flt_delays table..");
                                                    int delayTime = Integer.parseInt(tempDelayTime);

                                                    EventFlightDelays newData =
                                                            new EventFlightDelays();

                                                    newData.setEventId(wizardData.getEventId());
                                                    newData.setDelayCode(tempDelayCode);
                                                    newData.setDelayTime(delayTime);

                                                    eventFlightDelaysRepository.save(newData);
                                                    logger.info("After creating a new record in Event_flt_delays table..");

                                                    result = true;
                                                }
                                            } catch (Exception ee) {
                                                logger.warn(
                                                        "ERROR ADD Event addAffectedFlight() delay exception "
                                                                + ee.getMessage());
                                            }
                                        } //end of if tempDelayCode.length>0
                                    } //end of if delayCodeData.get(i)!=null
                                } //end of for loop.
                            }
                        
                    } catch (Exception e) {
                        logger.warn(
                                "ERROR ADD Event addAffectedFlight() update exception "
                                        + e.getMessage());
                    }
                } //end of checking the affected flight info !=null
        } catch (Exception flight) {
            logger.warn(
                    "ERROR ADD Event addAffectedFlight() flight exception "
                            + flight.getMessage());
        }
        return result;
    }

    /**
     The following method is a private/sub method to support the addEvent method, to add a Tub File Note to the database
     @ params WizardData Object
     @ return boolean result
     */
    private boolean addTFNotes(WizardEventData wizardData) {
        boolean result = false;
        try {
            //the following is to add a record to the EVENT_TF_NOTES table.
            int noteId = 0;
            int activeEvents = 0;
            int count=0;
            try {
                count=eventTfNotesRepository.getTfNotesCountdByEventId(wizardData.getEventId());
                if(count==0)
                {
                    noteId=0;
                }
                else{
                    noteId=eventTfNotesRepository.getMaxNoteId(wizardData.getEventId());
                }
                noteId = noteId + 1;
                logger.info("after incrementing noteId====== " + noteId);

                activeEvents=eventsRepository.getActiveOOSCount(wizardData.getACN());
                logger.info("activeEvents of event type OOS====== " + activeEvents);
            } catch (Exception n) {
                logger.warn(
                        "ERROR ADD Event addTFNotes() increment note id exception "
                                + n.getMessage());
            }
            if (noteId > 0) {
                String strEticComment = "", strETICForm = "";

                if (wizardData.getEticDateTime() == null
                        && wizardData.getEticInfo() != null) {
                    strETICForm = wizardData.getEticInfo();
                }
                if (wizardData.getEticDateTime() != null
                        && wizardData.getEticInfo() != null) {
                    strETICForm =
                            ETICHelper.getETICFormat(
                                    wizardData.getEticDateTime(),
                                    wizardData.getEticInfo());
                }
                if (wizardData.getEticDateTime() != null
                        && wizardData.getEticInfo() == null) {
                    strETICForm =
                            ETICHelper.getETICFormat(wizardData.getEticDateTime(), null);
                }

                if (wizardData.getEticComment() != null
                        && wizardData.getEticComment().trim().length() > 0)
                    strEticComment = ", " + wizardData.getEticComment();

                if (strETICForm != null && strETICForm.trim().length() > 0)
                    strETICForm = ", " + strETICForm;

                EventTfNotes eventTFNotesData = new EventTfNotes();
                EventTfNotesPk eventTfNotesPk = new EventTfNotesPk();
                eventTfNotesPk.setEventId(wizardData.getEventId());
                eventTfNotesPk.setTfDtTm(wizardData.getCreatedDateTime());

                eventTFNotesData.setEventTfNotesPk(eventTfNotesPk);
                eventTFNotesData.setLastUpdateDtTm(wizardData.getCreatedDateTime());
                eventTFNotesData.setEmpNum(wizardData.getUserId());
                eventTFNotesData.setEmpName(wizardData.getEmployeeName());
                eventTFNotesData.setEmpDepartment(wizardData.getEmpDepartment());
                eventTFNotesData.setEditedFlag("N");
                eventTFNotesData.setNoteId(noteId);

                if (wizardData.getEventType().trim().equals("TRK")) {
                    eventTFNotesData.setTfNote("New TRK " + wizardData.getEticComment());
                    eventTFNotesData.setNoteType(5);
                    eventTFNotesData.setChangeType(0);
                } else if (wizardData.getEventType().trim().equals("OOS")) {
                    eventTFNotesData.setChangeType(15);
                    if (wizardData.getAccessLevel().trim().equals("90")) {
                        eventTFNotesData.setTfNote(
                                "Posted New OOS: "
                                        + wizardData.getStatus()
                                        + strETICForm
                                        + strEticComment);
                        eventTFNotesData.setNoteType(3);
                    } else {
                        eventTFNotesData.setTfNote(
                                "Submitted New OOS: "
                                        + wizardData.getStatus()
                                        + strETICForm
                                        + strEticComment);
                        eventTFNotesData.setNoteType(1);
                    }
                } else if (wizardData.getEventType().trim().equals("DOA")) {
                    if (activeEvents == 0) {
                        eventTFNotesData.setTfNote(
                                "Posted New DOA: " + wizardData.getStatus() + strEticComment);
                        eventTFNotesData.setNoteType(3);
                        eventTFNotesData.setChangeType(15);
                    } else {
                        eventTFNotesData.setTfNote("New DOA: " + strEticComment);
                        eventTFNotesData.setNoteType(5);
                        eventTFNotesData.setChangeType(0);
                    }
                } else if (wizardData.getEventType().trim().equals("NOTE")) {
                    eventTFNotesData.setTfNote("New NOTE");
                    eventTFNotesData.setNoteType(5);
                    eventTFNotesData.setChangeType(0);
                }

                eventTFNotesData.setLastUpdateDtTm(wizardData.getCreatedDateTime());

                try {
                    if (eventTFNotesData != null) {
                        eventTfNotesRepository.save(eventTFNotesData);
                        logger.info("Added TF_Notes for event Id " + wizardData.getEventId() );
                        result = true;
                    }
                } catch (Exception tfNotes) {
                    logger.warn(
                            "ERROR ADD Event addTFNotes() create tfNotes exception "
                                    + tfNotes.getMessage());
                }

                //the following is to add the TF_Notes that the Client had added.
                if (wizardData.getTfNotesList() != null
                        && wizardData.getTfNotesList().size() > 0) {
                    for (int tf = 0; tf < wizardData.getTfNotesList().size(); tf++) {
                        //creating a new createdTimeStamp as the TFDateTime is the primary Key to the table.
                        java.sql.Timestamp tfNotesDateTime =
                                ServerDateHelper.getTimeStamp();
                        logger.info("*********************");
                        logger.info("tfNotesDateTime " + tfNotesDateTime);
                        logger.info(
                                "wizardData.getCreatedDateTime"
                                        + wizardData.getCreatedDateTime());
                        logger.info("noteId " + noteId);

                        if (tfNotesDateTime.equals(wizardData.getCreatedDateTime())) {
                            java.util.Date currentDate = ServerDateHelper.getDate_UTC();
                            tfNotesDateTime =
                                    new java.sql.Timestamp(currentDate.getTime() + 1000);
                        }

                        logger.info("tfNotesDateTime " + tfNotesDateTime);
                        logger.info(
                                "wizardData.getCreatedDateTime"
                                        + wizardData.getCreatedDateTime());

                        String clientData =
                                 wizardData.getTfNotesList().get(tf);

                        EventTfNotes eventTFNotesData1 = new EventTfNotes();

                        EventTfNotesPk eventTfNotesPk1 = new EventTfNotesPk();
                        eventTfNotesPk1.setEventId(wizardData.getEventId());
                        java.util.Date currentDate = ServerDateHelper.getDate_UTC();
                        tfNotesDateTime =
                                new java.sql.Timestamp(currentDate.getTime() + 1000);
                        eventTfNotesPk1.setTfDtTm(tfNotesDateTime);
                        eventTFNotesData1.setEventTfNotesPk(eventTfNotesPk1);
                        eventTFNotesData1.setLastUpdateDtTm(tfNotesDateTime);
                        eventTFNotesData1.setEmpNum(wizardData.getUserId());
                        eventTFNotesData1.setEmpName(wizardData.getEmployeeName());
                        eventTFNotesData1.setEmpDepartment(wizardData.getEmpDepartment());
                        eventTFNotesData1.setEditedFlag("N");
                        eventTFNotesData1.setNoteId(noteId+1);
                        eventTFNotesData1.setTfNote(clientData);
                        eventTFNotesData1.setNoteType(0);

                        if (wizardData.getEventType().trim().equals("OOS")) {
                            eventTFNotesData1.setChangeType(15);
                        } else if (
                                wizardData.getEventType().trim().equals("TRK")
                                        || wizardData.getEventType().trim().equals("NOTE")) {
                            eventTFNotesData1.setChangeType(0);
                        } else if (wizardData.getEventType().trim().equals("DOA")) {
                            if (activeEvents == 0) {
                                eventTFNotesData1.setChangeType(15);
                            } else {
                                eventTFNotesData1.setChangeType(0);
                            }
                        }

                        eventTFNotesData1.setLastUpdateDtTm(
                                wizardData.getCreatedDateTime());

                        try {
                            if (eventTFNotesData1 != null) {
                                eventTfNotesRepository.save(eventTFNotesData1);
                                logger.info("Added TF_Notes that the Client had added");
                                result = true;
                            }
                        } catch (Exception tfNotes) {
                            logger.warn(
                                    "ERROR ADD Event addTFNotes() create user notes exception "
                                            + tfNotes.getMessage());
                        }
                    } //end of for tfNotesVector
                } //end of if tfNotesVector !=null
            } //end of if noteId >0
        } catch (Exception notes) {
            logger.warn(
                    "ERROR ADD Event addTFNotes() create notes exception "
                            + notes.getMessage());
        }
        return result;
    }

    /**
     The following method is a private/sub method to support the addEvent method, to add a affected Inbound Flight detail to the database.
     @ params WizardData Object
     @ return boolean result
     */
    private boolean addInboundFlight(WizardEventData wizardData) {
        logger.info("....in the addInboundFlight() method to add Inbound Flight");
        boolean result = false;
        try {
            //the following is to insert a record for Inbound Flight data if the User has entered the Affected Flight Information.
            //retrieve the Flight Information from Forte_Leg and insert the details into Event_Flt_Info table.
                boolean insertedRecord = false;

                String strFlightNumber = "",
                        strFlightLegNumber = "",
                        strFlightACN = "",
                        strFlightDestination = "",
                        strFlightStatus = "",
                        strFlightType = "",
                        strTotalDelay = "",
                        strFlightOrigniation = "";

            List<Object> ListFlightData = new ArrayList<>(),
                    delayTimerData = new ArrayList<>(),
                    delayCodeData = new ArrayList<>();
            List<Object> forteResultsList = new ArrayList<>();

                java.sql.Timestamp schDepartureTime = null,
                        actDepartureTime = null,
                        schArrivalTime = null,
                        actArrivalTime = null;
                java.sql.Timestamp dateFlightDate = null;

                if (wizardData.getInboundFlightNumber() != null
                        && wizardData.getInboundFlightDate() != null
                        && wizardData.getInboundFlightDate().trim().length() > 0
                        && wizardData.getInboundFlightLegNumber() != null) {
                    try {
                        String strLookupDate =
                                ServerDateHelper.getLookUpFormat(
                                        wizardData.getInboundFlightDate());
                        forteResultsList=flightDetailsService.getFlightDetails(wizardData.getInboundFlightNumber(), wizardData.getInboundFlightLegNumber(), strLookupDate);
                        if (forteResultsList != null
                                && forteResultsList.size() == 3) {
                            ListFlightData = (List<Object>) forteResultsList.get(0);
                            delayTimerData = (List<Object>) forteResultsList.get(1);
                            delayCodeData = (List<Object>) forteResultsList.get(2);
                        }
                        logger.info("ListFlightData size ==> " + ListFlightData.size());

                        if (ListFlightData != null && ListFlightData.size() >= 12) {
                            strFlightNumber = (String) ListFlightData.get(0);
                            dateFlightDate = Timestamp.valueOf(ServerDateHelper.getTimestampFormat(String.valueOf((Timestamp)(ListFlightData.get(1)))));
                            strFlightLegNumber = (String) ListFlightData.get(2);
                            strFlightACN = (String) ListFlightData.get(3);
                            strFlightDestination = (String) ListFlightData.get(4);
                            strFlightStatus = (String) ListFlightData.get(5);
                            strFlightType = (String) ListFlightData.get(6);
                            schDepartureTime =
                                    Timestamp.valueOf(ServerDateHelper.getTimestampFormat(String.valueOf((Timestamp)(ListFlightData.get(7)))));
                            actDepartureTime =
                                    Timestamp.valueOf(ServerDateHelper.getTimestampFormat(String.valueOf((Timestamp)(ListFlightData.get(8)))));
                            strTotalDelay = (String) ListFlightData.get(9);

                            strFlightOrigniation = (String) ListFlightData.get(10);
                            schArrivalTime =
                                    Timestamp.valueOf(ServerDateHelper.getTimestampFormat(String.valueOf((Timestamp)(ListFlightData.get(11)))));
                            actArrivalTime =
                                    Timestamp.valueOf(ServerDateHelper.getTimestampFormat(String.valueOf((Timestamp)(ListFlightData.get(12)))));
                        }
                    } catch (Exception e) {
                        logger.warn(
                                "ERROR ADD Event addInboundFlight() exception " + e.getMessage());
                    }

                    try {
                        if (strFlightNumber != null
                                && strFlightNumber.trim().length() > 0) {
                            EventFlightInfo newData = new EventFlightInfo();

                            newData.getEventFltInfoPk().setEventId(wizardData.getEventId());
                            newData.setFlightNumber(strFlightNumber);

                            if (dateFlightDate != null)
                                newData.setFlightDate(dateFlightDate);

                            newData.setFlightLeg(strFlightLegNumber);
                            newData.setFlightACN(strFlightACN);
                            newData.setFlightDestination(strFlightDestination);
                            newData.setFlightStatus(strFlightStatus);
                            newData.setFlightType(strFlightType);

                            if (schDepartureTime != null)
                                newData.setFlightSchedDeptDateTime(schDepartureTime);

                            if (actDepartureTime != null)
                                newData.setFlightActualDeptDateTime(actDepartureTime);

                            if (schArrivalTime != null)
                                newData.setFlightSchedArrivalDateTime(schArrivalTime);

                            if (actArrivalTime != null)
                                newData.setFlightActualArrivalDateTime(actArrivalTime);

                            newData.setFlightOrigin(strFlightOrigniation);

                            newData.setFlightType("I");

                            if (strTotalDelay != null && strTotalDelay.trim().length() > 0) {
                                int totalDelayTime = Integer.parseInt(strTotalDelay.trim());
                                newData.setFlightTotalDelay(totalDelayTime);
                            }
                            flightInfoRepository.save(newData);
                            logger.info("== addInboundFlight().. data saved for event Id==>"+wizardData.getEventId());
                        }

                        result = true;

                    } catch (Exception e) {
                        logger.warn(
                                "ERROR ADD Event addInboundFlight() create exception "
                                        + e.getMessage());
                    }
                    /*	####################################
					Do not insert Delay records for inbound flights changed 04/22/03
					####################################
					to update the Event_flt_delays table...*/
                }
        } catch (Exception flight) {
            logger.warn(
                    "ERROR ADD Event addInboundFlight() flight exception "
                            + flight.getMessage());
        }

        return result;
    }

    /**
     The following method is a private/sub method to support the addEvent method, to start a NIW Timer for the Event
     @ params String EventId, TimerId, TimerStartDateTime.
     @ return boolean result.
     */
    private boolean startNIWTimer(
            String eventId,
            String timerId,
            String timerStartDateTime,
            java.sql.Timestamp createdDateTime) {
        boolean result = false;
            try {
                java.sql.Timestamp startDateTimeStamp = null;

                if (timerStartDateTime != null) {
                    startDateTimeStamp =
                            ServerDateHelper.getConvertedTimestamp(timerStartDateTime);
                    logger.info("startDateTimeStamp >>>>" + startDateTimeStamp);
                }

                if (startDateTimeStamp != null) {
                    EventTimers eventData = new EventTimers();

                    EventTimersPk pk= new EventTimersPk();
                    pk.setEventId(Integer.parseInt(eventId));
                    pk.setCreationDtTm(createdDateTime);
                    eventData.setEventTimersPk(pk);
                    eventData.setTimerId(timerId);
                    eventData.setTimerStartDtTm(startDateTimeStamp);
                    eventData.setLastUpdateDtTm(createdDateTime);

                    eventTimersRepository.save(eventData);
                    logger.info("== startNIWTimer().. data saved for event Id==>"+eventId);
                    result = true;
                }
            } catch (Exception ee) {
                logger.warn(
                        "ERROR ADD Event startNIWTimer() create exception "
                                + ee.getMessage());
                result = false;
            }
        return result;
    }

    /**
     The following method is a private/sub method to support the addEvent method, to add a Reporting Category to the database.
     @ params ReportCategoriesKeyValueData reportCategoriesKeyValueData.
     @ return boolean result.
     */
    private boolean setReportingCategories(
            ReportCategoriesKeyValueData reportCategoriesKeyValueData,
            java.sql.Timestamp createdDateTime)
            throws RemoteException {
        logger.info("== setReportingCategories().. ==>");
        boolean result = false;

        int eventId = 0;
        String levelOneId = "",
                levelTwoId = "",
                updatedLevelOneId = "",
                updatedLevelTwoId = "";

        eventId = reportCategoriesKeyValueData.getEventId();
        levelOneId = reportCategoriesKeyValueData.getLevelOneId();
        levelTwoId = reportCategoriesKeyValueData.getLevelTwoId();
        updatedLevelOneId = reportCategoriesKeyValueData.getUpdatedLevelOneId();
        updatedLevelTwoId = reportCategoriesKeyValueData.getUpdatedLevelTwoId();
        try {
                EventRepCatg data = new EventRepCatg();

                EventRepCatgPk pk=new EventRepCatgPk();
                pk.setEventId(eventId);
                pk.setLevel1Id(updatedLevelOneId);
                pk.setLevel2Id(updatedLevelTwoId);
                data.setEventRepCatgPk(pk);
                data.setLastUpdatedDtTm(createdDateTime);
                if (updatedLevelTwoId != null && updatedLevelTwoId.trim().length() > 0)
                    eventRepCatgRepository.save(data);
                logger.info("== setReportingCategories().. data saved for event Id==>"+eventId);
                    result = true;
        } catch (Exception update) {
            logger.warn(
                    "ERROR ADD Event setReportingCategories() create exception "
                            + update.getMessage());
        }
        return result;
    }

    /**
     The following method is a private/sub method to support the addEvent method, to add a linked discrepancy to the database.
     @ params EventDiscrepancyListData discrepancyData.
     @ return boolean result.
     */
    private boolean addEventDiscrepancyData(EventDiscrepancyListData discrepancyData)
            throws RemoteException {
        logger.info("in the addEventDiscrepancyListData of the ..........");
        boolean result = false;
        int eventId = 0;
        String ata = "", discNumber = "", eventType = "";
        boolean isDowningItem = false;

        eventId = discrepancyData.getEventId();
        ata = discrepancyData.getAta();
        //to convert back to 4 digit char(eliminate '-' from the string);
        ata = ata.substring(0, 2) + "" + ata.substring(3, 5);
        discNumber = discrepancyData.getNumber();
        eventType = discrepancyData.getEventType();
        isDowningItem = discrepancyData.isDowningItem();

        logger.info("eventId	   =" + eventId);
        logger.info("ata        =" + ata);
        logger.info("discNumber =" + discNumber);
        logger.info("eventType  =" + eventType);
        logger.info("isDowningItem  =" + isDowningItem);
        try {
                // If we're simply updating the discrepancy as a downing item, it may already exist in the DB
                    EventMaxiDisc data = new EventMaxiDisc();
                    EventMaxiDiscPk pk=new EventMaxiDiscPk();
                    pk.setEventId((long) eventId);
                    pk.setAta(ata);
                    pk.setDiscNum(discNumber);
                    data.setEventMaxiDisckPk(pk);
                    data.setType(eventType);

                    eventMaxiDiscRepository.save(data);
                    logger.info("== addEventDiscrepancyData().. data saved for event Id==>"+eventId);
                    result = true;
        } catch (Exception update) {
            logger.warn(
                    "ERROR ADD Event addEventDiscrepancyData() create exception "
                            + update.getMessage());
        }

        if(isDowningItem) {
            try {
                    EventMaxiDwningItm data = new EventMaxiDwningItm();

                    data.setEventId((long) eventId);
                    data.setAta(ata);
                    data.setDiscNum(discNumber);

                    eventMaxiDwningItmRepository.save(data);
                    logger.info("== addEventDiscrepancyData().. data saved for DwningItm for event Id==>"+eventId);
                    result = true;
            } catch (Exception create) {
                logger.warn(
                        " ERROR Event Discrepancies addEventDiscrepancyDowningItemData() create >> "
                                + create.getMessage());
                throw new RemoteException(
                        "Record could not be inserted " + create.getMessage());
            }

        }

        return result;
    }

    /**
     The following method is a private/sub method to support the addEvent method, to add a Reporting Category to the database.
     @ params ReportCategoriesKeyValueData reportCategoriesKeyValueData.
     @ return boolean result.
     */
    private boolean addDOAEvent(WizardEventData wizardData)
            throws RemoteException {
        logger.info("== addDOAEvent().. ==>");
        boolean result = false;
        String checkFleet = "N";

        if (wizardData.isCheckFlightRequired())
            checkFleet = "Y";

        try {
            java.sql.Timestamp doaSQLDate = null;
            java.sql.Timestamp doaETATimeStamp = null;

            logger.info(
                    "== wizardData.getAffectedFlightDate().. ==>"
                            + wizardData.getAffectedFlightDate());
            logger.info(
                    "== wizardData.getEstimatedArrivalDateTime().. ==>"
                            + wizardData.getEstimatedArrivalDateTime());

            if (wizardData.getAffectedFlightDate() != null
                    && wizardData.getAffectedFlightDate().trim().length() > 0) {
                doaSQLDate =
                        Timestamp.valueOf(ServerDateHelper.getTimestampFormat(
                                wizardData.getAffectedFlightDate()));
                logger.info("== doaSQLDate after conversion .. ==>" + doaSQLDate);
            }

            if (wizardData.getEstimatedArrivalDateTime() != null
                    && wizardData.getEstimatedArrivalDateTime().trim().length() > 0) {
                doaETATimeStamp =
                        Timestamp.valueOf(
                                wizardData.getEstimatedArrivalDateTime().replace("T", " "));
                logger.info(
                        "== doaETATimeStamp after conversion .. ==>" + doaETATimeStamp);
            }

            logger.info("== doaSQLDate().. ==>" + doaSQLDate);
                EventDoa data = new EventDoa();

                data.setEventId(wizardData.getEventId());
                data.setCheckFleet(checkFleet);
                data.setDoaFleetNumber(wizardData.getAffectedFlightNumber());
                data.setDoaFleetDate(doaSQLDate);
                data.setDoaFleetLeg(wizardData.getAffectedFlightLegNumber());
                data.setDoaFleetComments(wizardData.getAdditionalDescription());
                data.setLastUpdtDtTm(wizardData.getCreatedDateTime());
                data.setDoaFltDest(wizardData.getDestination());

                if (doaETATimeStamp != null) {
                    data.setDoaFltEta(doaETATimeStamp);
                }

                eventDoaRepository.save(data);
                logger.info("== addDOAEvent().. data saved for event Id==>"+wizardData.getEventId());
                    result = true;
        } catch (Exception update) {
            logger.warn(
                    "ERROR ADD Event addDOAEvent() create exception "
                            + update.getMessage());
        }
        return result;
    }

    /**
     private method to generate the Change Request Data to be inserted in to the ChangeRequest table.
     @ params	WizardEventData wizardData, java.sql.Timestamp eticDateTimeStamp
     @ return	ChangeRequestData
     */
    private ChangeRequest generateChangeRequestData(
            WizardEventData wizardData,
            java.sql.Timestamp eticDateTimeStamp) {
        ChangeRequest changeRequestData = new ChangeRequest();

        changeRequestData.setEventId(wizardData.getEventId());
        changeRequestData.setAcn(wizardData.getACN());
        changeRequestData.setLastUpdateDtTm(wizardData.getCreatedDateTime());
        changeRequestData.setEnteredInError("N");
        changeRequestData.setChangeType(15);
        changeRequestData.setCreatedDtTm(wizardData.getCreatedDateTime());

        if (eticDateTimeStamp != null)
            changeRequestData.setNewEticDtTm(eticDateTimeStamp);

        if (wizardData.getEticInfo() != null) {
            logger.info(
                    "= >>> NewEticText in change Request table >"
                            + wizardData.getEticInfo());
            changeRequestData.setNewEticText(wizardData.getEticInfo().trim());
        }

        if (wizardData.getEticComment() != null)
            changeRequestData.setNewComment(wizardData.getEticComment().trim());

        if (wizardData.getOST() != null)
            changeRequestData.setNewOst(wizardData.getOST().trim());

        if(wizardData.getEticRsnCd() != null) {
            changeRequestData.setNewEticRsnCd(wizardData.getEticRsnCd().trim());
        }

        if(wizardData.getEticRsnComment() != null) {
            changeRequestData.setNewEticRsnComment(wizardData.getEticRsnComment().trim());
        }

        changeRequestData.setNewStatus(wizardData.getStatus());

        if (wizardData.getAccessLevel().trim().equals("80")) {
            changeRequestData.setRequestStatus("U");
        } else if (wizardData.getAccessLevel().trim().equals("90")) {
            changeRequestData.setRequestStatus("S");
        }

        return changeRequestData;
    }

    /**
     private method to generate the Change Request Log Data to be inserted in to the ChangeRequest table.
     @ params	WizardEventData wizardData, java.sql.Timestamp eticDateTimeStamp
     @ return	changeRequestHistoryData
     */
    private ChangeRequestLog generateChangeRequestLogData(WizardEventData wizardData) {
        ChangeRequestLog changeRequestLogData = new ChangeRequestLog();

        ChangeRequestLogPk pk=new ChangeRequestLogPk();
        pk.setEventId(wizardData.getEventId());
        pk.setCreatedDtTm(wizardData.getCreatedDateTime());
        pk.setStatusChangedDtTm(wizardData.getCreatedDateTime());
        changeRequestLogData.setChangeRequestLogPk(pk);

        if (wizardData.getAccessLevel().trim().equals("80")) {
            changeRequestLogData.setNewRequestStatus("U");
        } else if (wizardData.getAccessLevel().trim().equals("90")) {
            changeRequestLogData.setNewRequestStatus("S");
        }

        return changeRequestLogData;
    }
    /**
     private method to generate the Change Request History Data to be inserted in to the ChangeRequest table.
     @ params	WizardEventData wizardData, java.sql.Timestamp eticDateTimeStamp
     @ return	changeRequestHistoryData
     */
    private ChangeRequestHistory generateChangeRequestHistoryData(
            WizardEventData wizardData,
            java.sql.Timestamp eticDateTimeStamp) {
        ChangeRequestHistory changeRequestHistoryData =
                new ChangeRequestHistory();

        ChangeRequestHistoryPk pk=new ChangeRequestHistoryPk();
        pk.setEventId(wizardData.getEventId());
        pk.setAcn(wizardData.getACN());
        pk.setCreatedDtTm(wizardData.getCreatedDateTime());
        changeRequestHistoryData.setChangeRequestHistoryPk(pk);
        changeRequestHistoryData.setNewStatus(wizardData.getStatus());
        changeRequestHistoryData.setLastUpdateDtTm(
                wizardData.getCreatedDateTime());
        changeRequestHistoryData.setEnteredInError("N");
        changeRequestHistoryData.setChangeType(15);

        if (eticDateTimeStamp != null)
            changeRequestHistoryData.setNewEticDtTm(eticDateTimeStamp);

        if (wizardData.getEticInfo() != null) {
            logger.info(
                    "= >>> NewEticText in History table >" + wizardData.getEticInfo());
            changeRequestHistoryData.setNewEticText(wizardData.getEticInfo().trim());
        }

        if (wizardData.getEticComment() != null)
            changeRequestHistoryData.setNewComment(
                    wizardData.getEticComment().trim());

        if (wizardData.getOST() != null)
            changeRequestHistoryData.setNewOst(
                    wizardData.getOST().trim());

        if(wizardData.getEticRsnCd() != null) {
            changeRequestHistoryData.setNewEticRsnCd(wizardData.getEticRsnCd().trim());
        }

        if(wizardData.getEticRsnComment() != null) {
            changeRequestHistoryData.setNewEticRsnComment(wizardData.getEticRsnComment().trim());
        }

        if (wizardData.getAccessLevel().trim().equals("80")) {
            changeRequestHistoryData.setRequestStatus("U");
        } else if (wizardData.getAccessLevel().trim().equals("90")) {
            changeRequestHistoryData.setRequestStatus("S");
        }

        return changeRequestHistoryData;
    }

    /**
     The following method is a private/sub method to support the addEvent method, to add a linked MSN to the database.
     @ params EventMsns msnData.
     @ return boolean result.
     */
    private boolean addMsn(EventMsns msnData)
            throws RemoteException {
        logger.info("in the addMsns of the ..........");
        boolean result = false;
        try {
            // If we're simply updating the discrepancy as a downing item, it may already exist in the DB
            EventMsns data = new EventMsns();
            data.setEventId(msnData.getEventId());
            data.setAta(msnData.getAta());
            data.setDiscNum(msnData.getDiscNum());
            data.setMsn(msnData.getMsn());

            eventMsnsRepository.save(data);
            logger.info("== addMsn().. data saved for event Id==>"+data.getEventId());
            result = true;
        } catch (Exception update) {
            logger.warn(
                    "ERROR ADD Event addMsn() create exception "
                            + update.getMessage());
        }
        return result;
    }

}
