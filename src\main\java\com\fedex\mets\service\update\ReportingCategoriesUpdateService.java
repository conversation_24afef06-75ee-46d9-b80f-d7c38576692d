package com.fedex.mets.service.update;

import com.fedex.mets.dao.ReportCategoryKeyValues;
import com.fedex.mets.dao.ReportingCategoryValues;
import com.fedex.mets.data.ReportCategoriesKeyValueData;
import com.fedex.mets.data.MetsEventUpdateEntity;
import com.fedex.mets.entity.mets.EventRepCatg;
import com.fedex.mets.entity.mets.EventRepCatgPk;
import com.fedex.mets.repository.mets.EventRepCatgRepository;
import com.fedex.mets.service.retrieval.ReportingCategoriesService;
import com.fedex.mets.util.IServerConstants;
import com.fedex.mets.util.ServerDateHelper;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class ReportingCategoriesUpdateService {

    private static final Logger logger = LoggerFactory.getLogger(ReportingCategoriesUpdateService.class);

    @Autowired
    private EventDiscrepanciesUpdateService eventDiscrepanciesUpdateService;

    @Autowired
    private ReportingCategoriesService reportingCategoriesService;


    @Autowired
    private EventRepCatgRepository eventRepCatgRepository;

    public Map<String, Object> editReportingCategories(MetsEventUpdateEntity request, Map<String, Object> hashMap,
                                                       List<ReportCategoriesKeyValueData> reportingCategoriesFromClient, String accessLevel, String userId, String tokenId,Boolean eventActive) throws Exception {

        List<List<?>> elements = new ArrayList<List<?>>();
        List<ReportingCategoryValues> reportCategories = new ArrayList<>();
        List<ReportCategoryKeyValues> reportCategoriesKeyValues = new ArrayList<>();
        boolean resultFromBean = false, isEventActive = true;

        if (eventActive != null) {
            isEventActive = eventActive.booleanValue();
        }
        int eventId = 0, lastUpdatedRecords = 0;

        try {

            logger.info("reportingCategoriesFromClient " + reportingCategoriesFromClient.size());

            for (ReportCategoriesKeyValueData data:reportingCategoriesFromClient) {
                eventId = data.getEventId();
                boolean modified = data.getIsModified();
                if (modified) {
                    logger.info("is Modified " + modified);
                    try {
                        //to check if the record was modified prior to this transaction.
                        lastUpdatedRecords = findLastUpdated(data);
                        if (lastUpdatedRecords == 0) {
                            resultFromBean = editReportingCategories(data, userId, tokenId, isEventActive);
                        }

                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                }
            }
        } catch (Exception exec) {

            logger.warn("ERROR MetsUpdate Servlet editReportingCategories() exec >> " + exec.getMessage());
            String msg = exec.getMessage();
            if (msg.trim().length() >= 3) {
                if (msg.substring(msg.length() - 3, msg.length()).equals("512")) {
                    hashMap.put(IServerConstants.ERROR, "512");
                } else {
                    hashMap.put(IServerConstants.ERROR, "Could not Alter Reporting Categories record in Database " + exec.getMessage());
                }
            } else {
                hashMap.put(IServerConstants.ERROR, "Could not Alter Reporting Categories record in Database " + exec.getMessage());
            }
        }

        if (resultFromBean) {
            String strEventId = "" + eventId;

            try {

                elements = reportingCategoriesService.getReportingCategories(strEventId, accessLevel);

            } catch (Exception exec) {

                logger.warn("ERROR MetsUpdate Service editReportingCategories() exec1 >> " + exec.getMessage());
                hashMap.put(IServerConstants.ERROR, exec.getMessage());

            }

            if (elements != null) {
                reportCategories = (List) elements.get(0);
                reportCategoriesKeyValues = (List) elements.get(1);
            }

            for (int i = 0; i < reportCategories.size(); i++) {
                ReportingCategoryValues data = (ReportingCategoryValues) reportCategories.get(i);

                logger.info("==>" + data.getLevel1Name()
                        + "--" + data.getDescription()
                        + "--" + data.getLevel1Id()
                        + "--" + data.getLevel2Id());
            }

            for (int j = 0; j < reportCategoriesKeyValues.size(); j++) {
                ReportCategoryKeyValues data = (ReportCategoryKeyValues) reportCategoriesKeyValues.get(j);

                logger.info("==>" + data.getEventId()
                        + "--" + data.getLevel1Id()
                        + "--" + data.getLevel2Id()
                        + "--" + data.getLevel2Name());
            }

            /******************Before publishing the Update******************/
            try {

                logger.info("Before publishing the REPORTING_CATEGORY_UPDATE Update on JMS ");
                boolean isMessagePublished = publishUpdate(eventId, IServerConstants.REPORTING_CATEGORY_UPDATE);
                logger.info("isMessagePublished >> " + isMessagePublished);

            } catch (Exception e) {
                logger.warn("ERROR MetsUpdate Servlet editReportingCategories() publish >> " + e.getMessage());
            }

        } else if (lastUpdatedRecords > 0) {
            hashMap.put(IServerConstants.ERROR, "Could not Alter the data as the Record has been modified prior to this transaction.");
        }

        hashMap.put(IServerConstants.REPORT_CATEGORIES, reportCategories);
        hashMap.put(IServerConstants.REPORT_CATEGORIES_KEY_VALUES, reportCategoriesKeyValues);

        return hashMap;
    }

    private boolean publishUpdate(int eventId, String updateType) throws Exception {
        boolean isMessagePublished = false;
        try {

            logger.info("Before publishing the Update on JMS");
            eventDiscrepanciesUpdateService.publishEventUpdate(eventId, updateType);
            isMessagePublished = true;

        } catch (Exception publishException) {
            logger.warn("ERROR MetsUpdate Servlet editNIWTimerDetails() publish >> " + publishException.getMessage());
        }
        return isMessagePublished;
    }

    public int findLastUpdated(ReportCategoriesKeyValueData reportCategoriesKeyValueData) throws Exception {
        String levelOneId = "",
                levelTwoId = "",
                lastUpdated = "";
        int existingRecords = 0;
        int eventId = reportCategoriesKeyValueData.getEventId();
        levelOneId = reportCategoriesKeyValueData.getLevelOneId();
        levelTwoId = reportCategoriesKeyValueData.getLevelTwoId();
        lastUpdated = reportCategoriesKeyValueData.getLastUpdatedTime();
        try {
            String lookupUpdatedTime = ServerDateHelper.getLookUpFormat(lastUpdated);
            existingRecords = eventRepCatgRepository.findLastUpdatedRptCatg(String.valueOf(eventId), levelOneId, levelTwoId, lookupUpdatedTime);
            logger.info("existingRecords ========================" + existingRecords);
        } catch (Exception count) {
            logger.warn(" ERROR Reporting categories findLastUpdated()  >> " + count.getMessage());
        }
        return existingRecords;
    }

    @Transactional
    @Modifying
    public boolean editReportingCategories(ReportCategoriesKeyValueData reportCategoriesKeyValueData, String userId, String tokenId, boolean isEventActive) throws Exception {

        logger.info("== editReportingCategories().. ==>");

        boolean result = false;

        int eventId = 0;
        String levelOneId = "",
                levelTwoId = "",
                updatedLevelOneId = "",
                updatedLevelTwoId = "";

        eventId = reportCategoriesKeyValueData.getEventId();
        levelOneId = reportCategoriesKeyValueData.getLevelOneId();
        levelTwoId = reportCategoriesKeyValueData.getLevelTwoId();
        updatedLevelOneId = reportCategoriesKeyValueData.getUpdatedLevelOneId();
        updatedLevelTwoId = reportCategoriesKeyValueData.getUpdatedLevelTwoId();

        logger.info("eventId				==>" + eventId);
        logger.info("levelOneId			==>" + levelOneId);
        logger.info("levelTwoId			==>" + levelTwoId);
        logger.info("updatedLevelOneId	==>" + updatedLevelOneId);
        logger.info("updatedLevelTwoId	==>" + updatedLevelTwoId);

            /*
            First Step before doing any thing would be to Check the Security Level of the User trying to ADD/UPDATE the Event.
            **/
//        securityCheck(userId, tokenId, isEventActive);
        int recordsFound = 0;

        try {
            EventRepCatg eventRepCatgData = eventRepCatgRepository.findEventRepCatgByL1AndL2(String.valueOf(eventId), levelOneId, levelTwoId);
            if (eventRepCatgData != null) {
                recordsFound += 1;
            }
        } catch (Exception e) {
            logger.warn(" ERROR query editReportingCategories() >> " + e.getMessage());
            throw new Exception("editReportingCategories() Problem querying data");

        }
        try {
            if (recordsFound > 0) {
                Timestamp updateTime = ServerDateHelper.getTimeStamp();
                logger.info("updateTime  after converting -------" + updateTime);
                logger.info("before updating the record with new value==> " + updatedLevelTwoId + " old value" + levelTwoId);
                if (updatedLevelTwoId == null || updatedLevelTwoId.trim().length() == 0) {
                    try {
                        EventRepCatg eventRepCatgRecord = eventRepCatgRepository.findEventRepCatgByL1AndL2("" + eventId, levelOneId, levelTwoId);
                        eventRepCatgRepository.delete(eventRepCatgRecord);
                        result = true;

                    } catch (Exception e) {
                        result = false;
                        logger.warn(" ERROR editReportingCategories() delete >> " + e.getMessage());
                        throw new Exception(e.getMessage());

                    }
                } else {
                    logger.info("updating the record as updated level 2  is " + updatedLevelTwoId);
                    try {
                        updateTime = ServerDateHelper.getTimeStamp();
                        EventRepCatg eventRepUpdatedRecord = eventRepCatgRepository.findEventRepCatgByL1AndL2("" + eventId, levelOneId, levelTwoId);
                        logger.info("What is this eventRepUpdatedRecord " + eventRepUpdatedRecord.getEventRepCatgPk().getEventId());
                        EventRepCatg updatedRecord = new EventRepCatg();
                        updatedRecord.setEventRepCatgPk(new EventRepCatgPk(eventId, updatedLevelOneId, updatedLevelTwoId));
//                        updatedRecord.getEventRepCatgPk().setEventId(eventId);
//                        updatedRecord.getEventRepCatgPk().setLevel1Id(updatedLevelOneId);
//                        updatedRecord.getEventRepCatgPk().setLevel2Id(updatedLevelTwoId);
                        updatedRecord.setLastUpdatedDtTm(updateTime);
                        eventRepCatgRepository.delete(eventRepUpdatedRecord);
                        eventRepCatgRepository.save(updatedRecord);
                        if (updatedRecord != null)
                            result = true;
                        else
                            result = false;

                    } catch (Exception e) {

                        result = false;
                        logger.warn(" ERROR editReportingCategories() update 2>> " + e.getMessage());
                        throw new Exception(e.getMessage());

                    }
                }
            } else {

                Timestamp updateTime = ServerDateHelper.getTimeStamp();
                logger.info("updateTime  after converting -------" + updateTime);
                if (updatedLevelTwoId != null && updatedLevelTwoId.trim().length() > 0) {
                    try {
                        EventRepCatg newEventRepCatg = new EventRepCatg();
                        newEventRepCatg.setEventRepCatgPk(new EventRepCatgPk(eventId, updatedLevelOneId, updatedLevelTwoId));
                        newEventRepCatg.setLastUpdatedDtTm(updateTime);
                        EventRepCatg recordsUpdated = eventRepCatgRepository.save(newEventRepCatg);
                        if (recordsUpdated != null)
                            result = true;
                        else
                            result = false;

                    } catch (Exception e) {
                        result = false;
                        logger.warn(" ERROR editReportingCategories() create >> " + e.getMessage());
                        throw new Exception(e.getMessage());

                    }
                }

            }

        } catch (Exception update) {
            logger.warn(" ERROR Reporting categories editReportingCategories()  create **** >> " + update.getMessage());
            throw new Exception(update.getMessage());
        }

        return result;
    }

    public void securityCheck(String userId, String tokenId, boolean isEventActive) throws Exception {

        try {

            List<Object> acessFlagList = null;

            if (userId != null && tokenId != null) {
                try {

//					acessFlagVector = SecurityHelper.getAccessFlags(userId, tokenId, strTransactionId);
                    acessFlagList = new ArrayList<Object>();
                    acessFlagList.add("SUCCESS");
                    acessFlagList.add("80");

                } catch (Exception e) {
                    logger.warn("ERROR NIWTimersUpdateRepo setNIWTimers() SecurityHelper.getAccessFlags exception " + e.getMessage());
                    throw new Exception("512");
                }
            }

            if (acessFlagList != null) {
                String firstElement = (String) acessFlagList.get(0);

                if (firstElement.trim().equals("SUCCESS")) {
                    if (isEventActive) {
                        String strSecurityAccess = (String) acessFlagList.get(1);

                        if (strSecurityAccess.trim().equals("80") || strSecurityAccess.trim().equals("90")) {

                            logger.info(" ");
                            logger.info("User " + userId + " has access to START/STOP Timer of an Event.");

                        } else {
                            throw new Exception("User does not have permission/access to START/STOP Timer of to an Event.");
                        }

                    } else {
                        String strSecurityAccess = (String) acessFlagList.get(2);

                        if (strSecurityAccess.trim().equals("99")) {

                            logger.info(" ");
                            logger.info("User " + userId + " has access to START/STOP Timer of an Event.");

                        } else {
                            throw new Exception("User does not have permission/access to to START/STOP Timer of an Inactive Event.");
                        }
                    }
                } else {

                    if (firstElement.trim().equals("512")) {
                        throw new Exception(firstElement);
                    } else {
                        String strSecurityAccessError = (String) acessFlagList.get(1);
                        throw new Exception(strSecurityAccessError);
                    }

                }
            } else if (acessFlagList == null) {
                throw new Exception("User does not have permission/access to START/STOP Timer of an Event.");
            }

        } catch (Exception securityRemote) {

            logger.warn(" ERROR in reporting categories update" + securityRemote.getMessage());
            throw new Exception(securityRemote.getMessage());

        }

    }


}
