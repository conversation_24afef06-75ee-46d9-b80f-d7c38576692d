
package com.fedex.mets.wsdl.emrStatus;

import jakarta.xml.bind.annotation.*;


/**
 * <p>Java class for statusInfo complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="statusInfo">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="status" type="{http://fedex.com/airops/emr/jaxws/services}flightReadinessStatus" minOccurs="0"/>
 *         &lt;element name="statusCode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="rvStatusCode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "statusInfo", propOrder = {
    "status",
    "statusCode",
    "rvStatusCode"
})
public class StatusInfo {

    @XmlSchemaType(name = "string",namespace = "http://fedex.com/airops/emr/jaxws/services")
    protected FlightReadinessStatus status;
    @XmlElement(namespace = "http://fedex.com/airops/emr/jaxws/services")
    protected String statusCode;
    @XmlElement(namespace = "http://fedex.com/airops/emr/jaxws/services")
    protected String rvStatusCode;

    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link FlightReadinessStatus }
     *     
     */
    public FlightReadinessStatus getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link FlightReadinessStatus }
     *     
     */
    public void setStatus(FlightReadinessStatus value) {
        this.status = value;
    }

    /**
     * Gets the value of the statusCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStatusCode() {
        return statusCode;
    }

    /**
     * Sets the value of the statusCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStatusCode(String value) {
        this.statusCode = value;
    }

    /**
     * Gets the value of the rvStatusCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRvStatusCode() {
        return rvStatusCode;
    }

    /**
     * Sets the value of the rvStatusCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRvStatusCode(String value) {
        this.rvStatusCode = value;
    }

}
