package com.fedex.mets.data;

import lombok.*;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class EventFlightEticDataEntity {
	
	private int eventId;
	private String initialEtic;
	private int eticNumber;
	private String pastDue;
	private String flightNumber;
	private String flightDate;
	private String flightLegNumber;
	private String acn;
	private String destination;
	private String flightStatus;
	private String flightType;
	private String scheduledDeparture;
	private String actualDeparture;
	private String totalDelay;
	private String delayCodes;
	private String fltIn;
	private String arrival;
	private String fltOut;
	private String departure;
	private String totalGroundTime;
	private String flightFlag;
	private boolean eticNumberModified = false;
	private boolean flightInfoModified = true;

}
