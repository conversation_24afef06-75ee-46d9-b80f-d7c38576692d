package com.fedex.mets.service.retrieval;

import com.fedex.mets.dao.AircraftBean;
import com.fedex.mets.dao.EventListView;
import com.fedex.mets.dao.ManagerDetails;
import com.fedex.mets.data.FlightDepartureDetails;
import com.fedex.mets.data.FlightDetails;
import com.fedex.mets.data.ListViewData;
import com.fedex.mets.entity.mets.EventDoa;
import com.fedex.mets.entity.mets.EventRepCatg;
import com.fedex.mets.repository.cache.SuperEquipmentRepository;
import com.fedex.mets.repository.mars.EmpDetailRepository;
import com.fedex.mets.repository.mets.EventDoaRepository;
import com.fedex.mets.repository.mets.EventRepCatgRepository;
import com.fedex.mets.repository.mets.EventsRepository;
import com.fedex.mets.repository.mss.ForteLegRepository;
import com.fedex.mets.util.DateHelper;
import com.fedex.mets.util.RvDBHelper;
import com.fedex.mets.util.ServerDateHelper;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.rmi.RemoteException;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;

@SuppressWarnings("unchecked")
@Service
@RequiredArgsConstructor
public class EventListViewService {
    private static final Logger logger = LoggerFactory.getLogger(EventListViewService.class);
    @Autowired
    private EventDoaRepository eventDoaRepository;

    @Autowired
    private EventsRepository eventsRepository;

    @Autowired
    private SuperEquipmentRepository superEquipmentRepository;

    @Autowired
    private EmpDetailRepository empDetailRepository;

    @Autowired
    private EventRepCatgRepository eventRepCatgRepository;

    @Autowired
    private ForteLegRepository forteLegRepository;

    @Autowired
    RvDBHelper rvDBHelper;


    /**
     * Private method to get the Manager DETAILS for a Station.
     *
     * @return List containing list of Manager Details.
     * @params String station.
     */
    public List<ManagerDetails> getManagerDetail(String station) {
        List<ManagerDetails> managerDetailsList = new ArrayList<>();
        try {
            logger.info("Fetching Managers for Station ID: " + station);
            managerDetailsList = empDetailRepository.getManagersList(station);
            logger.info("managerDetailList size...." + managerDetailsList.size());
        } catch (Exception e) {
            logger.warn("ERROR MetsRetrieval Servlet getManagerDetail() e >> " + e.getMessage());
        }
        return managerDetailsList;
    }

    private ListViewData setDOAFlightDetails(ListViewData data)
            throws SQLException {
        EventDoa eventDoaList = eventDoaRepository.getEventDOAInfo(data.getEventID());
        data.setDoaFlightNumber(eventDoaList.getDoaFleetNumber());
        data.setDoaFlightDate(eventDoaList.getDoaFleetNumber());
        data.setDoaFlightLegNumber(eventDoaList.getDoaFleetNumber());
        logger.info("setDOAFlightDetails completed for event" + data.getEventID());
        return data;
    }

    /**
     * The Following getDOAAlerts() is used to set the DOA Alerts for the Events if
     * the Station does not match with the Event Station for DOA Events.
     *
     * @parans List of Events
     * @return List of Events
     */
    private void getDOAAlerts(List<ListViewData> listView) {

        for (int i = 0; i < listView.size(); i++) {
            ListViewData data = (ListViewData) listView.get(i);

            if (data.getType().equalsIgnoreCase("DOA")) {
                try {
                    if (data.getDoaFlightDate() != null
                            && data.getDoaFlightDate().trim().length() > 0) {
                        String strLookupDate =
                                ServerDateHelper.getLookUpFormatHH_MM(data.getDoaFlightDate());

                        HashMap hmap = new HashMap();
                        String strDest=forteLegRepository.getIATADestCd(data.getDoaFlightNumber(), strLookupDate, data.getDoaFlightLegNumber());
                        if (strDest != null && strDest.trim().length() > 0) {
                            if (!strDest.trim().equalsIgnoreCase(data.getStation()))
                                data.setDoaAlert(true);

                        } else {
                            // no records found in the FORTE_LEG table with the FlightNumber,
                            //FlightDate and FlightLeg values retreived from the EVENT_DOA table
                            data.setDoaAlert(true);
                        }
                    } else {
                        // no records found in the EVENT_DOA table for ACN to check the FORTE_LEG table with the FlightNumber, FlightDate and FlightLeg.
                        data.setDoaAlert(true);
                    }
                } catch (Exception forte) {
                    logger.warn(
                            "ERROR getDOAAlerts() forte data >> " + forte.getMessage());
                }
            }
        }
    }

    /**
     * The following getSchedule() is used to retreive the Flight number and dates
     * for an aircraft.
     *
     * @params List of aircraft
     */
    public List<ListViewData> getSchedule(List<ListViewData> list) throws RemoteException {
        List<List<String>> tempList = new ArrayList<>();

        logger.info("EventListBean to getSchedule for aircraft ----------");
        try {
            List<AircraftBean> acbList = rvDBHelper.getNewAircraftRecordsFromRampview(RvDBHelper.ALL_ACN, list);

            logger.info("RvDBHelper retrieved " + acbList.size() + " acn records");

            for (AircraftBean acb : acbList) {
                List<String> result = new ArrayList<>();

                result.add(acb.getAcn());

                result.add(acb.getInboundFlight());
                result.add(acb.getInboundFlightDate());
                result.add(acb.getInboundFlightLeg());
                result.add(acb.getInboundFlightLegDate());
                result.add(acb.getInboundOrigination());
                result.add(acb.getInboundOutTime());// !!!
                result.add(acb.getInboundDestination());
                result.add(acb.getInboundArrivalTime());
                result.add(acb.getInboundArrivalDate());

                result.add(acb.getOutboundFlight());
                result.add(acb.getOutboundFlightDate());
                result.add(acb.getOutboundFlightLeg());
                result.add(acb.getOutboundFlightLegDate());
                result.add(acb.getOutboundOrigination());
                result.add(acb.getOutboundOutTime());// !!!
                result.add(acb.getOutboundDestination());
                result.add(acb.getOutboundArrivalTime());
                result.add(acb.getOutboundArrivalDate());
                result.add(acb.getCurrentStation());
                result.add(acb.getTotalGroundDays());

                tempList.add(result);
            }
        } catch (Exception e) {
            logger.warn("ERROR getSchedule() >> " + e.getMessage());
            throw new RemoteException("getShcedule() Problem finding the aircraftSchedule list");
        }

        try {
            for (int i = 0; i < list.size(); i++) {
                ListViewData data = (ListViewData) list.get(i);
                String acn = data.getACN().trim();

                for (int j = 0; j < tempList.size(); j++) {
                    List<String> vec = tempList.get(j);
                    String tempACN = vec.get(0);
                    String strGroundTimeDays = vec.get(19);
                    tempACN = tempACN.trim();
                    if (acn.equals(tempACN) && strGroundTimeDays != null && !strGroundTimeDays.startsWith("*")) {
                        GregorianCalendar gCalender, gLegCalender;

                        String strInboundDestination = (String) vec.get(7);
                        String strOutboundFlightNumber = (String) vec.get(10);
                        String strOutboundFlightDate = (String) vec.get(11);
                        String strOutboundLegDate = (String) vec.get(13);
                        String strOutboundFlightTime = (String) vec.get(15);
                        String strCurrentStation = (String) vec.get(18);

                        if (data.getType().trim().equals("NOTE")) {
                            if (strCurrentStation != null && strCurrentStation.trim().length() > 0) {
                                data.setStation(strCurrentStation);
                            } else {
                                data.setStation(strInboundDestination);
                            }
                        }

                        if ((strOutboundFlightDate != null && strOutboundFlightDate.trim().length() > 0)
                                && (strOutboundFlightNumber != null && strOutboundFlightNumber.trim().length() > 0)
                                && (strOutboundFlightTime != null && strOutboundFlightTime.trim().length() > 0)
                                && (strOutboundLegDate != null && strOutboundLegDate.trim().length() > 0)) {
                            // for Flight Date
                            gCalender = DateHelper.N8DateToGregorianCalendar(strOutboundFlightDate);

                            // for Leg Date
                            gLegCalender = DateHelper.N8DateToGregorianCalendar(strOutboundLegDate);

                            String flightDetails = String.valueOf(new FlightDetails(strOutboundFlightNumber, gCalender).getRenderingString());

                            int intHour = 0, intMinute = 0;
                            if (strOutboundFlightTime != null && strOutboundFlightTime.trim().length() > 0) {
                                intHour = Integer.parseInt(strOutboundFlightTime.substring(0, 2));
                                intMinute = Integer.parseInt(strOutboundFlightTime.substring(2, 4));
                            }

                            gLegCalender.set(Calendar.HOUR, intHour);
                            gLegCalender.set(Calendar.MINUTE, intMinute);

                            String flightDeptDetails = new FlightDepartureDetails(gLegCalender).getRenderingString();
                            Calendar flightDeptDate= new FlightDepartureDetails(gLegCalender);
                            Date date = flightDeptDate.getTime();
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            String formattedDate = sdf.format(date);

                            data.setFlightDepartureDetails(flightDeptDetails);
                            data.setFlightDetails(flightDetails);
                        }
                    }
                }

                if(data.getStartDateTime()!=null && data.getStartDateTime().length() > 0) {
                    // Calculate the duration of the event (in days and hours:minutes
                    int intHour = 0, intMinute = 0;
                    Duration duration=null;
                    if(!(data.getEndDateTime().equalsIgnoreCase("null")) )
                    {
                        if(data.getEndDateTime()!=null) {
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                            String formattedStartString = data.getStartDateTime().substring(0, (data.getStartDateTime().length() - 2));
                            LocalDateTime startDate = LocalDateTime.parse(formattedStartString, formatter);
                            String formattedEndDateString = data.getEndDateTime().substring(0, (data.getEndDateTime().length() - 2));
                            LocalDateTime endDate = LocalDateTime.parse(formattedEndDateString, formatter);
                            duration = Duration.between(startDate, endDate);
                        }
                    }
                    else{
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'")
                                .withZone(ZoneId.of("UTC"));
                        String formattedStartString = data.getStartDateTime().substring(0, (data.getStartDateTime().length() - 2))+'Z';
                        StringBuilder sb = new StringBuilder(formattedStartString);
                        sb.setCharAt(10, 'T');

                        LocalDateTime startDate = LocalDateTime.parse(sb.toString(), formatter);
                        Instant currentInstant = Instant.now();
                        String zuluDate = formatter.format(currentInstant);
                        LocalDateTime endDate = LocalDateTime.parse(zuluDate, DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"));
                        duration= Duration.between(startDate, endDate);
                    }

                    // Convert duration to total days
                    long totalSeconds = duration.getSeconds();
                    long totalDays = totalSeconds / (24 * 60 * 60);
                    long remainingSeconds = totalSeconds % (24 * 60 * 60);

                    // Convert remaining seconds to hours and minutes
                    String h="",m="";
                    long hours = remainingSeconds / (60 * 60);
                    long minutes = (remainingSeconds % (60 * 60)) / 60;
                    h=Long.toString(hours).length() == 1? "0" + hours: Long.toString(hours);
                    m=Long.toString(minutes).length() == 1? "0" + minutes: Long.toString(minutes);
                    String durationData = totalDays + "D " + h + ":" + m;
                    data.setDurationData(durationData);

                    // ************ Formatting StartDateTime *********************

                    String sDate, startTime;
                    if (String.valueOf(data.getStartDateTime()) != null && String.valueOf(data.getStartDateTime()).length() == 21) {
                        sDate = String.valueOf(data.getStartDateTime()).substring(0, 10);
                        sDate = sDate.replace("-", "");
                        startTime = String.valueOf(data.getStartDateTime()).substring(11, 16);
                        startTime = startTime.replace(":", "");

                        GregorianCalendar gStartDateTime;
                        gStartDateTime = DateHelper.N8DateToGregorianCalendar(sDate);
                        if (startTime != null && startTime.trim().length() > 0) {
                            intHour = Integer.parseInt(startTime.substring(0, 2));
                            intMinute = Integer.parseInt(startTime.substring(2, 4));
                        }
                        gStartDateTime.set(Calendar.HOUR, intHour);
                        gStartDateTime.set(Calendar.MINUTE, intMinute);

                        String startDateTimeDetails = new FlightDepartureDetails(gStartDateTime).getRenderingString();
                        data.setStartDateTime(startDateTimeDetails);
                    }
                }
            }

        } catch (Exception comparing) {
            logger.warn("ERROR getSchedule() comparing>> " + comparing.getMessage());
        }

        return list;
    }

    /**
     * The following getEventList() is used to retreive the all the active Event List
     *
     * @return List of events.
     */
    public List<ListViewData> getEventList() throws RemoteException {
        List<ListViewData> list = new ArrayList<>();

        try {
            getEventsListView(list);
        } catch (Exception e) {
            logger.warn("ERROR getAMCEventList() >> " + e.getMessage());
            throw new RemoteException("getAMCEventList() Problem finding the Events list");
        }
        CompletableFuture<Void> gateFuture = CompletableFuture.runAsync(() -> {
            try {
                List<Integer> eventLs = new ArrayList<>();
                List<Integer> eventList = new ArrayList<>();
                for (int i = 0; i < list.size(); i++) {
                    eventLs.add(list.get(i).eventID);
                }
                try {
                    getCurrentGate(list);
                    eventList = getPowerPlantCount(eventLs);
                    Set<Integer> eventSet = new HashSet<>(eventList);
                    for (int i = 0; i < list.size(); i++) {
                        ListViewData data = (ListViewData) list.get(i);
                        // Check if the event ID exists in the eventSet
                        if (eventSet.contains(data.getEventID())) {
                            data.setPowerPlantEvent(true);
                        }

                        if (data.getType().equalsIgnoreCase("DOA")) {
                            data = setDOAFlightDetails(data);
                        }
                    }

                } catch (SQLException powerPlant) {
                    logger.warn("ERROR getAMCEventList() powerplant>> " + powerPlant.getMessage());
                }
            } catch (Exception eeNew) {
                logger.warn("ERROR getAMCEventList() ****>> " + eeNew.getMessage());
            }

        });

        getDOAAlerts(list);
        CompletableFuture<List<ListViewData>> scheduleFuture = CompletableFuture.supplyAsync(() -> {
            try {
                logger.info("EventListBean list size" + list.size());
                getSchedule(list);
            } catch (RemoteException schedule) {
                logger.warn("ERROR getAMCEventList() getschedule>> " + schedule.getMessage());
            }
            return list;
        });

        CompletableFuture<List<ListViewData>> combinedFuture = gateFuture.thenCombine(scheduleFuture,
                (gateResult, scheduleResult) -> {
                    logger.info("ScheduleFuture,CurrentGate Both tasks completed.");
                    return scheduleResult;
                });

        return combinedFuture.join();
    }
    private void getEventsListView(List<ListViewData> list) throws RemoteException {
        try {
            List<EventListView> elements=eventsRepository.getListViewList();
            for (EventListView eventListView : elements) {
                ListViewData data = new ListViewData();

                data.setEventOnwerGroupId(eventListView.getEventOwnerGroupId());
                data.setEventID(eventListView.getEventId());
                data.setType(eventListView.getType());
                data.setStartDateTime(String.valueOf(eventListView.getStartDateTime()));
                data.setEndDateTime(String.valueOf(eventListView.getEndDateTime()));
                data.setACN(eventListView.getAcn());
                data.setFleetDesc(eventListView.getFleetDesc());
                data.setStation(eventListView.getStation());

                String strStatus = eventListView.getStatus();
                String strEticDateTime = String.valueOf(eventListView.getEticDateTime());
                if(strEticDateTime!=null && strEticDateTime.length() == 21) {
                    SimpleDateFormat sdf = new SimpleDateFormat("MM-dd-yy HH:mm:ss");
                    String formattedDate = sdf.format(eventListView.getEticDateTime());
                    String date =formattedDate.substring(0, 8);
                    date = date.replace("-", "");
                    String time = formattedDate.substring(9, 14);
                    time = time.replace(":", "");
                    strEticDateTime=time+"/"+date;
                }
                String strEticText = eventListView.getEticText();
                String strComment = (String) eventListView.getCurrentComment();

                data.setLastUpdateDateTime(String.valueOf(eventListView.getLastUpdateDtTm()));
                data.setLastUpdatedBy(eventListView.getLastUpdatedBy());
                data.setCreatedDateTime(String.valueOf(eventListView.getCreatedDateTime()));
                data.setCreatedBy(eventListView.getCreatedBy());

                String requestStatus = eventListView.getRequestStatus();
                if(eventListView.getChangeType() != null) {
                    int changeType = eventListView.getChangeType();
                    data.setRequestStatus(requestStatus);
                    data.setChangeType("" + changeType);
                }
                data.setLastUpdated(String.valueOf(eventListView.getLastUpdateDtTm()));

                String strNewStatus = eventListView.getNewStatus();
                String strNewEticText = eventListView.getNewEticText();
                String strNewComment = eventListView.getNewComment();
                String strNewEticDateTime = String.valueOf(eventListView.getNewEticDtTm());
                if(strNewEticDateTime!=null && strNewEticDateTime.length() == 21) {
                    SimpleDateFormat sdf = new SimpleDateFormat("MM-dd-yy HH:mm:ss");
                    String formattedDate = sdf.format(eventListView.getNewEticDtTm());
                    String date =formattedDate.substring(0, 8);
                    date = date.replace("-", "");
                    String time = formattedDate.substring(9, 14);
                    time = time.replace(":", "");
                    strNewEticDateTime=time+"/"+date;
                }

                data.setChangeRequestLastUpdateDtTime(String.valueOf(eventListView.getLastUpdateDtTm()));
                data.setAcOwnerGroupId(eventListView.getAcOwnerGroupId());

                if (requestStatus != null && requestStatus.trim().equalsIgnoreCase("S")) {
                    data.setStatus(strNewStatus);
                    data.setEticDateTime(strNewEticDateTime);
                    data.setEticText(strNewEticText);
                    data.setCurComment(strNewComment);

                    data.setNewStatus(strNewStatus);
                    data.setNewEticText(strNewEticText);
                    data.setNewEticComment(strNewComment);
                    data.setNewEticDateTime(strNewEticDateTime);

                } else if (requestStatus != null && requestStatus.trim().equalsIgnoreCase("U")) {
                    data.setStatus(strStatus);
                    data.setEticDateTime(strEticDateTime);
                    data.setEticText(strEticText);
                    data.setCurComment(strComment);

                    data.setNewStatus(strNewStatus);
                    data.setNewEticText(strNewEticText);
                    data.setNewEticComment(strNewComment);
                    data.setNewEticDateTime(strNewEticDateTime);
                } else if (requestStatus != null && requestStatus.trim().equalsIgnoreCase("C")) {
                    data.setStatus(strNewStatus);
                    data.setEticDateTime(strNewEticDateTime);
                    data.setEticText(strNewEticText);
                    data.setCurComment(strNewComment);

                    data.setNewStatus(strNewStatus);
                    data.setNewEticText(strNewEticText);
                    data.setNewEticComment(strNewComment);
                    data.setNewEticDateTime(strNewEticDateTime);
                } else {
                    data.setStatus(strStatus);
                    data.setEticDateTime(strEticDateTime);
                    data.setEticText(strEticText);
                    data.setCurComment(strComment);

                    data.setNewStatus(strNewStatus);
                    data.setNewEticText(strNewEticText);
                    data.setNewEticComment(strNewComment);
                    data.setNewEticDateTime(strNewEticDateTime);
                }

                data.setOwner(eventListView.getGroupTitle());

                String strActiveEvent = eventListView.getActiveEvent();

                if (strActiveEvent != null && strActiveEvent.trim().equalsIgnoreCase("Y")) {
                    data.setEventActive(true);
                }

                data.setOst(eventListView.getOst());
                data.setNewOST(eventListView.getNewOst());

                data.setEticReasonCd(eventListView.getEticRsnCd());
                data.setNewEticReasonCd(eventListView.getNewEticRsnCd());

                data.setEticRsnComments(eventListView.getEticRsnComment());
                data.setNewEticRsnComments(eventListView.getNewEticRsnComment());

                list.add(data);
            }
        } catch (Exception se) {
            throw new RemoteException("Problem finding the AMC Events list " + se.getMessage());
        }
    }

    private void getCurrentGate(List<ListViewData> list) throws RemoteException {
        try {
            List<Object[]> results = superEquipmentRepository.getCurrentGateListDetails();
            Map<String, String> gatesHash = new HashMap<>();
            if(results!=null)
            {
                for (Object[] result : results) {
                    String acnNumber = (String) result[0];
                    String gateNumber = (String) result[1];
                    gatesHash.put(acnNumber, gateNumber);
                }
                logger.info("getCurrentGate:" + gatesHash);
                for (int i = 0; i < list.size(); i++) {
                    ListViewData data = (ListViewData) list.get(i);
                    if (data.getType().equalsIgnoreCase("TRK") || data.getType().equalsIgnoreCase("OOS")
                            || data.getType().equalsIgnoreCase("NOTE")) {

                        data.setGate((String)gatesHash.get(data.getACN()));
                    }
                    logger.info("getCurrentGate:" + data.getGate());
                }
            }
            else{
                logger.info("getCurrentGate: No data found");
            }
            logger.info("getCurrentGate completed");
        } catch (Exception se) {
            logger.error("Problem finding the Gates for list", se);
            throw new RemoteException("Problem finding the Gates for list " + se.getMessage());
        }
    }

    /**
     * The following getManagerDetailList() is used to retrieve the Manager Details
     * for a station
     *
     * @return List of events.
     * @params
     */
    public List<ManagerDetails> getManagerDetailList(String station) {
        List<ManagerDetails> managerDetailsList = new ArrayList<>();
        try {
            managerDetailsList= empDetailRepository.getManagersList(station);
        } catch (Exception e) {
            logger.warn("ERROR getManagerDetailList() >> " + e.getMessage());
        }
        return managerDetailsList;
    }

    /**
     * getPowerPlantCount
     * @throws SQLException
     */
    private List<Integer> getPowerPlantCount(List<Integer> eventIDs)
            throws SQLException {
        logger.info("getPowerPlantCount() >> " + eventIDs);
        List<EventRepCatg> result=eventRepCatgRepository.getPowerPlantCount(eventIDs);
        List<Integer> events = new ArrayList<>();
        for (EventRepCatg event : result) {
            events.add(event.getEventRepCatgPk().getEventId());
        }
        logger.warn("getPowerPlantCount() >> " + events);
        return events;
    }
}
