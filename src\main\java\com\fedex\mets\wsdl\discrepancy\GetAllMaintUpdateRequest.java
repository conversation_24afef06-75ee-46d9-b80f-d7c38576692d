
package com.fedex.mets.wsdl.discrepancy;

import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;extension base="{http://www.fedex.com/airops/schemas/Common.xsd}GenericRequest">
 *       &lt;sequence>
 *         &lt;element name="acnDiscrepancyOids" type="{http://www.w3.org/2001/XMLSchema}long" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "acnDiscrepancyOids"
})
@XmlRootElement(name = "getAllMaintUpdateRequest",namespace = "http:///www.fedex.com/airops/schemas/Mach")
public class GetAllMaintUpdateRequest
    extends GenericRequest
{

    @XmlElement(type = Long.class)
    protected List<Long> acnDiscrepancyOids;

    /**
     * Gets the value of the acnDiscrepancyOids property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the acnDiscrepancyOids property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getAcnDiscrepancyOids().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link Long }
     * 
     * 
     */
    public List<Long> getAcnDiscrepancyOids() {
        if (acnDiscrepancyOids == null) {
            acnDiscrepancyOids = new ArrayList<Long>();
        }
        return this.acnDiscrepancyOids;
    }

}
