package com.fedex.mets.controller;

import com.fedex.mets.dao.*;
import com.fedex.mets.entity.mets.Question;
import com.fedex.mets.service.intakeForm.IntakeFormService;
import jakarta.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/mets/userIntakeForm")
@CrossOrigin("*")
public class UserIntakeFormController {
    @Autowired
    private IntakeFormService intakeFormService;

    @PostMapping("/create")
    public Boolean createUserIntakeForm(@RequestBody UserIntakeFormDao userIntakeFormDao) {
        return intakeFormService.createUserIntakeForm(userIntakeFormDao);
    }

    @PostMapping("/addNewQuestions")
    public List<Question> addNewQuestions(@RequestBody List<QuestionDao> questions) {
        return intakeFormService.addNewQuestions(questions);
    }

    @GetMapping("/getRoleAndEventType")
    public UserRoleEventTypeDao getRoleAndEventType() {
        return intakeFormService.getRoleAndEventType();
    }

    @GetMapping("/getAllUserIntakeForms")
    public List<UserIntakeFormDao> getAllUserIntakeForms() {
        return intakeFormService.getAllUserIntakeForms();
    }

    @DeleteMapping("/deleteUserIntakeForm")
    @Transactional
    public void deleteUserIntakeForm(@RequestParam("userIntakeFormId") int userIntakeFormId) {
        intakeFormService.deleteUserIntakeForm(userIntakeFormId);
    }


    @GetMapping("/getUserIntakeForm")
    public UserIntakeFormDao getUserIntakeForm(@RequestParam("roleId") int roleId, @RequestParam("eventId") int eventId) {
     return intakeFormService.getUserIntakeForm(roleId, eventId);
    }


    @PostMapping("/updateIntakeForm")
    public void editIntakeForm(@RequestBody ModifiedIntakeFormDao modifiedIntakeFormDao) {
        intakeFormService.editIntakeForm(modifiedIntakeFormDao);
    }

    @DeleteMapping("/deleteQuestion")
    public void deleteQuestion(@RequestParam("questionId") int questionId) {
        intakeFormService.deleteQuestion(questionId);
    }

    @PostMapping("/updateQuestion")
    public void updateQuestion(@RequestBody QuestionDao questionDao) {
        intakeFormService.updateQuestion(questionDao);
    }

//    public void editQuestion(List<Question> questions, List<QuestionDao> editedQuestions) {
//        Timestamp currentTimestamp = getCurrentTimestamp();
//        for (Question question : questions) {
//            editedQuestions.stream()
//                    .filter(editedQuestion -> editedQuestion.getQuestionId() == question.getQuestion_id())
//                    .findFirst()
//                    .ifPresent(editedQuestion -> {
//                        question.setQuestionTxt(editedQuestion.getQuestionTxt());
//                        question.setQuestionGrp(editedQuestion.getQuestionGrp());
//                        question.setUpdatedTMSTP(currentTimestamp);
//
//                        // Update or add answers
//                        updateAnswers(question, editedQuestion.getAnswers(), currentTimestamp);
//
//                        questionRepository.save(question);
//                    });
//        }
//    }


    @GetMapping("/getIntakeForms")
    public List<IntakeFormDao> getIntakeForms() {
        return intakeFormService.getIntakeForms();
    }

    @GetMapping("/getAllQuestions")
    public List<QuestionDao> getAllQuestions() {
        return intakeFormService.getAllQuestions();
    }

    @GetMapping("/getIntakeFormResponseByEventId")
    public IntakeFormResponseDao getIntakeFormByEventId(@RequestParam("eventId") int eventId) {
        return intakeFormService.getIntakeFormByEventId(eventId);
    }

    @PutMapping("/updateIntakeFormResponse")
    public void updateIntakeFormResponse(@RequestBody IntakeFormResponseDao intakeFormResponseDao) {
        intakeFormService.updateIntakeFormResponse(intakeFormResponseDao);
    }

}
