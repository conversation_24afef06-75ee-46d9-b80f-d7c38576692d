
package com.fedex.mets.wsdl.discrepancy;

import java.math.BigDecimal;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for EfvsCoOrdinateType complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="EfvsCoOrdinateType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="efvsCoOrdinateOid" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="xCenterNbr" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="yCenterNbr" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="xCornerNbr" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="yCornerNbr" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "EfvsCoOrdinateType", propOrder = {
    "efvsCoOrdinateOid",
    "xCenterNbr",
    "yCenterNbr",
    "xCornerNbr",
    "yCornerNbr"
})
public class EfvsCoOrdinateType {

    @XmlElement(required = true)
    protected BigDecimal efvsCoOrdinateOid;
    @XmlElement(required = true)
    protected BigDecimal xCenterNbr;
    @XmlElement(required = true)
    protected BigDecimal yCenterNbr;
    @XmlElement(required = true)
    protected BigDecimal xCornerNbr;
    @XmlElement(required = true)
    protected BigDecimal yCornerNbr;

    /**
     * Gets the value of the efvsCoOrdinateOid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getEfvsCoOrdinateOid() {
        return efvsCoOrdinateOid;
    }

    /**
     * Sets the value of the efvsCoOrdinateOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setEfvsCoOrdinateOid(BigDecimal value) {
        this.efvsCoOrdinateOid = value;
    }

    /**
     * Gets the value of the xCenterNbr property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getXCenterNbr() {
        return xCenterNbr;
    }

    /**
     * Sets the value of the xCenterNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setXCenterNbr(BigDecimal value) {
        this.xCenterNbr = value;
    }

    /**
     * Gets the value of the yCenterNbr property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getYCenterNbr() {
        return yCenterNbr;
    }

    /**
     * Sets the value of the yCenterNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setYCenterNbr(BigDecimal value) {
        this.yCenterNbr = value;
    }

    /**
     * Gets the value of the xCornerNbr property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getXCornerNbr() {
        return xCornerNbr;
    }

    /**
     * Sets the value of the xCornerNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setXCornerNbr(BigDecimal value) {
        this.xCornerNbr = value;
    }

    /**
     * Gets the value of the yCornerNbr property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getYCornerNbr() {
        return yCornerNbr;
    }

    /**
     * Sets the value of the yCornerNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setYCornerNbr(BigDecimal value) {
        this.yCornerNbr = value;
    }

}
