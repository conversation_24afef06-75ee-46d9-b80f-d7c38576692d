package com.fedex.mets.repository.rampview;

import com.fedex.mets.entity.rampview.RvAircraft;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RvAircraftRepository extends JpaRepository<RvAircraft,String> {
    @Query(value="SELECT FUEL_ACTL_ON_BOARD_LBS_QTY FROM rv_aircraft where acn_cd=:acn",nativeQuery=true)
    public Integer getFob(@Param("acn") String acn);
}
