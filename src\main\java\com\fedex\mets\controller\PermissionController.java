package com.fedex.mets.controller;

import com.fedex.mets.config.RequirePermission;
import com.fedex.mets.dao.UserPermissionResponse;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/mets/permissions")
//@CrossOrigin("*")
public class PermissionController {

    private static final Map<Character, String> ACTION_MAP = Map.of(
            'A', "Add",
            'B', "Browse",
            'D', "Delete",
            'C', "Change",
            'M', "Modify",
            'I', ""
    );

    @SuppressWarnings("unchecked")
    @GetMapping
    @RequirePermission(transaction = "METS", action = 'B')
    public ResponseEntity<List<UserPermissionResponse>> getUserPermissions(HttpServletRequest request) {
        // Retrieve user permissions from request
        Map<String, Set<Character>> userPermissions =
                (Map<String, Set<Character>>) request.getAttribute("userPermissions");

        if (userPermissions == null || userPermissions.isEmpty()) {
            return ResponseEntity.ok(Collections.emptyList());
        }

        // Transform data into the desired JSON format
        List<UserPermissionResponse> responseList = userPermissions.entrySet().stream()
                .map(entry -> new UserPermissionResponse(
                        entry.getKey(),
                        entry.getValue().stream()
                                .map(ACTION_MAP::get)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList())
                ))
                .collect(Collectors.toList());

        return ResponseEntity.ok(responseList);
    }
}
