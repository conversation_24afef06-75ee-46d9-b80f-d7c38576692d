package com.fedex.mets.dao;

import jakarta.persistence.Column;
import lombok.*;

import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@ToString
@Data
public class EventListView {
    @Column(name = "EVENT_OWNER_GROUP_ID")
    private String eventOwnerGroupId;

    @Column(name = "EVENT_ID")
    public Integer eventId;

    @Column(name = "TYPE")
    public String type;

    @Column(name = "START_DT_TM")
    public Timestamp startDateTime;

    @Column(name = "END_DT_TM")
    public Timestamp endDateTime;

    @Column(name = "ACN")
    public String acn;

    @Column(name = "FLEET_DESC")
    public String fleetDesc;

    @Column(name = "STATION")
    public String station;

    @Column(name = "STATUS")
    public String status;

    @Column(name = "ETIC_DT_TM")
    public Timestamp eticDateTime;

    @Column(name = "ETIC_TEXT")
    public String eticText;

//    @Column(name = "CUR_COMMENT")
//    public String curComment;
//
//    @Column(name = "ORIG_COMMENT")
//    public String origComment;

    public Object currentComment;

    @Column(name = "LAST_UPDATE_DT_TM")
    public Timestamp lastUpdateDateTime;

    @Column(name = "LAST_UPDATE_BY")
    public String lastUpdatedBy;

    @Column(name = "CREATED_DT_TM")
    public Timestamp createdDateTime;

    @Column(name = "CREATED_BY")
    public String createdBy;

    @Column(name = "REQUEST_STATUS")
    public String requestStatus;

    @Column(name = "CHANGE_TYPE")
    public Integer changeType;

    @Column(name = "LAST_UPDATE_DT_TM")
    private Timestamp lastUpdateDtTm;

    @Column(name = "NEW_STATUS")
    public String newStatus;

    @Column(name = "NEW_ETIC_TEXT")
    public String newEticText;

    @Column(name = "NEW_COMMENT")
    public String newComment;
////
////    @Column(name = "OLD_COMMENT")
////    private String oldComment;

    @Column(name = "NEW_ETIC_DT_TM")
    public Timestamp newEticDtTm;

    @Column(name = "AC_OWNER_GROUP_ID")
    private String acOwnerGroupId;

    @Column(name = "GROUP_TITLE")
    private String groupTitle;

    @Column(name = "ACTIVE_EVENT")
    public String activeEvent;

    @Column(name = "OST")
    public String ost;

    @Column(name = "NEW_OST")
    public String newOst;

    @Column(name = "ETIC_RSN_CD")
    public String eticRsnCd;

    @Column(name = "NEW_ETIC_RSN_CD")
    public String newEticRsnCd;

    @Column(name = "ETIC_RSN_COMMENT")
    public String eticRsnComment;

    @Column(name = "NEW_ETIC_RSN_COMMENT")
    public String newEticRsnComment;

    public EventListView(String eventOwnerGroupId, Integer eventId, String type,
                         Timestamp startDateTime, Timestamp endDateTime, String acn, String fleetDesc,
                         String station, String status, Timestamp eticDateTime, String eticText,
                         Object currentComment, Timestamp lastUpdateDateTime, String lastUpdatedBy,
                         Timestamp createdDateTime, String createdBy, String requestStatus, Integer changeType,
                         Timestamp lastUpdateDtTm, String newStatus, String newEticText, String newComment,
                         Timestamp newEticDtTm, String acOwnerGroupId, String groupTitle,
                         String activeEvent, String ost, String newOst, String eticRsnCd, String newEticRsnCd,
                         String eticRsnComment, String newEticRsnComment) {
        this.eventOwnerGroupId = eventOwnerGroupId;
        this.eventId = eventId;
        this.type = type;
        this.startDateTime = startDateTime;
        this.endDateTime = endDateTime;
        this.acn = acn;
        this.fleetDesc = fleetDesc;
        this.station = station;
        this.status = status;
        this.eticDateTime = eticDateTime;
        this.eticText = eticText;
        this.currentComment = currentComment;
        this.lastUpdateDateTime = lastUpdateDateTime;
        this.lastUpdatedBy = lastUpdatedBy;
        this.createdDateTime = createdDateTime;
        this.createdBy = createdBy;
        this.requestStatus = requestStatus;
        this.changeType = changeType;
        this.lastUpdateDtTm = lastUpdateDtTm;
        this.newStatus = newStatus;
        this.newEticText = newEticText;
        this.newComment = newComment;
        this.newEticDtTm = newEticDtTm;
        this.acOwnerGroupId = acOwnerGroupId;
        this.groupTitle = groupTitle;
        this.activeEvent = activeEvent;
        this.ost = ost;
        this.newOst = newOst;
        this.eticRsnCd = eticRsnCd;
        this.newEticRsnCd = newEticRsnCd;
        this.eticRsnComment = eticRsnComment;
        this.newEticRsnComment = newEticRsnComment;
    }



}

