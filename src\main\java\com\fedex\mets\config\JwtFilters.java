package com.fedex.mets.config;

import com.fedex.airops.mss.auth.jwt.JwtUtil;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Collections;

@Component
public class JwtFilters extends OncePerRequestFilter {
    private Logger log = LoggerFactory.getLogger(JwtFilters.class);
    @Autowired
    private Environment env;

    @Autowired
    private DSSPermissionService permissionService;


    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {

        String requestURI = request.getRequestURI();
        if (requestURI.startsWith("/static/") || requestURI.startsWith("/css/") ||
                requestURI.startsWith("/js/") || requestURI.startsWith("/images/") ||
                requestURI.equals("/favicon.ico") || requestURI.equals("/index.html") ||
                requestURI.equals("/") || requestURI.endsWith(".js") ||
                requestURI.endsWith(".css") || requestURI.startsWith("/authorization-code/callback")
                || requestURI.startsWith("/assets/")
                || requestURI.startsWith("/actuator")
                || requestURI.startsWith("/swagger")
                || requestURI.startsWith("/v3/api-docs")
                || requestURI.startsWith("/websocket")
        ) {
            filterChain.doFilter(request, response);
            return;
        }
        String token = getToken(request);
        boolean isValid = tokenIsValid(token);
        if (!isValid) {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.getWriter().write("Invalid or expired token");
            return;
        }
        log.info("****Token validated****");
        // Extract user ID from token
        //TODO: Need to uncomment the below line for fetching userID
        String userid = JwtUtil.getEmployeeNumber(token);
        System.out.println("The employee number is: " + userid);
        if (userid != null && !userid.isEmpty()) { // Fix string comparison
            UsernamePasswordAuthenticationToken auth =
                    new UsernamePasswordAuthenticationToken(userid, null, Collections.emptyList());
            SecurityContextHolder.getContext().setAuthentication(auth);
        }

//        System.out.println("Token validated for user: " + SecurityContextHolder.getContext().getAuthentication().isAuthenticated());
//        String userId = request.getHeader("userId");
//        System.out.println("Token validated for user: " + userId);
//        Map<String, Set<Character>> userPermissions = null;
//        try {
//            userPermissions = permissionService.fetchUserPermissions(token, "F5850603");
//        } catch (ClassNotFoundException e) {
//            throw new RuntimeException(e);
//        }
//        request.setAttribute("userPermissions", userPermissions);

        filterChain.doFilter(request, response);
    }

    public String getToken(HttpServletRequest request) {
        String authorizationHeader = request.getHeader("Authorization");
        if (authorizationHeader != null && authorizationHeader.startsWith("Bearer ")) {
            return authorizationHeader.substring(7);
        }
        return null;

    }

    public boolean tokenIsValid(String token) {
        if (token == null || token.trim().isEmpty()) {
            return false;
        }
        if (JwtUtil.isJwt(token)) {
            return JwtUtil.validateJwtLocallyWithoutSignature(token, env.getProperty("app.id"), env.getProperty("app.okta.uri"));
        } else {
            return false;
        }
    }
}
