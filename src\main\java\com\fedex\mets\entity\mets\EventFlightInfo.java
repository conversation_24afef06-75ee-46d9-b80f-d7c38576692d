package com.fedex.mets.entity.mets;

import jakarta.persistence.*;
import lombok.*;

import java.sql.Date;
import java.sql.Time;
import java.sql.Timestamp;

@Getter
@Setter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Table(name = "EVENT_FLT_INFO")
public class EventFlightInfo {
    @EmbeddedId
    public EventFltInfoPk eventFltInfoPk;
//    @Id
//    @Id
//    @GeneratedValue(strategy = GenerationType.IDENTITY)
//    private Long id;
//    @Column(name = "EVENT_ID")
//    public Integer eventId;

    @Column(name = "FLT_NUM")
    public String flightNumber;

    @Column(name = "FLT_DATE")
    public Timestamp flightDate;

    @Column(name = "FLT_LEG")
    public String flightLeg;

    @Column(name = "FLT_ACN")
    public String flightACN;

    @Column(name = "FLT_DEST")
    public String flightDestination;

    @Column(name = "FLT_STATUS")
    public String flightStatus;

    @Column(name = "FLT_TYPE")
    public String flightType;

    @Column(name = "FLT_SCHED_DEPT_DT_TM")
    public Timestamp flightSchedDeptDateTime;

    @Column(name = "FLT_ACT_DEPT_DT_TM")
    public Timestamp flightActualDeptDateTime;

    @Column(name = "FLT_TOTAL_DELAY")
    public int flightTotalDelay;

    @Column(name = "FLT_ORIGIN")
    public String flightOrigin;

    @Column(name = "FLT_SCHED_ARRIVAL_DT_TM")
    public Timestamp flightSchedArrivalDateTime;

    @Column(name = "FLT_ACT_ARRIVAL_DT_TM")
    public Timestamp flightActualArrivalDateTime;

//    @Column(name = "FLT_FLAG")
//    public Character flightFlag;

}
