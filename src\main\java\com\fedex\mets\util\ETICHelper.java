package com.fedex.mets.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;

public class ETICHelper {
    private static final Logger logger = LoggerFactory.getLogger(ETICHelper.class);

    /**
     * Returns a String.
     * 
     * @param eticTimeStamp
     *                  String
     * @param eticInfo
     *                  String
     * 
     * @return String
     */
    public static String getETICFormat(String eticTimeStamp, String eticInfo) {
        StringBuffer strBuffer = new StringBuffer();
        String formatString = "HHmm/MMddyy";
        if (eticTimeStamp == null || eticTimeStamp.trim().length() == 0) {
            if (eticInfo != null)
                strBuffer.append(eticInfo);
        } else {
            if (eticInfo != null && eticInfo.trim().length() != 0)
                formatString = "HHmm/MMdd";
            if (eticInfo == null)
                eticInfo = "";
            Timestamp dateTime = Timestamp.valueOf(eticTimeStamp);
            SimpleDateFormat df = new SimpleDateFormat(formatString);
            strBuffer.append(df.format(dateTime));
            strBuffer.append(eticInfo);
        }
        return strBuffer.toString();
    }
}
