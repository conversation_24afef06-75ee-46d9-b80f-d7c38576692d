package com.fedex.mets.util;

import jakarta.mail.Message;
import jakarta.mail.Session;
import jakarta.mail.Transport;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Properties;

@Service
public class MailHelper{
    private static final Logger logger = LoggerFactory.getLogger(MailHelper.class);

    private String mailhost = new String();
    private String message = new String();
    private String to_email = new String();
    private String from_email = new String();
    private String subject = new String();
    private Properties mailprops = null;
    private Session session = null;


    public MailHelper() {
        set_mail_host("smtp.mail.fedex.com");
        mailprops = System.getProperties();
        mailprops.put("mail.smtp.host",mailhost);
        session = Session.getDefaultInstance(mailprops,null);
    }

    public void set_mail_host(String myhost){
        mailhost=myhost;
    }
    public void set_from_email(String myemail){
        from_email=myemail;
    }
    private String get_from_email(){
        return from_email;
    }
    public void set_to_email(String myemail){
        to_email=myemail;
    }
    private String get_to_email(){
        return to_email;
    }
    public void set_message(String mymessage){
        message=mymessage;
    }
    private String get_message(){
        return message;
    }

    public void set_subject(String mysubject){
        subject=mysubject;
    }

    private String get_subject(){
        return subject;
    }

    public boolean sendmail(){
        boolean issent=true;
        MimeMessage message = new MimeMessage(session);
        try{
            message.setFrom(new InternetAddress(get_from_email()));
            message.addRecipient(Message.RecipientType.TO, new InternetAddress(get_to_email()));
            message.addRecipient(Message.RecipientType.CC, new InternetAddress(get_from_email()));
            message.setSubject(get_subject());
            message.setText(get_message());
            Transport.send(message);
        }
        catch(Exception e){
            issent=false;
            logger.debug("Unable to send email.", e);
        }

        return issent;
    }

}