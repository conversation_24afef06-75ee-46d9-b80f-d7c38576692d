package com.fedex.mets.entity.mets;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.sql.Timestamp;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "intake_form_response")
public class IntakeFormResponse {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "intake_form_response_seq")
    @SequenceGenerator(name = "intake_form_response_seq", sequenceName = "intake_form_response_seq", allocationSize = 1)
    @Column(name = "INTAKE_FORM_RESPONSE_ID")
    private int intakeFormResponseId;

    @Column(name = "EMP_NBR")
    private int empNbr;

    @Column(name="EMP_NM")
    private String empNm;

    @Column(name="RESPONSE_JSON")
    private String responseJson;

    @JsonFormat(
            shape = JsonFormat.Shape.STRING,
            pattern = "yyyy-MM-dd HH:mm:ss.SSS",
            timezone = "UTC")
    @CreatedDate
    @Column(name = "created_tmstp")
    private Timestamp createdTMSTP;

    @JsonFormat(
            shape = JsonFormat.Shape.STRING,
            pattern = "yyyy-MM-dd HH:mm:ss.SSS",
            timezone = "UTC")
    @LastModifiedDate
    @Column(name = "updated_tmstp")
    private Timestamp updatedTMSTP;

    @Column(name="event_id")
    private int eventId;
}
