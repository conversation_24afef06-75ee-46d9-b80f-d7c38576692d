package com.fedex.mets.dao;

import jakarta.persistence.Column;
import lombok.*;

import java.sql.Timestamp;
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
public class EventDetailView {
    @Column(name = "EVENT_ID")
    public Integer eventId;

    @Column(name = "TYPE")
    public String type;

    @Column(name = "START_DT_TM")
    public Timestamp startDateTime;

    @Column(name = "END_DT_TM")
    public Timestamp endDateTime;

    @Column(name = "ACN")
    public String acn;

    @Column(name = "FLEET_DESC")
    public String fleetDesc;

    @Column(name = "STATION")
    public String station;

    @Column(name = "STATUS")
    public String status;

    @Column(name = "ETIC_DT_TM")
    public Timestamp eticDateTime;

    @Column(name = "ETIC_TEXT")
    public String eticText;


//    @Column(name = "CUR_COMMENT")
//    public String curComment;
//
//    @Column(name = "ORIG_COMMENT")
//    public String origComment;


    public Object currentOrigComment;

    @Column(name = "LAST_UPDATE_DT_TM")
    public Timestamp lastUpdateDateTime;

    @Column(name = "LAST_UPDATE_BY")
    public String lastUpdatedBy;

    @Column(name = "CREATED_DT_TM")
    public Timestamp createdDateTime;

    @Column(name = "CREATED_BY")
    public String createdBy;

    @Column(name = "PRIMARY_CONTACT")
    private String primaryContact;

    @Column(name = "AC_OWNER_GROUP_ID")
    private String acOwnerGroupId;

    @Column(name = "EVENT_OWNER_GROUP_ID")
    private String eventOwnerGroupId;


    @Column(name = "LIST_ORDER")
    private Integer listOrder;

    @Column(name = "REQUEST_STATUS")
    public String requestStatus;

    @Column(name = "CHANGE_TYPE")
    public Integer changeType;

    @Column(name = "ORIG_COMMENT")
    private String origComment;

    private Object mgrNotes;

    @Column(name = "NEW_STATUS")
    public String newStatus;

    @Column(name = "CANCELLED")
    private String cancelled;

    @Column(name = "NEW_ETIC_TEXT")
    public String newEticText;


    public Object newOldComment;

//    @Column(name = "NEW_COMMENT")
//    public String newComment;
//
//    @Column(name = "OLD_COMMENT")
//    private String oldComment;


    @Column(name = "NEW_ETIC_DT_TM")
    public Timestamp newEticDtTm;

    @Column(name="FEDEXID_NBR")
    private String fedexIdNumber;

    @Column(name = "OST")
    private String ost;

    @Column(name = "ETIC_RSN_CD")
    public String eticRsnCd;

    @Column(name = "ETIC_RSN_COMMENT")
    public String eticRsnComment;

    @Column(name = "MEM_DESK_CONTACT")
    private String memDeskContact;

}

