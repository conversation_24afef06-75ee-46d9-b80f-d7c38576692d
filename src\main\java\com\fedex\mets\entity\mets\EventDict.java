package com.fedex.mets.entity.mets;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "EVENT_DICT")
public class EventDict {

    @Id
    @Column(name = "TYPE", nullable = false)
    private String type;

    @Column(name = "DESCRIPTION")
    private String description;

    @Column(name = "LIST_ORDER")
    private Integer listOrder;

    @Column(name = "NARRATIVE")
    private String narrative;

    @Column(name = "SUPER_STATUS")
    private String superStatus;

    @Column(name = "SUPER_ETIC")
    private String superEtic;
}