package com.fedex.mets.data;

import com.fedex.mets.entity.mets.EventMsns;
import lombok.*;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class DetailViewDataEntity {


	public int eventID;
	public String eventType;
	public String startDateTime;
	public String startDateTimeUTC;
	public String endDateTime;
	public String eventACN;
	public String eventFleetDesc;
	public String eventStation;
	public String eventStatus;
	public String eventEticDateTime;
	public String eventEticText;
	public String eventCurrentComment;
	public String eventOST;
	public String eventLastUpdateDateTime;
	public String eventLastUpdatedBy;
	public String eventCreatedDateTime;
	public String eventCreatedBy;
	public String eventOnwerGroupId;
	public String acOwnerGroupId;
	public String errorText;
	public String gate;
	public String mxSpeedDial;
	public String crew;
	public String contact;
	public String eventEticReasonCd;
	public String eventEticReasonComment;

	public String inboundFlightNumber;
	public String inboundFlightDate;
	public String inboundLegNumber;
	public String inboundLegDate;
	public String inboundOrigination;
	public String inboundFlightDepartureTime;
	public String inboundDestination;
	public String inboundArrivalDate;
	public String inboundArrivalTime;

	public String outboundFlightNumber;
	public String outboundFlightDate;
	public String outboundLegNumber;
	public String outboundLegDate;
	public String outboundOrigination;
	public String outboundFlightDepartureTime;
	public String outboundDestination;
	public String outboundArrivalDate;

	public String equipmentType;

	public boolean activeTimer = false;
	public boolean doaAlert = false;
	public String numberOfDiscrepancies;
	public String requestStatus;
	public int changeType;
	public List<DOADiscrepancyData> linkedDiscList;
	public List<EventMsns> linkedMsns;
	public String numberOfMSN;
	public String groupId;
	public List<String> contactInfoOwnerList;
	private DOADataEntity doaData = null;
	public boolean isEventCancelled = false;
	public String eventOriginalComment;
	public boolean isEventActive = false;
	public String managerNote;
	public String eventNewStatus;
	public String doaFlightNumber;
	public String doaFlightDate;
	public String doaFlightLegNumber;
	public String resMgrId;
	public String memDeskContact;
	public String duration;

}
