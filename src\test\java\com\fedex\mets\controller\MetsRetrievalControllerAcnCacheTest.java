package com.fedex.mets.controller;

import com.fedex.mets.dao.AcnCacheDetail;
import com.fedex.mets.service.retrieval.*;
import com.fedex.mets.util.JsonFileUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(MetsRetrievalController.class)
@AutoConfigureMockMvc(addFilters = false)
class MetsRetrievalControllerAcnCacheTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private AcnCacheService acnCacheService;
    
    @MockBean
    private JsonFileUtil jsonFileUtil;
    
    // Mock all other services used by MetsRetrievalController
    @MockBean private EventListViewService eventListViewService;
    @MockBean private ReportingCategoriesService rcService;
    @MockBean private MOCCRegionService moccRegionService;
    @MockBean private EventListDetailViewService eventListDetailViewService;
    @MockBean private EventFlightEticInfoService eventFlightEticInfoService;
    @MockBean private NIWTimersService niwTimerService;
    @MockBean private DiscrepancyDetailService discrepancyDetailService;
    @MockBean private EventDiscrepanciesService eventDiscrepanciesService;
    @MockBean private MsnDetailService msnDetailService;
    @MockBean private FlightLegDetailService flightLegDetailService;
    @MockBean private TfNotesEmailService tubFlileEmailServive;

    private HashMap<String, List<?>> mockCacheData;

    @BeforeEach
    void setUp() {
        mockCacheData = new HashMap<>();
        List<AcnCacheDetail> acnDetailList = new ArrayList<>();
        
        AcnCacheDetail detail1 = new AcnCacheDetail();
        detail1.setAcn("ACN123");
        detail1.setFleetCode("B777");
        detail1.setStatus("A");
        acnDetailList.add(detail1);
        
        AcnCacheDetail detail2 = new AcnCacheDetail();
        detail2.setAcn("ACN456");
        detail2.setFleetCode("B767");
        detail2.setStatus("A");
        acnDetailList.add(detail2);
        
        mockCacheData.put("ACN_CACHE_DETAIL", acnDetailList);
        
        acnCacheService.jsonFileUtil = jsonFileUtil;
    }

    @Test
    void testGetAcnCacheData_Success() throws Exception {
        when(acnCacheService.getAcnCacheFromFile()).thenReturn(mockCacheData);
        
        mockMvc.perform(get("/api/mets/retrieval/acn-cache"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.ACN_CACHE_DETAIL").isArray())
                .andExpect(jsonPath("$.ACN_CACHE_DETAIL.length()").value(2))
                .andExpect(jsonPath("$.ACN_CACHE_DETAIL[0].acn").value("ACN123"))
                .andExpect(jsonPath("$.ACN_CACHE_DETAIL[1].acn").value("ACN456"));
    }

    @Test
    void testGetAcnCacheData_NotFound() throws Exception {
        when(acnCacheService.getAcnCacheFromFile()).thenReturn(null);
        
        mockMvc.perform(get("/api/mets/retrieval/acn-cache"))
                .andExpect(status().isNotFound())
                .andExpect(content().string("ACN cache data not found"));
    }

    @Test
    void testGetAcnCacheData_EmptyCache() throws Exception {
        HashMap<String, List<?>> emptyCacheData = new HashMap<>();
        emptyCacheData.put("ACN_CACHE_DETAIL", new ArrayList<>());
        
        when(acnCacheService.getAcnCacheFromFile()).thenReturn(emptyCacheData);
        
        mockMvc.perform(get("/api/mets/retrieval/acn-cache"))
                .andExpect(status().isNotFound())
                .andExpect(content().string("ACN cache details not found or empty"));
    }

    @Test
    void testGetAcnCacheData_Exception() throws Exception {
        when(acnCacheService.getAcnCacheFromFile()).thenThrow(new RuntimeException("Test exception"));
        
        mockMvc.perform(get("/api/mets/retrieval/acn-cache"))
                .andExpect(status().isInternalServerError())
                .andExpect(content().string("Error retrieving ACN cache data: Test exception"));
    }

    @Test
    void testUpdateAcnCacheData_Success() throws Exception {
        when(acnCacheService.getAcnCacheDetail()).thenReturn(mockCacheData);
        when(jsonFileUtil.saveToJsonFile(eq(AcnCacheService.ACN_CACHE_FILE_PATH), any())).thenReturn(true);
        
        mockMvc.perform(get("/api/mets/retrieval/acn-cache/update"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.ACN_CACHE_DETAIL").isArray())
                .andExpect(jsonPath("$.ACN_CACHE_DETAIL.length()").value(2))
                .andExpect(jsonPath("$.ACN_CACHE_DETAIL[0].acn").value("ACN123"))
                .andExpect(jsonPath("$.ACN_CACHE_DETAIL[1].acn").value("ACN456"));
    }

    @Test
    void testUpdateAcnCacheData_SaveFailure() throws Exception {
        when(acnCacheService.getAcnCacheDetail()).thenReturn(mockCacheData);
        when(jsonFileUtil.saveToJsonFile(eq(AcnCacheService.ACN_CACHE_FILE_PATH), any())).thenReturn(false);
        
        mockMvc.perform(get("/api/mets/retrieval/acn-cache/update"))
                .andExpect(status().isInternalServerError())
                .andExpect(content().string("Failed to save ACN cache data to JSON file"));
    }

    @Test
    void testUpdateAcnCacheData_Exception() throws Exception {
        when(acnCacheService.getAcnCacheDetail()).thenThrow(new RuntimeException("Test exception"));
        
        mockMvc.perform(get("/api/mets/retrieval/acn-cache/update"))
                .andExpect(status().isInternalServerError())
                .andExpect(content().string("Error updating ACN cache data: Test exception"));
    }
}
