
package com.fedex.mets.wsdl.discrepancy;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for DiscrepancySpecDetail complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="DiscrepancySpecDetail">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="AcnDiscrepancyOid" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="AircraftNbr" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="AtaNbr" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="DscrpNbr" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="deferralType" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="modType" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="modNumber" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="workRlsPriority" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="specDetails" type="{http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd}SpecDetail" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "DiscrepancySpecDetail", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", propOrder = {
    "acnDiscrepancyOid",
    "aircraftNbr",
    "ataNbr",
    "dscrpNbr",
    "deferralType",
    "modType",
    "modNumber",
    "workRlsPriority",
    "specDetails"
})
public class DiscrepancySpecDetail {

    @XmlElement(name = "AcnDiscrepancyOid", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal acnDiscrepancyOid;
    @XmlElement(name = "AircraftNbr", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String aircraftNbr;
    @XmlElement(name = "AtaNbr", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal ataNbr;
    @XmlElement(name = "DscrpNbr", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String dscrpNbr;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String deferralType;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String modType;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String modNumber;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String workRlsPriority;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected List<SpecDetail> specDetails;

    /**
     * Gets the value of the acnDiscrepancyOid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAcnDiscrepancyOid() {
        return acnDiscrepancyOid;
    }

    /**
     * Sets the value of the acnDiscrepancyOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAcnDiscrepancyOid(BigDecimal value) {
        this.acnDiscrepancyOid = value;
    }

    /**
     * Gets the value of the aircraftNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAircraftNbr() {
        return aircraftNbr;
    }

    /**
     * Sets the value of the aircraftNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAircraftNbr(String value) {
        this.aircraftNbr = value;
    }

    /**
     * Gets the value of the ataNbr property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAtaNbr() {
        return ataNbr;
    }

    /**
     * Sets the value of the ataNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAtaNbr(BigDecimal value) {
        this.ataNbr = value;
    }

    /**
     * Gets the value of the dscrpNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDscrpNbr() {
        return dscrpNbr;
    }

    /**
     * Sets the value of the dscrpNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDscrpNbr(String value) {
        this.dscrpNbr = value;
    }

    /**
     * Gets the value of the deferralType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDeferralType() {
        return deferralType;
    }

    /**
     * Sets the value of the deferralType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDeferralType(String value) {
        this.deferralType = value;
    }

    /**
     * Gets the value of the modType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getModType() {
        return modType;
    }

    /**
     * Sets the value of the modType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setModType(String value) {
        this.modType = value;
    }

    /**
     * Gets the value of the modNumber property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getModNumber() {
        return modNumber;
    }

    /**
     * Sets the value of the modNumber property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setModNumber(String value) {
        this.modNumber = value;
    }

    /**
     * Gets the value of the workRlsPriority property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWorkRlsPriority() {
        return workRlsPriority;
    }

    /**
     * Sets the value of the workRlsPriority property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWorkRlsPriority(String value) {
        this.workRlsPriority = value;
    }

    /**
     * Gets the value of the specDetails property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the specDetails property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getSpecDetails().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link SpecDetail }
     * 
     * 
     */
    public List<SpecDetail> getSpecDetails() {
        if (specDetails == null) {
            specDetails = new ArrayList<SpecDetail>();
        }
        return this.specDetails;
    }

}
