package com.fedex.mets.repository;

import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.net.URI;
import java.net.URL;
import java.net.URLConnection;
import java.sql.Connection;
import java.sql.Date;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;

import com.fedex.mets.data.DOADataEntity;
import com.fedex.mets.util.IServerConstants;
import com.fedex.mets.util.ServerDateHelper;

//@Repository
public class EventDOAUpdateRepository {
	
    private final JdbcTemplate metsJdbcTemplate;
	
	private static final Logger logger = LoggerFactory.getLogger(EventDOAUpdateRepository.class);
	
    public EventDOAUpdateRepository(
            @Qualifier("metsJdbcTemplate") JdbcTemplate metsJdbcTemplate) {
        
        this.metsJdbcTemplate = metsJdbcTemplate;
    }
	
	public int findLastUpdated(DOADataEntity doaData) throws Exception {
		
		Statement stat = null;
		Connection conn = null;
		ResultSet rs = null;
		String queryString = "";

		int eventId = 0, existingRecords = 0;

		String lastUpdated = "";

		eventId = doaData.getEventId();
		lastUpdated = doaData.getLastUpdated();

		try {
			conn = metsJdbcTemplate.getDataSource().getConnection();
			stat = conn.createStatement();

			String lookupUpdatedTime = ServerDateHelper.getLookUpFormat(lastUpdated);

			queryString = "Select count(*) from EVENT_DOA where EVENT_ID='"
					+ eventId
					+ "' and LAST_UPDATE_DT_TM > to_date('"
					+ lookupUpdatedTime
					+ "','mm/dd/yy hh24:mi:ss')";

			logger.info("Look up query==========" + queryString);

			rs = stat.executeQuery(queryString);

			while (rs.next()) {
				existingRecords = rs.getInt(1);
				logger.info("existingRecords ========================" + existingRecords);
			}

		} catch (Exception count) {
			logger.warn(" ERROR findLastUpdated() >>" + count.getMessage());
		} finally {
			try {
				if (rs != null)
					rs.close();

				if (stat != null)
					stat.close();

				if (conn != null)
					conn.close();

			} catch (Exception e) {
				logger.warn(" ERROR findLastUpdated() connection closing>>" + e.getMessage());
			}
		}

		return existingRecords;
	}
	
	public boolean addEventDOAData(DOADataEntity doaData,boolean isEventActive) throws Exception {
		logger.info("In the addEventDOAData of the EventDOAUpdateSessionBean..........");
		boolean result = false;

		int eventId = 0;
		boolean checkFleet = false, maintCW = false;
		String strCheckFleet = "N", strMaintCW = "N";

		eventId = doaData.getEventId();

		checkFleet = doaData.isCheckFlightRequrired();
		if (checkFleet)
			strCheckFleet = "Y";

		maintCW = doaData.isMaintenanceCrew();
		if (maintCW)
			strMaintCW = "Y";

		logger.info("eventId==" + eventId);
		logger.info("strMaintCW==" + strMaintCW);
		logger.info("strCheckFleet==" + strCheckFleet);
		logger.info("doaData.getFlightNumber()==" + doaData.getFlightNumber());
		logger.info("doaData.getFlightLegNumber()==" + doaData.getFlightLegNumber());

		String strFlightNumber = "",
			strFlightLegNumber = "",
			strFlightACN = "",
			strFlightDestination = "",
			strFlightStatus = "",
			strFlightType = "",
			strTotalDelay = "",
			strFlightOrigniation = "";

		List<Object> listFlightData = new ArrayList<Object>(),
			listTime = new ArrayList<Object>(),
			listCD = new ArrayList<Object>();
		List<Object> forteResultsList = new ArrayList<Object>();

		Timestamp schDepartureTime = null,
			actDepartureTime = null,
			schArrivalTime = null,
			actArrivalTime = null;
		Date dateFlightDate = null;

		if (doaData.getFlightNumber() != null
			&& doaData.getFlightDate() != null
			&& doaData.getFlightDate().trim().length() > 0
			&& doaData.getFlightLegNumber() != null) {
			
//			String strMetForteServletURL = ConfigHelper.getProperty("MetsForteServletURL");
			String strMetForteServletURL = null; // This line is added as replacement for above line for time being until ConfigHelper works. 
			
			try {
				
				String strLookupDate = ServerDateHelper.getLookUpFormat(doaData.getFlightDate());
				HashMap<String, Object> hMap = new HashMap<String, Object>();

				hMap.put(IServerConstants.MODE, IServerConstants.FLIGHT_DETAILS);
				hMap.put(IServerConstants.FLIGHT_NUMBER, doaData.getFlightNumber());
				hMap.put(IServerConstants.FLIGHT_DATE, strLookupDate);
				hMap.put(IServerConstants.FLIGHT_LEG_NUMBER, doaData.getFlightLegNumber());

				URI uri = new URI(strMetForteServletURL);
				URL url = uri.toURL(); 
				URLConnection conn = url.openConnection();
				conn.setDoInput(true);
				conn.setDoOutput(true);
				logger.info("strURL ------------ " + strMetForteServletURL);
				
				ObjectOutputStream objOut = new ObjectOutputStream(conn.getOutputStream());
				objOut.writeObject(hMap);
				objOut.flush();
				objOut.close();

				ObjectInputStream objIn = new ObjectInputStream(conn.getInputStream());
				Map<String, Object> resultData = (Map<String, Object>) objIn.readObject();

				if (resultData.get(IServerConstants.ERROR) == null) {
					forteResultsList = (List<Object>) resultData.get(IServerConstants.FLIGHT_DETAILS_RESULTS);

					if (forteResultsList != null && forteResultsList.size() == 3) {
						
						listFlightData = (List<Object>) forteResultsList.get(0);
						listTime = (List<Object>) forteResultsList.get(1);
						listCD = (List<Object>) forteResultsList.get(2);
					}
				}

				logger.info("Forte Interface Server Err Val: " + resultData.get(IServerConstants.ERROR));
				logger.info("vectFlightData size ==> " + listFlightData.size());

				if (listFlightData != null && listFlightData.size() >= 12) {
					
					strFlightNumber = (String) listFlightData.get(0);
					dateFlightDate = (Date) listFlightData.get(1);
					strFlightLegNumber = (String) listFlightData.get(2);
					strFlightACN = (String) listFlightData.get(3);
					strFlightDestination = (String) listFlightData.get(4);
					strFlightStatus = (String) listFlightData.get(5);
					strFlightType = (String) listFlightData.get(6);
					schDepartureTime = (Timestamp) listFlightData.get(7);
					actDepartureTime = (Timestamp) listFlightData.get(8);
					strTotalDelay = (String) listFlightData.get(9);
					strFlightOrigniation = (String) listFlightData.get(10);
					schArrivalTime = (Timestamp) listFlightData.get(11);
					actArrivalTime = (Timestamp) listFlightData.get(12);
					
				}
			} catch (Exception e) {
				
				logger.warn(" ERROR updateFlight()  >>" + e.getMessage());
				throw new Exception(e.getMessage());
				
			}
		}
		
		Statement stat = null;
		Connection conn = null;
		ResultSet rs = null;
		int totalRecords = 0;
		String queryString = "";

		try {
			conn = metsJdbcTemplate.getDataSource().getConnection();
			stat = conn.createStatement();

			queryString = "SELECT * FROM EVENT_DOA WHERE EVENT_ID=" + eventId;

			logger.info("Look up query ==========" + queryString);

			rs = stat.executeQuery(queryString);

			while (rs.next()) {
				totalRecords += 1;
			}

		} catch (Exception e) {
			
			logger.warn(" ERROR EVENT DOA count >> " + e.getMessage());
			
		} finally {
			
			try {
				
				if (rs != null)
					rs.close();
				if (stat != null)
					stat.close();
				if (conn != null)
					conn.close();
				
			} catch (Exception e) {
				logger.warn(" ERROR Event DOA addEventDOA() closing connection >> " + e.getMessage());
			}
			
		}

		if (totalRecords == 0) {
					
				Date sqlFlightDate = null;

				Timestamp etaDateTime = null;

				logger.info("doaData.getEstimatedTimeOfArrival() >> " + doaData.getEstimatedTimeOfArrival());
					
				if (doaData.getEstimatedTimeOfArrival() != null) {
					etaDateTime = ServerDateHelper.getConvertedTimestamp(doaData.getEstimatedTimeOfArrival());
				}
					
				logger.info("etaDateTime  after converting >>>> " + etaDateTime);

				Timestamp lastUpdatedDateTime = ServerDateHelper.getTimeStamp();
				logger.info("lastUpdatedDateTime  after converting -------" + lastUpdatedDateTime);

				if (doaData.getFlightDate() != null) {
						
					sqlFlightDate = ServerDateHelper.getConvertedSQLDate(doaData.getFlightDate());
					logger.info("sqlFlightDate  after converting -------" + sqlFlightDate);
						
				}

				stat = null;
				conn = null;
				queryString = "";
				
				try {
					conn = metsJdbcTemplate.getDataSource().getConnection();
					stat = conn.createStatement();
					
					if(sqlFlightDate != null) {

						queryString = "INSERT INTO EVENT_DOA(EVENT_ID, CHK_FLT, "
								+ "MAINT_CW, DOA_FLT_NUM, DOA_FLT_DATE, DOA_FLT_COMMENTS, "
								+ "DOA_FLT_LEG, LAST_UPDATE_DT_TM, DOA_FLT_ETA, DOA_FLT_DEST) VALUES(" + eventId + ", " + strCheckFleet
								+ ", " + strMaintCW + ", " + doaData.getFlightNumber() + ", " + sqlFlightDate + ", " + doaData.getAdditionalDescription()
								+ ", " + doaData.getFlightLegNumber() + ", " + lastUpdatedDateTime + ", " + schArrivalTime + ", " + strFlightDestination + ")";
						
					} else {
						
						queryString = "INSERT INTO EVENT_DOA(EVENT_ID, CHK_FLT, "
								+ "MAINT_CW, DOA_FLT_NUM, DOA_FLT_COMMENTS, "
								+ "DOA_FLT_LEG, LAST_UPDATE_DT_TM, DOA_FLT_ETA, DOA_FLT_DEST) VALUES(" + eventId + ", " + strCheckFleet
								+ ", " + strMaintCW + ", " + doaData.getFlightNumber() + ", " + doaData.getAdditionalDescription()
								+ ", " + doaData.getFlightLegNumber() + ", " + lastUpdatedDateTime + ", " + schArrivalTime + ", " + strFlightDestination + ")";
						
					}
			

					logger.info("updateQueryString ========================" + queryString);
					int recordsUpdated = stat.executeUpdate(queryString);

					if(recordsUpdated > 0)
						result = true;
					else
						result = false;

				} catch (Exception e) {
					
					result = false;
					logger.warn(" ERROR Event DOA addEventDOA() insert >> " + e.getMessage());
					throw new Exception(e.getMessage());
					
				} finally {
					try {
						
						if (stat != null)
							stat.close();

						if (conn != null)
							conn.close();

					} catch (Exception e) {
						logger.warn(" ERROR Event DOA addEventDOA() 2 connection closing >> " + e.getMessage());
					}
				}
				
		} else {

				Date sqlFlightDate = null;

				Timestamp etaDateTime = null;

				logger.info("doaData.getEstimatedTimeOfArrival() >> " + doaData.getEstimatedTimeOfArrival());
				
				if (doaData.getEstimatedTimeOfArrival() != null) {
					etaDateTime =ServerDateHelper.getConvertedTimestamp(doaData.getEstimatedTimeOfArrival());
				}
				
				logger.info("etaDateTime  after converting >>>> " + etaDateTime);

				Timestamp lastUpdatedDateTime = ServerDateHelper.getTimeStamp();
				logger.info("lastUpdatedDateTime  after converting -------" + lastUpdatedDateTime);

				if (doaData.getFlightDate() != null) {
					
					sqlFlightDate = ServerDateHelper.getConvertedSQLDate(doaData.getFlightDate());
					logger.info("sqlFlightDate  after converting -------" + sqlFlightDate);
					
				}

				stat = null;
				conn = null;
				queryString = "";
				
				try {
					conn = metsJdbcTemplate.getDataSource().getConnection();
					stat = conn.createStatement();
					
					if(sqlFlightDate != null) {
						
						queryString = "UPDATE EVENT_DOA SET CHK_FLT=" + strCheckFleet + ", MAINT_CW=" + strMaintCW + ", DOA_FLT_NUM=" + doaData.getFlightNumber()
								+ ", DOA_FLT_DATE=" + sqlFlightDate + ", DOA_FLT_LEG=" + doaData.getFlightLegNumber() + ", DOA_FLT_COMMENTS=" + doaData.getAdditionalDescription()
								+ ", LAST_UPDATE_DT_TM=" + lastUpdatedDateTime + ", DOA_FLT_ETA=" + schArrivalTime + ", DOA_FLT_DEST=" + strFlightDestination
								+ " WHERE EVENT_ID=" + eventId;
						
					} else {
						
						queryString = "UPDATE EVENT_DOA SET CHK_FLT=" + strCheckFleet + ", MAINT_CW=" + strMaintCW + ", DOA_FLT_NUM=" + doaData.getFlightNumber()
								+ ", DOA_FLT_LEG=" + doaData.getFlightLegNumber() + ", DOA_FLT_COMMENTS=" + doaData.getAdditionalDescription()
								+ ", LAST_UPDATE_DT_TM=" + lastUpdatedDateTime + ", DOA_FLT_ETA=" + schArrivalTime + ", DOA_FLT_DEST=" + strFlightDestination
								+ " WHERE EVENT_ID=" + eventId;
						
					}

					logger.info("updateQueryString ========================" + queryString);
					int recordsUpdated = stat.executeUpdate(queryString);

					if(recordsUpdated > 0)
						result = true;
					else
						result = false;

				} catch (Exception e) {
					
					result = false;
					logger.warn(" ERROR Event DOA addEventDOA() update >> " + e.getMessage());
					throw new Exception(e.getMessage());
					
				} finally {
					try {
						
						if (stat != null)
							stat.close();

						if (conn != null)
							conn.close();

					} catch (Exception e) {
						logger.warn(" ERROR Event DOA addEventDOA() 3 connection closing >> " + e.getMessage());
					}
				}

		}

		if (result) {
			
			stat = null;
			conn = null;
			queryString = "";
			
			try {
				conn = metsJdbcTemplate.getDataSource().getConnection();
				stat = conn.createStatement();
					
				queryString = "UPDATE EVENTS SET STATION=" + strFlightDestination + " WHERE EVENT_ID=" + eventId + " AND TRIM(TYPE)=DOA";

				logger.info("updateQueryString ========================" + queryString);
				int recordsUpdated = stat.executeUpdate(queryString);

				if(recordsUpdated > 0)
					result = true;
				else
					result = false;

			} catch (Exception e) {
				
				result = false;
				logger.warn(" ERROR Event DOA addEventDOA() update event>> " + e.getMessage());
				throw new Exception(e.getMessage());
				
			} finally {
				try {
					
					if (stat != null)
						stat.close(); 

					if (conn != null)
						conn.close();

				} catch (Exception e) {
					logger.warn(" ERROR Event DOA addEventDOA() 4 connection closing >> " + e.getMessage());
				}
			}
		}

		return result;
	}
	
	public boolean deleteEventDOAData(DOADataEntity doaData) throws Exception {
		
		logger.info("in the deleteEventDOAData of the EventDOAUpdateSessionBean ..........");
		boolean result = false;


		int eventId = doaData.getEventId();

		logger.info("eventId	   =" + eventId);
		
		Statement stat = null;
		Connection conn = null;
		String queryString = "";
		
		try {
			conn = metsJdbcTemplate.getDataSource().getConnection();
			stat = conn.createStatement();
			
			queryString = "DELETE FROM EVENT_DOA WHERE EVENT_ID=" + eventId;
			logger.info("updateQueryString ========================" + queryString);
			int recordsUpdated = stat.executeUpdate(queryString);

			if(recordsUpdated > 0)
				result = true;
			else
				result = false;

		} catch (Exception e) {
			result = false;
			logger.warn(" ERROR deleteEventDOA() delete >> " + e.getMessage());
			throw new Exception(e.getMessage());
			
		} finally {
			try {
				
				if (stat != null)
					stat.close();

				if (conn != null)
					conn.close();

			} catch (Exception e) {
				logger.warn(" ERROR deleteEventDOA() connection closing >> " + e.getMessage());
			}
		}

		return result;
	}
	
	public boolean closeEventDOAData(DOADataEntity doaData) throws Exception {

		return deleteEventDOAData(doaData);
		
	}
	
	public boolean cancelEventDOAData(DOADataEntity doaData) throws Exception {
		logger.info("in the cancelEventDOAData of the EventDOAUpdateSessionBean ..........");
		boolean result = false;

		int eventId = doaData.getEventId();

		logger.info("eventId	   =" + eventId);

		Statement stat = null;
		Connection conn = null;
		String queryString = "";
		
		try {
			conn = metsJdbcTemplate.getDataSource().getConnection();
			stat = conn.createStatement();
			
			Timestamp updateTime = ServerDateHelper.getTimeStamp();
			
			queryString = "UPDATE EVENTS SET ACTIVE_EVENT='N', CANCELLED='Y' WHERE EVENT_ID=" + eventId;

			logger.info("updateQueryString ========================" + queryString);
			int recordsUpdated = stat.executeUpdate(queryString);

			if(recordsUpdated > 0)
				result = true;
			else
				result = false;

		} catch (Exception e) {
			
			result = false;
			logger.warn(" ERROR cancelEventDOAData() update >> " + e.getMessage());
			throw new Exception(e.getMessage());
			
		} finally {
			try {
				
				if (stat != null)
					stat.close();

				if (conn != null)
					conn.close();

			} catch (Exception e) {
				logger.warn(" ERROR cancelEventDOAData() connection closing >> " + e.getMessage());
			}
		}

		return result;
	}
	
	public boolean validateFlight(DOADataEntity doaData) throws Exception {
		boolean result = false;
		
//		String strMetForteServletURL = ConfigHelper.getProperty("MetsForteServletURL");
		String strMetForteServletURL = "";
		
		try {
			String strLookupDate = ServerDateHelper.getLookUpFormat(doaData.getFlightDate());

			Map<String, String> hMap = new HashMap<String, String>();

			hMap.put(IServerConstants.MODE, IServerConstants.VALIDATE_FLIGHT);
			hMap.put(IServerConstants.FLIGHT_NUMBER, doaData.getFlightNumber());
			hMap.put(IServerConstants.FLIGHT_DATE, strLookupDate);
			hMap.put(IServerConstants.FLIGHT_LEG_NUMBER, doaData.getFlightLegNumber());

			URI uri = new URI(strMetForteServletURL);
			URL url = uri.toURL();
			URLConnection conn = url.openConnection();
			conn.setDoInput(true);
			conn.setDoOutput(true);
			logger.info("strURL ------------ " + strMetForteServletURL);
			ObjectOutputStream objOut = new ObjectOutputStream(conn.getOutputStream());
			objOut.writeObject(hMap);
			objOut.flush();
			objOut.close();

			ObjectInputStream objIn = new ObjectInputStream(conn.getInputStream());
			Map<String, String> reusltData = (HashMap<String, String>) objIn.readObject();

			if (reusltData.get(IServerConstants.ERROR) == null) {
				String strResult = (String) reusltData.get(IServerConstants.VALID_FLIGHT);

				if (strResult != null && strResult.trim().equals("TRUE"))
					result = true;
			}
		} catch (Exception e) {
			logger.warn(" ERROR validateFlight() e >>" + e.getMessage());
		}

		return result;
	}
	
	public void securityCheck(String userId, String tokenId, boolean isEventActive) throws Exception {
		
		try {
			
			List<Object> acessFlagList = null;

			if (userId != null && tokenId != null) {
				try{
					
//					acessFlagVector = SecurityHelper.getAccessFlags(userId, tokenId, strTransactionId);
					acessFlagList = new ArrayList<Object>();
					acessFlagList.add("SUCCESS");
					acessFlagList.add("80");
					
				} catch(Exception e){
					logger.warn("ERROR NIWTimersUpdateRepo setNIWTimers() SecurityHelper.getAccessFlags exception " + e.getMessage());
					throw new Exception("512");
				}
			}

			//for Start/Stop Timer check access flag 1 is 80 or 90 else throw exception
			if (acessFlagList != null) {
				String firstElement = (String) acessFlagList.get(0);

				if (firstElement.trim().equals("SUCCESS")) {
					if (isEventActive) {
						String strSecurityAccess = (String) acessFlagList.get(1);

						if (strSecurityAccess.trim().equals("80") || strSecurityAccess.trim().equals("90")) {
							
							logger.info(" ");
							logger.info("User " + userId + " has access to START/STOP Timer of an Event.");
							
						} else {
							throw new Exception("User does not have permission/access to START/STOP Timer of to an Event.");
						}
						
					} else {
						String strSecurityAccess = (String) acessFlagList.get(2);

						if (strSecurityAccess.trim().equals("99")) {
							
							logger.info(" ");
							logger.info("User " + userId + " has access to START/STOP Timer of an Event.");
							
						} else {
							throw new Exception("User does not have permission/access to to START/STOP Timer of an Inactive Event.");
						}
					}
				} else {
					
					if (firstElement.trim().equals("512")) {
						throw new Exception(firstElement);
					} else {
						String strSecurityAccessError = (String) acessFlagList.get(1);
						throw new Exception(strSecurityAccessError);
					}
					
				}
			} else if (acessFlagList == null) {
				throw new Exception("User does not have permission/access to START/STOP Timer of an Event.");
			}
			
		} catch (Exception securityRemote) {
			
			logger.warn(" ERROR NIW Timers setNIWTimerDetails() securityRemote >> " + securityRemote.getMessage());
			throw new Exception(securityRemote.getMessage());
			
		}
		
	}
	

}
