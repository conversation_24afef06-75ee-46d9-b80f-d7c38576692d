package com.fedex.mets.util;

import com.fedex.mets.dto.DscrpTxt;
import com.fedex.mets.wsdl.discrepancy.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.xml.datatype.XMLGregorianCalendar;
import java.util.*;

public class DiscrepancyTextProcessor {
    public static final String DT_TM_Format = "ddMMMyy HH:mm";
    public static final String DT_Format = "ddMMMyy";
    public static final String SMIS_S_KEY = "S";
    public static final String SMIS_E_KEY = "E";
    public static final String MDIS_KEY = "M";
    public static final String PDIS_KEY = "P";
    public static final String LMPI_KEY = "L";
    public static final String MTSI_KEY = "T";
    public static final String IERR_KEY = "I";
    public static final String NDIS_KEY = "N";
    private static final Logger logger = LoggerFactory.getLogger(DiscrepancyTextProcessor.class);
    private static final String CREATE_TYPE = "B";
    private static final String PLANNED_TEXT_TYPE = "X";
    /**
     * Comparator: compare by add date
     */
    public static Comparator<AcnDiscrepancyMaintUpdt> ACN_DISCREPANCY_MAINT_UPDT_SORT_WORK_DT = new Comparator<AcnDiscrepancyMaintUpdt>() {
        public int compare(AcnDiscrepancyMaintUpdt o1, AcnDiscrepancyMaintUpdt o2) {
            // give highest priority to type 'B'
            if (CREATE_TYPE.equals(o1.getMaintUpdateType())) {
                return -1;
            } else if (CREATE_TYPE.equals(o2.getMaintUpdateType())) {
                return 1;
            }
            // Planned Items Text
            if (PLANNED_TEXT_TYPE.equals(o1.getMaintUpdateType())) {
                return -1;
            } else if (PLANNED_TEXT_TYPE.equals(o2.getMaintUpdateType())) {
                return 1;
            }
            if (o1.getWorkDate() == null || o2.getWorkDate() == null) {
                return o1.getAcnDscrpMaintUpdtOid().compareTo(o2.getAcnDscrpMaintUpdtOid());
            }
            int index = o1.getWorkDate().compare(o2.getWorkDate());
            if (index == 0) {
                return o1.getAcnDscrpMaintUpdtOid().compareTo(o2.getAcnDscrpMaintUpdtOid());
            }
            return index;
        }
    };
    // Token to denote an image is to be inserted here
    private static String IMAGE_TOKEN = "\uFFFC";
    private static List<String> splitExpList = new ArrayList<String>();
    private static List<String> riSplitExpList = new ArrayList<String>();
    private static List<String> partSplitExpList = new ArrayList<String>();
    private static List<DscrpTxt> dscrpTexts;
    private static DscrpTxt dscrpTxt;
    private static Map<String, String> typesMainframeMatch = new HashMap<String, String>() {
        {
            put(SMIS_E_KEY, "SMIS");
            put(IERR_KEY, "IERR");
            put(LMPI_KEY, "LMPI");
            put(MDIS_KEY, "MDIS");
            put(PDIS_KEY, "PDIS");
            put(SMIS_S_KEY, "SMIS");
            put(MTSI_KEY, "MTSI");
        }
    };

    static {
        splitExpList.add("OIL Reference:");
        splitExpList.add("Intermediate");
        //splitExpList.add("SPC:");
        //splitExpList.add("EXP:");
        splitExpList.add("Terminating");
        splitExpList.add("SPC");
        splitExpList.add("SPEC");
        splitExpList.add("EXP");
        splitExpList.add("EXPIRATION");
        splitExpList.add("MCAML OILA ACCOMPLISHED");
        splitExpList.add("INTERMEDIATE REPETITIVE");
        splitExpList.add("Intermediate Repetitive");
        splitExpList.add("for");
        splitExpList.add("MCMEL Spec CHANGE");
        splitExpList.add("Changed by:");
        riSplitExpList.add(" OFF ");
        riSplitExpList.add("SRL: ");
        riSplitExpList.add("SRL ");
        riSplitExpList.add("Company Part Nbr: ");
        riSplitExpList.add("MPN: ");
        riSplitExpList.add("MSN: ");
        riSplitExpList.add("MFG ");
        riSplitExpList.add(" ON ");
        partSplitExpList.add("IDENTIFIED PARTD");
        partSplitExpList.add("DELETED PARTS");
        partSplitExpList.add("ORDER PART");
        partSplitExpList.add("ADDED");
        partSplitExpList.add("UPDATED");
        partSplitExpList.add("DELETED");
        partSplitExpList.add("CHANGE PARTS QTY");
        partSplitExpList.add("MPN");
        partSplitExpList.add("MSN");
    }

    public Boolean fetchOnlyUpdtedTtxsFlag = false;
    protected StringBuffer sb = new StringBuffer();
    //	protected String createdBy;
    protected List<AcnDiscrepancyMaintUpdt> acnDscrpMaintList;
    protected List<AcnDiscrepancy> acnDscrpList;
    protected List<DscrpAttachedLogpageNbrs> dscrpAttachedLogpageNbrs;
    protected List<PlannedItemAsgmtType> plannedItemAsgmtTypes;
    protected HashMap<Long, List<AcnDiscrepancyMaintUpdt>> dscrpOidMaintMap;
    protected HashMap<Long, List<PlannedItemAsgmtType>> dscrpOidPlannedHstryMap;
    protected boolean isSummaryText = false;
    protected StringBuffer dscrUpdtFormattedTxtBuffer;
    protected StringBuffer dscrUpdtTxtBuffer;
    private long previousLmpiTsiOid = 0;


    /**
     * @param acnDscrpList
     * @param acnDscrpMaintList
     */
    public DiscrepancyTextProcessor(List<AcnDiscrepancy> acnDscrpList,
                                    List<AcnDiscrepancyMaintUpdt> acnDscrpMaintList,
                                    List<DscrpAttachedLogpageNbrs> dscrpAttachedLogpageNbrs,
                                    List<PlannedItemAsgmtType> plannedItemAsgmtTypes,
                                    boolean isSummaryText, boolean fetchOnlyUpdtedTtxs) {
        this.acnDscrpList = acnDscrpList;
        this.acnDscrpMaintList = acnDscrpMaintList;
        this.dscrpAttachedLogpageNbrs = dscrpAttachedLogpageNbrs;
        this.isSummaryText = isSummaryText;
        this.plannedItemAsgmtTypes = plannedItemAsgmtTypes;
        this.fetchOnlyUpdtedTtxsFlag = fetchOnlyUpdtedTtxs;
        createDscrpOidMaintMap();
        createDscrpOidPlannedMap(plannedItemAsgmtTypes);
    }

    public static String getDiscrepancyTypeMainframeMatch(String type) {
        String retVal = typesMainframeMatch.get(type);
        if (retVal == null) {
            return StringUtils.EMPTY;
        } else {
            return retVal;
        }
    }

    public static final String formatDiscrepancyNumber(String discrepancyNumber) {
        if (discrepancyNumber != null) {
            discrepancyNumber = discrepancyNumber.replaceFirst("^0+(?!$)", "");
            return String.format("%04d", Integer.parseInt(discrepancyNumber));
        }
        return discrepancyNumber;
    }

    public static boolean isConnectedRecord(AcnDiscrepancyMaintUpdt dscrpMaintUpdt) {
        boolean isConnectedRecord = false;
        if (dscrpMaintUpdt != null) {
            if ((DscrpMaintUptType.MEL.getType().equalsIgnoreCase(dscrpMaintUpdt.getMaintUpdateType())
                    || DscrpMaintUptType.CDL.getType().equalsIgnoreCase(dscrpMaintUpdt.getMaintUpdateType())
                    || DscrpMaintUptType.OIL.getType().equalsIgnoreCase(dscrpMaintUpdt.getMaintUpdateType())
                    || DscrpMaintUptType.NEF.getType().equalsIgnoreCase(dscrpMaintUpdt.getMaintUpdateType())
                    || DscrpMaintUptType.OILS.getType().equalsIgnoreCase(dscrpMaintUpdt.getMaintUpdateType())
                    || DscrpMaintUptType.OILC.getType().equalsIgnoreCase(dscrpMaintUpdt.getMaintUpdateType())
                    || DscrpMaintUptType.OILA.getType().equalsIgnoreCase(dscrpMaintUpdt.getMaintUpdateType())
                    || DscrpMaintUptType.OILX.getType().equalsIgnoreCase(dscrpMaintUpdt.getMaintUpdateType())
            )) {
                isConnectedRecord = true;
            }
        }
        return isConnectedRecord;
    }

    public static List<String> getDscrpMaintText(AcnDiscrepancyMaintUpdt dscrpMaintUpdt) {
        List<String> details = new ArrayList<String>();
        String[] maintText = getDscrpDeferralText(dscrpMaintUpdt).split("\n");
        for (String text : maintText) {
            details.add(text.trim());
        }
        return details;
    }

    public static String getDscrpDeferralText(AcnDiscrepancyMaintUpdt dscrpMaintUpdt) {
        StringBuilder text = new StringBuilder();
        if (dscrpMaintUpdt != null) {
            if (isConnectedRecord(dscrpMaintUpdt)) {

                for (DscrpProcessControl processControl : dscrpMaintUpdt.getMaintPrcsControl()) {
                    if ("MEL_RSVD".equalsIgnoreCase(processControl.getProcessControlTypeCd())) {
                        text.append(processControl.getStatusReasonDesc());
                        break;
                    }
                }

            }
            if (dscrpMaintUpdt.getAutoGenText() != null && !dscrpMaintUpdt.getAutoGenText().isEmpty()) {
                text.append(dscrpMaintUpdt.getAutoGenText());
                if (!dscrpMaintUpdt.getAutoGenText().endsWith("\n")) {
                    text.append("\n");
                }

            }
            if (dscrpMaintUpdt.getMaintUpdateText() != null && !dscrpMaintUpdt.getMaintUpdateText().isEmpty()) {
                text.append(dscrpMaintUpdt.getMaintUpdateText());
            }

            if (DscrpMaintUptType.OILS.getType().equalsIgnoreCase(dscrpMaintUpdt.getMaintUpdateType())
                    || DscrpMaintUptType.OILC.getType().equalsIgnoreCase(dscrpMaintUpdt.getMaintUpdateType())
                    || DscrpMaintUptType.OILA.getType().equalsIgnoreCase(dscrpMaintUpdt.getMaintUpdateType())
            ) {
                String oilspec = text.toString().replaceAll("\n", " ");
                return formantOilText(oilspec, 0);
            }

            if (DscrpMaintUptType.R_I.getType().equalsIgnoreCase(dscrpMaintUpdt.getMaintUpdateType())
            ) {

                return formantRemoveInstallText(text.toString());
            }
            if (DscrpMaintUptType.PART.getType().equalsIgnoreCase(dscrpMaintUpdt.getMaintUpdateType())
            ) {

                return formantPartText(text.toString());
            }
        }
        return text.toString();

    }

    public static String formantOilText(String text, int lineCount) {
        if (!text.contains("\n")) {

            for (String expersText : splitExpList) {
                text = text.replace(expersText, ("\n" + expersText.trim() + " "));
            }

        }
        if (text.startsWith("\n")) {
            text = text.substring(1);
        }
        return text;
    }

    public static String formantPartText(String text) {
        if (!text.contains("\n")) {
            for (String expersText : partSplitExpList) {
                text = text.replace(expersText, ("\n" + expersText.trim() + " "));
            }

        }
        if (text.startsWith("\n")) {
            text = text.substring(1);
        }
        return text;
    }

    public static String formantRemoveInstallText(String text) {
        if (!text.contains("\n")) {
            text = text.replace("OFFMPN:", " OFF MPN:");
            text = text.replace("ONMPN:", " ON MPN:");
            text = text.replace("OFF MFG", " OFF MFG ");
            text = text.replace("ON MFG", " ON MFG ");
            for (String expersText : riSplitExpList) {
                text = text.replace(expersText, ("\n" + expersText.trim() + " "));
            }

        }
        return text;
    }

    private void createDscrpOidPlannedMap(List<PlannedItemAsgmtType> plannedItemAsgmtTypes) {
        dscrpOidPlannedHstryMap = new HashMap<Long, List<PlannedItemAsgmtType>>();

        if (plannedItemAsgmtTypes != null) {
            for (PlannedItemAsgmtType plannedItemAsgmtType : plannedItemAsgmtTypes) {
                if (dscrpOidPlannedHstryMap.containsKey(plannedItemAsgmtType
                        .getDiscrepancyOid().longValue())) {
                    dscrpOidPlannedHstryMap.get(
                            plannedItemAsgmtType.getDiscrepancyOid()
                                    .longValue()).add(plannedItemAsgmtType);
                } else {
                    List<PlannedItemAsgmtType> plannedHstryLst = new ArrayList<PlannedItemAsgmtType>();
                    plannedHstryLst.add(plannedItemAsgmtType);
                    dscrpOidPlannedHstryMap.put(plannedItemAsgmtType
                            .getDiscrepancyOid().longValue(), plannedHstryLst);
                }

            }
        }

    }

    protected void createDscrpOidMaintMap() {
        if (dscrpOidMaintMap == null) {
            dscrpOidMaintMap = new HashMap<Long, List<AcnDiscrepancyMaintUpdt>>();

            // get maint text into each discrepancy and put in hash map
            if (acnDscrpMaintList != null) {
                for (int i = 0; i < acnDscrpMaintList.size(); i++) {
                    AcnDiscrepancyMaintUpdt updt = acnDscrpMaintList.get(i);
                    logger.info("acnDiscrepancy : " + updt.getAcnDiscrepancyOid() + " maintOid:" + updt.getAcnDscrpMaintUpdtOid());

                    List<AcnDiscrepancyMaintUpdt> dscrpUpdatesListForOid = dscrpOidMaintMap.get(updt.getAcnDiscrepancyOid().longValue());
                    if (dscrpUpdatesListForOid == null) {
                        dscrpUpdatesListForOid = new ArrayList<AcnDiscrepancyMaintUpdt>();

                        dscrpOidMaintMap.put(Long.valueOf(updt.getAcnDiscrepancyOid().longValue()), dscrpUpdatesListForOid);
                    }
                    dscrpUpdatesListForOid.add(updt);
                }
            }

        }
    }

    public List<DscrpTxt> fetchAllUpdatedTxts() {
        createFormatedText();
        return dscrpTexts;
    }

    /**
     * create styled content
     */
    public String createTextContent() throws Exception {
        if (sb == null || sb.toString().trim().length() == 0) {
            logger.info("createTextContent() ...");
            createFormatedText();
        }
        return sb.toString();
    }

    protected void createFormatedText() {
        logger.info("createFormatedText() ...");

        for (AcnDiscrepancy acnDiscrepancy : acnDscrpList) {
            dscrpTexts = new ArrayList<DscrpTxt>();
            boolean isNewTsiDscrp = MTSI_KEY.equals(acnDiscrepancy.getDiscrepancyType()) && acnDiscrepancy.getTsiDefOid() != null;
            List<AcnDiscrepancyMaintUpdt> updateTextList = dscrpOidMaintMap.get(acnDiscrepancy.getAcnDiscrepancyOid().longValue());
            if (updateTextList == null) {
                continue;
            }
            Collections.sort(updateTextList, ACN_DISCREPANCY_MAINT_UPDT_SORT_WORK_DT);

            int count = 0;
            for (AcnDiscrepancyMaintUpdt acnDiscrepancyMaintUpdt : updateTextList) {
                dscrpTxt = new DscrpTxt();
                dscrUpdtTxtBuffer = new StringBuffer();
                dscrUpdtFormattedTxtBuffer = new StringBuffer();

                AcnDiscrepancyMaintUpdt updt = acnDiscrepancyMaintUpdt;

                if ("B".equals(updt.getMaintUpdateType())) {
                    if (isNewTsiDscrp) {
                        if (!isSummaryText) {
                            addLine();
                        }
                        createTsiDiscepancyCreateText(acnDiscrepancy, updt);
                        if (!isSummaryText) {
                            createReportedBy(acnDiscrepancy, updt);
                        }
                    } else {
                        if (!isSummaryText) {
                            addLine();
                        }
                        createDiscepancyCreateText(acnDiscrepancy, updt);
                        if (!isSummaryText) {
                            createReportedBy(acnDiscrepancy, updt);
                            if (!fetchOnlyUpdtedTtxsFlag) {
                                addPlannedItemsHstry(dscrpOidPlannedHstryMap.get(acnDiscrepancy.getAcnDiscrepancyOid().longValue()));
                            }
                        }
                    }
                } else if (MTSI_KEY.equals(acnDiscrepancy.getDiscrepancyType()) || LMPI_KEY.equals(acnDiscrepancy.getDiscrepancyType())) {
                    if (MTSI_KEY.equals(updt.getMaintUpdateType()) || LMPI_KEY.equals(updt.getMaintUpdateType())) {
                        createLmpiTsiUpdateText(updt, count == 0);
                        count++;
                    } else if ("F".equals(updt.getMaintUpdateType()) || isNewTsiDscrp) {
                        if (!isSummaryText) {
                            addLine();
                        }
                        createMaintUpdateText(updt);
                        if (!isSummaryText) {
                            createReportedBy(acnDiscrepancy, updt);
                        }
                    }
                } else {
                    if (!isSummaryText) {
                        addLine();
                    }
                    createMaintUpdateText(updt);
                    if (!isSummaryText) {
                        createReportedBy(acnDiscrepancy, updt);
                    }

                }// not R

                if (isSummaryText) {
                    // only first
                    break;
                }
                dscrpTxt.setActualTxt(dscrUpdtTxtBuffer.toString());
                dscrpTxt.setFormattedTxt(dscrUpdtFormattedTxtBuffer.toString());
                dscrpTexts.add(dscrpTxt);
            }
            fetchOnlyUpdtedTtxsFlag = false; // reset for next discrepancy
        }

    }

    protected void addLine() {
        sb.append("\n");
    }

    protected void createTsiDiscepancyCreateText(AcnDiscrepancy acnDiscrepancy, AcnDiscrepancyMaintUpdt updt) {
        // Heading ..
        if (!isSummaryText) {
            createDiscrepancyHeader(acnDiscrepancy, updt);
            addLogpageNbr(acnDiscrepancy);
        }

        // detail text ...
        DetailLineTsiFormat dlf = new DetailLineTsiFormat();
        List<String> details = new ArrayList<String>();

        if (updt.getMaintUpdateText() != null) {
            if (fetchOnlyUpdtedTtxsFlag) {
                dscrUpdtFormattedTxtBuffer.append(updt.getMaintUpdateText());
            }
            String[] maintText = updt.getMaintUpdateText().split("\n");
            for (String text : maintText) {
                details.add(text);
            }
        }

        //details.add();
        dlf.getSfd().getFields().get(1).setValueList(details);
        addTextAndStyle(dlf);

    }

    protected void createDiscrepancyHeader(AcnDiscrepancy acnDiscrepancy, AcnDiscrepancyMaintUpdt updt) {
        HeadingLineFormat hlf = new HeadingLineFormat();
        hlf.getSfd().getFields().get(0).setValue(getDiscrepancyTypeMainframeMatch(acnDiscrepancy.getDiscrepancyType()));
        hlf.getSfd().getFields().get(1).setValue(acnDiscrepancy.getAircraftNbr());
        hlf.getSfd().getFields().get(2).setValue(getAtaNbr(acnDiscrepancy.getAtaNbr().toString()));
        hlf.getSfd().getFields().get(3).setValue(formatDiscrepancyNumber(acnDiscrepancy.getDscrpNbr()));


        hlf.getSfd().getFields().get(4).setValue(getOpenDate(acnDiscrepancy.getOpenDt()));
        hlf.getSfd().getFields().get(5).setValue(acnDiscrepancy.getOpenStationCd());
        hlf.getSfd().getFields().get(6).setValue(getOpenCloseStr(acnDiscrepancy.getOpenFlg()));
        hlf.getSfd().getFields().get(7).setValue(updt.getReviewFlg());


        if (acnDiscrepancy != null && acnDiscrepancy.isImages()) {
            hlf.getSfd().addTextToSentence(" " + IMAGE_TOKEN);
        }

        addTextAndStyle(hlf);
    }

    protected String getAtaNbr(String ataNbr) {
        String result = ataNbr == null ? "" : ataNbr;
        String prepended = TextFormatUtil.prepend(result, 4, "0");
        return TextFormatUtil.insert(prepended, 2, "-");
    }

    /**
     * @param xmlGregorianCalendar
     * @return
     */
    protected String getOpenDate(XMLGregorianCalendar xmlGregorianCalendar) {
        String result = "";
        try {
            result = DateUtil.formatGMT(xmlGregorianCalendar.toGregorianCalendar().getTime(), DT_Format);
        } catch (Exception e) {

        }
        return result;
    }

    /**
     * @param openFlg
     * @return
     */
    protected String getOpenCloseStr(String openFlg) {
        String result = "";

        if (openFlg != null) {
            result = openFlg;
            if (result.equalsIgnoreCase("C") || result.equalsIgnoreCase("CLOSE")) {
                result = "FACT";
            } else if (result.equalsIgnoreCase("O")) {
                result = "Open";
            }
        }
        return result;
    }

    /**
     * @param dlf
     */
    protected void addTextAndStyle(BaseLineFormat dlf) {

        //TODO: refactor to addText();
        if (fetchOnlyUpdtedTtxsFlag) {
            dscrUpdtTxtBuffer.append(dlf.getSfd().getSentence());
            dscrUpdtTxtBuffer.append("\n");
            return;
        }
        sb.append(dlf.getSfd().getSentence());
        sb.append("\n");

    }

    private void createReportedBy(AcnDiscrepancy acnDiscrepancy, AcnDiscrepancyMaintUpdt updt) {
        ReportedEnteredLLineFormat relf = new ReportedEnteredLLineFormat();


        relf.getSfd().getFields().get(1).setValue("");
        if (updt.getReportedByUserId() != null) {
            relf.getSfd().getFields().get(7).setValue(updt.getReportedByUserId().longValue() + "\n");
        } else {
            relf.getSfd().getFields().get(6).setValue("       ");
            relf.getSfd().getFields().get(7).setValue("        ");
        }

        if (updt.getEnteredByUserId() != null) {
            relf.getSfd().getFields().get(5).setValue(updt.getEnteredByUserId().longValue() + "");
        } else {
            relf.getSfd().getFields().get(5).setValue("        ");
        }
        if (updt.getRiiByUserId() != null) {

            relf.sfd.setLeadingSpace(0);
            relf.sfd.setTrailingSpace(16);

            relf.getSfd().getFields().get(3)
                    .setValue(updt.getRiiByUserId().longValue() + "\t");

        } else {
            relf.getSfd().getFields().get(2).setValue("");
        }
        if (updt.getEtopsAtaSupUserId() != null) {
            relf.getSfd().getFields().get(9)
                    .setValue(updt.getEtopsAtaSupUserId());
        } else {
            relf.getSfd().getFields().get(8).setValue("");
        }

        if (isConnectedRecord(updt)) {
            //AcnDiscrepancy acnDiscr = gacnDiscrepancy;
            if (acnDiscrepancy.getDeferralControlNbr() != null) {
                relf.getSfd().getFields().get(1).setValue(acnDiscrepancy.getDeferralControlNbr().toPlainString());
            } else {
                relf.getSfd().getFields().get(0).setValue("");
                relf.getSfd().getFields().get(1).setValue("");
            }
        } else {
            relf.getSfd().getFields().get(0).setValue("");
            relf.getSfd().getFields().get(1).setValue("");
        }

        addTextAndStyle(relf);

    }

    private void addLogpageNbr(AcnDiscrepancy acnDiscrepancy) {
        if (dscrpAttachedLogpageNbrs != null && !dscrpAttachedLogpageNbrs.isEmpty()) {
            for (DscrpAttachedLogpageNbrs dscrpLogpageDtl : dscrpAttachedLogpageNbrs) {
                if (!dscrpLogpageDtl.getLogpageNbrs().isEmpty() && acnDiscrepancy.getAircraftNbr().equalsIgnoreCase(dscrpLogpageDtl.getAircraftNbr())
                        && acnDiscrepancy.getAtaNbr().equals(dscrpLogpageDtl.getAtaNbr())
                        & acnDiscrepancy.getDscrpNbr().equals(dscrpLogpageDtl.getDscrpNbr())
                ) {
                    LogpagesLineFormat lgpagesLineFormat = new LogpagesLineFormat();
                    lgpagesLineFormat.getSfd().getFields().get(1).setValueList(dscrpLogpageDtl.getLogpageNbrs());

                    addTextAndStyle(lgpagesLineFormat);

                }
            }

        }

    }

    protected void createDiscepancyCreateText(AcnDiscrepancy acnDiscrepancy, AcnDiscrepancyMaintUpdt updt) {
        // Heading ..
        if (!isSummaryText) {
            createDiscrepancyHeader(acnDiscrepancy, updt);
            addLogpageNbr(acnDiscrepancy);
        }
        // detail text ...
        DetailLineFormat dlf = new DetailLineFormat();
        List<String> details = new ArrayList<String>();

        if (updt.getMaintUpdateText() != null) {
            if (fetchOnlyUpdtedTtxsFlag) {
                dscrUpdtFormattedTxtBuffer.append(updt.getMaintUpdateText());
            }
            String[] maintText = updt.getMaintUpdateText().split("\n");
            for (String text : maintText) {
                details.add(text);
            }
        }

        //details.add();
        dlf.getSfd().getFields().get(1).setValueList(details);

        addTextAndStyle(dlf);

    }

    private void addPlannedItemsHstry(List<PlannedItemAsgmtType> plannedItemsHstry) {
        if (plannedItemsHstry != null) {
            for (PlannedItemAsgmtType plannedItemAsgmtType : plannedItemsHstry) {
                PlannedLineFormat plf = new PlannedLineFormat();
                plf.getSfd().getFields().get(2).setValue(plannedItemAsgmtType.getStartDate() == null ? "" : DateUtil.formatddMMMyyGMT(plannedItemAsgmtType.getStartDate()));
                plf.getSfd().getFields().get(4).setValue(plannedItemAsgmtType.getGrpStatDept() == null ? "" : new StringBuffer(plannedItemAsgmtType.getGrpStatDept()).insert(3, "-").toString());
                plf.getSfd().getFields().get(6).setValue(plannedItemAsgmtType.getUpdatedUserId() == null ? "" : plannedItemAsgmtType.getUpdatedUserId().toString());
                plf.getSfd().getFields().get(8).setValue(plannedItemAsgmtType.getLogAddTm() == null ? "" : DateUtil.formatGMT(plannedItemAsgmtType.getLogAddTm(), "ddMMMyy HH:mm:ss:s"));
                addTextAndStyle(plf);

            }

        }
    }

    private void createLmpiTsiUpdateText(AcnDiscrepancyMaintUpdt updt, boolean isNew) {
        if (!checkDateRange(updt)) {
            return;
        }

        if (updt.getAcnDiscrepancyOid().longValue() != previousLmpiTsiOid) {
            UpdateLineFormat ulf = createUldateLineFormat(updt);

            addTextAndStyle(ulf);

        }
        previousLmpiTsiOid = updt.getAcnDiscrepancyOid().longValue();

        // get detail text
        DetailLineFormatTsiLmpi dlf = new DetailLineFormatTsiLmpi();
        dlf.resetLines(isNew);
        List<String> details = new ArrayList<String>();
        if (fetchOnlyUpdtedTtxsFlag) {
            dscrUpdtFormattedTxtBuffer.append(updt.getMaintUpdateText());
        }
        details.add(updt.getMaintUpdateText());
        dlf.getSfd().getFields().get(1).setValueList(details);

        addTextAndStyle(dlf);

    }

    /**
     * @return
     */
    protected boolean checkDateRange(AcnDiscrepancyMaintUpdt updt) {
        return true;
    }

    /**
     * @param updt
     * @return
     */
    protected UpdateLineFormat createUldateLineFormat(AcnDiscrepancyMaintUpdt updt) {

        UpdateLineFormat ulf = new UpdateLineFormat();
        ulf.getSfd().getFields().get(0).setValue(DscrpMaintUptType.getDiscrepancyDoutTypeFromType(updt.getMaintUpdateType()));


        XMLGregorianCalendar date = null;
        if (updt.getWorkDate() != null) {
            date = updt.getWorkDate();
        } else if (updt.getAddDate() != null) {
            //this field should never be null, mandy looking into this
            date = updt.getAddDate();
        }
        if (date != null) {
            ulf.getSfd()
                    .getFields()
                    .get(1)
                    .setValue(
                            DateUtil.formatGMT(date
                                    .toGregorianCalendar().getTime(), DT_TM_Format));
        } else {
            ulf.getSfd().getFields().get(1).setValue("             ");
        }
        ulf.getSfd().getFields().get(2).setValue(updt.getWorkStationCd());
        ulf.getSfd().getFields().get(3).setValue(updt.getReviewFlg());
        return ulf;
    }

    protected void createMaintUpdateText(AcnDiscrepancyMaintUpdt updt) {
        if (!checkDateRange(updt)) {
            return;
        }

        UpdateLineFormat ulf = createUldateLineFormat(updt);
        addTextAndStyle(ulf);

        // get detail text
        DetailLineFormat dlf = new DetailLineFormat();
        List<String> details = new ArrayList<String>();
        details.addAll(getDscrpMaintText(updt));
        if (fetchOnlyUpdtedTtxsFlag) {
            dscrUpdtFormattedTxtBuffer.append(StringUtils.join(details, "\n"));
        }
        dlf.getSfd().getFields().get(1).setValueList(details);

        addTextAndStyle(dlf);

    }

}

