package com.fedex.mets.util;

public enum DscrpMaintUptType {

	// NEF is different from server side, need to keep like this.
	// A MEL Connect to Discrepancy Record
	NEF("NEF", "N"),

	CDL("CDL", "C"),
	// A MEL Connect to Discrepancy Record
	MEL("MEL", "M"),
	// A OIL Connect to Discrepancy Record
	OIL("OIL", "O"),
	// An Intermediate OIL Accomplished Record
	OILA("OILA", "A"),
	// An OIL Spec Record
	OILS("OILS", "J"),
	// An OIL Spec Change Record
	OILC("OILC", "K"),
	// A Deferral - NOT used any more- prior to OIL MCMEL process
	DFRL("DFRL", "D"),
	// A Discrepancy Close Record
	FACT("FACT", "F"),
	// An Informational Record
	INFO("INFO", "I"),
	// A PARTS Needed Record
	PART("PART", "P"),
	// A Short Sign Record ( in Work)
	SHGN("SHGN", "S"),
	// An Update Record
	UPDT("UPDT", "U"),
	// A Remove / Install record from CCRIR Parts/RI
	R_I("R/I", "Z"),

	PLAN_TEXT("PLNTXT", "X"),
	// New Added.
	// An Old OIL Spec Record
	OILX("OILX", "1"),
	// Dscrp Update Text
	UPDTTX("UDTT", "2"),
	// Reopen Dscrp Text
	REOPENDP("REOP", "3"),
	// An Max Power Record
	MXPW("MXPW", "4"),
	// Dscrp Create Text
	DTTX("DTTX", "B"),
	// Structural Repair
	STRP("STRP", "R"),
	// Damage
	DMGE("DMGE", "G"),

	// R_I ERROR
	R_I_Error("RIER", "5"),

	// R_I ERROR
	RII_SIGNOFF("RISG", "6"),
	// R_I ERROR
	PUT_INWORK("PTINWK", "7"),

	// LIMP TSI Text
	LIMP_TXT("LITT", "L"),
	// TSI TEXT
	TSI_TXT("TSITT", "T"),

	// IN FLIGHT
	IN_FLIGHT("INFGHT", "8"),

	// TSI Complete
	TSI_COMPLETE("TSICPT", "9"),
	
	ACN_ATA_CHANGE("ACATCHG","W")
	;

	private String doutType;
	private String type;

	private DscrpMaintUptType(String doutType, String type) {
		this.doutType = doutType;
		this.type = type;
	}

	public String getDoutType() {
		return doutType;
	}

	public String getType() {
		return type;
	}

	public DscrpMaintUptType getDiscrepancyTypeFromType(String type) {
		if (type != null) {
			for (DscrpMaintUptType discrepancyType : DscrpMaintUptType.values()) {
				if (discrepancyType.getType().equals(type)) {
					return discrepancyType;
				}

			}
		}
		return null;
	}

	public static String getDiscrepancyDoutTypeFromType(String type) {
		if (type != null) {
			for (DscrpMaintUptType discrepancyType : DscrpMaintUptType.values()) {
				if (discrepancyType.getType().equals(type)) {
					if (DscrpMaintUptType.NEF.doutType.equalsIgnoreCase(discrepancyType.doutType)) {
						return DscrpMaintUptType.OIL.doutType;
					}
					return discrepancyType.doutType;
				}

			}
		}
		return null;
	}

	public DscrpMaintUptType getDiscrepancyTypeFromdoutType(String doutType) {
		if (doutType != null) {
			for (DscrpMaintUptType discrepancyType : DscrpMaintUptType.values()) {
				if (discrepancyType.getDoutType().equals(doutType)) {
					return discrepancyType;
				}

			}
		}
		return null;
	}

}

