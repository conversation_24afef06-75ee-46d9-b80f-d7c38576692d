package com.fedex.mets.service.update;

import java.sql.Timestamp;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.fedex.mets.data.MetsEventUpdateEntity;

@Service
public class MetsSetNIWTimerFromWLMService {

	public Map<String, Object> setNIWTimerFromWLM(MetsEventUpdateEntity request, Map<String, Object> hashMap, 
			String timerId, String flag, String acn, String userId, String tokenId, Timestamp wlmNIWTimerStartTime, Timestamp wlmNIWTimerStopTime) throws Exception {
		
		return null;
	}
	
}
