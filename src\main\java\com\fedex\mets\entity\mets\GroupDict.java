package com.fedex.mets.entity.mets;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "Group_Dict")
public class GroupDict {

    @Id
    @Column(name = "GROUP_ID", nullable = false)
    private String groupId;

    @Column(name = "GROUP_TITLE", nullable = false)
    private String groupTitle;

    @Column(name = "EVENT_OWNER", nullable = false)
    private String eventOwner;

    @Column(name = "AC_OWNER", nullable = false)
    private String acOwner;

    @Column(name = "ACCESS_LEVEL")
    private Integer accessLevel;
}

