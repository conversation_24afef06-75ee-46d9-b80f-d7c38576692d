package com.fedex.mets.repository.mets;

import com.fedex.mets.dao.EventNIWTimers;
import com.fedex.mets.entity.mets.EventTimers;
import com.fedex.mets.entity.mets.EventTimersPk;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Enumeration;
import java.util.List;

@Repository
public interface EventTimersRepository extends JpaRepository<EventTimers, EventTimersPk>{

    @Query(value = "select * from EVENT_TIMERS where EVENT_ID=:eventId \n" +
            "and TIMER_STOP_DT_TM is NULL", nativeQuery = true)
    public List<EventTimers> getEventActiveTimers(@Param("eventId") int eventId);


    @Query("select new com.fedex.mets.dao.EventNIWTimers(e.eventTimersPk.eventId, e.timerId, sum((e.eventTimersPk.eventId-3)*24) as totalAccumulatedTime) \n" +
            "from EventTimers e where e.eventTimersPk.eventId=:eventId \n" +
            "and e.timerStopDtTm > e.timerStartDtTm group by e.eventTimersPk.eventId, e.timerId")
    public EventNIWTimers getEventTimers(@Param("eventId") int eventId);


    @Query(value = "select * from EVENT_TIMERS \n" +
            "where EVENT_ID=:eventId \n" +
            "and TIMER_ID=:timerId",nativeQuery = true)
    public List<EventTimers> getNIWTimerDetails(@Param("eventId") String eventId, @Param("timerId") String timerId);


    @Query(value = "select count(*) from EVENT_TIMERS where EVENT_ID=:eventId \n" +
            "and TIMER_STOP_DT_TM is NULL",nativeQuery = true)
    public int getTimerCount(@Param("eventId") String eventId);

    @Query(value = "Select count(*) from EVENT_TIMERS where EVENT_ID=:eventId\n" +
            "and TIMER_ID=:strActiveTimerId\n" +
            "and CREATION_DT_TM = to_date(:strCreatedDateTime,'YYYY-MM-DD HH24:MI:SS')\n" +
            "and LAST_UPDATE_DT_TM > to_date(:strLastUpdatedDateTime,'YYYY-MM-DD HH24:MI:SS')",nativeQuery = true)
    public int getTimerCountByEventIdAndTimerId(@Param("eventId") Integer eventId,
                                                @Param("strActiveTimerId") String strActiveTimerId,
                                                @Param("strCreatedDateTime") String strCreatedDateTime,
                                                @Param("strLastUpdatedDateTime") String strLastUpdatedDateTime);

    @Query(value = "Select * from EVENT_TIMERS where EVENT_ID=:eventId",nativeQuery = true)
    public List<EventTimers> findActiveTimers(@Param("eventId") Integer eventId);


    @Modifying
    @Transactional
    @Query(value = "update EVENT_TIMERS set TIMER_STOP_DT_TM=TO_DATE(:stopdatetm,'YYYY-MM-DD HH24:MI:SS'),LAST_UPDATE_DT_TM=TO_DATE(:stopdatetm,'YYYY-MM-DD HH24:MI:SS') where EVENT_ID=:eventId\n" +
            "and TIMER_ID=:timerId",nativeQuery = true)
    public void updateTimers( @Param("eventId") int eventId,@Param("stopdatetm") String stopdatetm,@Param("timerId") String timerId);
}
