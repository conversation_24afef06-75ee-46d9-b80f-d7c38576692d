package com.fedex.mets.dto;

import com.fedex.mets.dao.FlightDetail;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@Schema(description = "Response containing flight details")
public class FlightDetailResponse {
    @Schema(description = "Current flight details")
    private FlightDetail currentFlight;

    @Schema(description = "List of upcoming flights")
    private List<FlightDetail> upcomingFlights;

    @Schema(description = "List of past flights")
    private List<FlightDetail> pastFlights;

    public FlightDetailResponse() {
        this.currentFlight = new FlightDetail();
        this.upcomingFlights = new ArrayList<>();
        this.pastFlights = new ArrayList<>();
    }
}