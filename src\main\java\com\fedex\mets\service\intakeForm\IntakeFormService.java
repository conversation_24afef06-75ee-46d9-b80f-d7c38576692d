package com.fedex.mets.service.intakeForm;

import com.fedex.mets.dao.*;
import com.fedex.mets.entity.mets.Question;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface IntakeFormService {

    public Boolean createUserIntakeForm(UserIntakeFormDao userIntakeFormDao);

    public List<Question> addNewQuestions(List<QuestionDao> questions);

    public UserRoleEventTypeDao getRoleAndEventType();

    public List<UserIntakeFormDao> getAllUserIntakeForms();

    public void deleteUserIntakeForm(int userIntakeFormId);

    public void editIntakeForm(ModifiedIntakeFormDao modifiedIntakeFormDao);

    public void deleteQuestion(int questionId);

    public void updateQuestion(QuestionDao questionDao);

    public List<IntakeFormDao> getIntakeForms();

    public List<QuestionDao> getAllQuestions();

    public IntakeFormResponseDao getIntakeFormByEventId(int eventId);

    public void updateIntakeFormResponse(IntakeFormResponseDao intakeFormResponseDao);
    public UserIntakeFormDao getUserIntakeForm(int roleId,int eventId);
}
