package com.fedex.mets.service.retrieval;

import com.fedex.mets.data.DOAData;
import com.fedex.mets.data.DOADiscrepancyData;
import com.fedex.mets.data.EventDiscrepancyList;
import com.fedex.mets.dto.MetsRequest;
import com.fedex.mets.entity.mets.EventDoa;
import com.fedex.mets.entity.mets.EventMaxiDisc;
import com.fedex.mets.entity.mets.Events;
import com.fedex.mets.repository.mets.EventDoaRepository;
import com.fedex.mets.repository.mets.EventMaxiDiscRepository;
import com.fedex.mets.repository.mets.EventsRepository;
import com.fedex.mets.util.IServerConstants;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

public class EventDoaServiceTest {

    @Mock
    private EventDoaRepository eventDoaRepository;

    @Mock
    private EventDiscrepanciesService eventDiscrepanciesService;

    @Mock
    private EventsRepository eventsRepository;

    @Mock
    private EventMaxiDiscRepository eventMaxiDiscRepository;

    @InjectMocks
    private EventDoaService eventDoaService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testGetEventDOAInfo_Success() {
        String eventId = "1";
        String userId = "User1";
        MetsRequest request = new MetsRequest();
        HashMap<String, Object> hashmap = new HashMap<>();

        EventDoa eventDoa = new EventDoa();
        eventDoa.setEventId(1);
        eventDoa.setDoaFleetNumber("Flight123");
        eventDoa.setDoaFleetDate(Timestamp.valueOf("2025-04-11 00:00:00.0"));
        eventDoa.setDoaFleetLeg("Leg1");
        eventDoa.setDoaFleetComments("Comments");
        eventDoa.setCheckFleet("Y");
        eventDoa.setClosedBy("User1");
        eventDoa.setClosedDtTm(Timestamp.valueOf("2025-04-12 00:00:00.0"));
        eventDoa.setMaintCW("Y");
        eventDoa.setLastUpdtDtTm(Timestamp.valueOf("2025-04-13 00:00:00.0"));
        eventDoa.setDoaFltDest("Destination");

        Events event = new Events();
        event.setEventId(1);
        event.setCreatedBy("User1");
        event.setCreatedDateTime(Timestamp.valueOf("2025-04-10 00:00:00.0"));
        event.setType("DOA");
        event.setCurComment("Current Comment");
        event.setOrigComment("Original Comment");
        event.setAcn("ACN123");

        EventMaxiDisc eventMaxiDisc = new EventMaxiDisc();
//        eventMaxiDisc.setAta("1234");
//        eventMaxiDisc.setDiscNum("DISC123");
        eventMaxiDisc.setType("Type");

        List<EventMaxiDisc> eventMaxiDiscList = new ArrayList<>();
        eventMaxiDiscList.add(eventMaxiDisc);

        EventDiscrepancyList eventDiscrepancyList = new EventDiscrepancyList();
        eventDiscrepancyList.setText(new String[]{"Discrepancy Text"});

        List<EventDiscrepancyList> eventDiscrepancyLists = new ArrayList<>();
        eventDiscrepancyLists.add(eventDiscrepancyList);

        when(eventDoaRepository.getEventDOAInfo(anyInt())).thenReturn(eventDoa);
        when(eventsRepository.getEventsByEventId(anyInt())).thenReturn(event);
        when(eventMaxiDiscRepository.getEventMaxiDiscInfo(anyInt())).thenReturn(eventMaxiDiscList);
        when(eventDiscrepanciesService.getDOAAircraftDscrps(anyString(), anyString(), anyString(), anyString())).thenReturn(eventDiscrepancyLists);

        HashMap<String, Object> result = eventDoaService.getEventDOAInfo(request, hashmap, eventId, userId);

        assertEquals(eventDoa.getEventId(), ((DOAData) result.get(IServerConstants.DOA_DETAILS)).getEventId());
        assertEquals("Flight123", ((DOAData) result.get(IServerConstants.DOA_DETAILS)).getFlightNumber());
        assertEquals("2025-04-11 00:00:00.0", ((DOAData) result.get(IServerConstants.DOA_DETAILS)).getFlightDate());
        assertEquals("Leg1", ((DOAData) result.get(IServerConstants.DOA_DETAILS)).getFlightLegNumber());
        assertEquals("Comments", ((DOAData) result.get(IServerConstants.DOA_DETAILS)).getAdditionalDescription());
        assertEquals(true, ((DOAData) result.get(IServerConstants.DOA_DETAILS)).isCheckFlightRequrired());
        assertEquals("User1", ((DOAData) result.get(IServerConstants.DOA_DETAILS)).getClosedBy());
        assertEquals("2025-04-12 00:00:00.0", ((DOAData) result.get(IServerConstants.DOA_DETAILS)).getClosedAt());
        assertEquals(true, ((DOAData) result.get(IServerConstants.DOA_DETAILS)).isMaintenanceCrew());
        assertEquals("2025-04-13 00:00:00.0", ((DOAData) result.get(IServerConstants.DOA_DETAILS)).getLastUpdated());
        assertEquals("Destination", ((DOAData) result.get(IServerConstants.DOA_DETAILS)).getDestination());
        assertEquals("Destination", ((DOAData) result.get(IServerConstants.DOA_DETAILS)).getEstimatedTimeOfArrival());
        assertEquals("User1", ((DOAData) result.get(IServerConstants.DOA_DETAILS)).getDoaOriginator());
        assertEquals("2025-04-10 00:00:00.0", ((DOAData) result.get(IServerConstants.DOA_DETAILS)).getCreatedAt());
        assertEquals("Current Comment", ((DOAData) result.get(IServerConstants.DOA_DETAILS)).getComment());
        assertEquals("12-34", ((DOADiscrepancyData) ((DOAData) result.get(IServerConstants.DOA_DETAILS)).getDiscVector().get(0)).getAta());
        assertEquals("DISC123", ((DOADiscrepancyData) ((DOAData) result.get(IServerConstants.DOA_DETAILS)).getDiscVector().get(0)).getDiscrepancy());
        assertEquals("Type", ((DOADiscrepancyData) ((DOAData) result.get(IServerConstants.DOA_DETAILS)).getDiscVector().get(0)).getEventType());
        assertEquals("Discrepancy Text", ((DOADiscrepancyData) ((DOAData) result.get(IServerConstants.DOA_DETAILS)).getDiscVector().get(0)).getDiscrepancyText()[0]);
    }

    @Test
    public void testGetEventDOAInfo() {
        String eventId = "1";
        String userId = "User1";
        MetsRequest request = new MetsRequest();
        HashMap<String, Object> hashmap = new HashMap<>();

        HashMap<String, Object> result = eventDoaService.getEventDOAInfo(request, hashmap, eventId, userId);

        Assertions.assertTrue(result.containsKey("DOA_DETAILS"));
    }
}
