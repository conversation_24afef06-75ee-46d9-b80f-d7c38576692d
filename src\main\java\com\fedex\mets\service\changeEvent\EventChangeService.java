package com.fedex.mets.service.changeEvent;

import com.fedex.mets.data.WizardEventData;
import com.fedex.mets.repository.mets.*;
import com.fedex.mets.util.ETICHelper;
import com.fedex.mets.entity.mets.*;
import com.fedex.mets.util.IServerConstants;
import com.fedex.mets.util.ServerDateHelper;
import jakarta.persistence.EntityNotFoundException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.rmi.RemoteException;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;

@Service
public class EventChangeService {

    private static final Logger logger = LoggerFactory.getLogger(EventChangeService.class);

    @Autowired
    private EventVerfiyService eventVerifyService;

    @Autowired
    private EventsRepository eventsRepository;

    @Autowired
    private ChangeRequestHistoryRepository changeRequestHistoryRepository;

    @Autowired
    private ChangeRequestLogRepository changeRequestLogRepository;

    @Autowired
    private ChangeRequestRepository changeRequestRepository;

    @Autowired
    private EventTfNotesRepository eventTfNotesRepository;

    @Autowired
    private EventTimersRepository eventTimersRepository;

    Boolean deleteChangeRecord = false;

    /**
     * Method to change the Event.
     * @return hashmap.
     */
    public HashMap changeEvent(
            WizardEventData wizardEventData,
            String userId,
            String tokenId)
            throws Exception {
        HashMap hashmap = new HashMap();
        HashMap eventChanged = new HashMap();
        boolean activeEvent = true;
        String isTimerPublishRequired = "";
        try {
            String _activeEvent="";
            _activeEvent = eventsRepository.getActiveEvent(wizardEventData.getEventId());
            if(null!=_activeEvent && _activeEvent.trim().equalsIgnoreCase("N")){
                activeEvent = false;

                logger.info("ERROR ##   Event is No longer active to continue with Change process.");
                String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
                hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                hashmap.put(
                        IServerConstants.ERROR,
                        "Event has been marked inactive by the Server. Please add a new Event for the Aircraft.");
            }
        } catch (Exception e) {
            logger.error("Error in changeEvent method: " + e.getMessage());
            hashmap.put("error", e.getMessage());
        }

        if (activeEvent) {
            try {
                eventChanged = changeEvent(wizardEventData);
            } catch (RemoteException re) {
                logger.info("change Event Remote Exception " + re.getMessage());
            }

        }
        if (eventChanged != null && !(eventChanged.containsKey("ERROR"))) {
            if(activeEvent){
                isTimerPublishRequired =
                        (String) eventChanged.get(IServerConstants.IS_TIMER_PUBLISH_REQUIRED);

                    /*Delete the record from Action Table after completing the transaction on the Event.**/

                    boolean isTranscationComplete = false;

                        try {
                            isTranscationComplete =
                                    eventVerifyService.deleteEventAction(
                                            wizardEventData.getEventId(),
                                            null,
                                            wizardEventData.getEmployeeName());
                        } catch (Exception eventVerification) {
                            logger.info(
                                    "eventVerification delete Action"
                                            + eventVerification.getMessage());
                        }
                        logger.info(
                                " Is Event Action record Deleted >> " + isTranscationComplete);


                    //**************TODO : Need to implement the following code****************

//                        try {
//                            eventWizardSession = eventWizardSessionHome.create();
//                            eventWizardSession.publishEvent(
//                                    wizardEventData.getEventId(),
//                                    IServerConstants.CHANGE_EVENT);
//                        } catch (javax.ejb.CreateException ce) {
//                            logger.info(
//                                    "publish change Event create exception " + ce.getMessage());
//                        } catch (RemoteException eventVerification) {
//                            logger.info(
//                                    "publish Change Event Remote Exception "
//                                            + eventVerification.getMessage());
//                        }

                //**************TODO : Need to implement the following code****************
                    //the following is to publish the TF_Notes Object and Text Message when an Event is added
//                    boolean isTFNotesPublished =
//                            publishEventUpdate(
//                                    wizardEventData.getEventId(),
//                                    isTimerPublishRequired);
//                    logger.info(" isTFNotesPublished >> " + isTFNotesPublished);

                    String currentDateTime = ServerDateHelper.getCurrentTimeStamp();

                    hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                    hashmap.put(IServerConstants.CHANGE_EVENT, wizardEventData);



                //**************TODO : Need to implement the following code****************
                    // added to avoid race condition where GDI returns SUPER confirmation before CHANGE_REQUEST table is updated
//                    if(superUpdateSendBuffer != null) {
//                        boolean isSuperUpdated = SuperUpdateHelper.sendSuperUpdate(superUpdateSendBuffer);
//                        logger.info("MetsEventChangeServlet sendSuperUpdateBuffer " + (isSuperUpdated ? "succeeded" : "failed"));
//                    }
            }//end if(activeEvent)
        } else {
            String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
            hashmap=eventChanged;
        }

        return hashmap;
    }


    /*################################## START CHANGE EVENT ##########################################################################**/
    /**
     * The following changeEvent() is used to change the Event details and the related data in the database for a particular aircraft/Event.
     * @params WizardEventData wizardEventData.
     * @return Hashtable boolean eventChanged.
     */
    public HashMap changeEvent(WizardEventData wizardEventData)
            throws RemoteException {
        HashMap returnHashMap = new HashMap();
        Events events = null;
        ChangeRequest changeRequest=null;
        ChangeRequestHistory changeRequestHistory = null;
        ChangeRequestLog changeRequestLog = null;
        ChangeRequestHistory newChangeRequestHistoryData = null;
        /*First Step before doing any thing would be to Check the Security Level of the User trying to Change the Event.**/
        try {
            //TODO: Implement the following code
//            Vector acessFlagVector = null;
//            String strUserId = wizardEventData.getUserId();
//            String strTokenId = wizardEventData.getTokenId();
//            String strTransactionId = "METS";
//
//            if (strUserId != null && strTokenId != null) {
//                try{
//                    acessFlagVector =
//                            SecurityHelper.getAccessFlags(
//                                    strUserId,
//                                    strTokenId,
//                                    strTransactionId);
//                }catch(Exception e){
//                    logger.warn(
//                            "ERROR Change Event Session changeEvent() SecurityHelper.getAccessFlags exception "
//                                    + e.getMessage());
//                    throw new RemoteException("512");
//                }
//            }
//
//            //for all Changing Events check access flag 1 is 80 or 90 else throw exception
//            if (acessFlagVector != null) {
//                String firstElement = (String) acessFlagVector.firstElement();
//
//                if (firstElement.trim().equals("SUCCESS")) {
//                    String strSecurityAccess = (String) acessFlagVector.elementAt(1);
//
//                    if (strSecurityAccess.trim().equals("80")
//                            || strSecurityAccess.trim().equals("90")) {
//                        logger.info("User " + strUserId + " has access to Change the Event.");
//                    } else {
//                        logger.info(
//                                "User "
//                                        + strUserId
//                                        + " does not have access to Change the Event.");
//                        returnHashtable.put(
//                                IServerConstants.ERROR,
//                                "User does not have permission/access to Change the Event");
//                        return returnHashtable;
//                    }
//                } else {
//                    if (firstElement.trim().equals("512")) {
//                        returnHashtable.put(IServerConstants.ERROR, firstElement);
//                        return returnHashtable;
//                    } else {
//                        String strSecurityAccessError =
//                                (String) acessFlagVector.elementAt(1);
//                        returnHashtable.put(IServerConstants.ERROR, strSecurityAccessError);
//                        return returnHashtable;
//                    }
//                }
//            } else if (acessFlagVector == null) {
//                logger.info("User does not have permission/access to Change the Event");
//                returnHashtable.put(
//                        IServerConstants.ERROR,
//                        "User does not have permission/access to Change the Event");
//                return returnHashtable;
//            }
        } catch (Exception e) {
            logger.info("Error in changeEvent security >>: " + e.getMessage());
            returnHashMap.put("ERROR", e.getMessage());
        }
        int activeEvent = 0;
        /*this boolean value is to compare the "Old" values on the latest ChangeRequestHistory record to the "New" ETIC Date, Time and Info
		entered on Card 3 and if equal set the ENTERED_IN_ERROR flag on the current change request to "Y".**/
        boolean isRepeatEtic = false;

        //boolean value decalred to publish the TIMER is STARTED
        boolean isTimerPublishRequired = false;

        //boolean value declared to identify if Pending Request Cancelled is for a NEW Event.
        //new Logic added on 08/21/03
        boolean isCancelledEventNew = false;

        java.sql.Timestamp changedDateTimeStamp = null;
        java.sql.Timestamp eticDateTimeStamp = null, newEticDateTime = null;
        java.sql.Timestamp incrementedChangedDateTimeStamp = null;

        String strEticDateTime = wizardEventData.getEticDateTime();
        String strNewEticDateTime = wizardEventData.getNewEticDateTime();
        try {
            logger.info("strEticDateTime **** " + strEticDateTime);
            logger.info("strNewEticDateTime **** " + strNewEticDateTime);

            if (strEticDateTime != null) {
                eticDateTimeStamp =
                        ServerDateHelper.getConvertedTimestamp(strEticDateTime);
                logger.info(
                        "eticDateTimeStamp  after converting -------" + eticDateTimeStamp);
            }

            if (strNewEticDateTime != null) {
                newEticDateTime =
                        ServerDateHelper.getConvertedTimestamp(strNewEticDateTime);
                logger.info(
                        "newEticDateTime  after converting -------" + newEticDateTime);
            }

            java.util.Date currentDate = ServerDateHelper.getDate_UTC();
            incrementedChangedDateTimeStamp =
                    new java.sql.Timestamp(currentDate.getTime() + 1000);
            logger.info(
                    "incrementedChangedDateTimeStamp >>>>>>>>>>>>>> "
                            + incrementedChangedDateTimeStamp);

            changedDateTimeStamp = ServerDateHelper.getTimeStamp();
            logger.info("changedDateTimeStamp >>>>>>>>>>>>>> " + changedDateTimeStamp);

            wizardEventData.setChangedDateTime(changedDateTimeStamp);
        } catch (Exception convert) {
            logger.warn(
                    "ERROR Change Event changeEvent() conver time stamp >> "
                            + convert.getMessage());
            returnHashMap.put(
                    IServerConstants.ERROR,
                    "The Date Time String could not converted to TimeStamp variable."
                            + convert.getMessage());
            return returnHashMap;
        }
        try {
            boolean pendingRequest = wizardEventData.isPendingRequest();
            String strLastUpdateTime = "";
            int eventCheckCount = 0;
              /*
			i) If a pending change was cancelled or modified then the client must pass the server the last updated date/time of the change request being modified or cancelled (to be used to determine if there has been an intervening update).
            */
            if (!(wizardEventData.getChangeRequestLastUpdated().isEmpty())) {
                try {
                    strLastUpdateTime = String.valueOf(changeRequestHistoryRepository.getLastUpdateDtTm(wizardEventData.getEventId()));
                    logger.info("strLastUpdateTime >> " + strLastUpdateTime);
                    logger.info("changeRequestLastUpdated >> " + wizardEventData.getChangeRequestLastUpdated());
                    strLastUpdateTime=strLastUpdateTime.substring(0, strLastUpdateTime.length() - 2);
                    if (strLastUpdateTime != null
                            && wizardEventData.getChangeRequestLastUpdated() != null) {
                        if (strLastUpdateTime
                                .trim()
                                .equals(wizardEventData.getChangeRequestLastUpdated())) {
                            eventCheckCount = eventCheckCount + 1;
                        }
                    }
                    if (eventCheckCount == 0) {
                        logger.info("An intervening change has occurred. The Event cannot be changed.");
                        returnHashMap.put(
                                IServerConstants.ERROR,
                                "An intervening change has been made.\nThe changes made will not be saved.");
                        return returnHashMap;
                    }
                } catch (Exception updated) {
                    logger.warn(
                            "ERROR Close Event closeEvent() Checking Event Updated time >> "
                                    + updated.getMessage());
                }
            }
            /*
			ii) If an AMC user cancelled or modified a pending change from Card 2 then verify that the change request has not been updated (e.g. been submitted by MOCC, confirmed from Super) since the user started the wizard.
				(1) If no record in the change request table for the event or if the LAST_UPDATED_DT_TM of the change request has been updated then ..
				(2) Display "An intervening change has occurred. The pending change can no longer be cancelled."  (or "modified" as appropriate)
				(3) End the wizard without making any changes to the database.

			iii) If an MOCC user modified a pending change then verify that the change request has not been updated (e.g. another MOCC user changed the request) since the user started the wizard.
				(1) if the LAST_UPDATED_DT_TM of the change request has been updated then..
				(2) Display "An intervening change has occurred. The pending change can no longer be modified".
				(3) End the wizard without making any changes to the database.
				(4) NOTE: if there is no change request record  (the pending change has been confirmed from Super) then changes will be made to the database creating a new change request.

			iv) If an AMC or MOCC user entered a new change request then verify that another change request has not been submitted since the wizard was started.
				(1) If there is a record in the change request table for the event then...
				(2) Display "An intervening change has occurred.  Any changes will not be saved."  and end the wizard without making any changes to the database.
			**/
            if (pendingRequest) {
                if (wizardEventData.getAccessLevel().trim().equals("80")) {
                    //condition ii)
                    activeEvent=changeRequestRepository.getCountByEventIdAndLastUpdateDtTm(wizardEventData.getEventId(), ServerDateHelper.getLookUpFormat(
                            wizardEventData.getChangeRequestLastUpdated()));
                    if (activeEvent == 0) {
                        String strMessage = "";

                        if (wizardEventData.isModifyPendingEvent())
                            strMessage = "Modified";
                        else if (wizardEventData.isCancelPendingEvent())
                            strMessage = "Cancelled";

                        logger.info(
                                "An intervening change has occurred. The pending change can no longer be "
                                        + strMessage
                                        + ".");
                        returnHashMap.put(
                                IServerConstants.ERROR,
                                "An intervening change has occurred. The pending change can no longer be "
                                        + strMessage
                                        + ".");
                        return returnHashMap;

                    }
                } else if (wizardEventData.getAccessLevel().trim().equals("90")) {
                    //condition iii)
                    int records = 0;
                    records=changeRequestRepository.getCountByEventId(wizardEventData.getEventId());
                    if (records > 0) {
                        activeEvent=changeRequestRepository.getCountByEventIdAndLastUpdateDtTm(wizardEventData.getEventId(), ServerDateHelper.getLookUpFormat(
                                wizardEventData.getChangeRequestLastUpdated()));
                        if (activeEvent == 0) {
                            logger.info("An intervening change has occurred. The pending change can no longer be modified.");
                            returnHashMap.put(
                                    IServerConstants.ERROR,
                                    "An intervening change has occurred. The pending change can no longer be modified.");
                            return returnHashMap;
                        }
                    }
                }
            }
            else {
                //condition iv)
                activeEvent=changeRequestRepository.getCoundByEventIdAndStatus(wizardEventData.getEventId());
                if (activeEvent > 0) {
                    logger.info("An intervening change has occurred. Any changes will not be saved.");
                    returnHashMap.put(
                            IServerConstants.ERROR,
                            "An intervening change has occurred. Any changes will not be saved.");
                    return returnHashMap;
                }
            }

        } catch (Exception e) {
            logger.warn("ERROR Change Event changeEvent() Checking Event Updated time >> " + e.getMessage());
        }

        try {
            events = eventsRepository.getEventsByEventId(wizardEventData.getEventId());
            logger.info("events >> " + events);
        } catch (Exception e) {
            logger.warn("ERROR Change Event changeEvent() event do not exist >> " + e.getMessage());
            returnHashMap.put(
                    IServerConstants.ERROR,
                    "This event does not exist, and hence cannot be changed.");
            return returnHashMap;
        }
        try {
            if (events != null) {
                logger.info(
                        "before updating the Change Request for the event "
                                + wizardEventData.getEventId());
                try {
                    changeRequest=changeRequestRepository.getChangeRequestByAcn(wizardEventData.getACN());
                } catch (Exception e) {
                    logger.warn("ERROR Change Event changeEvent() changeRequest do not exist >> " + e.getMessage());
                }
                /*
				"ETIC ENTERED IN ERROR"   If user checked "Previous ETIC entered in Error" on Card 3 then:
					i) find the last ETIC change in the CHANGE_REQUEST_HISTORY table for the event (not including any current ETIC change entered on Card 3).
					ii) set the ENTERED_IN_ERROR = "Y" for this record
					iii) compare the "Old" values on this record to the "New" ETIC Date, Time and Info entered on Card 3 and if equal set the ENTERED_IN_ERROR flag on the current change request to "Y".
					NOTE: This is to handle the case where the user may have changed the ETIC on the wrong aircraft then immediately changed it back to the previous values.  In the case the system really sees 2 ETIC changes when in reality there were 0 ETIC changes.
					iv) the ETIC_INITIAL and ETIC_NUM fields in the EVENTS table will not be updated until the change is rec'd from SUPER.
				**/
                try {
                    java.sql.Timestamp createdTimeStamp = null;
                    Timestamp strLastUpdated = null;
                    logger.info(
                            "wizardEventData.getEticEnteredInError() > "
                                    + wizardEventData.isEticEnteredInError());

                    if (wizardEventData.isEticEnteredInError()) {
                        logger.info(
                                "If user checked Previous ETIC entered in Error:");
                        strLastUpdated= changeRequestHistoryRepository.getLastUpdateDtTmByEventIdAndAcn(wizardEventData.getEventId(), wizardEventData.getACN());
                        logger.info("strLastUpdated > " + strLastUpdated);
                        if (strLastUpdated!=null) {
                            createdTimeStamp=changeRequestHistoryRepository.getCreatedDtTmByEventIdAndAcnAndLastUpdateDtTm(wizardEventData.getEventId(), wizardEventData.getACN(), strLastUpdated);
                            logger.info("createdTimeStamp > " + createdTimeStamp);
                        }
                        if (createdTimeStamp != null) {
                            try {
                                changeRequestHistory=changeRequestHistoryRepository.getChangeRequestHistory(wizardEventData.getEventId(), wizardEventData.getACN(), createdTimeStamp);
                            } catch (Exception e) {
                                logger.info(
                                        "ERROR Change Event changeEvent() changeRequestHistory do not exist >> "
                                                + e.getMessage());
                            }
                            if (changeRequestHistory != null) {
                                try {
                                    //Value set to true with out comparing the ETIC Values
                                    isRepeatEtic = true;
                                    changeRequestHistoryRepository.updateEnteredInErrorValue(wizardEventData.getEventId(), wizardEventData.getACN(), createdTimeStamp);
                                    logger.info("Updated the ENTERED_IN_ERROR value in the CHANGE_REQUEST_HISTORY table.");
                                } catch (Exception update) {
                                    logger.warn(
                                            "ERROR Change Event changeEvent() update  event >> "
                                                    + update.getMessage());
                                }
                            } //end if(changeRequestHistory!=null)
                        } //end if(createdTimeStamp!=null)
                    } //end if(wizardEventData.getEticEnteredInError())
                    logger.info("isRepeatEtic > " + isRepeatEtic);
                } catch (Exception eticError) {
                    logger.warn(
                            "ERROR Change Event changeEvent() eticError >> "
                                    + eticError.getMessage());
                }
                /*
				Change Request Tables
					i) If a pending change that was cancelled by AMC (access level =80 and cancel pending request selected on Card 2) then perform the following updates:
						(1) copy current record from CHANGE_REQUEST table to CHANGE_REQUEST_HISTORY with a REQUEST_STATUS = "X" for cancelled.
						(2)  insert record into CHANGE_REQUEST_LOG with OLD_REQUEST_STATUS = "U" and NEW_REQUEST_STATUS = "X"
						(3) delete current record from CHANGE_REQUEST table
				**/
                try {
                    if (wizardEventData.isPendingRequest()) {
                        if (wizardEventData.isCancelPendingEvent()
                                && wizardEventData.getAccessLevel().trim().equals("80")
                                && changeRequest != null) {
                            logger.info("If a pending change that was cancelled by AMC (access level =80 and cancel pending request selected on Card 2) then perform the following updates.");

                            ChangeRequest changeRequestData = changeRequestRepository.getChangeRequestByAcn(wizardEventData.getACN());
                            //copying the ChangeRequest Data to ChangeRequest History.

                            if (changeRequestData.getChangeType() == 15)
                                isCancelledEventNew = true;

                            boolean enteredInError = false;
                            ChangeRequestHistory changeRequestHistoryData =
                                    generateChangeRequestHistoryData(
                                            changeRequestData,
                                            wizardEventData.getChangedDateTime(),
                                            enteredInError);
                            deleteChangeRecord = true;
//                            changeRequestRepository.deleteChangeRequestRecord(wizardEventData.getACN());
                            //deleting the ChangeRequest record from the database.

                            //creating a record in the ChangeRequest Log table.
                            try {
                                changeRequestLog = new ChangeRequestLog();

                                ChangeRequestLogPk changeRequestLogPk = new ChangeRequestLogPk();

                                changeRequestLogPk.setEventId(wizardEventData.getEventId());
                                changeRequestLogPk.setStatusChangedDtTm(
                                        wizardEventData.getChangedDateTime());
                                changeRequestLogPk.setCreatedDtTm(
                                        changeRequestData.getCreatedDtTm());
                                changeRequestLog.setChangeRequestLogPk(changeRequestLogPk);

                                changeRequestLog.setOldRequestStatus("U");
                                changeRequestLog.setNewRequestStatus("X");
                            } catch (Exception log) {
                                logger.warn(
                                        "ERROR Change Event changeEvent() log **** >> "
                                                + log.getMessage());
                            }
                            changeRequestData = null;
                            //as the record had been deleted the data object is destroyed.
                        }

                        /*
						ii)  If a pending change that was modified by AMC or MOCC then perform the following updates to the change request tables:
							(1) If there is an existing record in the CHANGE_REQUEST table then copy it to the CHANGE_REQUEST_HISTORY table with
								ENTERED_IN_ERROR = "Y", REQUEST_STATUS="X" and delete the existing record in the CHANGE_REQUEST table.
							(2) Insert a new record in the CHANGE_REQUEST table with the Current and New values from Card 3 and update the value of
								CHANGE_TYPE if necessary.  For example, the previous change could have been just an ETIC change (change type = 2)
								then the user could modify the change to also include a STATUS change (change type = 6).
						**/
                        else if (wizardEventData.isModifyPendingEvent()) {
                            logger.info("If a pending change that was modified by AMC or MOCC then perform the following updates to the change request tables:");
                            if (changeRequest != null) {
                                try {
                                    changeRequest = changeRequestRepository.getChangeRequestByAcn(wizardEventData.getACN());
                                    //copying the ChangeRequest Data to ChangeRequest History.
                                    boolean enteredInError = true;
                                    changeRequestHistory =
                                            generateChangeRequestHistoryData(
                                                    changeRequest,
                                                    wizardEventData.getChangedDateTime(),
                                                    enteredInError);
                                    deleteChangeRecord = true;
//                                    changeRequestRepository.deleteChangeRequestRecord(wizardEventData.getACN());
                                    //deleting the exisiting ChangeRequest record from the database.

                                    changeRequest = null;
                                    //as the record had been deleted the data object is destroyed.

                                } catch (Exception change) {
                                    logger.warn(
                                            "ERROR Change Event changeEvent() change **** >> "
                                                    + change.getMessage());
                                }

                                //create a new record in the History table with the new request.
                                newChangeRequestHistoryData =
                                        generateNewChangeRequestHistoryData(
                                                wizardEventData,
                                                eticDateTimeStamp,
                                                newEticDateTime,
                                                incrementedChangedDateTimeStamp);

                                //create a new Record in the ChangeRequest table.
                                boolean checkNewStatus = false;

                                /*
								Insert the change Request only when change Type > 0
								*/
                                if (wizardEventData.getChangeType() > 0) {
                                    changeRequest =
                                            generateChangeRequestData(
                                                    wizardEventData,
                                                    incrementedChangedDateTimeStamp,
                                                    eticDateTimeStamp,
                                                    newEticDateTime,
                                                    checkNewStatus,
                                                    isRepeatEtic);
                                }

                                //creating a record in the ChangeRequest Log table.	/* NOT REQUIRED....*/
                            } //end of if(changeRequest!=null)
                        }
                    } else if (!wizardEventData.isPendingRequest()) {
                        logger.info("If not a pending change then perform the following updates to the change request tables:");
                        //if not a pending request open then, insert a new record in change request, history and log tables.
                        try {
                            changeRequest=changeRequestRepository.getChangeRequestByAcn(wizardEventData.getACN());
                        } catch (Exception e) {
                            logger.warn(
                                    "ERROR Change Event getChangeEvent() >> "
                                            + e.getMessage());
                        }

                        if (changeRequest != null) {
                            String strRequestStatus = changeRequest.getRequestStatus();
                            String strNewStatus = changeRequest.getNewStatus();

                            /*The following is to check and see if there is Change Request for and Event with a Confirmation from Super
							and the Status other than 'UP', delete the record from the database and then insert a new Record in the table.**/
                            if (strRequestStatus != null
                                    && strRequestStatus.trim().equals("C")) {
                                logger.info("As there is a record with Request_Status = C deleting the record from the table:");
                                deleteChangeRecord = true;
//                                changeRequestRepository.deleteChangeRequestRecord(wizardEventData.getACN());
                                changeRequest = null;
                            } else {
                                returnHashMap.put(
                                        IServerConstants.ERROR,
                                        "A Change Request Record already exist, a new Change Request could not be inserted.");
                                return returnHashMap;
                            }
                        }

                        //creating a record in the changeRequest table.
                        boolean checkNewStatus = true;
                        changeRequest =
                                generateChangeRequestData(
                                        wizardEventData,
                                        incrementedChangedDateTimeStamp,
                                        eticDateTimeStamp,
                                        newEticDateTime,
                                        checkNewStatus,
                                        isRepeatEtic);

                        //creating a record in the changeRequest History table.
                        changeRequestHistory =
                                generateChangeRequestHistoryData(
                                        wizardEventData,
                                        isRepeatEtic,
                                        eticDateTimeStamp,
                                        newEticDateTime);

                        //creating a record in the ChangeRequest Log table.
                        changeRequestLog =
                                generateChangeRequestLogData(
                                        wizardEventData,
                                        changeRequest.getCreatedDtTm());

                    } //end of else if(! wizardEventData.getIsPendingRequest())
                } catch (Exception changeReq) {
                    logger.warn(
                            "ERROR Change Event changeEvent() changeReq **** >> "
                                    + changeReq.getMessage());
                    returnHashMap.put(
                            IServerConstants.ERROR,
                            "Could not update Change Request for the event "
                                    + wizardEventData.getEventId()
                                    + "\n"
                                    + changeReq.getMessage());
                    return returnHashMap;
                }
            } //end if events !=null
        } catch (Exception chRequest) {
            logger.warn(
                    "ERROR Change Event changeEvent() chRequest **** >> "
                            + chRequest.getMessage());
            returnHashMap.put(
                    IServerConstants.ERROR,
                    "Could not add Change Request for the event "
                            + wizardEventData.getEventId()
                            + "\n"
                            + chRequest.getMessage());
            return returnHashMap;
        }

        /*
		The follwing is to add a TF_Notes to the database.
		**/
        try {
            if (events != null) {
                logger.info(
                        "before adding the TF_Notes for the event "
                                + wizardEventData.getEventId());
                boolean tfNotesAdded = addTFNotes(wizardEventData);
                logger.info("Added TF Notes >>>>>>" + tfNotesAdded);

                if (!tfNotesAdded) {
                    returnHashMap.put(
                            IServerConstants.ERROR,
                            "Could not add TF_Notes for the event "
                                    + wizardEventData.getEventId());
                    return returnHashMap;
                }
            }
        } catch (Exception tfNote) {
            logger.warn(
                    "ERROR Change Event changeEvent() Could not add TF_Notes for the event "
                            + wizardEventData.getEventId()
                            + "\n"
                            + tfNote.getMessage());
            returnHashMap.put(
                    IServerConstants.ERROR,
                    "Could not add TF_Notes for the event "
                            + wizardEventData.getEventId()
                            + "\n"
                            + tfNote.getMessage());
            return returnHashMap;
        }

        //the following is to Stop a NIW Timer started by the client.
        try {
            if (events != null) { // && wizardEventData.getActiveTimerId()!=null){
                if (wizardEventData.getTimerStartDateTime() != null
                        || wizardEventData.getTimerStopDateTime() != null) {
                    boolean resultFromBean = false;
                    String strActiveTimerId = wizardEventData.getActiveTimerId();
                    String strTimerId = wizardEventData.getTimerId();
                    String timerStartDateTime = wizardEventData.getTimerStartDateTime();
                    String timerStopDateTime = wizardEventData.getTimerStopDateTime();
                    String timerCreatedDateTime =
                            wizardEventData.getTimerCreatedDateTime();
                    String lastUpdated = wizardEventData.getTimerLastUpdated();

                    try {
                        resultFromBean =
                                startNIWTimer(
                                        wizardEventData.getEventId(),
                                        strActiveTimerId,
                                        strTimerId,
                                        timerStartDateTime,
                                        timerStopDateTime,
                                        timerCreatedDateTime,
                                        lastUpdated);

                        if (resultFromBean) {
                            returnHashMap.put(
                                    IServerConstants.IS_TIMER_PUBLISH_REQUIRED,
                                    "TIMER_PUBLISH_REQUIRED");
                        }
                    } catch (Exception stop) {
                        logger.warn(
                                "ERROR Change Event changeEvent() stop timer >>"
                                        + stop.getMessage());
                        returnHashMap.put(
                                IServerConstants.ERROR,
                                "Could not Stop the Timer"
                                        + wizardEventData.getTimerId()
                                        + " for the event "
                                        + wizardEventData.getEventId()
                                        + "\n"
                                        + stop.getMessage());
                    }

                    logger.info("Timer Id Started >>>>>>" + resultFromBean);
                }
            }
        } catch (Exception timers) {
            logger.warn(
                    "ERROR Change Event changeEvent() timers >>" + timers.getMessage());
            returnHashMap.put(
                    IServerConstants.ERROR,
                    "Could not Stop the Timer"
                            + wizardEventData.getTimerId()
                            + " for the event "
                            + wizardEventData.getEventId()
                            + "\n"
                            + timers.getMessage());
        }


        try {
            if (events != null) {
                    try {
                        if (changeRequestLog != null) {
                            changeRequestLogRepository.save(changeRequestLog);
                            logger.info(
                                    "After creating the ChangeRequest log for event Id----->"
                                            + changeRequestLog.getChangeRequestLogPk().getEventId());
                        }
                    } catch (Exception log) {
                        logger.warn(
                                "ERROR Change Event changeEvent() changeRequestLog >>"
                                        + log.getMessage());
                    }
                    try {
                        if (changeRequestHistory != null) {
                            changeRequestHistoryRepository.save(changeRequestHistory);
                            logger.info(
                                    "After creating the ChangeRequestHistory for event Id----->"
                                            + changeRequestHistory.getChangeRequestHistoryPk().getEventId());
                        }
                    } catch (Exception log) {
                        logger.warn(
                                "ERROR Change Event changeEvent() changeRequestHistory >>"
                                        + log.getMessage());
                    }
                    try {
                        if (newChangeRequestHistoryData != null) {
                            changeRequestHistoryRepository.save(newChangeRequestHistoryData);
                            logger.info(
                                    "After creating the newChangeRequestHistoryData for event Id----->"
                                            + newChangeRequestHistoryData.getChangeRequestHistoryPk().getEventId());
                        }
                    } catch (Exception log) {
                        logger.warn(
                                "ERROR Change Event changeEvent() newChangeRequestHistory >>"
                                        + log.getMessage());
                    }
                  try {
                    if (changeRequest != null) {
                        ChangeRequest changeReq=changeRequestRepository.findById(wizardEventData.getACN()).orElseThrow(() -> new EntityNotFoundException("Change Record not found"));
                        changeReq.setAcn(changeRequest.getAcn());
                        changeReq.setEventId(changeRequest.getEventId());
                        changeReq.setChangeType(changeRequest.getChangeType());
                        changeReq.setRequestStatus(changeRequest.getRequestStatus());
                        changeReq.setOldStatus(changeRequest.getOldStatus());
                        changeReq.setNewStatus(changeRequest.getNewStatus());
                        changeReq.setOldOst(changeRequest.getOldOst());
                        changeReq.setNewOst(changeRequest.getNewOst());
                        changeReq.setOldEticText(changeRequest.getOldEticText());
                        changeReq.setNewEticText(changeRequest.getNewEticText());
                        changeReq.setNewEticDtTm(changeRequest.getNewEticDtTm());
                        changeReq.setOldEticDtTm(changeRequest.getOldEticDtTm());
                        changeReq.setEnteredInError(changeRequest.getEnteredInError());
                        changeReq.setCreatedDtTm(changeRequest.getCreatedDtTm());
                        changeReq.setLastUpdateDtTm(changeRequest.getLastUpdateDtTm());
                        changeReq.setNewComment(changeRequest.getNewComment());
                        changeReq.setOldComment(changeRequest.getOldComment());
                        changeReq.setOldEticRsnCd(changeRequest.getOldEticRsnCd());
                        changeReq.setNewEticRsnCd(changeRequest.getNewEticRsnCd());
                        changeReq.setNewEticRsnComment(changeRequest.getNewEticRsnComment());
                        changeReq.setOldEticRsnComment(changeRequest.getOldEticRsnComment());
                        changeRequestRepository.save(changeRequest);
                        deleteChangeRecord=false;
                        logger.info(
                                "After creating the ChangeRequest for event Id----->"
                                        + changeRequest.getEventId());
                        if(changeRequest!=null) {
                            String endDateTime = null;
                            Integer eventId = events.getEventId();
                            Timestamp lastUpdateTime = null;
                            String lastUpdateBy = null;
                            String primaryContact = null;
                            String memDeskContact = null;
                            try {
                                logger.info("Updating the Events table.");
                                logger.info(
                                        "eventsData.getEndDateTime() >>  "
                                                + events.getEndDateTime());

                                //Update the Events table only if the EndDate Time exists in the database and the Event is changed to a diff status.
                                if (events.getEndDateTime() != null) {
                                    endDateTime = null;
                                }

                                lastUpdateTime = wizardEventData.getChangedDateTime();
                                lastUpdateBy = wizardEventData.getUserId();

                                if (null != wizardEventData.getResMgrId() && wizardEventData.getResMgrId().trim().length() > 0) {
                                    //changes made to update Responsible Manager Id 06/29/2012
                                    primaryContact = wizardEventData.getResMgrId();
                                }

                                if (null != wizardEventData.getMemDeskContact() && wizardEventData.getMemDeskContact().trim().length() > 0) {
                                    //changes made to update Responsible Manager Id 06/29/2012
                                    memDeskContact = wizardEventData.getMemDeskContact();
                                }

								/*
								if MOCC submits a change in ETIC and there is already a value for initial ETIC then the value of ETIC_NUM
								in the events table should be incremented by 1  unless the "Previous ETIC entered in Error" check box is selected
								by the user on the Change wizard in which case the value of ETIC_NUM is not incremented
								
								The same is done in SUPER Confirmation.
								**/
                                eventsRepository.updateChangeEvent(
                                        endDateTime,
                                        lastUpdateTime,
                                        lastUpdateBy,
                                        primaryContact,
                                        memDeskContact,
                                        eventId);
                            } catch (Exception eventsUpdate) {
                                logger.warn(
                                        "ERROR Change Event changeEvent() eventsUpdate >>"
                                                + eventsUpdate.getMessage());
                            }
                        }
                    } else if (changeRequest == null && isCancelledEventNew) {
                            events = eventsRepository.findById(events.getEventId())
                                    .orElseThrow(() -> new EntityNotFoundException("Event not found"));
                            events.setType("TRK");
                            events.setStatus("TRK");
                            events.setEticDateTime(null);
                            events.setEticText("");
                            events.setEticNumber(0);
                            events.setEticInitial(null);
                            events.setLastUpdateDateTime(
                                wizardEventData.getChangedDateTime());
                            events.setLastUpdatedBy(wizardEventData.getUserId());

                        if(null!=wizardEventData.getResMgrId() && wizardEventData.getResMgrId().trim().length() >0){
                            //changes made to update Responsible Manager Id 06/29/2012
                            events.setPrimaryContact(wizardEventData.getResMgrId());
                        }

                        if(null!=wizardEventData.getMemDeskContact() && wizardEventData.getMemDeskContact().trim().length() >0){
                            //changes made to update Responsible Manager Id 06/29/2012
                            events.setMemDeskContact(wizardEventData.getMemDeskContact());
                        }

                        eventsRepository.save(events);
                        logger.info(
                                "After converting the Event to TRK in the Events table. for event Id----->"
                                        + events.getEventId());
                    }
                } catch (Exception log) {
                    logger.warn(
                            "ERROR Change Event changeEvent() eventsUpdate log >>"
                                    + log.getMessage());
                }
            }
        } catch (Exception close) {
            logger.warn(
                    "ERROR Change Event changeEvent() eventsUpdate close >>"
                            + close.getMessage());
        }
        try{
            if(deleteChangeRecord == true){
                changeRequestRepository.deleteChangeRequestRecord(wizardEventData.getACN());
            }
        }catch (Exception e){
            logger.warn(
                    "ERROR Change Event changeEvent() deleteChangeRecord >>"
                            + e.getMessage());
            returnHashMap.put(
                    IServerConstants.ERROR,
                    "Could not delete the Change Request Record for the event "
                            + wizardEventData.getEventId()
                            + "\n"
                            + e.getMessage());
            return returnHashMap;
        }
        //TODO: Implement the following FUNCTIONALITY
        /*
		Update Super if the Access Leve of the User is 90.
		Calling the GDI_CoreServlet to Update Operational Status in SUPER
		**/

        /*
        ################################## END of CHANGE EVENT ##########################################################################
        **/
        return returnHashMap;
    }

    /**
     private method to generate the Change Request History Data to be inserted in to the ChangeRequest table.
     @ params	ChangeRequestData changeRequestData, java.sql.Timestamp changedDateTime
     @ return	changeRequestHistoryData
     */
    private ChangeRequestHistory generateChangeRequestHistoryData(
            ChangeRequest changeRequestData,
            java.sql.Timestamp changedDateTime,
            boolean enteredInError) {
        logger.info(
                "= >>> generateChangeRequestHistoryData() enteredInError >"
                        + enteredInError);
        ChangeRequestHistory changeRequestHistoryData =
                new ChangeRequestHistory();

        ChangeRequestHistoryPk changeRequestHistoryPk = new ChangeRequestHistoryPk();

        changeRequestHistoryPk.setEventId(changeRequestData.getEventId());
        changeRequestHistoryPk.setAcn(changeRequestData.getAcn());
        changeRequestHistoryPk.setCreatedDtTm(changedDateTime);

        changeRequestHistoryData.setChangeRequestHistoryPk(changeRequestHistoryPk);

        if (changeRequestData.getOldEticText() != null
                && changeRequestData.getOldEticText().startsWith("M")) {
            logger.info(
                    "= >>> ERROR Me OldEticText in History table >"
                            + changeRequestData.getOldEticText());
        }
        logger.info(
                "= >>> OldEticText in History table >"
                        + changeRequestData.getOldEticText());
        changeRequestHistoryData.setOldEticText(changeRequestData.getOldEticText());
        changeRequestHistoryData.setOldComment(changeRequestData.getOldComment());
        changeRequestHistoryData.setOldStatus(changeRequestData.getOldStatus());
        changeRequestHistoryData.setOldEticDtTm(
                changeRequestData.getOldEticDtTm());

        changeRequestHistoryData.setOldOst(changeRequestData.getOldOst());
        changeRequestHistoryData.setOldEticRsnCd(changeRequestData.getOldEticRsnCd());
        changeRequestHistoryData.setOldEticRsnComment(changeRequestData.getOldEticRsnComment());


        logger.info(
                "= >>> NewEticText in History table >"
                        + changeRequestData.getNewEticText());
        changeRequestHistoryData.setNewEticText(changeRequestData.getNewEticText());
        changeRequestHistoryData.setNewComment(changeRequestData.getNewComment());
        changeRequestHistoryData.setNewStatus(changeRequestData.getNewStatus());
        changeRequestHistoryData.setNewEticDtTm(
                changeRequestData.getNewEticDtTm());

        changeRequestHistoryData.setNewOst(changeRequestData.getNewOst());
        changeRequestHistoryData.setNewEticRsnCd(changeRequestData.getNewEticRsnCd());
        changeRequestHistoryData.setNewEticRsnComment(changeRequestData.getNewEticRsnComment());

        changeRequestHistoryData.setRequestStatus("X");
        changeRequestHistoryData.setLastUpdateDtTm(changedDateTime);
        //changeRequestHistoryData.setEnteredInError(changeRequestData.getEnteredInError());
        changeRequestHistoryData.setChangeType(changeRequestData.getChangeType());

        if (enteredInError)
            changeRequestHistoryData.setEnteredInError("Y");
        else
            changeRequestHistoryData.setEnteredInError("N");

        logger.info(
                "= >>> generateChangeRequestHistoryData() enteredInError changeRequestHistoryData.getEnteredInError>"
                        + changeRequestHistoryData.getEnteredInError());

        return changeRequestHistoryData;
    }


    /**
     private method to generate the Change Request History Data to be inserted in to the ChangeRequest table.
     @ params	WizardEventData wizardEventData, boolean isRepeatEtic,
     java.sql.Timestamp eticDateTimeStamp, java.sql.Timestamp newEticDateTime
     @ return	changeRequestHistoryData
     */
    private ChangeRequestHistory generateChangeRequestHistoryData(
            WizardEventData wizardEventData,
            boolean isRepeatEtic,
            java.sql.Timestamp eticDateTimeStamp,
            java.sql.Timestamp newEticDateTime) {
        logger.info(
                "= >>> generateChangeRequestHistoryData() isRepeatEtic >" + isRepeatEtic);
        ChangeRequestHistory changeRequestHistoryData =
                new ChangeRequestHistory();

        ChangeRequestHistoryPk changeRequestHistoryPk = new ChangeRequestHistoryPk();
        changeRequestHistoryPk.setEventId(wizardEventData.getEventId());
        changeRequestHistoryPk.setAcn(wizardEventData.getACN());
        changeRequestHistoryPk.setCreatedDtTm(
                wizardEventData.getChangedDateTime());

        changeRequestHistoryData.setChangeRequestHistoryPk(changeRequestHistoryPk);
        changeRequestHistoryData.setLastUpdateDtTm(
                wizardEventData.getChangedDateTime());
        changeRequestHistoryData.setChangeType(wizardEventData.getChangeType());

        if (wizardEventData.getEticInfo() != null) {
            if (wizardEventData.getEticInfo().startsWith("M")) {
                logger.info(
                        "= >>> ERROR Me OldEticText in History table >"
                                + wizardEventData.getEticInfo());
            }
            logger.info(
                    "= >>> OldEticText in History table >" + wizardEventData.getEticInfo());
            changeRequestHistoryData.setOldEticText(
                    wizardEventData.getEticInfo().trim());
        }

        if (wizardEventData.getEticComment() != null)
            changeRequestHistoryData.setOldComment(
                    wizardEventData.getEticComment().trim());

        if (wizardEventData.getOST() != null)
            changeRequestHistoryData.setOldOst(
                    wizardEventData.getOST().trim());

        if(wizardEventData.getEticRsnCd() != null) {
            changeRequestHistoryData.setOldEticRsnCd(wizardEventData.getEticRsnCd().trim());
        }

        if(wizardEventData.getEticRsnComment() != null) {
            changeRequestHistoryData.setOldEticRsnComment(wizardEventData.getEticRsnComment().trim());
        }

        if (eticDateTimeStamp != null)
            changeRequestHistoryData.setOldEticDtTm(eticDateTimeStamp);

        changeRequestHistoryData.setOldStatus(wizardEventData.getStatus());

        if (wizardEventData.getNewEticInfo() != null) {
            logger.info(
                    "= >>> NewEticText in History table >"
                            + wizardEventData.getNewEticInfo());
            changeRequestHistoryData.setNewEticText(
                    wizardEventData.getNewEticInfo().trim());
        }

        if (wizardEventData.getNewEticComment() != null)
            changeRequestHistoryData.setNewComment(
                    wizardEventData.getNewEticComment().trim());

        if (wizardEventData.getNewOST() != null)
            changeRequestHistoryData.setNewOst(
                    wizardEventData.getNewOST().trim());

        if (wizardEventData.getNewEticRsnCd() != null) {
            changeRequestHistoryData.setNewEticRsnCd(wizardEventData.getNewEticRsnCd().trim());
        }

        if (wizardEventData.getNewEticRsnComment() != null) {
            changeRequestHistoryData.setNewEticRsnComment(wizardEventData.getNewEticRsnComment().trim());
        }

        //check if the new status value is set by the user else insert preivous status value for the Event
        if (wizardEventData.getNewStatus() == null)
            changeRequestHistoryData.setNewStatus(wizardEventData.getStatus());
        else
            changeRequestHistoryData.setNewStatus(wizardEventData.getNewStatus());

        if (newEticDateTime != null)
            changeRequestHistoryData.setNewEticDtTm(newEticDateTime);

        if (wizardEventData.getAccessLevel().trim().equals("80"))
            changeRequestHistoryData.setRequestStatus("U");
        else if (wizardEventData.getAccessLevel().trim().equals("90"))
            changeRequestHistoryData.setRequestStatus("S");

        /*
		Commented on 09-18-2003 as the new record should not have the enteredInError falgged as 'Y'.
		if(isRepeatEtic)
			changeRequestHistoryData.setEnteredInError("Y");
		else
			changeRequestHistoryData.setEnteredInError("N");
		*/
        changeRequestHistoryData.setEnteredInError("N");

        logger.info(
                "= >>> generateChangeRequestHistoryData() isRepeatEtic changeRequestHistoryData.getEnteredInError>"
                        + changeRequestHistoryData.getEnteredInError());

        return changeRequestHistoryData;
    }

    /**
     private method to generate the Change Request History Data to be inserted in to the ChangeRequest table.
     @ params	WizardEventData wizardEventData, java.sql.Timestamp eticDateTimeStamp,
     java.sql.Timestamp newEticDateTime, java.sql.Timestamp incrementedChangedDateTimeStamp
     @ return	changeRequestHistoryData
     */
    private ChangeRequestHistory generateNewChangeRequestHistoryData(
            WizardEventData wizardEventData,
            java.sql.Timestamp eticDateTimeStamp,
            java.sql.Timestamp newEticDateTime,
            java.sql.Timestamp incrementedChangedDateTimeStamp) {
        ChangeRequestHistory newChangeRequestHistoryData =
                new ChangeRequestHistory();

        ChangeRequestHistoryPk changeRequestHistoryPk = new ChangeRequestHistoryPk();
        changeRequestHistoryPk.setEventId(wizardEventData.getEventId());
        changeRequestHistoryPk.setAcn(wizardEventData.getACN());
        changeRequestHistoryPk.setCreatedDtTm(
                incrementedChangedDateTimeStamp);
        newChangeRequestHistoryData.setChangeRequestHistoryPk(changeRequestHistoryPk);

        newChangeRequestHistoryData.setLastUpdateDtTm(
                wizardEventData.getChangedDateTime());
        newChangeRequestHistoryData.setEnteredInError("N");
        newChangeRequestHistoryData.setChangeType(wizardEventData.getChangeType());

        if (wizardEventData.getEticInfo() != null) {
            if (wizardEventData.getEticInfo().startsWith("M")) {
                logger.info(
                        "= >>> ERROR Me OldEticText in History table >"
                                + wizardEventData.getEticInfo());
            }
            logger.info(
                    "= >>> OldEticText in History table >" + wizardEventData.getEticInfo());
            newChangeRequestHistoryData.setOldEticText(
                    wizardEventData.getEticInfo().trim());
        }

        if (wizardEventData.getEticComment() != null)
            newChangeRequestHistoryData.setOldComment(
                    wizardEventData.getEticComment().trim());

        if(wizardEventData.getOST() != null)
            newChangeRequestHistoryData.setOldOst(
                    wizardEventData.getOST().trim());

        if(wizardEventData.getEticRsnCd() != null) {
            newChangeRequestHistoryData.setOldEticRsnCd(wizardEventData.getEticRsnCd().trim());
        }

        if(wizardEventData.getEticRsnComment() != null) {
            newChangeRequestHistoryData.setOldEticRsnComment(wizardEventData.getEticRsnComment().trim());
        }

        newChangeRequestHistoryData.setOldStatus(wizardEventData.getStatus());

        if (eticDateTimeStamp != null)
            newChangeRequestHistoryData.setOldEticDtTm(eticDateTimeStamp);

        if (wizardEventData.getNewEticInfo() != null) {
            logger.info(
                    "= >>> NewEticText in History table >"
                            + wizardEventData.getNewEticInfo());
            newChangeRequestHistoryData.setNewEticText(
                    wizardEventData.getNewEticInfo().trim());
        }

        if (wizardEventData.getNewEticComment() != null)
            newChangeRequestHistoryData.setNewComment(
                    wizardEventData.getNewEticComment().trim());

        if(wizardEventData.getNewOST() != null)
            newChangeRequestHistoryData.setNewOst(
                    wizardEventData.getNewOST().trim());

        if(wizardEventData.getNewEticRsnCd() != null) {
            newChangeRequestHistoryData.setNewEticRsnCd(wizardEventData.getNewEticRsnCd().trim());
        }

        if(wizardEventData.getNewEticRsnComment() != null) {
            newChangeRequestHistoryData.setNewEticRsnComment(wizardEventData.getNewEticRsnComment().trim());
        }

        newChangeRequestHistoryData.setNewStatus(wizardEventData.getNewStatus());

        if (newEticDateTime != null)
            newChangeRequestHistoryData.setNewEticDtTm(newEticDateTime);

        if (wizardEventData.getAccessLevel().trim().equals("80"))
            newChangeRequestHistoryData.setRequestStatus("U");
        else if (wizardEventData.getAccessLevel().trim().equals("90"))
            newChangeRequestHistoryData.setRequestStatus("S");

        return newChangeRequestHistoryData;
    }

    /**
     private method to generate the Change Request History Data to be inserted in to the ChangeRequest table.
     @ params	WizardEventData wizardEventData, java.sql.Timestamp eticDateTimeStamp, java.sql.Timestamp newEticDateTime
     @ return	changeRequestData
     */
    private ChangeRequest generateChangeRequestData(
            WizardEventData wizardEventData,
            java.sql.Timestamp incrementedChangedDateTimeStamp,
            java.sql.Timestamp eticDateTimeStamp,
            java.sql.Timestamp newEticDateTime,
            boolean checkNewStatus,
            boolean isRepeatEtic) {
        ChangeRequest changeRequestData = new ChangeRequest();

        changeRequestData.setEventId(wizardEventData.getEventId());
        changeRequestData.setAcn(wizardEventData.getACN());
        changeRequestData.setLastUpdateDtTm(
                wizardEventData.getChangedDateTime());
        changeRequestData.setEnteredInError("N");
        changeRequestData.setChangeType(wizardEventData.getChangeType());

        if (wizardEventData.getEticInfo() != null) {
            if (wizardEventData.getEticInfo().startsWith("M")) {
                logger.info(
                        "= >>> ERROR Me OldEticText in table >"
                                + wizardEventData.getEticInfo());
            }
            logger.info(
                    "= >>> OldEticText in change Request table >"
                            + wizardEventData.getEticInfo());
            changeRequestData.setOldEticText(wizardEventData.getEticInfo().trim());
        }

        if (wizardEventData.getEticComment() != null)
            changeRequestData.setOldComment(wizardEventData.getEticComment().trim());

        if (wizardEventData.getOST() != null)
            changeRequestData.setOldOst(wizardEventData.getOST().trim());

        if(wizardEventData.getEticRsnCd() != null) {
            changeRequestData.setOldEticRsnCd(wizardEventData.getEticRsnCd().trim());
        }

        if(wizardEventData.getEticRsnComment() != null) {
            changeRequestData.setOldEticRsnComment(wizardEventData.getEticRsnComment().trim());
        }

        changeRequestData.setOldStatus(wizardEventData.getStatus());
        changeRequestData.setOldEticDtTm(eticDateTimeStamp);

        if (wizardEventData.getNewEticInfo() != null) {
            logger.info(
                    "= >>> NewEticText in change Request table >"
                            + wizardEventData.getNewEticInfo());
            changeRequestData.setNewEticText(wizardEventData.getNewEticInfo().trim());
        } else
            changeRequestData.setNewEticText("");

        if (wizardEventData.getNewEticComment() != null)
            changeRequestData.setNewComment(
                    wizardEventData.getNewEticComment().trim());
        else
            changeRequestData.setNewComment("");

        if (wizardEventData.getNewOST() != null)
            changeRequestData.setNewOst(wizardEventData.getNewOST().trim());

        if (wizardEventData.getNewEticRsnCd() != null) {
            changeRequestData.setNewEticRsnCd(wizardEventData.getNewEticRsnCd().trim());
        }

        if (wizardEventData.getNewEticRsnComment() != null) {
            changeRequestData.setNewEticRsnComment(wizardEventData.getNewEticRsnComment().trim());
        }

        changeRequestData.setNewEticDtTm(newEticDateTime);

        //check if the new status value is set by the user else insert preivous status value for the Event
        if (checkNewStatus) {
            if (wizardEventData.getNewStatus() != null)
                changeRequestData.setNewStatus(wizardEventData.getNewStatus());
            else
                changeRequestData.setNewStatus(wizardEventData.getStatus());

            changeRequestData.setCreatedDtTm(
                    wizardEventData.getChangedDateTime());

        } else {
            changeRequestData.setNewStatus(wizardEventData.getNewStatus());
            changeRequestData.setCreatedDtTm(incrementedChangedDateTimeStamp);
        }

        /*
		Changed on 08/21/03 Entered In Error is "N" always for a new Request. This flag is set to Y only of previous records.
		if(isRepeatEtic)
			changeRequestData.setEnteredInError("Y");
		else
			changeRequestData.setEnteredInError("N");
		*/
        changeRequestData.setEnteredInError("N");

        if (wizardEventData.getAccessLevel().trim().equals("80"))
            changeRequestData.setRequestStatus("U");
        else if (wizardEventData.getAccessLevel().trim().equals("90"))
            changeRequestData.setRequestStatus("S");

        return changeRequestData;
    }

    /**
     private method to generate the Change Request Log Data to be inserted in to the ChangeRequest table.
     @ params	WizardEventData wizardData
     @ return	changeRequestHistoryData
     */
    private ChangeRequestLog generateChangeRequestLogData(
            WizardEventData wizardEventData,
            java.sql.Timestamp changeRequestCreatedTime) {
        ChangeRequestLog changeRequestLogData = new ChangeRequestLog();

        ChangeRequestLogPk changeRequestLogPk = new ChangeRequestLogPk();
        changeRequestLogPk.setEventId(wizardEventData.getEventId());
        changeRequestLogPk.setStatusChangedDtTm(
                wizardEventData.getChangedDateTime());
        changeRequestLogPk.setCreatedDtTm(changeRequestCreatedTime);
        changeRequestLogData.setChangeRequestLogPk(changeRequestLogPk);

        if (wizardEventData.getAccessLevel().trim().equals("80"))
            changeRequestLogData.setNewRequestStatus("U");
        else if (wizardEventData.getAccessLevel().trim().equals("90"))
            changeRequestLogData.setNewRequestStatus("S");

        return changeRequestLogData;
    }


    /**
     The following method is a private/sub method to support the addEvent method, to add a Tub File Note to the database
     @ params WizardEventData Object
     @ return boolean result
     */
    private boolean addTFNotes(WizardEventData wizardEventData) {
        logger.info("addTFNotes.. ==========");
        boolean result = false;
        EventTfNotes eventTFNotes = null;
        try {
            //the following is to add a record to the EVENT_TF_NOTES table.
            int noteId = 0;
            int activeEvents = 0;
            try {
                noteId=eventTfNotesRepository.getMaxNoteId(wizardEventData.getEventId());
                noteId = noteId + 1;
                logger.info("after incrementing noteId====== " + noteId);
                activeEvents = eventsRepository.getActiveEventCount(wizardEventData.getACN());
                logger.info("activeEvents====== " + activeEvents);

            } catch (Exception n) {
                logger.warn(
                        "ERROR Change Event addTFNotes() increment note id >> "
                                + n.getMessage());
            }
            if (noteId > 0) {
                String strEticComment = "",
                        strETICForm = "",
                        strStatus = "",
                        strTFNotesString = "";
                wizardEventData.setNewEticDateTime(String.valueOf(ServerDateHelper.getConvertedTimestamp(wizardEventData.getNewEticDateTime())));


                if (wizardEventData.getNewEticDateTime() == null
                        && wizardEventData.getNewEticInfo() != null) {
                    strETICForm = wizardEventData.getNewEticInfo();
                }
                if (wizardEventData.getNewEticDateTime() != null
                        && wizardEventData.getNewEticInfo() != null) {
                    strETICForm =
                            ETICHelper.getETICFormat(
                                    wizardEventData.getNewEticDateTime(),
                                    wizardEventData.getNewEticInfo());
                }
                if (wizardEventData.getNewEticDateTime() != null
                        && wizardEventData.getNewEticInfo() == null) {
                    strETICForm =
                            ETICHelper.getETICFormat(
                                    wizardEventData.getNewEticDateTime(),
                                    null);
                }

                if (wizardEventData.getNewEticComment() != null
                        && wizardEventData.getNewEticComment().trim().length() > 0)
                    strEticComment = wizardEventData.getNewEticComment();

                if (strETICForm != null && strETICForm.trim().length() > 0)
                    strETICForm = strETICForm;

                if (wizardEventData.getNewStatus() != null
                        && wizardEventData.getNewStatus().length() > 0)
                    strStatus = wizardEventData.getNewStatus();

                if (wizardEventData.getChangeType() == 1)
                    strTFNotesString = strEticComment;
                else if (wizardEventData.getChangeType() == 2)
                    strTFNotesString = strETICForm;
                else if (wizardEventData.getChangeType() == 3)
                    strTFNotesString = strETICForm + ", " + strEticComment;
                else if (wizardEventData.getChangeType() == 4)
                    strTFNotesString = strStatus;
                else if (wizardEventData.getChangeType() == 5)
                    strTFNotesString = strStatus + ", " + strEticComment;
                else if (wizardEventData.getChangeType() == 6)
                    strTFNotesString = strStatus + ", " + strETICForm;
                else if (wizardEventData.getChangeType() >= 7)
                    strTFNotesString =
                            strStatus + ", " + strETICForm + ", " + strEticComment;

                EventTfNotes eventTFNotesData = new EventTfNotes();

                EventTfNotesPk eventTfNotesPk = new EventTfNotesPk();
                eventTfNotesPk.setEventId(wizardEventData.getEventId());
                eventTfNotesPk.setTfDtTm(wizardEventData.getChangedDateTime());
                eventTFNotesData.setEventTfNotesPk(eventTfNotesPk);

                eventTFNotesData.setEmpNum(wizardEventData.getUserId());
                eventTFNotesData.setEmpName(wizardEventData.getEmployeeName());
                eventTFNotesData.setEmpDepartment(wizardEventData.getEmpDepartment());
                eventTFNotesData.setEditedFlag("N");
                eventTFNotesData.setNoteId(noteId);
                eventTFNotesData.setChangeType(wizardEventData.getChangeType());

                logger.info(
                        "wizardEventData.getStatusModified()"
                                + wizardEventData.isStatusModified());
                logger.info(
                        "wizardEventData.getEticModified()"
                                + wizardEventData.isEticModified());
                logger.info(
                        "wizardEventData.getCommentModified()"
                                + wizardEventData.isCommentModified());
                logger.info(
                        "wizardEventData.getIsPendingRequest()"
                                + wizardEventData.isPendingRequest());
                logger.info(
                        "wizardEventData.getCancelPendingEvent()"
                                + wizardEventData.isCancelPendingEvent());
                logger.info(
                        "wizardEventData.getModifyPendingEvent()"
                                + wizardEventData.isModifyPendingEvent());

                /*
				1) If MOCC user modifies a pending change: "Previous change modified. " plus
					a) if STATUS changed then "Submit New Status:" + value of New Status + New ETIC (if any) + New Comment (if any)
					b) if ETIC changed then "Submit New ETIC:" + value of New ETIC + New Comment (if any)
					c) if Comment changed then "Submit New Comment:" + New Comment
				2) If AMC cancels a pending change: "Previous change cancelled. "
				3) If AMC user modifies a pending change: "Previous change modified. " plus ...
					a) if STATUS changed then "Request New Status:" + value of New Status + New ETIC (if any) + New Comment (if any)
					b) if ETIC changed then "Request New ETIC:" + value of New ETIC + New Comment (if any)
					c) if Comment changed then "Request New Comment:" + New Comment
				**/
                if (wizardEventData.getAccessLevel().trim().equals("90")) {
                    if (wizardEventData.isPendingRequest()
                            && wizardEventData.isModifyPendingEvent()) {
                        if (wizardEventData.isStatusModified()) {
                            eventTFNotesData.setTfNote(
                                    "Previous change modified "
                                            + "Posted New Status: "
                                            + strTFNotesString);
                        } else if (wizardEventData.isEticModified()) {
                            eventTFNotesData.setTfNote(
                                    "Previous change modified "
                                            + "Posted New ETIC: "
                                            + strTFNotesString);
                        } else if (wizardEventData.isCommentModified()) {
                            eventTFNotesData.setTfNote(
                                    "Previous change modified "
                                            + "Posted New Comment: "
                                            + strTFNotesString);
                        } else if (wizardEventData.isOstModified()) {
                            eventTFNotesData.setTfNote(
                                    "Previous change modified "
                                            + "Posted New OST Status: "
                                            + strTFNotesString);
                        }
                        //penidng change modified so note type is 7
                        eventTFNotesData.setNoteType(7);
                    } else if (
                            wizardEventData.isPendingRequest()
                                    && wizardEventData.isCancelPendingEvent()) {
                        eventTFNotesData.setTfNote("Previous change cancelled ");
                        //penidng change cancelled so note type is 6
                        eventTFNotesData.setNoteType(6);
                    } else {
                        //new change(not a pending change) added on 12/30/02
                        if (wizardEventData.isStatusModified()) {
                            eventTFNotesData.setTfNote(
                                    "Posted New Status: " + strTFNotesString);
                        } else if (wizardEventData.isEticModified()) {
                            eventTFNotesData.setTfNote(
                                    "Posted New ETIC: " + strTFNotesString);
                        } else if (wizardEventData.isCommentModified()) {
                            eventTFNotesData.setTfNote(
                                    "Posted New Comment: " + strTFNotesString);
                        } else if (wizardEventData.isOstModified()) {
                            eventTFNotesData.setTfNote(
                                    "Posted New OST Status: " + strTFNotesString);
                        }
                        //new change(not a pending change)
                        eventTFNotesData.setNoteType(3);
                    }
                } else if (wizardEventData.getAccessLevel().trim().equals("80")) {
                    if (wizardEventData.isPendingRequest()
                            && wizardEventData.isCancelPendingEvent()) {
                        eventTFNotesData.setTfNote("Previous change cancelled ");
                        //penidng change cancelled so note type is 6
                        eventTFNotesData.setNoteType(6);
                    } else if (
                            wizardEventData.isPendingRequest()
                                    && wizardEventData.isModifyPendingEvent()) {
                        if (wizardEventData.isStatusModified()) {
                            eventTFNotesData.setTfNote(
                                    "Previous change modified "
                                            + "Submitted New Status: "
                                            + strTFNotesString);
                        } else if (wizardEventData.isEticModified()) {
                            eventTFNotesData.setTfNote(
                                    "Previous change modified "
                                            + "Submitted New ETIC: "
                                            + strTFNotesString);
                        } else if (wizardEventData.isCommentModified()) {
                            eventTFNotesData.setTfNote(
                                    "Previous change modified "
                                            + "Submitted New Comment: "
                                            + strTFNotesString);
                        } else if (wizardEventData.isOstModified()) {
                            eventTFNotesData.setTfNote(
                                    "Previous change modified "
                                            + "Submitted New OST Status: "
                                            + strTFNotesString);
                        }
                        //penidng change modified so note type is 7
                        eventTFNotesData.setNoteType(7);
                    } else {
                        //new change(not a pending change) added on 12/30/02
                        logger.info("new change(not a pending change)");
                        if (wizardEventData.isStatusModified()) {
                            eventTFNotesData.setTfNote(
                                    "Submitted New Status: " + strTFNotesString);
                        } else if (wizardEventData.isEticModified()) {
                            eventTFNotesData.setTfNote(
                                    "Submitted New ETIC: " + strTFNotesString);
                        } else if (wizardEventData.isCommentModified()) {
                            eventTFNotesData.setTfNote(
                                    "Submitted New Comment: " + strTFNotesString);
                        } else if (wizardEventData.isOstModified()) {
                            eventTFNotesData.setTfNote(
                                    "Submitted New OST Status: " + strTFNotesString);
                        }
                        //new change(not a pending change)
                        eventTFNotesData.setNoteType(1);
                    }
                }

                eventTFNotesData.setLastUpdateDtTm(wizardEventData.getChangedDateTime());

                try {
                    eventTfNotesRepository.save(eventTFNotesData);
                    result = true;
                } catch (Exception tfNotes) {
                    logger.warn(
                            "ERROR Change Event addTFNotes() tfNotes >> "
                                    + tfNotes.getMessage());
                }

                //the following is to add the TF_Notes that the Client had added.
                if (wizardEventData.getTfNotesList() != null
                        && wizardEventData.getTfNotesList().size() > 0) {
                    logger.info("User added TFNotes.. ==========");
                    for (int tf = 0;
                         tf < wizardEventData.getTfNotesList().size();
                         tf++) {
                        //creating a new createdTimeStamp as the TFDateTime is the primary Key to the table.
                        java.sql.Timestamp tfNotesDateTime =
                                ServerDateHelper.getTimeStamp();
                        logger.info("tfNotesDateTime >>>>>>>>>>>>>> " + tfNotesDateTime);

                        logger.info("*********************");
                        logger.info("tfNotesDateTime " + tfNotesDateTime);
                        logger.info(
                                "wizardEventData.getChangedDateTime() "
                                        + wizardEventData.getChangedDateTime());
                        logger.info("noteId " + noteId);
                        logger.info("*********************");

                        if (tfNotesDateTime.equals(wizardEventData.getChangedDateTime())) {
                            java.util.Date currentDate = ServerDateHelper.getDate_UTC();
                            tfNotesDateTime =
                                    new java.sql.Timestamp(currentDate.getTime() + 1000);
                            logger.info(
                                    "incremented tfNotesDateTime >>>>>>>>>>>>>> "
                                            + tfNotesDateTime);
                        }

                        String clientData = wizardEventData.getTfNotesList().get(tf);

                        EventTfNotes eventTFNotesData1 = new EventTfNotes();

                        EventTfNotesPk eventTfNotesPk1 = new EventTfNotesPk();
                        eventTfNotesPk1.setEventId(wizardEventData.getEventId());
                        eventTfNotesPk1.setTfDtTm(tfNotesDateTime);
                        eventTFNotesData1.setEventTfNotesPk(eventTfNotesPk1);

                        eventTFNotesData1.setEmpNum(wizardEventData.getUserId());
                        eventTFNotesData1.setEmpName(wizardEventData.getEmployeeName());
                        eventTFNotesData1.setEmpDepartment(
                                wizardEventData.getEmpDepartment());
                        eventTFNotesData1.setEditedFlag("N");
                        eventTFNotesData1.setNoteId(noteId);
                        eventTFNotesData1.setTfNote(clientData);
                        eventTFNotesData1.setNoteType(0);
                        eventTFNotesData1.setChangeType(wizardEventData.getChangeType());
                        eventTFNotesData1.setLastUpdateDtTm(
                                wizardEventData.getChangedDateTime());
                        try {
                            eventTfNotesRepository.save(eventTFNotesData1);
                            result = true;
                        } catch (Exception tfNotes) {
                            logger.warn(
                                    "ERROR Change Event addTFNotes() tfNotes #1 >> "
                                            + tfNotes.getMessage());
                        }
                    } //end of for tfNotesVector
                } //end of if tfNotesVector !=null
            } //end of if noteId >0
        } catch (Exception notes) {
            logger.warn(
                    "ERROR Change Event addTFNotes() notes >> " + notes.getMessage());
        }
        return result;
    }

    /**
     The following method is a private/sub method to support the addEvent method, to start a NIW Timer for the Event
     @ params int EventId, String ActiveTimerId, TimerId, TimerStartDateTime, createdDateTime, lastUpdated.
     @ return boolean result.
     */
    private boolean startNIWTimer(
            int eventId,
            String strActiveTimerId,
            String timerId,
            String timerStartDateTime,
            String timerStopDateTime,
            String createdDateTime,
            String lastUpdated)
            throws RemoteException {
        logger.info("startNIWTimer.. ==========");
        EventTimers eventTimers = null;
        boolean result = false;
        int lastUpdatedRecords = 0;
        List<EventTimers> niwTimerList = null;
        String stopDateTime = "", startDateTime = "";

        try {
            String strCreatedDateTime =
                    String.valueOf(ServerDateHelper.getConvertedTimestamp(createdDateTime));
            strCreatedDateTime= strCreatedDateTime.substring(0, strCreatedDateTime.length() - 2);
            String strLastUpdatedDateTime =
                    String.valueOf(ServerDateHelper.getConvertedTimestamp(lastUpdated));
            strLastUpdatedDateTime= strLastUpdatedDateTime.substring(0, strLastUpdatedDateTime.length() - 2);
            logger.info(
                    "strCreatedDateTime ========================" + strCreatedDateTime);
            logger.info("strLastUpdatedDateTime ========================" + strLastUpdatedDateTime);
            logger.info("strActiveTimerId ========================" + strActiveTimerId);
            lastUpdatedRecords= eventTimersRepository.getTimerCountByEventIdAndTimerId(eventId, strActiveTimerId, strCreatedDateTime, strLastUpdatedDateTime);
            logger.info(
                    "Last updated Records ========================" + lastUpdatedRecords);

        } catch (Exception count) {
            logger.warn(
                    "ERROR Change Event startNIWTimer() count >> " + count.getMessage());
        }
        if (lastUpdatedRecords <= 0) {
            //as the stop date time is supplied from the client. else take the current time on the server.
            if (timerStopDateTime != null) {
                stopDateTime =
                        String.valueOf(ServerDateHelper.getConvertedTimestamp(timerStopDateTime));
                stopDateTime= stopDateTime.substring(0, stopDateTime.length() - 2);
                logger.info("stopDateTime  after converting -------" + stopDateTime);
            } else {
                String strCurrentTimeStamp = ServerDateHelper.getCurrentTimeStamp();
                logger.info("current data time :"+strCurrentTimeStamp);
                stopDateTime =strCurrentTimeStamp;
                logger.info(
                        "stopDateTime  in else after converting -------" + stopDateTime);
            }

            //as the start date time is supplied from the client. else take the current time on the server.
            if (timerStartDateTime != null) {
                startDateTime =
                        String.valueOf(ServerDateHelper.getConvertedTimestamp(timerStartDateTime));
                startDateTime= startDateTime.substring(0, startDateTime.length() - 2);
                logger.info("startDateTime  after converting -------" + startDateTime);
            } else {
                String strCurrentTimeStamp = ServerDateHelper.getCurrentTimeStamp();

                startDateTime =
                        String.valueOf(ServerDateHelper.getConvertedTimestamp(strCurrentTimeStamp));
                startDateTime= startDateTime.substring(0, startDateTime.length() - 2);
                logger.info(
                        "startDateTime in else after converting -------" + startDateTime);
            }

            if (Timestamp.valueOf(stopDateTime).after(Timestamp.valueOf(startDateTime))) {
                stopDateTime = startDateTime;
            }
            //stop the existing timer first
            try {
                logger.info("Stoping the current NIW Timer");
                eventTimersRepository.updateTimers(eventId,stopDateTime,strActiveTimerId);
                result = true;
            } catch (Exception e) {
                throw new RemoteException(
                        "The active timer for eventId "
                                + eventId
                                + " could not be found Exception finding the active timer");
            }
            //start a new timer
            try {
                logger.info(">>>>>>>>>>>>>>>>>>>>>>>Starting a new Timer");
                if (startDateTime != null && timerId != null) {
                    EventTimers eventTimersData = new EventTimers();

                    EventTimersPk eventTimersPk = new EventTimersPk();
                    eventTimersPk.setEventId(eventId);
                    eventTimersPk.setCreationDtTm(Timestamp.valueOf(startDateTime));
                    eventTimersData.setEventTimersPk(eventTimersPk);

                    eventTimersData.setTimerId(timerId);
                    eventTimersData.setTimerStartDtTm(Timestamp.valueOf(startDateTime));
                    eventTimersData.setLastUpdateDtTm(Timestamp.valueOf(startDateTime));

                    eventTimersRepository.save(eventTimersData);

                    result = true;
                }
            } catch (Exception ee) {
                logger.warn(
                        "ERROR Change Event startNIWTimer() create >> " + ee.getMessage());
                result = false;
            }
        }
        if (lastUpdatedRecords > 0) {
            logger.info("Not In Work Timer has been updated, could not STOP the Timer");
        }
        return result;
    }



}

