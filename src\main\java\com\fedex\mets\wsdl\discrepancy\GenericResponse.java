package com.fedex.mets.wsdl.discrepancy;

import com.fedex.mets.wsdl.acnCache.GetCacheDataResponse;
import com.fedex.mets.wsdl.aicraftStatus.GetServiceWarningDaysResponse;
import com.fedex.mets.wsdl.flightSearch.GetCurrentFlightLegResponse;
import com.fedex.mets.wsdl.flightSearch.GetFlightLegDetailsResponse;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for GenericResponse complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="GenericResponse">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="headerType" type="{http://www.fedex.com/airops/schemas/Common.xsd}HeaderType"/>
 *         &lt;element name="status" type="{http://www.fedex.com/airops/schemas/Common.xsd}ResultType"/>
 *         &lt;element name="session" type="{http://www.fedex.com/airops/schemas/Common.xsd}SessionType"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "GenericResponse",namespace = "http://www.fedex.com/airops/schemas/Common.xsd", propOrder = {
    "headerType",
    "status",
    "session"
})
@XmlSeeAlso({
	GetCacheDataResponse.class,
    GetAcnAircraftResponse.class,
    GetDetailAircraftDiscrepancyResponse.class,
    GetAllMaintUpdateResponse.class,
    GetDscrpWorkRlseHistoryResponse.class,
    GetAircraftDiscrepanciesResponse.class,
        GetFlightLegDetailsResponse.class,
        GetCurrentFlightLegResponse.class,
        GetServiceWarningDaysResponse.class,
        GetCurrentFlightLegResponse.class,
        GetOpenDscrpSpecDetailResponse.class,
        GetWorkRlseStandardResponse.class
})
public class GenericResponse {

    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Common.xsd",name="headerType",required = true)
    protected HeaderType headerType;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Common.xsd",name="status",required = true)
    protected ResultType status;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Common.xsd",name="session",required = true)
    protected SessionType session;

    /**
     * Gets the value of the headerType property.
     * 
     * @return
     *     possible object is
     *     {@link HeaderType }
     *     
     */
    public HeaderType getHeaderType() {
        return headerType;
    }

    /**
     * Sets the value of the headerType property.
     * 
     * @param value
     *     allowed object is
     *     {@link HeaderType }
     *     
     */
    public void setHeaderType(HeaderType value) {
        this.headerType = value;
    }

    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link ResultType }
     *     
     */
    public ResultType getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link ResultType }
     *     
     */
    public void setStatus(ResultType value) {
        this.status = value;
    }

    /**
     * Gets the value of the session property.
     * 
     * @return
     *     possible object is
     *     {@link SessionType }
     *     
     */
    public SessionType getSession() {
        return session;
    }

    /**
     * Sets the value of the session property.
     * 
     * @param value
     *     allowed object is
     *     {@link SessionType }
     *     
     */
    public void setSession(SessionType value) {
        this.session = value;
    }

}
