
package com.fedex.mets.wsdl.discrepancy;

import java.math.BigDecimal;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for workRelseItem complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="workRelseItem">
 *   &lt;complexContent>
 *     &lt;extension base="{http://www.fedex.com/airops/schemas/Planning.xsd}StandardsReqmt">
 *       &lt;sequence>
 *         &lt;element name="workRelseOid" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="discrepancyOid" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="acn" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ata" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="dscrpNbr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "workRelseItem", namespace = "http://www.fedex.com/airops/schemas/Planning.xsd", propOrder = {
    "workRelseOid",
    "discrepancyOid",
    "acn",
    "ata",
    "dscrpNbr"
})
public class WorkRelseItem
    extends StandardsReqmt
{

    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd", required = true)
    protected BigDecimal workRelseOid;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd", required = true)
    protected BigDecimal discrepancyOid;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd", required = true)
    protected String acn;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd", required = true)
    protected BigDecimal ata;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd", required = true)
    protected String dscrpNbr;

    /**
     * Gets the value of the workRelseOid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getWorkRelseOid() {
        return workRelseOid;
    }

    /**
     * Sets the value of the workRelseOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setWorkRelseOid(BigDecimal value) {
        this.workRelseOid = value;
    }

    /**
     * Gets the value of the discrepancyOid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getDiscrepancyOid() {
        return discrepancyOid;
    }

    /**
     * Sets the value of the discrepancyOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setDiscrepancyOid(BigDecimal value) {
        this.discrepancyOid = value;
    }

    /**
     * Gets the value of the acn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAcn() {
        return acn;
    }

    /**
     * Sets the value of the acn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAcn(String value) {
        this.acn = value;
    }

    /**
     * Gets the value of the ata property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAta() {
        return ata;
    }

    /**
     * Sets the value of the ata property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAta(BigDecimal value) {
        this.ata = value;
    }

    /**
     * Gets the value of the dscrpNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDscrpNbr() {
        return dscrpNbr;
    }

    /**
     * Sets the value of the dscrpNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDscrpNbr(String value) {
        this.dscrpNbr = value;
    }

}
