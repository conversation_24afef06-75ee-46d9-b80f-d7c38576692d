package com.fedex.mets.controller;

import com.fedex.mets.data.WizardEventData;
import com.fedex.mets.dto.MetsResponse;
import com.fedex.mets.service.changeEvent.EventChangeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;

//@CrossOrigin(origins = "*") // Allows all origins
@RestController
@RequestMapping("/api/mets")
@Tag(name = "METS CHANGE EVENT", description = "Endpoint for changing status/etic of an event.")
public class MetsEventChangeController {
    private static final Logger logger = LoggerFactory.getLogger(MetsEventChangeController.class);

    @Autowired
    private EventChangeService eventChangeService;

    @Operation(summary = "Changing an event.", description = "Changing status/etic of an event in METS system.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully changed status/etic."),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @PostMapping(path = "/changeEvent")
    public ResponseEntity<MetsResponse> changeEvent(@RequestBody WizardEventData request) throws Exception {
        MetsResponse changeEventResponse = new MetsResponse();
        HashMap hashmap = new HashMap();

        String userId = request.getUserId();
        String tokenId = request.getTokenId();
        try {
            if (request != null) {
                if (!userId.isEmpty() && !tokenId.isEmpty()) {
                    logger.info(".....calling the changeEvent");
                    hashmap = eventChangeService.changeEvent(request, userId, tokenId);
                    changeEventResponse.setData(hashmap);
                } else {
                    logger.info("One or more essential parameters are missing from the request.");
                }
            }
            return ResponseEntity.ok(changeEventResponse);
        }
        catch (IllegalArgumentException e) {
            logger.error("Invalid input for changing Event: Wizard Event: {}. Error: {}", request, e.getMessage());
            return ResponseEntity.badRequest().build();
        }
        catch (Exception e) {
            logger.error("Error changing an event", e);
            return ResponseEntity.status(500).build();
        }
    }
}
