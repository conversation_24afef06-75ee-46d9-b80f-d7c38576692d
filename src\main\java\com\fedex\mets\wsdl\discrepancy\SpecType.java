
package com.fedex.mets.wsdl.discrepancy;

import jakarta.xml.bind.annotation.XmlEnum;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for SpecType.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="SpecType">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="CYCLES"/>
 *     &lt;enumeration value="HOURS"/>
 *     &lt;enumeration value="DAYS"/>
 *     &lt;enumeration value="DUE_DATE"/>
 *     &lt;enumeration value="FLYING_DATE"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 * 
 */
@XmlType(name = "SpecType", namespace = "http://www.fedex.com/airops/schemas/EnumTypes.xsd")
@XmlEnum
public enum SpecType {

    CYCLES,
    HOURS,
    DAYS,
    DUE_DATE,
    FLYING_DATE;

    public String value() {
        return name();
    }

    public static SpecType fromValue(String v) {
        return valueOf(v);
    }

}
