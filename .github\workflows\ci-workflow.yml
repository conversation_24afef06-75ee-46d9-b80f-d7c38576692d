name: Continuous Integration
 
'on':
  workflow_dispatch:
    inputs:
      build-download-choice:
        description: 'Build or Download from GH Packages'
        type: choice
        default: Build
        options:
          - Build
          - Download
      build-options:
        description: 'Build options if build is true.'
        type: string
      build-tasks:
        description: 'Build tasks if build is true'
        type: string
      publish-flag:
        description: 'Publish if build is true'
        type: boolean
        default: true
      code-ql-flag:
        description: 'Run CodeQL (SAST) scan if true'
        type: boolean
        default: false
      deploy-flag:
        description: 'Deploy to Cloud Ops'
        type: boolean
        default: true
      foss-flag:
        description: 'Run FOSS'
        type: boolean
        default: false
      requestor:
        description: 'FOSS requestor'
        type: string
      foss-process:
        description: 'FOSS process'
        type: choice
        default: Scan, Save and Submit
        options:
          - Scan
          - <PERSON>an, Save and Submit
          - Scan, Save and Submit All False
          - Submit
 
jobs:
  encrypt-dev-ssh-commands:
    runs-on: arc2-runner-set
    environment: 'development'
    outputs:
       encrypted: ${{ steps.encrypt.outputs.encrypted }}
    steps:
       # GitHub does not allow passing secrets as an input to a job, so this must be encrypted.
       - name: Encrypt SSH Post Copy Script
         id: encrypt
         uses: FedEx/eai-3538069-encrypt/encrypt@v1
         with:
            # Specify ALL secrets here, so they will be redacted when printed out by GitHub.
            secrets: |
              ${{ secrets.FDX_SECURITY_LDAP_PASSWORD }}
            encrypt: |
              set -ex
              export SPRING_PROFILES_ACTIVE=develop
              export FDX_SECURITY_LDAP_MANAGERDN=${{ vars.FDX_SECURITY_LDAP_MANAGERDN }}
              export FDX_SECURITY_LDAP_PASSWORD=${{ secrets.FDX_SECURITY_LDAP_PASSWORD }}
              chmod u+x /opt/fedex/github/current/control.sh
              /opt/fedex/github/current/control.sh restart
  build:
    name: Build Maven Project
    uses: FedEx/eai-3538069-cicd-workflows/.github/workflows/ci-java-ssh.yml@main
    needs: [encrypt-dev-ssh-commands]
    with:
      build-tool: maven
      build-flag: ${{ inputs.build-download-choice == 'Build' && 'true' || 'false' }}
      build-options: ${{ inputs.build-options }} 
      build-tasks: ${{ inputs.build-tasks }} 
      publish-flag: ${{ inputs.publish-flag }}
      code-ql-flag: ${{ inputs.code-ql-flag }}
      foss-flag: ${{ inputs.foss-flag }}
      foss-process: ${{ inputs.foss-process }}
 
      deploy-flag: ${{ inputs.deploy-flag }}
      ssh-hosts: 'u1071566.test.cloud.fedex.com'
      ssh-user: 'appUser'
      user-dir: '/opt/fedex/appuser'
      ssh-copy-files: '*.jar scripts/*'
      ssh-encrypted-post-copy-script: ${{ needs.encrypt-dev-ssh-commands.outputs.encrypted }}
 
      requestor:  ${{ inputs.requestor }}
      foss-zone: ${{ vars.FOSS_ZONE }}
      cicd-svc-acc-user: ${{ vars.CICD_SVC_ACC_USER }}
    secrets:
      cicd-svc-acc-pw: ${{ secrets.CICD_SVC_ACC_PW }}
      ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}
