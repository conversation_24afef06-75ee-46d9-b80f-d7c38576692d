package com.fedex.mets.data;

import lombok.*;

import java.io.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
public class ListViewData implements Serializable{
	
	public int			eventID;                
	public String 		type;                     
	public String 		startDateTime;           
	public String 		endDateTime;            
	public String 		ACN;
	public String 		fleetDesc;             
	public String 		station;                  
	public String 		status; 
	public String 		eticDateTime;            
	public String 		eticText;               
	public String 		origComment;       
	public String 		curComment;        
	public String 		lastUpdateDateTime; 
	public String 		lastUpdatedBy;      
	public String 		createdDateTime;       
	public String 		createdBy;
	public String		eventOnwerGroupId;
	public String		changeRequestLastUpdateDtTime;
	public String		requestStatus;
	public String		newStatus;
	public String		newEticText;
	public String		newEticComment;
	public String		newEticDateTime;
	public String		changeType;
	public String		lastUpdated;
	public String		errorText;
	public String		currentDuration;

	//added for AMCM List view.
	public String		gate;
	public String		acOwnerGroupId;
	public String		owner;

	//added to publish Manager Notes
	public String		managerNote;

	public boolean		isAddEventMessage = false;

	private String flightDepartureDetails=null;
	private String	flightDetails=null;

	private ETICData	eticData=null;
	private	String	durationData=null;

	//added for client side presentation to indicate number of forms for a particular event. 08/27/02
	public boolean		doaAlert=false;

	//added for client side presentation 12/13/02
	public boolean		isBold=false;

	public boolean		isEventActive = false;
	public boolean		isPowerPlantEvent = false;//05-07-2003 Added for Power Plant Events.
	public String		changeTypeForClient;		
	
	//added for removing resource bottle necks used on Server side //12-31-2003
	public String		doaFlightNumber;
	public String		doaFlightDate;
	public String		doaFlightLegNumber;
	
	public boolean		_isTubFileAlert;
	
	public String	resMgrId;//added on 06-29-2012, Server would retrieve the EMP DEPT ID based on USER_ID from MARS database while inserting the tub file note.
	
	public String	memDeskContact;//added 01/26/2016, MEM desk lead for MOCC Orange Man project, requested by Ebben Raves
	
	public String 	ost;//added on 09-07-2012 for reducing OOS times initiative by MOCC
	public String 	newOST;
	
	// added 05/2013 for PCS-METS etic slide enhancements ITG 146194
	public String 	eticReasonCd;
	public String 	newEticReasonCd;
	public String 	eticRsnComments;
	public String 	newEticRsnComments;

	public ListViewData(int i) {
	}
}