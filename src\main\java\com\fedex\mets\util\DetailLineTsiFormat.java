package com.fedex.mets.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class DetailLineTsiFormat extends BaseLineFormat {

	private static final Logger log = LoggerFactory.getLogger(DetailLineTsiFormat.class);

	@Override
	protected void init(){
		sfd = new SentenceFormatDef("Update Line", 80, 6, 2,false);
		sfd.setMultiLine(true);
		
		addHeader();
		addDetailValues();
	}

	protected void addHeader() {
		FieldFormatDef ffd = new FieldFormatDef("Header Dummy",  0, true);
		ffd.setValue("");
		sfd.getFields().add(ffd);
	}
	
	protected void addDetailValues() {
		FieldFormatDef ffd = new FieldFormatDef("Detail Lines",  72, false);
		sfd.getFields().add(ffd);
	}

	
	public static void main(String[] args) {
		DetailLineTsiFormat dlf = new DetailLineTsiFormat();

		
		List<String> details = new ArrayList<String>();
		// ABCDEFGHIJKLMNOP is in next line
//		details.add("123456789012 3456789012 3456789012345 ABCDEFGHIJKLMNOP 23456789012345678901 234567890123 4 5 6 789012345678 9012345678901 2345678901234567890");

		// ABCDEFGHIJKLM NOP ---> NOP is in next line
//		details.add("123456789012 3456789012 3456789012345 ABCDEFGHIJKLM NOP 23456789012345678901 234567890123 4 5 6 789012345678 9012345678901 2345678901234567890");

		// ABCDEFGHIJKLMN OP --> ABCDEFGHIJKLMN is next line
//		details.add("123456789012 3456789012 3456789012345 ABCDEFGHIJKLMN OP 23456789012345678901 234567890123 4 5 6 789012345678 9012345678901 2345678901234567890");

		// ABCDEFGHIJ K L M N O P  --> L M N O P is next line
//		details.add("123456789012 3456789012 3456789012345 ABCDEFGHIJ K L M N O P 23456789012345678901 234567890123 4 5 6 789012345678 9012345678901 2345678901234567890");
		
		// ABCDEFGHIJK L M N O P  --> M N O P is next line
//		details.add("123456789012 3456789012 3456789012345 ABCDEFGHIJK L M N O P 23456789012345678901 234567890123 4 5 6 789012345678 9012345678901 2345678901234567890");

		// ABCDEFGHIJK  L M N O P  --> L M N O P is next line (2 char between K and L)
//		details.add("123456789012 3456789012 3456789012345 ABCDEFGHIJK  L M N O P 23456789012345678901 234567890123 4 5 6 789012345678 9012345678901 2345678901234567890");

		//  ABCDEFGHIJK    L M N O P  (4 char between K and L) -->      L M N O P is next line and L is preceded by a space 
//		details.add("123456789012 3456789012 3456789012345 ABCDEFGHIJK    L M N O P 23456789012345678901 234567890123 4 5 6 789012345678 9012345678901 2345678901234567890");
		
		
		// This caused out of memory
//		
		details.add("* THIS REPETITIVE EO SHOULD BE ASSIGNED AT 1D OR_0D******THIS ITEM CAN BE ASSIGNED AT ANY STATION *****====================================================");

//		details.add("PERFORMED APU OPS CHECK,OPS NORMAL REF A-30012345699PERFORMED APU OPS CHECK,OPS NORMAL REF A-300123456999999999999999999999978");
//		details.add("JRH.STARTED APU TWICE AND CYCLED BLEED SW,ALL BLEEDX");
//		details.add("'MEL ");
//		details.add("O NOT USE APU BLEED AIR.'");
//		details.add("F4005678");
//		details.add("Any input provided by a user in a GUI application must typically be validated in one way or another. There is a number of ways this gets done, while some applications have just ignored the matter altogether. When crafting an Eclipse RCP application, there are some help provided by SWT and JFace. We can add ModifyListeners and VerifyListeners to certain SWT widgets. JFace also provides ControlDecorations to help us indicate to the user where a problem with a specific input value exists. The problem is that these are at a low level, and we need to do a lot of 'monkey'-coding just to add basic validation and error indication to a widget, and then we're not even touching the world of input masks. If you're like me, you want to concentrate on solving your business problem, and don't want to write lots of basic UI code over and over. This is where the RCP Toolbox is very useful. It provides a light-weight validation framework (among other features) that makes it much easier to add validation and input masks to SWT Text, Combo and CCombo widgets.");

		dlf.getSfd().getFields().get(1).setValueList(details);

		
		log.info( dlf.getSfd().getSentence());
	}


}
