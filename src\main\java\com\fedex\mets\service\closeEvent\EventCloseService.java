package com.fedex.mets.service.closeEvent;

import com.fedex.mets.data.EventDiscrepancyListData;
import com.fedex.mets.data.ReportCategoriesKeyValueData;
import com.fedex.mets.data.WizardEventData;
import com.fedex.mets.entity.mets.*;
import com.fedex.mets.entity.pcs.WorksOrders;
import com.fedex.mets.repository.mets.*;
import com.fedex.mets.repository.pcs.WorksOrdersRepository;
import com.fedex.mets.service.reviewEvent.SuperUpdateHelper;
import com.fedex.mets.util.ETICHelper;
import com.fedex.mets.util.IServerConstants;
import com.fedex.mets.util.ServerDateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;

@Service
public class EventCloseService {
    private static final Logger logger = LoggerFactory.getLogger(EventCloseService.class);

    @Autowired
    private EventsRepository eventsRepository;

    @Autowired
    private ChangeRequestRepository changeRequestRepository;

    @Autowired
    private ChangeRequestHistoryRepository changeRequestHistoryRepository;

    @Autowired
    private ChangeRequestLogRepository changeRequestLogRepository;

    @Autowired
    private EventTfNotesRepository eventTfNotesRepository;

    @Autowired
    private EventRepCatgRepository eventRepCatgRepository;

    @Autowired
    private EventTimersRepository eventTimersRepository;

    @Autowired
    private EventMaxiDiscRepository eventMaxiDiscRepository;

    @Autowired
    private EventMaxiDwningItmRepository eventMaxiDwningItmRepository;

    @Autowired
    private EventDoaRepository eventDoaRepository;

    @Autowired
    private WorksOrdersRepository worksOrdersRepository;

    @Autowired
    private SuperUpdateHelper superUpdateHelper;

    /**
     * The following closeEvent() is used to close the Event and the related data in the database for a particular aircraft.
     *
     * @return HashMap.
     * @params WizardEventData wizardEventData.
     */
    public HashMap closeEvent(WizardEventData wizardEventData) throws Exception {
        HashMap hashmap = new HashMap();
        HashMap returnHashMap = new HashMap();
        int activeEvent = 0;
        int alreadyUP = 0;
        int activeDOAEvent = 0;
        int doaEventId = 0;
        //the event id of the DOA event on the Aircraft which needs to be inserted in TF_Note & Change Request tables
        int activeOOSEvent = 0;

        Events events = null;
        Events eventsData = null;
        ChangeRequest changeRequest = null;
        ChangeRequest changeRequestData = null;
        ChangeRequestHistory changeRequestHistory = null;
        ChangeRequestHistory changeRequestHistoryData = null;
        ChangeRequestHistory newChangeRequestHistory = null;
        ChangeRequestHistory newChangeRequestHistoryData = null;
        ChangeRequestLog changeRequestLog = null;
        ChangeRequestLog newChangeRequestLog = null;
        ChangeRequestLog changeRequestLogData = null;
        ChangeRequestLog newChangeRequestLogData = null;

        java.sql.Timestamp closedDateTimeStamp = null;
        java.sql.Timestamp eventEndDateTimeStamp = null, eticDateTimeStamp = null, newEticDateTime = null;
        java.sql.Timestamp incrementedClosedDateTime = null;

        String strEndDateTime = wizardEventData.getEventEndDateTime();
        String strEticDateTime = wizardEventData.getEticDateTime();
        String newEticText = "", newEticComment = "", newStatus = "", newOst = wizardEventData.getOST(),
                newEticRsnCd = wizardEventData.getEticRsnCd(), newEticRsnComment = wizardEventData.getEticRsnComment();

        String changeRequestNewStatus = "";
        //this value is used for inserting in to the Event_TF_Notes table based on the conditions on page 9 of METS Close Wizard document.


        try {
            if (strEndDateTime != null) {
                eventEndDateTimeStamp = ServerDateHelper.getConvertedTimestamp(strEndDateTime);
                logger.info("eventEndDateTimeStamp  after converting -------" + eventEndDateTimeStamp);
            }

            if (strEticDateTime != null) {
                eticDateTimeStamp = ServerDateHelper.getConvertedTimestamp(strEticDateTime);
                logger.info("eticDateTimeStamp  after converting -------" + eticDateTimeStamp);
            }

            closedDateTimeStamp = ServerDateHelper.getTimeStamp();
            logger.info("closedDateTimeStamp >>>>>>>>>>>>>> " + closedDateTimeStamp);

            java.util.Date currentDate = ServerDateHelper.getDate_UTC();
            incrementedClosedDateTime = new java.sql.Timestamp(currentDate.getTime() + 1000);
            logger.info("incrementedClosedDateTime >>>>>>>>>>>>>> " + incrementedClosedDateTime);

            wizardEventData.setClosedDateTime(closedDateTimeStamp);

        } catch (Exception convert) {
            logger.warn("ERROR Close Event closeEvent() convert time stamp >> " + convert.getMessage());
            returnHashMap.put(IServerConstants.ERROR, "The Date Time String could not converted to TimeStamp variable.");
        }

        try {
            //following is to check for intervening updates on Event
            String strLastUpdateTime = "";
            int eventCheckCount = 0;
            if (wizardEventData.getChangeRequestLastUpdated() != null) {
                try {
                    strLastUpdateTime = String.valueOf(changeRequestHistoryRepository.getLastUpdateDtTm(wizardEventData.getEventId()));
                    logger.debug("strLastUpdateTime ========================" + strLastUpdateTime);
                    if (strLastUpdateTime != null && wizardEventData.getChangeRequestLastUpdated() != null) {
                        if (strLastUpdateTime.trim().equals(wizardEventData.getChangeRequestLastUpdated())) {
                            eventCheckCount = eventCheckCount + 1;
                        }
                    }
                    if (eventCheckCount == 0) {
                        logger.info("An intervening change has occurred. The Event cannot be closed.");
                        returnHashMap.put(IServerConstants.ERROR, "An intervening change has been made.\nThe changes made will not be saved.");
                        return returnHashMap;
                    }
                } catch (Exception updated) {
                    logger.warn(
                            "ERROR Close Event closeEvent() Checking Event Updated time >> "
                                    + updated.getMessage());
                }
            }
            try {
                activeEvent = eventsRepository.getActiveEventsCount(wizardEventData.getEventId());
                //the following is to check if the NEW_STATUS for the ACN is UP in the Events table.
                if (wizardEventData.getEventType().trim().equalsIgnoreCase("OOS")) {
                    alreadyUP = eventsRepository.getActiveUpEventsCount(wizardEventData.getACN(), wizardEventData.getEventId());
                }
                //the following is to check if there is an active OOS event for the same aircraft.
                if (wizardEventData.getEventType().trim().equalsIgnoreCase("DOA")) {
                    activeOOSEvent = eventsRepository.getActiveEventCount(wizardEventData.getACN());
                    logger.info("activeOOSEvent ========================" + activeOOSEvent);
                }
                //the following is to check if there is an active DOA Event for the Same aircraft.
                if (wizardEventData.getEventType().trim().equalsIgnoreCase("OOS")) {
                    String doaEventResult = eventsRepository.getEarliestDOAEvent(wizardEventData.getACN());
                    String[] doaEvent = doaEventResult != null ? doaEventResult.split(",") : null;
                    if (doaEventResult != null && doaEventResult.trim().length() > 0) {
                        newEticDateTime = Timestamp.valueOf(doaEvent[0]);
                        newEticText = doaEvent[1];
                        newEticComment = doaEvent[2];
                        newStatus = doaEvent[3];
                        newOst = doaEvent[5];
                        newEticRsnCd = doaEvent[6];
                        newEticRsnComment = doaEvent[7];
                        if (newStatus != null && newStatus.trim().length() > 0)
                            activeDOAEvent = activeDOAEvent + 1;
                        logger.info("Existing active DOA event====== " + activeDOAEvent);
                    }
                }
                if (activeEvent == 0) {
                    logger.info("Event Id could not be located to close the Event.");
                    returnHashMap.put(IServerConstants.ERROR, "This event is no longer active and cannot be re-closed.");
                    return returnHashMap;
                }
            } catch (Exception eventCheck) {
                logger.warn("ERROR Close Event closeEvent() eventCheck >> " + eventCheck.getMessage());
            }
        } catch (Exception e) {
            logger.warn("ERROR Close Event closeEvent() exception >> " + e.getMessage());
        }
        try {
            if (activeEvent > 0) {
                try {
                    events = eventsRepository.findById(wizardEventData.getEventId()).orElse(null);
                } catch (Exception pk) {
                    logger.info("Could not close the Record into Events Table." + pk.getMessage());
                    returnHashMap.put(IServerConstants.ERROR, "This event does not exist, and hence cannot be closed.");
                    return returnHashMap;
                }
            }
        } catch (Exception rem) {
            logger.warn("ERROR Close Event closeEvent() rem exception >> " + rem.getMessage());
        }

        try {
            if (wizardEventData.getEventType().trim().equals("OOS")
                    || wizardEventData.getEventType().trim().equals("DOA")) {
                if (events != null) {
                    logger.info("before updating the Change Request for the event " + wizardEventData.getEventId());
                    try {
                        changeRequest = changeRequestRepository.findById(wizardEventData.getACN()).orElse(null);
                    } catch (Exception pk) {
                        logger.info("Could not close the Record into Change Request Table." + pk.getMessage());
                        returnHashMap.put(IServerConstants.ERROR, "This event does not exist, and hence cannot be closed.");
                        return returnHashMap;
                    }
                    try {
                        if (changeRequest == null) {
                            //There is no record in the Change Request table so creating a record in CHANGE_REQUEST,CHANGE_REQUEST_HIST,CHANGE_REQUEST_LOG tables.
                            //passing null parameter because wizardEventData.getClosedDateTime() should be set as createdDateTime for the record.
                            changeRequestData =
                                    generateChangeRequestData(
                                            wizardEventData,
                                            eticDateTimeStamp,
                                            newEticDateTime,
                                            activeDOAEvent,
                                            doaEventId,
                                            newEticText,
                                            newEticComment,
                                            newStatus,
                                            newOst,
                                            newEticRsnCd,
                                            newEticRsnComment,
                                            null);

                            if (activeDOAEvent > 0
                                    && wizardEventData.getAccessLevel().trim().equals("90"))
                                changeRequestNewStatus = newStatus;
                            else
                                changeRequestNewStatus = "UP";

                            //creating a new record in change request Log table.
                            //passing null parameter because wizardEventData.getClosedDateTime() should be set as createdDateTime for the record.
                            changeRequestHistoryData =
                                    generateChangeRequestHistoryData(
                                            wizardEventData,
                                            eticDateTimeStamp,
                                            newEticDateTime,
                                            activeDOAEvent,
                                            doaEventId,
                                            newEticText,
                                            newEticComment,
                                            newStatus,
                                            newOst,
                                            newEticRsnCd,
                                            newEticRsnComment,
                                            null);

                            //creating a new record in change request Log table.
                            //passing null parameter because wizardEventData.getClosedDateTime() should be set as createdDateTime for the record.
                            //and old request status should not be set in this case.
                            changeRequestLogData =
                                    generateChangeRequestLogData(
                                            wizardEventData,
                                            wizardEventData.getEventId(),
                                            null,
                                            null);
                        } else if (changeRequest != null) {
                            changeRequestData = changeRequest;

                            /*
                            #################################################################################################
                            Only a new record has to be inserted in the Database Copying the exisitng record not required
                            #################################################################################################

                            //copy the change request data to change request history table.
                            changeRequestHistoryData = generateChangeRequestHistoryData(changeRequestData, wizardEventData.getClosedDateTime());*/

                            //copy the change request data to change request log table.
                            changeRequestLogData =
                                    generateChangeRequestLogData(wizardEventData);

                            try {
                                logger.info(" -- removing the current record from Change Request table.");
                                changeRequestData = null;
                                changeRequestRepository.delete(changeRequest);
                                changeRequest = null;
                            } catch (Exception remove) {
                                logger.warn("ERROR Close Event closeEvent() delete record exception >> " + remove.getMessage());
                                logger.info(" -- remove " + remove.getMessage());
                            }
                            //insert a new record in to the CHANGE_REQUEST & CHANGE_REQUEST_HISTORY & CHANGE_REQUEST_LOG TABLE
                            try {
                                logger.info(" -- inserting new records in CHANGE_REQUEST & CHANGE_REQUEST_HISTORY & CHANGE_REQUEST_LOG tables");
                                //***************************insert a new record in to the CHANGE_REQUEST***************************
                                changeRequestData =
                                        generateChangeRequestData(
                                                wizardEventData,
                                                eticDateTimeStamp,
                                                newEticDateTime,
                                                activeDOAEvent,
                                                doaEventId,
                                                newEticText,
                                                newEticComment,
                                                newStatus,
                                                newOst,
                                                newEticRsnCd,
                                                newEticRsnComment,
                                                incrementedClosedDateTime);

                                if (activeDOAEvent > 0
                                        && wizardEventData.getAccessLevel().trim().equals("90"))
                                    changeRequestNewStatus = newStatus;
                                else
                                    changeRequestNewStatus = "UP";

                                //************************insert a new record in to the CHANGE_REQUEST_HISTORY***************************
                                //creating a new record in change request Log table.
                                newChangeRequestHistoryData =
                                        generateChangeRequestHistoryData(
                                                wizardEventData,
                                                eticDateTimeStamp,
                                                newEticDateTime,
                                                activeDOAEvent,
                                                doaEventId,
                                                newEticText,
                                                newEticComment,
                                                newStatus,
                                                newOst,
                                                newEticRsnCd,
                                                newEticRsnComment,
                                                incrementedClosedDateTime);

                                //***************************insert a new record in to the CHANGE_REQUEST_LOG TABLE***************************
                                newChangeRequestLogData =
                                        generateChangeRequestLogData(
                                                wizardEventData,
                                                changeRequestData.getEventId(),
                                                incrementedClosedDateTime,
                                                changeRequestData.getRequestStatus());
                            } catch (Exception changeReq) {
                                logger.warn(
                                        "ERROR Close Event closeEvent() change request exception >> "
                                                + changeReq.getMessage());
                                events = null;
                                returnHashMap.put(IServerConstants.ERROR, "Could not insert the record in to Change Request table for the event "
                                        + wizardEventData.getEventId()
                                        + "\n"
                                        + changeReq.getMessage());
                                logger.info("Could not insert the record in to Change Request table for the event "
                                        + wizardEventData.getEventId()
                                        + " "
                                        + changeReq.getMessage());
                                return returnHashMap;
                            }
                        }
                    } catch (Exception changeReq) {
                        logger.warn("ERROR Close Event closeEvent() change request exception 1 >> "
                                + changeReq.getMessage());
                        events = null;
                        logger.info("Could not update Change Request for the event "
                                + wizardEventData.getEventId()
                                + " "
                                + changeReq.getMessage());
                        returnHashMap.put(IServerConstants.ERROR, "Could not update Change Request for the event "
                                + wizardEventData.getEventId()
                                + "\n"
                                + changeReq.getMessage());
                        return returnHashMap;
                    }
                }
            }
        } catch (Exception chRequest) {
            logger.warn(
                    "ERROR Close Event closeEvent() chRequest exception >> "
                            + chRequest.getMessage());
            logger.info(
                    "Could not add Change Request for the event "
                            + wizardEventData.getEventId()
                            + " "
                            + chRequest.getMessage());
        }
        /*The follwing is to add a TF_Notes to the database.**/
        try {
            if (events != null && activeEvent > 0) {
                logger.info("before adding the TF_Notes for the event " + wizardEventData.getEventId());
                boolean tfNotesAdded = false;
                //first step insert a note for OOS Event being closed irrespective of Active DOA Event existing for the Aircraft.
                tfNotesAdded = addTFNotes(wizardEventData, changeRequestNewStatus, 0, incrementedClosedDateTime);

                //second step insert a note for DOA Event if Exists which will be submitted to SUPER.
                if (doaEventId > 0)
                    tfNotesAdded = addTFNotes(wizardEventData, changeRequestNewStatus, doaEventId, incrementedClosedDateTime);

                if (!tfNotesAdded) {
                    events = null;
                    returnHashMap.put(IServerConstants.ERROR, "Could not add TF_Notes for the event " + wizardEventData.getEventId());
                    return returnHashMap;
                }
            }
        } catch (Exception tfNote) {
            logger.warn("ERROR Close Event closeEvent() tfNote add exception >> " + tfNote.getMessage());
            events = null;
            returnHashMap.put(IServerConstants.ERROR, "Could not add TF_Notes for the event "
                    + wizardEventData.getEventId()
                    + "\n"
                    + tfNote.getMessage());
            return returnHashMap;
        }

        //the following is to update Reporting categories edited by the client.
        try {
            if (events != null && wizardEventData.getReportingCategoriesKeys() != null && wizardEventData.getReportingCategoriesKeys().size() > 0) {
                java.sql.Timestamp closedDateTime = wizardEventData.getClosedDateTime();
                try {
                    for (int rep = 0; rep < wizardEventData.getReportingCategoriesKeys().size(); rep++) {
                        ReportCategoriesKeyValueData data = wizardEventData.getReportingCategoriesKeys().get(rep);
                        data.setEventId(wizardEventData.getEventId());
                        boolean modified = data.getIsModified();
                        if (modified) {
                            setReportingCategories(data, closedDateTime);
                        }
                    }
                } catch (Exception exec) {
                    logger.warn("ERROR Close Event closeEvent() Report Categories exception >> " + exec.getMessage());
                }
            }
        } catch (Exception repCategories) {
            logger.warn("ERROR Close Event closeEvent() repCategories exception >> " + repCategories.getMessage());
            logger.info("Could not add Reporting Categories for the event "
                    + wizardEventData.getEventId()
                    + " "
                    + repCategories.getMessage());
        }

        //the following is to Stop a NIW Timer started by the client.
        try {
            if (events != null && wizardEventData.getActiveTimerId() != null) {
                boolean resultFromBean = false;
                String strTimerId = wizardEventData.getActiveTimerId();
                String timerStopDateTime = wizardEventData.getTimerStopDateTime();
                String timerCreatedDateTime = wizardEventData.getTimerCreatedDateTime();
                String lastUpdated = wizardEventData.getTimerLastUpdated();

                logger.info("before stoping the NIW timer for Event Id "
                        + wizardEventData.getEventId()
                        + " and Timer id "
                        + wizardEventData.getActiveTimerId());
                try {
                    resultFromBean =
                            stopNIWTimer(
                                    wizardEventData.getEventId(),
                                    strTimerId,
                                    timerStopDateTime,
                                    timerCreatedDateTime,
                                    lastUpdated,
                                    wizardEventData.getClosedDateTime());
                } catch (Exception stop) {
                    logger.warn("ERROR Close Event closeEvent() stop timer exception >> " + stop.getMessage());
                    logger.info("Could not Stop the Timer "
                            + wizardEventData.getActiveTimerId()
                            + " for the event "
                            + wizardEventData.getEventId()
                            + " "
                            + stop.getMessage());
                }

                logger.info("Timer Id Stopped >>>>>>" + resultFromBean);
                if (!resultFromBean) {
                    returnHashMap.put(IServerConstants.ERROR, "Could not Stop the Timer for the event " + wizardEventData.getEventId());
                    events = null;
                    return returnHashMap;
                } else {
                    returnHashMap.put(IServerConstants.IS_TIMER_PUBLISH_REQUIRED, "TIMER_PUBLISH_REQUIRED");
                }
            }
        } catch (Exception timers) {
            logger.warn("ERROR Close Event closeEvent() timers exception >> " + timers.getMessage());
            logger.info("Could not Stop the Timer for the event "
                    + wizardEventData.getEventId()
                    + " "
                    + timers.getMessage());
        }

        //the following is to add/delete a Linked Discrepancy
        try {
            if (events != null && wizardEventData.getDiscrepancyList() != null && wizardEventData.getDiscrepancyList().size() > 0) {
                boolean result = false;
                try {
                    for (int disc = 0; disc < wizardEventData.getDiscrepancyList().size(); disc++) {
                        EventDiscrepancyListData data = wizardEventData.getDiscrepancyList().get(disc);
                        data.setEventId(wizardEventData.getEventId());

                        boolean modified = data.isModified();
                        boolean linkedDiscrepancy = data.isLink();
                        if (modified) {
                            if (linkedDiscrepancy) {
                                result = addEventDiscrepancyData(data);
                            } else {
                                result = deleteEventDiscrepancyData(data);
                            }
                        }
                    }
                } catch (Exception exec) {
                    logger.warn("ERROR Close Event closeEvent() EventDiscrepancyData exception >> " + exec.getMessage());
                    events = null;
                    returnHashMap.put(IServerConstants.ERROR, "Could not add Linked Discrepancies for the event "
                            + wizardEventData.getEventId()
                            + "\n"
                            + exec.getMessage());
                    logger.info("Could not add Linked Discrepancies for the event "
                            + wizardEventData.getEventId()
                            + " "
                            + exec.getMessage());
                }
            }
        } catch (Exception discrepnacy) {
            logger.warn("ERROR Close Event closeEvent() discrepnacy exception >> " + discrepnacy.getMessage());
        }
        //the following is to update the Event_DOA table
        try {
            if (wizardEventData.getEventType().trim().equals("DOA") || wizardEventData.isDOAComplied()) {
                EventDoa eventDOA = null;
                String strDoaCompliedMaintenance = "Y";
                try {
                    eventDOA = eventDoaRepository.findById(wizardEventData.getEventId()).orElse(null);
                    if (eventDOA != null) {
                        eventDOA.setClosedBy(wizardEventData.getUserId());
                        eventDOA.setClosedDtTm(wizardEventData.getClosedDateTime());
                        eventDOA.setMaintCW(strDoaCompliedMaintenance);
                        eventDOA.setLastUpdtDtTm(wizardEventData.getClosedDateTime());

                        eventDoaRepository.save(eventDOA);
                    }
                } catch (Exception doaUpdate) {
                    logger.debug("ERROR Close Event closeEvent() doaUpdate exception >> " + doaUpdate.getMessage());
                }
            }
        } catch (Exception doa) {
            logger.debug("ERROR Close Event closeEvent() doa exception >> " + doa.getMessage());
        }

        try {
            if (events != null) {
                eventsData = eventsRepository.findById(events.getEventId()).orElse(null);
                if (eventEndDateTimeStamp != null)
                    eventsData.setEndDateTime(eventEndDateTimeStamp);
                eventsData.setLastUpdateDateTime(wizardEventData.getClosedDateTime());
                eventsData.setLastUpdatedBy(wizardEventData.getUserId());
                if (null != wizardEventData.getResMgrId() && wizardEventData.getResMgrId().trim().length() > 0) {
                    //changes made to update Responsible Manager Id 06/29/2012
                    eventsData.setMemDeskContact(wizardEventData.getResMgrId());
                }
                if (null != wizardEventData.getMemDeskContact() && wizardEventData.getMemDeskContact().trim().length() > 0) {
                    eventsData.setMemDeskContact(wizardEventData.getMemDeskContact());
                }
                logger.info("wizardEventData.getEventType() " + wizardEventData.getEventType());
                if (!wizardEventData.getEventType().trim().equals("OOS")) {
                    if (wizardEventData.getEventType().trim().equals("DOA")) {
                        if (activeOOSEvent > 0)
                            eventsData.setActiveEvent("N");
                    } else {
                        eventsData.setActiveEvent("N");
                    }
                } else if (wizardEventData.getEventType().trim().equals("OOS")) {
                    logger.info("....As the Event being Closed is OOS \n And is the Status Already up, Count of already UP....>>>>...." + alreadyUP);
                    //should not set the value UP untill the confirmation is back from SUPER.
                    if (alreadyUP > 0) {
                        eventsData.setActiveEvent("N");
                    }

                    //as the DOA Event should be active when OOS is closed and
                    //the DOA would receive the confirmation back from SUPER.
                    //leave the OOS Event Active. Super confirmation Server will take care of the confirmation for DOA Event.
                    if (activeDOAEvent > 0
                            && wizardEventData.getAccessLevel().trim().equals("90")) {
                        eventsData.setStatus("UP");
                        eventsData.setEticDateTime(null);
                        eventsData.setEticText("");
                    }
                }
                //finally updating the Events table.
                eventsRepository.save(eventsData);
                logger.info("After updating the record in the Events table. for event Id----->" + wizardEventData.getEventId());

                try {
                    if (changeRequestLogData != null) {
                        changeRequestLog = changeRequestLogRepository.save(changeRequestLogData);
                        if (activeDOAEvent > 0)
                            logger.info("After creating the ChangeRequest log for DOA event Id----->" + doaEventId);
                        else
                            logger.info("After creating the ChangeRequest log for OOS event Id----->" + wizardEventData.getEventId());
                    }
                } catch (Exception log) {
                    logger.warn("ERROR Close Event closeEvent() change request log exception >> " + log.getMessage());
                }

                try {
                    if (changeRequestHistoryData != null) {
                        changeRequestHistory = changeRequestHistoryRepository.save(changeRequestHistoryData);
                        logger.info("After creating the ChangeRequestHistory for event Id----->" + wizardEventData.getEventId());
                    }
                } catch (Exception log) {
                    logger.warn("ERROR Close Event closeEvent() change request History exception >> " + log.getMessage());
                }

                try {
                    if (newChangeRequestLogData != null) {
                        newChangeRequestLog = changeRequestLogRepository.save(newChangeRequestLogData);
                        if (activeDOAEvent > 0)
                            logger.info("After creating the New ChangeRequest log for DOA event Id----->" + doaEventId);
                        else
                            logger.info("After creating the New ChangeRequest log for OOS event Id----->" + wizardEventData.getEventId());
                    }
                } catch (Exception log) {
                    logger.warn("ERROR Close Event closeEvent() new change request log exception >> " + log.getMessage());
                }

                try {
                    if (newChangeRequestHistoryData != null) {
                        newChangeRequestHistory = changeRequestHistoryRepository.save(newChangeRequestHistoryData);
                        if (activeDOAEvent > 0)
                            logger.info("After creating the New ChangeRequest History for DOA event Id----->" + doaEventId);
                        else
                            logger.info("After creating the New ChangeRequest History for OOS event Id----->" + wizardEventData.getEventId());
                    }
                } catch (Exception log) {
                    logger.warn("ERROR Close Event closeEvent() new change request history exception >> " + log.getMessage());
                }

                try {
                    if (changeRequestData != null) {
                        changeRequest = changeRequestRepository.save(changeRequestData);
                        if (activeDOAEvent > 0)
                            logger.info("After creating the ChangeRequest for DOA event Id----->" + doaEventId);
                        else
                            logger.info("After creating the ChangeRequest for OOS event Id----->" + wizardEventData.getEventId());
                    }
                } catch (Exception log) {
                    logger.warn("ERROR Close Event closeEvent() change request exception 1 >> " + log.getMessage());
                }

                if (changeRequestData != null && changeRequest != null) {
                    boolean isPCSUpdated = false;
                    if (wizardEventData.getAcnRegistrationNumber() != null) {
                        isPCSUpdated = updatePCSData(wizardEventData.getAcnRegistrationNumber(), changeRequestData.getCreatedDtTm());
                    }
                }
            }
        } catch (Exception close) {
            logger.warn("ERROR Close Event closeEvent() close exception >> " + close.getMessage());
        }

        /*Update Super if the Access Level of the User is 90.
        Calling the GDI_CoreServlet to Update Operational Status in SUPER**/
        if (wizardEventData.getAccessLevel().trim().equals("90") && changeRequestData != null && events != null) {
            boolean isSuperUpdateRequired = false;
            try {
                String strTempEticDateTime = "";
                if (changeRequestData.getNewEticDtTm() != null)
                    strTempEticDateTime = "" + changeRequestData.getNewEticDtTm();
                isSuperUpdateRequired =
                        superUpdateHelper.superUpdateRequired(
                                wizardEventData.getACN(),
                                changeRequestData.getNewStatus(),
                                strTempEticDateTime,
                                changeRequestData.getNewEticText(),
                                changeRequestData.getNewComment());
            } catch (Exception updateRequired) {
                logger.warn("ERROR Close Event closeEvent() updateRequired exception >> " + updateRequired.getMessage());
            }

            try {
                if (isSuperUpdateRequired) {
                    //TODO: need to implement this functionality
                    // added to avoid race condition where GDI returns SUPER confirmation before CHANGE_REQUEST table is updated
//                    returnHashMap.put(IServerConstants.SUPER_UPDATE_SENDBUFFER, generateSendBufferData(wizardEventData, changeRequestData));

                } else {
                    /*As SUPER_EQUIPMENT data and Change Request Data matches, Auto Confirmation should take place.
                    Update Change Request Table, Events table and insert a record in TF_Notes table.**/

                    boolean isMetsDataUpdated = false;
                    if (newChangeRequestHistory != null)
                        isMetsDataUpdated =
                                updateMetsDataRecords(
                                        changeRequest,
                                        newChangeRequestHistory,
                                        changeRequestLog,
                                        events);
                    else
                        isMetsDataUpdated =
                                updateMetsDataRecords(
                                        changeRequest,
                                        changeRequestHistory,
                                        changeRequestLog,
                                        events);
                    logger.info("is Auto Confirmation DONE >>" + isMetsDataUpdated);
                }
            } catch (Exception superUpdate) {
                logger.warn("ERROR Close Event closeEvent() superUpdate exception >> " + superUpdate.getMessage());
            }
        }

        //for Inactive events deleting the changeRequest record....
        if (events != null
                && eventsData != null
                && eventsData.getActiveEvent() != null
                && eventsData.getActiveEvent().equals("N")) {
            logger.info(
                    "deleting the change request record as  eventsData.getActiveEvent()>>"
                            + eventsData.getActiveEvent());
            if (changeRequest != null && activeDOAEvent <= 0) {
                //delete the change Request if exists and only if there is no Active DOA Event.
                try {
                    changeRequestRepository.delete(changeRequest);
                } catch (Exception changeRequestRemove) {
                    logger.warn(
                            "ERROR Close Event change request remove "
                                    + changeRequestRemove.getMessage());
                }
            }
        }


        return returnHashMap;
    }

    /**
     * private method to generate the Change Request Data to be inserted in to the ChangeRequest table.
     *
     * @ params	WizardEventData wizardEventData, java.sql.Timestamp eticDateTimeStamp,java.sql.Timestamp newEticDateTime,
     * int activeDOAEvent, String newEticText,	String newEticComment, String newStatus, java.sql.Timestamp newEticDateTime
     * @ return	changeRequestData
     */
    private ChangeRequest generateChangeRequestData(
            WizardEventData wizardEventData,
            java.sql.Timestamp eticDateTimeStamp,
            java.sql.Timestamp newEticDateTime,
            int activeDOAEvent,
            int doaEventId,
            String newEticText,
            String newEticComment,
            String newStatus,
            String newOST,
            String newEticRsnCd,
            String newEticRsnComment,
            java.sql.Timestamp incrementedClosedDateTime) {
        ChangeRequest changeRequestData = new ChangeRequest();

        changeRequestData.setEventId(wizardEventData.getEventId());
        changeRequestData.setAcn(wizardEventData.getACN());
        changeRequestData.setLastUpdateDtTm(wizardEventData.getClosedDateTime());
        changeRequestData.setEnteredInError("N");
        changeRequestData.setChangeType(7);

        if (incrementedClosedDateTime != null)
            changeRequestData.setCreatedDtTm(incrementedClosedDateTime);
        else
            changeRequestData.setCreatedDtTm(wizardEventData.getClosedDateTime());

        changeRequestData.setOldEticDtTm(eticDateTimeStamp);

        if (wizardEventData.getEticInfo() != null) {
            if (wizardEventData.getEticInfo().startsWith("M")) {
                logger.info("= >>> ERROR Me OldEticText in table >" + wizardEventData.getEticInfo());
            }
            logger.info("= >>> close Event OldEticText in change Request table >" + wizardEventData.getEticInfo());
            changeRequestData.setOldEticText(wizardEventData.getEticInfo().trim());
        } else
            changeRequestData.setOldEticText("");

        if (wizardEventData.getEticComment() != null)
            changeRequestData.setOldComment(wizardEventData.getEticComment().trim());
        else
            changeRequestData.setOldComment("");

        if (wizardEventData.getOST() != null) {
            changeRequestData.setOldOst(wizardEventData.getOST().trim());
        } else {
            changeRequestData.setOldOst("");
        }

        if (wizardEventData.getEticRsnCd() != null) {
            changeRequestData.setOldEticRsnCd(wizardEventData.getEticRsnCd().trim());
        } else {
            changeRequestData.setOldEticRsnCd("");
        }

        if (wizardEventData.getEticRsnComment() != null) {
            changeRequestData.setOldEticRsnComment(wizardEventData.getEticRsnComment().trim());
        } else {
            changeRequestData.setOldEticRsnComment("");
        }

        changeRequestData.setNewOst(newOST);

        changeRequestData.setNewEticRsnCd(newEticRsnCd);
        changeRequestData.setNewEticRsnComment(newEticRsnComment);

        changeRequestData.setOldStatus(wizardEventData.getStatus());

        if (activeDOAEvent > 0 && wizardEventData.getAccessLevel().trim().equals("90")) {
            changeRequestData.setEventId(doaEventId);
            if (newEticDateTime != null) {
                changeRequestData.setNewEticDtTm(newEticDateTime);
            }

            logger.info("= >>> close Event  NewEticText in change Request table >" + newEticText);
            changeRequestData.setNewEticText(newEticText);
            changeRequestData.setNewComment(newEticComment);
            changeRequestData.setNewStatus(newStatus);
        } else {
            changeRequestData.setNewStatus("UP");
        }

        if (wizardEventData.getAccessLevel().trim().equals("80")) {
            changeRequestData.setRequestStatus("U");
        } else if (wizardEventData.getAccessLevel().trim().equals("90")) {
            changeRequestData.setRequestStatus("S");
        }
        return changeRequestData;
    }

    /**
     * private method to generate the Change Request History Data to be inserted in to the ChangeRequest History table.
     *
     * @ params	ChangeRequestData changeRequestData, java.sql.Timestamp closedDateTime
     * @ return	changeRequestHistoryData
     */
    private ChangeRequestHistory generateChangeRequestHistoryData(
            ChangeRequest changeRequestData,
            java.sql.Timestamp closedDateTime) {
        ChangeRequestHistory changeRequestHistoryData = new ChangeRequestHistory();
        ChangeRequestHistoryPk changeRequestHistoryPk = new ChangeRequestHistoryPk();
        changeRequestHistoryPk.setEventId(changeRequestData.getEventId());
        changeRequestHistoryPk.setAcn(changeRequestData.getAcn());
        changeRequestHistoryPk.setCreatedDtTm(closedDateTime);
        changeRequestHistoryData.setChangeRequestHistoryPk(changeRequestHistoryPk);

        if (changeRequestData.getOldEticText() != null && changeRequestData.getOldEticText().startsWith("M")) {
            logger.info("= >>> ERROR Me OldEticText in History table >" + changeRequestData.getOldEticText());
        }
        logger.info("= >>> OldEticText in History table >" + changeRequestData.getOldEticText());
        changeRequestHistoryData.setOldEticText(changeRequestData.getOldEticText());
        changeRequestHistoryData.setOldComment(changeRequestData.getOldComment());
        changeRequestHistoryData.setOldStatus(changeRequestData.getOldStatus());
        logger.info("= >>> NewEticText in History table >" + changeRequestData.getNewEticText());
        changeRequestHistoryData.setNewEticText(changeRequestData.getNewEticText());
        changeRequestHistoryData.setNewComment(changeRequestData.getNewComment());
        changeRequestHistoryData.setNewStatus(changeRequestData.getNewStatus());
        changeRequestHistoryData.setRequestStatus("X");
        changeRequestHistoryData.setLastUpdateDtTm(changeRequestData.getLastUpdateDtTm());
        changeRequestHistoryData.setEnteredInError(changeRequestData.getEnteredInError());
        changeRequestHistoryData.setChangeType(changeRequestData.getChangeType());
        return changeRequestHistoryData;
    }

    /**
     * private method to generate the Change Request History Data to be inserted in to the ChangeRequest History table.
     *
     * @ params	WizardEventData wizardEventData, java.sql.Timestamp eticDateTimeStamp,java.sql.Timestamp newEticDateTime,
     * int activeDOAEvent, String newEticText,	String newEticComment, String newStatus, java.sql.Timestamp newEticDateTime
     * @ return	changeRequestHistoryData
     */
    private ChangeRequestHistory generateChangeRequestHistoryData(
            WizardEventData wizardEventData,
            java.sql.Timestamp eticDateTimeStamp,
            java.sql.Timestamp newEticDateTime,
            int activeDOAEvent,
            int doaEventId,
            String newEticText,
            String newEticComment,
            String newStatus,
            String newOst,
            String newEticRsnCd,
            String newEticRsnComment,
            java.sql.Timestamp incrementedClosedDateTime) {
        ChangeRequestHistory changeRequestHistoryData = new ChangeRequestHistory();
        ChangeRequestHistoryPk changeRequestHistoryPk = new ChangeRequestHistoryPk();
        changeRequestHistoryPk.setEventId(wizardEventData.getEventId());
        changeRequestHistoryPk.setAcn(wizardEventData.getACN());
        if (incrementedClosedDateTime != null)
            changeRequestHistoryPk.setCreatedDtTm(incrementedClosedDateTime);
        else
            changeRequestHistoryPk.setCreatedDtTm(wizardEventData.getClosedDateTime());
        changeRequestHistoryData.setChangeRequestHistoryPk(changeRequestHistoryPk);

        changeRequestHistoryData.setLastUpdateDtTm(wizardEventData.getClosedDateTime());
        changeRequestHistoryData.setEnteredInError("N");
        changeRequestHistoryData.setChangeType(7);

        if (eticDateTimeStamp != null)
            changeRequestHistoryData.setOldEticDtTm(eticDateTimeStamp);

        if (wizardEventData.getEticInfo() != null) {
            if (wizardEventData.getEticInfo().startsWith("M")) {
                logger.info("= >>> ERROR Me OldEticText in History table >" + wizardEventData.getEticInfo());
            }
            logger.info("= >>> OldEticText in History table >" + wizardEventData.getEticInfo());
            changeRequestHistoryData.setOldEticText(wizardEventData.getEticInfo().trim());
        }

        if (wizardEventData.getEticComment() != null)
            changeRequestHistoryData.setOldComment(wizardEventData.getEticComment().trim());

        changeRequestHistoryData.setOldStatus(wizardEventData.getStatus());

        if (wizardEventData.getOST() != null) {
            changeRequestHistoryData.setOldOst(wizardEventData.getOST());
        } else {
            changeRequestHistoryData.setOldOst("");
        }

        if (wizardEventData.getEticRsnCd() != null) {
            changeRequestHistoryData.setOldEticRsnCd(wizardEventData.getEticRsnCd().trim());
        } else {
            changeRequestHistoryData.setOldEticRsnCd("");
        }

        if (wizardEventData.getEticRsnComment() != null) {
            changeRequestHistoryData.setOldEticRsnComment(wizardEventData.getEticRsnComment().trim());
        } else {
            changeRequestHistoryData.setOldEticRsnComment("");
        }

        changeRequestHistoryData.setNewOst(newOst);

        changeRequestHistoryData.setNewEticRsnCd(newEticRsnCd);
        changeRequestHistoryData.setNewEticRsnComment(newEticRsnComment);

        if (activeDOAEvent > 0) {
            changeRequestHistoryPk.setEventId(doaEventId);
            changeRequestHistoryPk.setAcn(wizardEventData.getACN());
            changeRequestHistoryPk.setCreatedDtTm(wizardEventData.getClosedDateTime());
            if (incrementedClosedDateTime != null)
                changeRequestHistoryPk.setCreatedDtTm(incrementedClosedDateTime);
            else
                changeRequestHistoryPk.setCreatedDtTm(wizardEventData.getClosedDateTime());
            changeRequestHistoryData.setChangeRequestHistoryPk(changeRequestHistoryPk);
            if (newEticDateTime != null) {
                changeRequestHistoryData.setNewEticDtTm(newEticDateTime);
            }
            logger.info("= >>> NewEticText in History table >" + newEticText);
            changeRequestHistoryData.setNewEticText(newEticText);
            changeRequestHistoryData.setNewComment(newEticComment);
            changeRequestHistoryData.setNewStatus(newStatus);
        } else {
            changeRequestHistoryData.setNewEticDtTm(null);
            changeRequestHistoryData.setNewEticText(null);
            changeRequestHistoryData.setNewComment(null);
            changeRequestHistoryData.setNewStatus("UP");
        }

        if (wizardEventData.getAccessLevel().trim().equals("80")) {
            changeRequestHistoryData.setRequestStatus("U");
        } else if (wizardEventData.getAccessLevel().trim().equals("90")) {
            changeRequestHistoryData.setRequestStatus("S");
        }

        return changeRequestHistoryData;
    }

    /**
     * private method to generate the Change Request Log Data to be inserted in to the ChangeRequest Log table.
     *
     * @ params	WizardEventData wizardData, int eventId, java.sql.Timestamp incrementedClosedDateTime
     * @ return	newChangeRequestLogData
     */
    private ChangeRequestLog generateChangeRequestLogData(
            WizardEventData wizardEventData,
            int eventId,
            java.sql.Timestamp incrementedClosedDateTime,
            String oldRequestStatus) {
        ChangeRequestLog newChangeRequestLogData = new ChangeRequestLog();
        ChangeRequestLogPk changeRequestLogPk = new ChangeRequestLogPk();
        changeRequestLogPk.setEventId(eventId);
        changeRequestLogPk.setStatusChangedDtTm(wizardEventData.getClosedDateTime());

        if (incrementedClosedDateTime != null)
            changeRequestLogPk.setCreatedDtTm(incrementedClosedDateTime);
        else
            changeRequestLogPk.setCreatedDtTm(wizardEventData.getClosedDateTime());

        newChangeRequestLogData.setChangeRequestLogPk(changeRequestLogPk);
        if (oldRequestStatus != null)
            newChangeRequestLogData.setOldRequestStatus(oldRequestStatus);

        if (wizardEventData.getAccessLevel().trim().equals("80")) {
            newChangeRequestLogData.setNewRequestStatus("U");
        } else if (wizardEventData.getAccessLevel().trim().equals("90")) {
            newChangeRequestLogData.setNewRequestStatus("S");
        }

        return newChangeRequestLogData;
    }

    /**
     * private method to generate the Change Request Log Data to be inserted in to the ChangeRequest Log table.
     *
     * @ params	WizardEventData wizardData
     * @ return	changeRequestLogData
     */
    private ChangeRequestLog generateChangeRequestLogData(WizardEventData wizardEventData) {
        ChangeRequestLog changeRequestLogData = new ChangeRequestLog();
        ChangeRequestLogPk changeRequestLogPk = new ChangeRequestLogPk();

        changeRequestLogPk.setEventId(wizardEventData.getEventId());
        changeRequestLogPk.setStatusChangedDtTm(wizardEventData.getClosedDateTime());
        changeRequestLogPk.setCreatedDtTm(wizardEventData.getClosedDateTime());
        changeRequestLogData.setChangeRequestLogPk(changeRequestLogPk);
        changeRequestLogData.setOldRequestStatus("S");
        changeRequestLogData.setNewRequestStatus("X");

        return changeRequestLogData;
    }

    /**
     * The following method is a private/sub method to support the change Event method, to add a Tub File Note to the database
     *
     * @ params WizardEventData Object
     * @ return boolean result
     */
    private boolean addTFNotes(
            WizardEventData wizardEventData,
            String changeRequestNewStatus,
            int doaEventId,
            java.sql.Timestamp incrementedTimestamp) {
        boolean result = false;
        EventTfNotes eventTFNotes = null;
        int tempEventId = 0;
        if (doaEventId > 0)
            tempEventId = doaEventId;
        else
            tempEventId = wizardEventData.getEventId();
        logger.info("adding TF Notes for Event Id >>>>>>>>>> " + tempEventId);

        try {
            //the following is to add a record to the EVENT_TF_NOTES table.			
            int noteId = 0;
            try {
                noteId = eventTfNotesRepository.getMaxNoteId(wizardEventData.getEventId());
                noteId = noteId + 1;
                logger.info("after incrementing noteId====== " + noteId);
            } catch (Exception n) {
                logger.warn("ERROR Close Event addTFNotes() increment note id exception >> " + n.getMessage());
            }
            if (noteId > 0) {
                EventTfNotes eventTFNotesData = new EventTfNotes();
                EventTfNotesPk eventTfNotesPk = new EventTfNotesPk();

                eventTfNotesPk.setEventId(tempEventId);
                eventTfNotesPk.setTfDtTm(wizardEventData.getClosedDateTime());
                eventTFNotesData.setEventTfNotesPk(eventTfNotesPk);

                eventTFNotesData.setEmpNum(wizardEventData.getUserId());
                eventTFNotesData.setEmpName(wizardEventData.getEmployeeName());
                eventTFNotesData.setEmpDepartment(wizardEventData.getEmpDepartment());
                eventTFNotesData.setEditedFlag("N");
                eventTFNotesData.setNoteId(noteId);

                if (wizardEventData.getEventType().trim().equals("TRK")) {
                    eventTFNotesData.setTfNote("Close TRK ");
                    eventTFNotesData.setNoteType(5);
                    eventTFNotesData.setChangeType(0);
                } else if (wizardEventData.getEventType().trim().equals("OOS")) {
                    eventTFNotesData.setChangeType(7);
                    if (wizardEventData.getAccessLevel().trim().equals("90")) {
                        eventTFNotesData.setTfNote("Posted New Status: " + changeRequestNewStatus);
                        eventTFNotesData.setNoteType(3);
                    } else {
                        eventTFNotesData.setTfNote("Submitted New Status: " + changeRequestNewStatus);
                        eventTFNotesData.setNoteType(1);
                    }
                } else if (wizardEventData.getEventType().trim().equals("NOTE")) {
                    eventTFNotesData.setTfNote("Close NOTE ");
                    eventTFNotesData.setNoteType(5);
                    eventTFNotesData.setChangeType(0);
                }

                eventTFNotesData.setLastUpdateDtTm(wizardEventData.getClosedDateTime());

                try {
                    if (eventTFNotesData != null) {
                        eventTFNotes = eventTfNotesRepository.save(eventTFNotesData);
                        result = true;
                    }
                    eventTFNotes = null;
                } catch (Exception tfNotes) {
                    logger.warn(
                            "ERROR Close Event addTFNotes() tfNotes create exception >> "
                                    + tfNotes.getMessage());
                }
                //the following is to add the TF_Notes that the Client had added.
                if (wizardEventData.getTfNotesList() != null && wizardEventData.getTfNotesList().size() > 0) {
                    for (int tf = 0; tf < wizardEventData.getTfNotesList().size(); tf++) {
                        //creating a new createdTimeStamp as the TFDateTime is the primary Key to the table.
                        java.sql.Timestamp tfNotesDateTime = ServerDateHelper.getTimeStamp();

                        if (tfNotesDateTime.equals(wizardEventData.getClosedDateTime())) {
                            java.util.Date currentDate = ServerDateHelper.getDate_UTC();
                            tfNotesDateTime = new java.sql.Timestamp(currentDate.getTime() + 1000);
                            logger.info("incremented tfNotesDateTime >>>>>>>>>>>>>> " + tfNotesDateTime);
                        }

                        String clientData = wizardEventData.getTfNotesList().get(tf);

                        EventTfNotes eventTFNotesData1 = new EventTfNotes();
                        EventTfNotesPk eventTfNotesPk1 = new EventTfNotesPk();
                        eventTfNotesPk1.setEventId(tempEventId);
                        eventTfNotesPk1.setTfDtTm(tfNotesDateTime);
                        eventTFNotesData1.setEventTfNotesPk(eventTfNotesPk1);

                        eventTFNotesData1.setEmpNum(wizardEventData.getUserId());
                        eventTFNotesData1.setEmpName(wizardEventData.getEmployeeName());
                        eventTFNotesData1.setEmpDepartment(wizardEventData.getEmpDepartment());
                        eventTFNotesData1.setEditedFlag("N");
                        eventTFNotesData1.setNoteId(noteId);
                        eventTFNotesData1.setTfNote(clientData);
                        eventTFNotesData1.setNoteType(0);

                        if (wizardEventData.getEventType().trim().equals("OOS")) {
                            eventTFNotesData1.setChangeType(7);
                        } else if (wizardEventData.getEventType().trim().equals("TRK") || wizardEventData.getEventType().trim().equals("NOTE")) {
                            eventTFNotesData1.setChangeType(0);
                        }
                        eventTFNotesData1.setLastUpdateDtTm(wizardEventData.getClosedDateTime());

                        try {
                            if (eventTFNotesData1 != null) {
                                eventTFNotes = eventTfNotesRepository.save(eventTFNotesData1);
                                result = true;
                            }
                            eventTFNotes = null;
                        } catch (Exception tfNotes) {
                            logger.warn("ERROR Close Event addTFNotes() tfNotes create exception 1>> " + tfNotes.getMessage());
                        }
                    } //end of for tfNotesList
                } //end of if tfNotesList !=null
            } //end of if noteId >0
        } catch (Exception notes) {
            logger.warn("ERROR Close Event addTFNotes() notes create exception >> " + notes.getMessage());
        }
        return result;
    }

    /**
     * The following method is a private/sub method to support the close Event method, to add a Reporting Category to the database.
     *
     * @ params ReportCategoriesKeyValueData reportCategoriesKeyValueData.
     * @ return boolean result.
     */
    private boolean setReportingCategories(ReportCategoriesKeyValueData reportCategoriesKeyValueData, java.sql.Timestamp closedDateTime) {
        logger.info("== setReportingCategories().. ==>");
        boolean result = false;
        int eventId = 0;
        String levelOneId = "", levelTwoId = "", updatedLevelOneId = "", updatedLevelTwoId = "";

        eventId = reportCategoriesKeyValueData.getEventId();
        levelOneId = reportCategoriesKeyValueData.getLevelOneId();
        levelTwoId = reportCategoriesKeyValueData.getLevelTwoId();
        updatedLevelOneId = reportCategoriesKeyValueData.getUpdatedLevelOneId();
        updatedLevelTwoId = reportCategoriesKeyValueData.getUpdatedLevelTwoId();

        EventRepCatg data = new EventRepCatg();

        try {
            EventRepCatgPk pk = new EventRepCatgPk();
            pk.setEventId(eventId);
            pk.setLevel1Id(levelOneId);
            pk.setLevel2Id(levelTwoId);
            data = eventRepCatgRepository.findById(pk).orElse(null);
        } catch (Exception e) {
            logger.info("The reporting category for eventId " + eventId + " and level_1_Id " + levelOneId + " could not be found");
        }

        try {
            if (data != null) {
                //update an existing record.
                if (updatedLevelTwoId == null || updatedLevelTwoId.trim().length() == 0) {
                    try {
                        logger.info("deleting the record as updated level 2  is " + updatedLevelTwoId);
                        eventRepCatgRepository.delete(data);
                        result = true;
                    } catch (Exception rem) {
                        logger.warn("ERROR Review Event setReportingCategories remove >> " + rem.getMessage());
                    }
                } else {
                    logger.info("updating the record as updated level 2  is " + updatedLevelTwoId);
                    EventRepCatg repCatdata = new EventRepCatg();
                    EventRepCatgPk pk = new EventRepCatgPk();
                    pk.setEventId(eventId);
                    pk.setLevel1Id(updatedLevelOneId);
                    pk.setLevel2Id(updatedLevelTwoId);
                    repCatdata.setEventRepCatgPk(pk);
                    repCatdata.setLastUpdatedDtTm(closedDateTime);
                    try {
                        eventRepCatgRepository.delete(data);
                        eventRepCatgRepository.save(repCatdata);
                        result = true;
                    } catch (Exception u) {
                        logger.warn("ERROR Review Event setReportingCategories u >> " + u.getMessage());
                    }
                }
            } else {
                //create a new record
                EventRepCatg repCatgdata = new EventRepCatg();
                EventRepCatgPk pk = new EventRepCatgPk();
                pk.setEventId(eventId);
                pk.setLevel1Id(updatedLevelOneId);
                pk.setLevel2Id(updatedLevelTwoId);
                repCatgdata.setEventRepCatgPk(pk);
                repCatgdata.setLastUpdatedDtTm(closedDateTime);

                if (updatedLevelTwoId != null && updatedLevelTwoId.trim().length() > 0)
                    eventRepCatgRepository.save(repCatgdata);
                result = true;
            }
        } catch (Exception update) {
            logger.warn(
                    "ERROR Close Event setReportingCategories() update exception >> "
                            + update.getMessage());
        }
        return result;
    }

    /**
     * The following method is a private/sub method to support the close event method, to start a NIW Timer for the Event
     *
     * @ params String EventId, TimerId, TimerStartDateTime.
     * @ return boolean result.
     */
    private boolean stopNIWTimer(
            int eventId,
            String timerId,
            String timerStopDateTime,
            String createdDateTime,
            String lastUpdated,
            java.sql.Timestamp closedDateTime)
            throws Exception {
        EventTimers eventTimers = null;
        boolean result = false;
        int lastUpdatedRecords = 0;

        String strLookupCreatedDateTime = ServerDateHelper.getLookUpFormat(createdDateTime);
        String strLookupLastUpdatedDateTime = ServerDateHelper.getLookUpFormat(lastUpdated);

        try {
            lastUpdatedRecords = eventTimersRepository.getTimerCountByEventIdAndTimerId(eventId, timerId, strLookupCreatedDateTime, strLookupLastUpdatedDateTime);
            logger.info("Last updated Records ========================" + lastUpdatedRecords);

        } catch (Exception count) {
            logger.warn("ERROR Close Event stopNIWTimer() count exception >> " + count.getMessage());
        }
        if (lastUpdatedRecords == 0) {
            try {
                java.sql.Timestamp stopDateTimeStamp = null, createdDateTimeStamp = null;
                if (timerStopDateTime != null) {
                    stopDateTimeStamp = ServerDateHelper.getConvertedTimestamp(timerStopDateTime);
                    logger.info("stopDateTimeStamp  after converting -------" + stopDateTimeStamp);
                }

                if (createdDateTime != null) {
                    createdDateTimeStamp = ServerDateHelper.getConvertedTimestamp(createdDateTime);
                    logger.info("createdDateTimeStamp  after converting -------" + createdDateTimeStamp);
                }

                if (stopDateTimeStamp != null && createdDateTimeStamp != null) {
                    try {
                        eventTimers = eventTimersRepository.findById(new EventTimersPk(eventTimers.getEventTimersPk().getEventId(), eventTimers.getEventTimersPk().getCreationDtTm())).orElse(null);
                        if (eventTimers != null) {
                            eventTimers.setTimerStopDtTm(stopDateTimeStamp);
                            eventTimers.setLastUpdateDtTm(closedDateTime);
                            eventTimersRepository.save(eventTimers);
                            result = true;
                        }
                    } catch (Exception e) {
                        logger.warn("ERROR Close Event stopNIWTimer() update exception >> " + e.getMessage());
                        result = false;
                    }
                }
            } catch (Exception ee) {
                logger.warn("ERROR Close Event stopNIWTimer() update exception >> " + ee.getMessage());
                result = false;
            }
        }
        if (lastUpdatedRecords > 0) {
            throw new Exception("Not In Work Timer has been updated, could not STOP the Timer");
        }
        return result;
    }

    /**
     * The following method is a private/sub method to support the close event method, to add a linked discrepancy to the database.
     *
     * @ params EventDiscrepancyListData discrepancyData.
     * @ return boolean result.
     */
    private boolean addEventDiscrepancyData(EventDiscrepancyListData discrepancyData) throws Exception {
        logger.info("in the addEventDiscrepancyListData of the ..........");
        boolean result = false;

        EventMaxiDisc eventMaxiDiscrepancy = null;
        EventMaxiDwningItm eventMaxiDiscrepancyDowningItem = null;

        int eventId = 0;
        String ata = "", discNumber = "", eventType = "";
        boolean isDowningItem = false;

        eventId = discrepancyData.getEventId();
        ata = discrepancyData.getAta();
        //to convert back to 4 digit char(eliminate '-' from the string);
        ata = ata.substring(0, 2) + "" + ata.substring(3, 5);
        discNumber = discrepancyData.getNumber();
        eventType = discrepancyData.getEventType();
        isDowningItem = discrepancyData.isDowningItem();

        try {
            EventMaxiDiscPk primaryKey = new EventMaxiDiscPk((long) eventId, ata, discNumber);
            if (primaryKey != null) {
                eventMaxiDiscrepancy = eventMaxiDiscRepository.findById(primaryKey).orElse(null);
            }
            // If we're simply updating the discrepancy as a downing item, it may already exist in the DB
            if (eventMaxiDiscrepancy == null) {
                EventMaxiDisc data = new EventMaxiDisc();
                EventMaxiDiscPk pk = new EventMaxiDiscPk((long) eventId, ata, discNumber);
                data.setEventMaxiDisckPk(pk);
                data.setType(eventType);
                eventMaxiDiscRepository.save(data);
            }
            if (eventMaxiDiscrepancy != null)
                result = true;
        } catch (Exception update) {
            logger.warn("ERROR Close Event addEventDiscrepancyData() create exception >> " + update.getMessage());
        }

        if (isDowningItem) {
            try {
                EventMaxiDwningItm data = new EventMaxiDwningItm();
                data.setEventId((long) eventId);
                data.setAta(ata);
                data.setDiscNum(discNumber);
                eventMaxiDwningItmRepository.save(data);
                result = result && data != null;
            } catch (Exception create) {
                logger.warn(" ERROR Event Discrepancies addEventDiscrepancyDowningItemData() create >> " + create.getMessage());
                throw new Exception("Record could not be inserted " + create.getMessage());
            }

        }
        return result;
    }

    /**
     * The following deleteEventDiscrepancyData() is used to delete Discrepancy Detail of a particular event.
     *
     * @return boolean result.
     * @params EventDiscrepancyListData discrepancyData.
     */
    public boolean deleteEventDiscrepancyData(EventDiscrepancyListData discrepancyData) throws Exception {
        logger.info("in the deleteEventDiscrepancyData of the EventDiscrepanciesUpdateSessionBean bean..........");
        boolean result = false;

        EventMaxiDisc eventMaxiDiscrepancy = null;

        int eventId = 0;
        String ata = "", discNumber = "", eventType = "";

        eventId = discrepancyData.getEventId();
        ata = discrepancyData.getAta();

        //to convert back to 4 digit char(eliminate '-' from the string);
        ata = ata.substring(0, 2) + "" + ata.substring(3, 5);

        discNumber = discrepancyData.getNumber();
        eventType = discrepancyData.getEventType();

        try {
            EventMaxiDiscPk primaryKey = null;
            try {
                primaryKey = new EventMaxiDiscPk((long) eventId, ata, discNumber);
            } catch (Exception pk) {
                return false;
            }
            if (primaryKey != null) {
                eventMaxiDiscrepancy =
                        eventMaxiDiscRepository.findById(primaryKey).orElse(null);
            }
        } catch (Exception e) {
            logger.warn("ERROR Close Event deleteEventDiscrepancyData eeeee >> " + e.getMessage());
        }
        //the following is to delete a record from the EVENT_MAXI_DISC table.
        try {
            if (eventMaxiDiscrepancy != null) {
                eventMaxiDiscRepository.delete(eventMaxiDiscrepancy);
                result = true;
            }
        } catch (Exception update) {
            logger.warn("ERROR Close Event deleteEventDiscrepancyData() update ****---- " + update.getMessage());
        }
        return result;
    }

    /**
     * private method to update PCS Work Order Records for the Aircraft being called UP
     *
     * @ params String acn
     */
    private boolean updatePCSData(String acnRegistration, java.sql.Timestamp updatedTime) {
        boolean isPCSUpdated = false;
        long tempWorkOrder = 0;
        logger.info("updatePCSData.............. acnRegistration >> " + acnRegistration);
        /*
        when an acn is called "UP" (requested by AMC, submitted and confirmed) update any work order for the ACN that does not have a value
        for wo_actual_rfm_dt_tm but does have a value for maxi_first_booking_dt_tm in the PCS works orders table. the update will be populating
        the wo_actual_rfm_dt_tm field with the current system z date/time.*/
        try {
            List<WorksOrders> pcsRecords = worksOrdersRepository.findWORecords(acnRegistration);
            for (int i = 0; i < pcsRecords.size(); i++) {
                try {
                    WorksOrders pcsWorkOrderData = worksOrdersRepository.findById(pcsRecords.get(i).getWoNo()).orElse(null);
                    tempWorkOrder = pcsWorkOrderData.getWoNo();
                    pcsWorkOrderData.setWoActualRfmDtTm(updatedTime);
                    logger.info("..PCS Work Order >" + tempWorkOrder + "WO_Actual_Rfm_Dt_Tm " + pcsWorkOrderData.getWoActualRfmDtTm());

                    WorksOrders wo = worksOrdersRepository.save(pcsWorkOrderData);
                    logger.info(".....Successfully updated  pcsWorkOrder " + wo.getWoNo());
                    if (wo != null) {
                        isPCSUpdated = true;
                    }
                } catch (Exception update) {
                    logger.warn("ERROR Close Event updatePCSData() update exception >> " + update.getMessage());
                    logger.info("Error updating PCS Record for Work Order >> "
                            + tempWorkOrder
                            + " Error Mesg >"
                            + update.getMessage());
                }
            } //end of while.
        } catch (Exception finder) {
            logger.warn("ERROR Close Event updatePCSData() finder exception >> " + finder.getMessage());
        }
            /*
        If user "Cancels" an OOS event in METS then need to check if any work orders in PCS for the ACN that have a value for wo_actual_rtm_dt_tm
        but do not have a value for the wo_first_booking_dt_tm....and if any then clear the value of the wo_actual_rtm_dt_tm.  (this is to handle cases
        where MOCC releases the a/c to the hangar but before the hangar starts work the MOCC takes the aircraft back)*/
        try {
            List<WorksOrders> pcsRecords = worksOrdersRepository.findWOWithRTMDateTime(acnRegistration);
            for (int i = 0; i < pcsRecords.size(); i++) {
                try {
                    WorksOrders pcsWorkOrderData = worksOrdersRepository.findById(pcsRecords.get(i).getWoNo()).orElse(null);
                    tempWorkOrder = pcsWorkOrderData.getWoNo();
                    pcsWorkOrderData.setWoActualRtmDtTm(null);
                    logger.info("..pcsWorkOrderWithRTMDateTime >" + tempWorkOrder);
                    WorksOrders wo = worksOrdersRepository.save(pcsWorkOrderData);
                    logger.info(".....Successfully updated  pcsWorkOrderWithRTMDateTime " + wo.getWoNo());
                    if (wo != null) {
                        isPCSUpdated = true;
                    }
                } catch (Exception update) {
                    logger.warn("ERROR Close Event updatePCSData() update exception 1 >> " + update.getMessage());
                    logger.info("Error ## 1 updating PCS Record for Work Order >> "
                            + tempWorkOrder
                            + " Error Mesg >"
                            + update.getMessage());
                }
            } //end of while.
        } catch (Exception finder) {
            logger.warn("ERROR Close Event updatePCSData() finder exception 1 >> " + finder.getMessage());
        }

        return isPCSUpdated;
    }


    /**
     * private method to update METS Data as SUPER_Equipment Data matches with Change Request created by the Client.
     *
     * @ params	ChangeRequest changeRequest, ChangeRequestHistory changeRequestHistory, ChangeRequestLog changeRequestLog, Events events
     * @ return	result
     */
    private boolean updateMetsDataRecords(
            ChangeRequest changeRequest,
            ChangeRequestHistory changeRequestHistory,
            ChangeRequestLog changeRequestLog,
            Events events) {
        boolean isMetsUpdated = false;

        try {
            java.util.Date currentDate = ServerDateHelper.getDate_UTC();
            java.sql.Timestamp incrementedClosedDateTime =
                    new java.sql.Timestamp(currentDate.getTime() + 5000);
            if (changeRequest != null) {
                ChangeRequest superChangeRequestData = null;
                Events superConfirmationData = eventsRepository.findById(events.getEventId()).orElse(null);

                try {
                    logger.info(" Before confirming Change Request Data");
                    superChangeRequestData = changeRequestRepository.findById(changeRequest.getAcn()).orElse(null);
                    superChangeRequestData.setRequestStatus("C");
                    changeRequestRepository.save(superChangeRequestData);
                } catch (Exception changeRequestError) {
                    logger.warn("ERROR Close Event updateMetsDataRecords() changeRequestError exception >> "
                            + changeRequestError.getMessage());
                    events = null;
                }

                try {
                    logger.info(" Before confirming Change Request Hisotry Data");
                    ChangeRequestHistory superChangeRequestHistoryData = changeRequestHistoryRepository.findById(changeRequestHistory.getChangeRequestHistoryPk()).orElse(null);
                    superChangeRequestHistoryData.setRequestStatus("C");
                    changeRequestHistoryRepository.save(superChangeRequestHistoryData);
                } catch (Exception changeRequestHistoryError) {
                    logger.warn(
                            "ERROR Close Event updateMetsDataRecords() changeRequestHistoryError exception >> "
                                    + changeRequestHistoryError.getMessage());
                }

                try {
                    logger.info(" Before confirming Change Request Log Data");
                    ChangeRequestLog superChangeRequestLogData = new ChangeRequestLog();
                    ChangeRequestLogPk superChangeRequestLogPk = new ChangeRequestLogPk();
                    superChangeRequestLogPk.setEventId(superConfirmationData.getEventId());
                    superChangeRequestLogPk.setStatusChangedDtTm(incrementedClosedDateTime);
                    superChangeRequestLogPk.setCreatedDtTm(incrementedClosedDateTime);
                    superChangeRequestLogData.setChangeRequestLogPk(superChangeRequestLogPk);
                    superChangeRequestLogData.setNewRequestStatus("C");
                    changeRequestLog = changeRequestLogRepository.save(superChangeRequestLogData);
                } catch (Exception changeReqLog) {
                    logger.warn("ERROR Close Event updateMetsDataRecords() changeReqLog exception >> "
                            + changeReqLog.getMessage());
                }

                if (events != null) {
                    try {
                        logger.info(" Before confirming Events Data");
                        superConfirmationData.setStatus(
                                superChangeRequestData.getNewStatus());
                        superConfirmationData.setEticDateTime(
                                superChangeRequestData.getNewEticDtTm());
                        superConfirmationData.setEticText(
                                superChangeRequestData.getNewEticText());
                        superConfirmationData.setOrigComment(
                                superChangeRequestData.getOldComment());
                        superConfirmationData.setCurComment(
                                superChangeRequestData.getNewComment());
                        superConfirmationData.setLastUpdateDateTime(
                                incrementedClosedDateTime);
                        superConfirmationData.setLastUpdatedBy("SUPER");

                        eventsRepository.save(superConfirmationData);
                    } catch (Exception eventsUpdated) {
                        logger.warn(
                                "ERROR Close Event updateMetsDataRecords() eventsUpdated exception >> "
                                        + eventsUpdated.getMessage());
                        events = null;
                    }
                }

                if (events != null) {
                    try {
                        logger.info(" Before inserting confirmation in TF Notes table");
                        String strEticComment = "",
                                strETICForm = "",
                                strStatus = "",
                                strTFNotesString = "";

                        String strChangeRequestEticDateTime = null;

                        if (superChangeRequestData.getNewEticDtTm() != null)
                            strChangeRequestEticDateTime =
                                    "" + superChangeRequestData.getNewEticDtTm();

                        if (strChangeRequestEticDateTime == null
                                && superChangeRequestData.getNewEticText() != null) {
                            strETICForm = superChangeRequestData.getNewEticText();
                        }
                        if (strChangeRequestEticDateTime != null
                                && superChangeRequestData.getNewEticText() != null) {
                            strETICForm =
                                    ETICHelper.getETICFormat(
                                            strChangeRequestEticDateTime,
                                            superChangeRequestData.getNewEticText());
                        }
                        if (strChangeRequestEticDateTime != null
                                && superChangeRequestData.getNewEticText() == null) {
                            strETICForm =
                                    ETICHelper.getETICFormat(strChangeRequestEticDateTime, null);
                        }

                        if (superChangeRequestData.getNewComment() != null
                                && superChangeRequestData.getNewComment().trim().length() > 0)
                            strEticComment = superChangeRequestData.getNewComment();

                        if (strETICForm != null && strETICForm.trim().length() > 0)
                            strETICForm = strETICForm;

                        if (superChangeRequestData.getNewStatus() != null
                                && superChangeRequestData.getNewStatus().length() > 0)
                            strStatus = superChangeRequestData.getNewStatus();

                        if (superChangeRequestData.getChangeType() == 1)
                            strTFNotesString = "Confirm New Comment: " + strEticComment;
                        else if (superChangeRequestData.getChangeType() == 2)
                            strTFNotesString = "Confirm New ETIC: " + strETICForm;
                        else if (superChangeRequestData.getChangeType() == 3)
                            strTFNotesString =
                                    "Confirm New ETIC: " + strETICForm + ", " + strEticComment;
                        else if (superChangeRequestData.getChangeType() == 4)
                            strTFNotesString = "Confirm New Status: " + strStatus;
                        else if (superChangeRequestData.getChangeType() == 5)
                            strTFNotesString =
                                    "Confirm New Status: " + strStatus + ", " + strEticComment;
                        else if (superChangeRequestData.getChangeType() == 6)
                            strTFNotesString =
                                    "Confirm New Status: " + strStatus + ", " + strETICForm;
                        else if (superChangeRequestData.getChangeType() >= 7) {
                            if (strStatus != null && strStatus.trim().equalsIgnoreCase("UP"))
                                strTFNotesString = "Confirm New Status: " + strStatus;
                            else
                                strTFNotesString =
                                        "Confirm New Status: "
                                                + strStatus
                                                + ", "
                                                + strETICForm
                                                + ", "
                                                + strEticComment;
                        }

                        try {
                            boolean tfNoteAdded =
                                    addTFNotes(
                                            superConfirmationData.getEventId(),
                                            strTFNotesString,
                                            incrementedClosedDateTime);
                        } catch (Exception tfNote) {
                            logger.warn("ERROR Close Event updateMetsDataRecords() tfNote exception >> " + tfNote.getMessage());
                        }
                    } catch (Exception tfNoteInserted) {
                        logger.warn("ERROR Close Event updateMetsDataRecords() tfNoteInserted exception >> " + tfNoteInserted.getMessage());
                        events = null;
                    }
                }

                if (events != null)
                    isMetsUpdated = true;
            }
        } catch (Exception pk) {
            logger.warn("ERROR Close Event updateMetsDataRecords() pk exception >> " + pk.getMessage());
        }

        return isMetsUpdated;
    }

    /**
     * The following method is a private/sub method to support the close Event method, to add a Tub File Note to the database
     *
     * @ params int eventId, String tfNote, Timestamp UpdatedTime.
     * @ return boolean result
     */
    private boolean addTFNotes(int eventId, String tfNote, java.sql.Timestamp updatedTimeStamp) {
        boolean result = false;
        String queryString = "";
        EventTfNotes eventTFNotes = null;
        int noteId = 0;
        try {
            noteId = eventTfNotesRepository.getMaxNoteId(eventId);
            noteId = noteId + 2;
            logger.debug("after incrementing noteId====== " + noteId);
        } catch (Exception e) {
            logger.warn("ERROR Close Event addTFNotes() incremente note id 1 >> " + e.getMessage());
        }
        if (noteId > 0) {
            logger.info(" tfNote while inserting the record ======" + tfNote + "<<");

            EventTfNotes eventTFNotesData = new EventTfNotes();
            EventTfNotesPk eventTfNotesPk = new EventTfNotesPk();
            eventTfNotesPk.setEventId(eventId);
            eventTfNotesPk.setTfDtTm(updatedTimeStamp);
            eventTFNotesData.setEventTfNotesPk(eventTfNotesPk);
            eventTFNotesData.setEditedFlag("N");
            eventTFNotesData.setNoteId(noteId);
            eventTFNotesData.setTfNote(tfNote);
            eventTFNotesData.setNoteType(4);
            eventTFNotesData.setLastUpdateDtTm(updatedTimeStamp);

            try {
                eventTfNotesRepository.save(eventTFNotesData);
                result = true;
                eventTFNotes = null;
            } catch (Exception tfNotes) {
                logger.warn("ERROR Close Event addTFNotes() tfNotes #1 exception >> " + tfNotes.getMessage());
            }
        } //end of if(noteId>0)
        return result;
    }
}
