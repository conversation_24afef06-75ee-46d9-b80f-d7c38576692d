
package com.fedex.mets.wsdl.discrepancy;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for DocRef complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="DocRef">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="DocRefOid" type="{http://www.w3.org/2001/XMLSchema}long" minOccurs="0"/>
 *         &lt;element name="DocumentSummary" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="DocumentText" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="DocumentUrl" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="SearchTags" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="DocRefType" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "DocRef", propOrder = {
    "docRefOid",
    "documentSummary",
    "documentText",
    "documentUrl",
    "searchTags",
    "docRefType"
})
public class DocRef {

    @XmlElement(name = "DocRefOid")
    protected Long docRefOid;
    @XmlElement(name = "DocumentSummary")
    protected String documentSummary;
    @XmlElement(name = "DocumentText")
    protected String documentText;
    @XmlElement(name = "DocumentUrl")
    protected String documentUrl;
    @XmlElement(name = "SearchTags")
    protected String searchTags;
    @XmlElement(name = "DocRefType")
    protected String docRefType;

    /**
     * Gets the value of the docRefOid property.
     * 
     * @return
     *     possible object is
     *     {@link Long }
     *     
     */
    public Long getDocRefOid() {
        return docRefOid;
    }

    /**
     * Sets the value of the docRefOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link Long }
     *     
     */
    public void setDocRefOid(Long value) {
        this.docRefOid = value;
    }

    /**
     * Gets the value of the documentSummary property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDocumentSummary() {
        return documentSummary;
    }

    /**
     * Sets the value of the documentSummary property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDocumentSummary(String value) {
        this.documentSummary = value;
    }

    /**
     * Gets the value of the documentText property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDocumentText() {
        return documentText;
    }

    /**
     * Sets the value of the documentText property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDocumentText(String value) {
        this.documentText = value;
    }

    /**
     * Gets the value of the documentUrl property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDocumentUrl() {
        return documentUrl;
    }

    /**
     * Sets the value of the documentUrl property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDocumentUrl(String value) {
        this.documentUrl = value;
    }

    /**
     * Gets the value of the searchTags property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSearchTags() {
        return searchTags;
    }

    /**
     * Sets the value of the searchTags property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSearchTags(String value) {
        this.searchTags = value;
    }

    /**
     * Gets the value of the docRefType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDocRefType() {
        return docRefType;
    }

    /**
     * Sets the value of the docRefType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDocRefType(String value) {
        this.docRefType = value;
    }

}
