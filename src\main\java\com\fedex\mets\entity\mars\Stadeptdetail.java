package com.fedex.mets.entity.mars;

import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "STADEPTDETAIL")
public class Stadeptdetail {
    @EmbeddedId
    public StadeptdetailPk stadeptdetailPk;

    @Column(name = "STA_ADDRESS1")
    public String staAddress1;

    @Column(name = "STA_ADDRESS2")
    public String staAddress2;

    @Column(name = "STA_CITY")
    public String staCity;

    @Column(name = "STA_STATE")
    public String staState;

    @Column(name = "STA_ZIP")
    public String staZip;

    @Column(name = "STA_COMAIL")
    public String staComail;

    @Column(name = "STA_GMT_OFFSET")
    public Integer staGmtOffset;

    @Column(name = "MX_PHONE")
    public String mxPhone;

    @Column(name = "MX_CELL")
    public String mxCell;

    @Column(name = "MX_PAGER")
    public String mxPager;

    @Column(name = "MX_FAX")
    public String mxFax;

    @Column(name = "DISTRICT_ID")
    public String districtId;

    @Column(name = "MOCC_MX_SPD_DIAL")
    public String moccMxSpdDial;

    @Column(name = "MOCC_RAMP_SPD_DIAL")
    public String moccRampSpdDial;

    @Column(name = "FDX_MANNED")
    public String fdxManned;

    @Column(name = "MX_CONTACT")
    public String mxContact;

    @Column(name = "INTL")
    public String intl;

    @Column(name = "STA_RAMP_PHONE")
    public String staRampPhone;

    @Column(name = "MX_COMMENTS")
    public String mxComments;

    @Column(name = "MOCC_COMMENTS")
    public String moccComments;

    @Column(name = "STA_COUNTRYCODE")
    public String staCountryCode;

    @Column(name = "STA_ADDRESS3")
    public String staAddress3;

    @Column(name = "FIRE_PHONE")
    public String firePhone;

    @Column(name = "POLICE_PHONE")
    public String policePhone;

    @Column(name = "STA_VPORG")
    public String staVporg;

    @Column(name = "AMBULANCE_PHONE")
    public String ambulancePhone;

    @Column(name = "TOWER_PHONE")
    public String towerPhone;

    @Column(name = "TOWER_FREQ")
    public String towerFreq;

    @Column(name = "MX_FREQ")
    public String mxFreq;

    @Column(name = "HZD_PHONE")
    public String hzdPhone;

    @Column(name = "POISON_PHONE")
    public String poisonPhone;

    @Column(name = "DISTRICT_DESC")
    public String districtDesc;

    @Column(name = "REGION_ID")
    public String regionId;

    @Column(name = "REGION_DESC")
    public String regionDesc;

    @Column(name = "SECTOR_ID")
    public String sectorId;

    @Column(name = "SECTOR_DESC")
    public String sectorDesc;

    @Column(name = "OPS_HOURS")
    public String opsHours;

    @Column(name = "LASTUPDATED")
    public Timestamp lastUpdated;

    @Column(name = "LASTUPDATEDBY")
    public String lastUpdatedBy;

    @Column(name = "REGION_RESP_EMPNUM")
    public String regionRespEmpNum;

    @Column(name = "REGION_RESP_EMPNAME")
    public String regionRespEmpName;

    @Column(name = "SECTOR_RESP_EMPNUM")
    public String sectorRespEmpNum;

    @Column(name = "SECTOR_RESP_EMPNAME")
    public String sectorRespEmpName;

    @Column(name = "VENDOR_NAME")
    public String vendorName;

    @Column(name = "STA_EMAIL")
    public String staEmail;

    @Column(name = "STA_PRINTER")
    public String staPrinter;

    @Column(name = "STA_SITA_ADDRESS")
    public String staSitaAddress;

    @Column(name = "STA_RAMP_FAX")
    public String staRampFax;

    @Column(name = "STA_MISC_REMARKS")
    public String staMiscRemarks;

    @Column(name = "DEPT_DESC")
    public String deptDesc;

    @Column(name = "SECTOR_PA_EMPNUM")
    public String sectorPaEmpNum;

    @Column(name = "SECTOR_RESP_PHONE")
    public String sectorRespPhone;

    @Column(name = "REGION_RESP_PHONE")
    public String regionRespPhone;

    @Column(name = "REGION_RESP_FAX")
    public String regionRespFax;

    @Column(name = "REGION_PA_EMPNUM")
    public String regionPaEmpNum;

    @Column(name = "COMMON_CITY")
    public String commonCity;

    @Column(name = "TIME_ZONE")
    public String timeZone;

    @Column(name = "SIDA")
    public String sida;

    @Column(name = "CAL_STA_ID")
    public String calStaId;

    @Column(name = "CAL_DEPT_ID")
    public String calDeptId;

}