package com.fedex.mets.service.addEvent;

import com.fedex.mets.data.ActiveEventResults;
import com.fedex.mets.data.ListViewData;
import com.fedex.mets.data.WizardEventData;
import com.fedex.mets.util.IServerConstants;
import com.fedex.mets.util.ServerDateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

@Service
public class EventEntryService {
    private static final Logger logger = LoggerFactory.getLogger(EventEntryService.class);

    @Autowired
    private ActiveEventService activeEventService;

    @Autowired
    private  ValidateDoaEventService validateDoaEventService;

    @Autowired
    private AddAircraftEventService addAircraftEventService;

    /**
     * Method to add the Event.
     * @return hashmap.
     */
    public HashMap addEvent(
            WizardEventData wizardEventData)
            throws Exception {
        HashMap hashmap = new HashMap();
        try {
            ActiveEventResults resultList = new ActiveEventResults();
                                // events that are active in the database(contains activeEvents && overridableEvents)

            List<ListViewData> activeEvents = new ArrayList<>(), //events that are active in the database.
                               unReviewedEvents = new ArrayList<>(), //unReviewed Events(Events that are closed and requiring Duty Mgr Review).
                               convertableEvents = new ArrayList<>(),//events that are Active and could be converted.
                               doaActiveEventList = new ArrayList<>(), // DOA events list active and current.
                               overridableEvents = new ArrayList<>();//events that can be overrided as the Request status is 'S' and New Status is 'UP'

            try {
                resultList =
                        activeEventService.findActiveEvents(wizardEventData.getACN());

                if (resultList != null){
                    activeEvents = resultList.getActiveEvents();
                    overridableEvents =resultList.getOverridenEvents();
                }
                /*########################################################################
					 To check for Events which can be overrided.
					 Events which are submitted to SUPER with Status as "UP".**/
                if (activeEvents != null && activeEvents.size() > 0) {
                    ListViewData overridableData = null;
                    int overridableEventId = 0, activeEventId = 0;

                    if (overridableEvents != null && overridableEvents.size() > 0) {
                        overridableData = (ListViewData) overridableEvents.get(0);

                        overridableEventId = overridableData.getEventID();
                        wizardEventData.setOverrideEventId(overridableEventId);
                        logger.info("overridableEventId >> " + overridableEventId);
                    }

                    Iterator iterator = activeEvents.iterator();
                    while (iterator.hasNext()) {
                        ListViewData activeData = (ListViewData) iterator.next();
                        activeEventId = activeData.getEventID();
                        logger.info("activeEventId >> " + activeEventId);

                        if (activeEventId == overridableEventId) {
                            iterator.remove();
                        }
                    }

//                    if (wizardEventData.getOverrideRequest())
//                         overridableEvents.clear();
                    }
                    /*#########################################################*/

                    if (activeEvents.size() > 0) {
                        logger.info("  ");
                        logger.info("As there are active Events, Check for DOA Active. && check for Unreviewed Events.");

                        //to get active Future/Current/unclosed DOA events.
                        doaActiveEventList =
                                validateDoaEventService.validateDOAEvents(wizardEventData.getACN());

                        //to get the unreviewed OOS Events that are closed
                        unReviewedEvents =
                                activeEventService.findUnReviewedEvents(wizardEventData.getACN());
                    }
                } catch (Exception exec) {
                    hashmap.put(IServerConstants.ERROR, exec.getMessage());
                }


			/*The following is to remove the DOA Events from the Active Events list.
			 the business logic involved in categorizing a DOA event as acitve are different
			    The active DOA events could be
			  - Future DOA Events
			  - Unclosed
			  - Current.
			    The active Future DOA events are left as they are and the Client can add an Event with out interruption.*/
            if (activeEvents != null
                    && activeEvents.size() > 0
                    && wizardEventData.getEventType().trim().equalsIgnoreCase("TRK")) {
                Iterator iterator = activeEvents.iterator();
                while (iterator.hasNext()) {
                    ListViewData activeData = (ListViewData) iterator.next();
                    if (activeData.getType().equalsIgnoreCase("NOTE")) {
                        logger.info(
                                "*****************As there are active Events, Removing the events "
                                        + activeData.getType()
                                        + " from active list.");
                        iterator.remove();
                    }
                }
            }
            /*This condition is to add the current Active DOA events to the activeEvents List;*/
            if (doaActiveEventList != null && doaActiveEventList.size() > 0) {
                logger.info("  ");
                logger.info("As there are doaActiveEvents adding them to the Active events List.");
                for (int active = 0; active < doaActiveEventList.size(); active++) {
                    ListViewData listData =
                            (ListViewData) doaActiveEventList.get(active);
                    activeEvents.add(listData);
                }
            }
			/*The following conditions are IF Event Type DOA we check for only unReviewed events
			       Else if event type OOS or TRK is being added ,we check for past convertible events and active events.**/
            if (wizardEventData.getEventType().trim().equalsIgnoreCase("DOA")) {
                if (unReviewedEvents.size() > 0) {
                    String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
                    logger.info(
                            "eventType   ----  "
                                    + wizardEventData.getEventType()
                                    + " **  ----         ");
                    logger.info(
                            "ERROR ##        Aircraft "
                                    + wizardEventData.getACN()
                                    + " has a closed OOS Event requiring Duty Manager's Review, before a DOA can be added.");
                    hashmap.put(
                            IServerConstants.ERROR,
                            "Aircraft "
                                    + wizardEventData.getACN()
                                    + " has a closed OOS Event \nrequiring Duty Manager's Review before a DOA can be added.");
                    hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                    hashmap.put(
                            IServerConstants.ACTION_REQUIRED_EVENTS,
                            unReviewedEvents);

                } else if (activeEvents.size() > 0) {
                    //As, only ONE DOA per aircraft is allowed and if active OOS event let add the first DOA event on that aircraft.
                    Iterator iterator = activeEvents.iterator();
                    while (iterator.hasNext()) {
                        ListViewData activeData = (ListViewData) iterator.next();
                        if (activeData.getType().equalsIgnoreCase("OOS")
                                || activeData.getType().equalsIgnoreCase("NOTE")
                                || activeData.getType().equalsIgnoreCase("TRK")) {
                            logger.info(
                                    "*****************While adding DOA check and Removing the events "
                                            + activeData.getType()
                                            + " from active list.");
                            iterator.remove();
                        }
                    }

                    if (activeEvents.size() > 0) {
                        String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
                        logger.info(" ");
                        logger.info(
                                "ERROR ##        Aircraft "
                                        + wizardEventData.getACN()
                                        + " has active events requiring action and a "
                                        + wizardEventData.getEventType()
                                        + " cannot be added.\nPlease return");

                        hashmap.put(
                                IServerConstants.ERROR,
                                "Aircraft "
                                        + wizardEventData.getACN()
                                        + " has active events requiring action and a "
                                        + wizardEventData.getEventType()
                                        + " cannot be added.\nPlease return");
                        hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                        hashmap.put(
                                IServerConstants.ACTION_REQUIRED_EVENTS,
                                activeEvents);
                    } else {
                        if (wizardEventData.isAddNewEvent() == true){
                                logger.info("**** NO Active Events **** proceeding with adding the Event.");
                            try {
                                wizardEventData = addAircraftEventService.addAircraftEvent(wizardEventData);
                                logger.info("Successfully added new Event:" + wizardEventData.getEventId());
                            } catch (Exception addingEvent) {
                                String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
                                hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                                hashmap.put(IServerConstants.ERROR, addingEvent.getMessage());
                            }

                            if (wizardEventData != null
                                    && wizardEventData.getServerError() == null) {
                                String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
                                hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                                hashmap.put(
                                        IServerConstants.SUCCESS, "The Event " + wizardEventData.getEventId() + " was successfully added.");
                                hashmap.put(
                                        IServerConstants.WIZARD_EVENT_DATA,
                                        wizardEventData);
                            } else {
                                String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
                                hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                                hashmap.put(IServerConstants.ERROR, "The Event could not be added.");
                            }
                        }
                        else{
                            hashmap.put(IServerConstants.ADD_EVENT_FLAG, true);
                        }
                    }
                } else if (overridableEvents.size() > 0) {
                    if (wizardEventData.isOverrideRequest()) {
                        logger.info(" As the USER Requested to Over ride the existing Event....");
                        try {
                            wizardEventData = addAircraftEventService.addAircraftEvent(wizardEventData);
                        } catch (Exception addingEvent) {
                            String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
                            hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                            hashmap.put(IServerConstants.ERROR, addingEvent.getMessage());
                        }

                        if (wizardEventData != null
                                && wizardEventData.getServerError() == null) {
                            String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
                            hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                            hashmap.put(
                                    IServerConstants.SUCCESS, "The Event " +wizardEventData.getEventId()+" was successfully added.");
                            hashmap.put(
                                    IServerConstants.WIZARD_EVENT_DATA,
                                    wizardEventData);
                        } else if (
                                wizardEventData != null
                                        && wizardEventData.getServerError() != null) {
                            String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
                            hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                            hashmap.put(
                                    IServerConstants.ERROR,
                                    wizardEventData.getServerError());
                        } else {
                            String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
                            hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                            hashmap.put(
                                    IServerConstants.ERROR,
                                    "The Event could not be added.");
                        }
                    } else {
                        String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
                        logger.info(
                                "ERROR ##        Aircraft "
                                        + wizardEventData.getACN()
                                        + " has the following active event which has been called UP \nbut has not yet been confirmed from SUPER.\nDo you want to continue adding the new "+wizardEventData.getEventType()+"event?");
                        hashmap.put(
                                IServerConstants.ERROR,
                                "Aircraft "
                                        + wizardEventData.getACN()
                                        + " has the following active event which has been called UP \nbut has not yet been confirmed from SUPER.\nDo you want to continue adding the new " +wizardEventData.getEventType()+ " event?");
                        hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                        hashmap.put(
                                IServerConstants.OVERRIDABLE_EVENTS,
                                overridableEvents);
                    } //user did not specify to Over ride the existing Event...
                } else {
                    if (wizardEventData.isAddNewEvent() == true){
                        logger.info("****** NO Active Events, proceeding with adding the Event.");
                        try {
                            wizardEventData = addAircraftEventService.addAircraftEvent(wizardEventData);
                        } catch (Exception addingEvent) {
                            String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
                            hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                            hashmap.put(IServerConstants.ERROR, addingEvent.getMessage());
                        }

                        if (wizardEventData != null
                                && wizardEventData.getServerError() == null) {
                            String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
                            hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                            hashmap.put(
                                    IServerConstants.SUCCESS, "The Event " + wizardEventData.getEventId() + " was successfully added.");
                            hashmap.put(IServerConstants.WIZARD_EVENT_DATA, wizardEventData);
                        } else if (
                                wizardEventData != null
                                        && wizardEventData.getServerError() != null) {
                            String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
                            hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                            hashmap.put(
                                    IServerConstants.ERROR,
                                    wizardEventData.getServerError());
                        } else {
                            String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
                            hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                            hashmap.put(
                                    IServerConstants.ERROR,
                                    "The Event could not be added.");
                        }
                    }
                    else{
                        hashmap.put(IServerConstants.ADD_EVENT_FLAG, true);
                    }
                }
            } else if (
                    wizardEventData.getEventType().trim().equalsIgnoreCase("NOTE")) {
                if(wizardEventData.isAddNewEvent() == true) {
                    logger.info(
                            "****** As Event type being added is ** > "
                                    + wizardEventData.getEventType().trim()
                                    + ".... proceeding with adding the Event.");
                    try {
                        wizardEventData = addAircraftEventService.addAircraftEvent(wizardEventData);
                    } catch (Exception addingEvent) {
                        String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
                        hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                        hashmap.put(IServerConstants.ERROR, addingEvent.getMessage());
                    }
                    if (wizardEventData != null
                            && wizardEventData.getServerError() == null) {
                        String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
                        hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                        hashmap.put(
                                IServerConstants.SUCCESS, "The Event " + wizardEventData.getEventId() + " was successfully added.");
                        hashmap.put(IServerConstants.WIZARD_EVENT_DATA, wizardEventData);
                    }
                }
                else{
                    hashmap.put(IServerConstants.ADD_EVENT_FLAG, true);
                }
            } else {
                String strErrorMessage = "";
                if (activeEvents.size() > 0) {
                    String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
                    //The following is to check and see if there are any events active that needs to be converted.
                    for (int activeEvent = 0;
                         activeEvent < activeEvents.size();
                         activeEvent++) {
                        ListViewData data = (ListViewData) activeEvents.get(activeEvent);
                        if (wizardEventData.getEventType().trim().equalsIgnoreCase("TRK")
                                && data.getType().trim().equalsIgnoreCase("DOA")) {

                            logger.info("As there are Active Events that should be converted adding them to the Convertable events List. -- 1");
                            convertableEvents.add(data);
                        } else if (
                                wizardEventData.getEventType().trim().equalsIgnoreCase("TRK")
                                        && data.getType().trim().equalsIgnoreCase("OOS")) {
                            if (data.getStatus() != null
                                    && data.getStatus().trim().equals("UP")
                                    && wizardEventData.isAddNewEvent()) {
                                logger.info(" As the USER Requested to Re-Open the Existing Event/Conitnue adding Event....");
                                try {
                                    wizardEventData = addAircraftEventService.addAircraftEvent(wizardEventData);
                                } catch (Exception addingEvent) {
                                    hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                                    hashmap.put(
                                            IServerConstants.ERROR,
                                            addingEvent.getMessage());
                                }

                                if (wizardEventData != null
                                        && wizardEventData.getServerError() == null) {
                                    hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                                    hashmap.put(
                                            IServerConstants.SUCCESS, "The Event " +wizardEventData.getEventId()+" was successfully added.");
                                    hashmap.put(
                                            IServerConstants.WIZARD_EVENT_DATA,
                                            wizardEventData);
                                } else if (
                                        wizardEventData != null
                                                && wizardEventData.getServerError() != null) {
                                    hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                                    hashmap.put(
                                            IServerConstants.ERROR,
                                            wizardEventData.getServerError());
                                } else {
                                    hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                                    hashmap.put(
                                            IServerConstants.ERROR,
                                            "The Event could not be added.");
                                }
                            } else {
                                Iterator iterator = activeEvents.iterator();
                                while (iterator.hasNext()) {
                                    ListViewData activeData = (ListViewData) iterator.next();

                                    if (activeData.getType().equalsIgnoreCase("NOTE")) {
                                        iterator.remove();
                                    }
                                }
                                logger.info(
                                        "ERROR ##        Aircraft "
                                                + wizardEventData.getACN()
                                                + " has active events requiring action and a "
                                                + wizardEventData.getEventType()
                                                + " cannot be added.\nPlease return.");
                                hashmap.put(
                                        IServerConstants.ERROR,
                                        "Aircraft "
                                                + wizardEventData.getACN()
                                                + " has active events requiring action and a "
                                                + wizardEventData.getEventType()
                                                + " cannot be added.\nPlease return.");
                                hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                                hashmap.put(
                                        IServerConstants.ACTION_REQUIRED_EVENTS,
                                        activeEvents);
                            }
                        } else if (
                                wizardEventData.getEventType().trim().equalsIgnoreCase("OOS")) {
                            if (data.getType().trim().equalsIgnoreCase("DOA")
                                    || data.getType().trim().equalsIgnoreCase("NOTE")) {
                                logger.info("As there are Active Events that should be converted adding them to the Convertable events List. -- 1");
                                convertableEvents.add(data);
                            } else if (data.getType().trim().equalsIgnoreCase("TRK")) {
                                logger.info("As there are Active Events that should be converted adding them to the Convertable events List. -- 1  A");
                                strErrorMessage =
                                        "Aircraft "
                                                + wizardEventData.getACN()
                                                + " has the following active TRK event.\nDo you want to convert this event to an OOS event ?";
                                convertableEvents.add(data);
                            } else if (data.getType().trim().equalsIgnoreCase("OOS")) {
                                if (data.getStatus() != null
                                        && data.getStatus().trim().equals("UP")
                                        && wizardEventData.isAddNewEvent()) {

                                    logger.info(" As the USER Requested to Re-Open the Existing Event/Conitnue adding Event....");
                                    try {
                                        wizardEventData = addAircraftEventService.addAircraftEvent(wizardEventData);
                                    } catch (Exception addingEvent) {
                                        hashmap.put(
                                                IServerConstants.CURRENT_TIME,
                                                currentDateTime);
                                        hashmap.put(
                                                IServerConstants.ERROR,
                                                addingEvent.getMessage());
                                    }

                                    if (wizardEventData != null
                                            && wizardEventData.getServerError() == null) {
                                        hashmap.put(
                                                IServerConstants.CURRENT_TIME,
                                                currentDateTime);
                                        hashmap.put(
                                                IServerConstants.SUCCESS, "The Event " +wizardEventData.getEventId()+" was successfully added.");
                                        hashmap.put(
                                                IServerConstants.WIZARD_EVENT_DATA,
                                                wizardEventData);
                                    } else if (
                                            wizardEventData != null
                                                    && wizardEventData.getServerError() != null) {
                                        hashmap.put(
                                                IServerConstants.CURRENT_TIME,
                                                currentDateTime);
                                        hashmap.put(
                                                IServerConstants.ERROR,
                                                wizardEventData.getServerError());
                                    } else {
                                        hashmap.put(
                                                IServerConstants.CURRENT_TIME,
                                                currentDateTime);
                                        hashmap.put(
                                                IServerConstants.ERROR,
                                                "The Event could not be added.");
                                    }
                                } else if(unReviewedEvents.size() > 0) {
                                    logger.info(
                                            "eventType   ----  "
                                                    + wizardEventData.getEventType()
                                                    + " **  ----         ");
                                    logger.info(
                                            "ERROR ##        Aircraft "
                                                    + wizardEventData.getACN()
                                                    + " has a closed OOS Event requiring Duty Manager's Review");
                                    hashmap.put(
                                            IServerConstants.ERROR,
                                            "Aircraft "
                                                    + wizardEventData.getACN()
                                                    + " has a closed OOS Event \nrequiring Duty Manager's Review /n Please return");
                                    hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                                    hashmap.put(
                                            IServerConstants.UNREVIEWED_EVENTS,
                                            unReviewedEvents);
                                }
                                else {
                                    Iterator iterator = activeEvents.iterator();
                                    while (iterator.hasNext()) {
                                        ListViewData activeData = (ListViewData) iterator.next();

                                        if (activeData.getType().equalsIgnoreCase("NOTE")) {
                                            iterator.remove();
                                        }
                                    }
                                    logger.info(
                                            "ERROR ##  Aircraft "
                                                    + wizardEventData.getACN()
                                                    + " has active events requiring action and a "
                                                    + wizardEventData.getEventType()
                                                    + " cannot be added.\nPlease return.");
                                    hashmap.put(
                                            IServerConstants.ERROR,
                                            "Aircraft "
                                                    + wizardEventData.getACN()
                                                    + " has active events requiring action and a "
                                                    + wizardEventData.getEventType()
                                                    + " cannot be added.\nPlease return.");
                                    hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                                    hashmap.put(
                                            IServerConstants.ACTION_REQUIRED_EVENTS,
                                            activeEvents);
                                }
                            }
                        }
                    }

                    if (convertableEvents.size() > 0) {
                        if (wizardEventData.isContinueAddingEvent()) {
                            if (overridableEvents.size() > 0) {
                                if (wizardEventData.isOverrideRequest()){
                                    logger.info(" As the USER Requested to Over ride the Existing Event....");
                                    try {
                                        wizardEventData = addAircraftEventService.addAircraftEvent(wizardEventData);
                                    } catch (Exception addingEvent) {
                                        hashmap.put(
                                                IServerConstants.CURRENT_TIME,
                                                currentDateTime);
                                        hashmap.put(
                                                IServerConstants.ERROR,
                                                addingEvent.getMessage());
                                    }

                                    if (wizardEventData != null
                                            && wizardEventData.getServerError() == null) {
                                        hashmap.put(
                                                IServerConstants.CURRENT_TIME,
                                                currentDateTime);
                                        hashmap.put(
                                                IServerConstants.WIZARD_EVENT_DATA,
                                                wizardEventData);
                                    } else if (
                                            wizardEventData != null
                                                    && wizardEventData.getServerError() != null) {
                                        hashmap.put(
                                                IServerConstants.CURRENT_TIME,
                                                currentDateTime);
                                        hashmap.put(
                                                IServerConstants.ERROR,
                                                wizardEventData.getServerError());
                                    } else {
                                        hashmap.put(
                                                IServerConstants.CURRENT_TIME,
                                                currentDateTime);
                                        hashmap.put(
                                                IServerConstants.ERROR,
                                                "The Event could not be added.");
                                    }
                                } else {
                                    logger.info(
                                            "ERROR ##        Aircraft "
                                                    + wizardEventData.getACN()
                                                    + " has the following active event which has been called UP \nbut has not yet been confirmed from SUPER.\nDo you want to continue adding the new "+wizardEventData.getEventType()+" event?");
                                    hashmap.put(
                                            IServerConstants.ERROR,
                                            "Aircraft "
                                                    + wizardEventData.getACN()
                                                    + " has the following active event which has been called UP \nbut has not yet been confirmed from SUPER.\nDo you want to continue adding the new "+wizardEventData.getEventType()+" event?");
                                    hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                                    hashmap.put(
                                            IServerConstants.OVERRIDABLE_EVENTS,
                                            overridableEvents);
                                } //User did not specify to over ride the existing event.
                            }

                            logger.info(" As the USER Requested to Continue adding the Event....");
                                try {
                                    wizardEventData = addAircraftEventService.addAircraftEvent(wizardEventData);
                                } catch (Exception addingEvent) {
                                    hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                                    hashmap.put(IServerConstants.ERROR, addingEvent.getMessage());
                                }

                                if (wizardEventData != null
                                        && wizardEventData.getServerError() == null) {
                                    hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                                    hashmap.put(
                                            IServerConstants.SUCCESS, "The Event " + wizardEventData.getEventId() + " was successfully added.");
                                    hashmap.put(
                                            IServerConstants.WIZARD_EVENT_DATA,
                                            wizardEventData);
                                } else if (
                                        wizardEventData != null
                                                && wizardEventData.getServerError() != null) {
                                    hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                                    hashmap.put(
                                            IServerConstants.ERROR,
                                            wizardEventData.getServerError());

                                } else {
                                    hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                                    hashmap.put(
                                            IServerConstants.ERROR,
                                            "The Event could not be added.");
                                }
                        } else {
                            logger.info(
                                    "ERROR ##     Aircraft "
                                            + wizardEventData.getACN()
                                            + " has the following active events that can be converted to an OOS event.\nPlease select an option to continue.");
                            if (strErrorMessage != null
                                    && strErrorMessage.trim().length() > 0)
                                hashmap.put(IServerConstants.ERROR, strErrorMessage);
                            else
                                hashmap.put(
                                        IServerConstants.ERROR,
                                        "Aircraft "
                                                + wizardEventData.getACN()
                                                + " has the following active events that can be converted to " +wizardEventData.getEventType()+" event.\nPlease select an option to continue.");
                            hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                            hashmap.put(
                                    IServerConstants.CONVERTABLE_EVENTS,
                                    convertableEvents);
                        }
                    }
                    else if(unReviewedEvents.size() > 0) {
                        logger.info(
                                "eventType   ----  "
                                        + wizardEventData.getEventType()
                                        + " **  ----         ");
                        logger.info(
                                "ERROR ##        Aircraft "
                                        + wizardEventData.getACN()
                                        + " has a closed OOS Event requiring Duty Manager's Review");
                        hashmap.put(
                                IServerConstants.ERROR,
                                "Aircraft "
                                        + wizardEventData.getACN()
                                        + " has a closed OOS Event \nrequiring Duty Manager's Review /n Please return");
                        hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                        hashmap.put(
                                IServerConstants.UNREVIEWED_EVENTS,
                                unReviewedEvents);
                    }else {
                        logger.info(
                                "ERROR ##        Aircraft "
                                        + wizardEventData.getACN()
                                        + " has active events requiring action and a "
                                        + wizardEventData.getEventType()
                                        + " cannot be added.\nPlease return.");
                        hashmap.put(
                                IServerConstants.ERROR,
                                "Aircraft "
                                        + wizardEventData.getACN()
                                        + " has active events requiring action and a "
                                        + wizardEventData.getEventType()
                                        + " cannot be added.\nPlease return.");
                        hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                        hashmap.put(
                                IServerConstants.ACTION_REQUIRED_EVENTS,
                                activeEvents);
                    }
                } else if (overridableEvents.size() > 0) {
                    if (wizardEventData.isOverrideRequest()) {
                        logger.info(" As the USER Requested to Over ride the Existing Event....");
                        try {
                            wizardEventData =addAircraftEventService.addAircraftEvent(wizardEventData);
                        } catch (Exception addingEvent) {
                            String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
                            hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                            hashmap.put(IServerConstants.ERROR, addingEvent.getMessage());
                        }

                        if (wizardEventData != null
                                && wizardEventData.getServerError() == null) {
                            String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
                            hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                            hashmap.put(
                                    IServerConstants.SUCCESS, "The Event " +wizardEventData.getEventId()+" was successfully added.");
                            hashmap.put(
                                    IServerConstants.WIZARD_EVENT_DATA,
                                    wizardEventData);
                        } else if (
                                wizardEventData != null
                                        && wizardEventData.getServerError() != null) {
                            String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
                            hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                            hashmap.put(
                                    IServerConstants.ERROR,
                                    wizardEventData.getServerError());
                        } else {
                            String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
                            hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                            hashmap.put(
                                    IServerConstants.ERROR,
                                    "The Event could not be added.");
                        }
                    }
                    else if(unReviewedEvents.size() > 0) {
                        String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
                        logger.info(
                                "eventType   ----  "
                                        + wizardEventData.getEventType()
                                        + " **  ----         ");
                        logger.info(
                                "ERROR ##        Aircraft "
                                        + wizardEventData.getACN()
                                        + " has a closed OOS Event requiring Duty Manager's Review /n Please return");
                        hashmap.put(
                                IServerConstants.ERROR,
                                "Aircraft "
                                        + wizardEventData.getACN()
                                        + " has a closed OOS Event \nrequiring Duty Manager's Review /n Please return");
                        hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                        hashmap.put(
                                IServerConstants.UNREVIEWED_EVENTS,
                                unReviewedEvents);
                    }
                    else {
                        String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
                        logger.info(
                                "ERROR ##        Aircraft "
                                        + wizardEventData.getACN()
                                        + " has the following active event which has been called UP \nbut has not yet been confirmed from SUPER.\nDo you want to continue adding the new "+wizardEventData.getEventType()+" event?");
                        hashmap.put(
                                IServerConstants.ERROR,
                                "Aircraft "
                                        + wizardEventData.getACN()
                                        + " has the following active event which has been called UP \nbut has not yet been confirmed from SUPER.\nDo you want to continue adding the new "+wizardEventData.getEventType()+" event?");
                        hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                        hashmap.put(
                                IServerConstants.OVERRIDABLE_EVENTS,
                                overridableEvents);
                    } //User did not specify to over ride the existing event.
                } else {
                    logger.info("****** NO Active (or) Past Events proceeding with adding the Event.");
                    if (wizardEventData.isAddNewEvent() == true){
                        try {
                            wizardEventData = addAircraftEventService.addAircraftEvent(wizardEventData);
                        } catch (Exception addingEvent) {
                            String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
                            hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                            hashmap.put(IServerConstants.ERROR, addingEvent.getMessage());
                        }

                        if (wizardEventData != null) {
                            String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
                            hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                            hashmap.put(
                                    IServerConstants.SUCCESS, "The Event " + wizardEventData.getEventId() + " was successfully added.");
                            hashmap.put(IServerConstants.WIZARD_EVENT_DATA, wizardEventData);
                        } else if (
                                wizardEventData != null) {
                            String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
                            hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                            hashmap.put(
                                    IServerConstants.ERROR,
                                    wizardEventData.getServerError());
                        } else {
                            String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
                            hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
                            hashmap.put(
                                    IServerConstants.ERROR,
                                    "The Event could not be added.");
                        }
                    }
                    else{
                        hashmap.put(IServerConstants.ADD_EVENT_FLAG, true);
                    }
                }
            }
        } catch (Exception e) {
            logger.info("Exception in addEvent method: " + e.getMessage());
            String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
            hashmap.put(IServerConstants.CURRENT_TIME, currentDateTime);
            hashmap.put(IServerConstants.ERROR, e.getMessage());
        }
        return hashmap;
    }

}
