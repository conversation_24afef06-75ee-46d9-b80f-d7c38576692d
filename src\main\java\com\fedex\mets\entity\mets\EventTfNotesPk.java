package com.fedex.mets.entity.mets;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Embeddable
public class EventTfNotesPk implements Serializable {
    @Column(name = "EVENT_ID", nullable = false)
    private int eventId;

    @Column(name = "TF_DT_TM", nullable = false)
    private Timestamp tfDtTm;
}
