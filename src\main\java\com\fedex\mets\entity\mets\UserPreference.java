package com.fedex.mets.entity.mets;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "user_preference")
public class UserPreference implements Serializable {

    @Column(name = "emp_nm")
    private String empName;

    @Id
    @Column(name = "emp_nbr")
    private int empNbr;

    @Column(name = "PREFERENCE_JSON")
    private String preferenceJson;

    @Column(name = "CREATED_TMSTP")
    private Timestamp createdTMSTP;

    @Column(name = "UPDATED_TMSTP")
    private Timestamp updatedTMSTP;

}