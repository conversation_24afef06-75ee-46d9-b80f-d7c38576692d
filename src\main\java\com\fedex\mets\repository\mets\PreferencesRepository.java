package com.fedex.mets.repository.mets;

import com.fedex.mets.entity.mets.UserPreference;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface PreferencesRepository extends JpaRepository<UserPreference, Integer> {

    @Query(value="SELECT preference_json FROM user_preference where emp_nbr = :id",nativeQuery = true)
    public String getPreferences(@Param("id") int empId);
    @Query(value = "SELECT JSON_UNQUOTE(JSON_EXTRACT(CAST(pref AS CHAR(10000) CHARACTER SET utf8), '$')) AS prefData FROM preferences WHERE emp_nbr = :id", nativeQuery = true)
    public String findByEmpId(@Param("id") int empId);
}
