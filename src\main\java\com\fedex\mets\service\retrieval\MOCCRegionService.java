package com.fedex.mets.service.retrieval;

import com.fedex.mets.repository.rampview.RvRegionsRepository;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
public class MOCCRegionService {
    private static final Logger logger = LoggerFactory.getLogger(MOCCRegionService.class);
    @Autowired
    private RvRegionsRepository rvRegionsRepository;

    public List<String> getMOCCRegions() throws Exception {
        List<String> regions = new ArrayList<>();
        try {
            regions = rvRegionsRepository.getRegions();
        } catch (Exception e) {
            logger.error("Problem finding the Regions list", e);
            throw new Exception("Problem finding the Regions list "
                    + e.getMessage());
        }
        logger.info("Number of MOCC Regions loaded: " + regions.size());
        return regions;
    }

    public List<String> getMOCCRegionStations(String regionDescription) throws Exception {
        List<String> stations = new ArrayList<>();
        try {
            stations = rvRegionsRepository.getRegionStations(regionDescription);
        } catch (Exception e) {
            logger.error("Problem finding the Region Stations list", e);
            throw new Exception("Problem finding the Regions Stations list "
                    + e.getMessage());
        }
        logger.info("Number of Stations Regions loaded: " + stations.size());
        return stations;
    }

}
