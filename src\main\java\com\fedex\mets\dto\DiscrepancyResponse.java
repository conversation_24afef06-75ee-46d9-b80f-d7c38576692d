package com.fedex.mets.dto;


import com.fedex.mets.data.EventDiscrepancyList;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@Schema(description = "Response containing discrepancy details")
public class DiscrepancyResponse {

    @Schema(description = "List of open Discrepancies")
    private List<EventDiscrepancyList> discrepancyList;

    @Schema(description = "List of linked Discrepancies")
    private List<EventDiscrepancyList> linkedDiscrepancyList;
    public DiscrepancyResponse() {
        this.discrepancyList = new ArrayList<>();
        this.linkedDiscrepancyList = new ArrayList<>();
    }
}