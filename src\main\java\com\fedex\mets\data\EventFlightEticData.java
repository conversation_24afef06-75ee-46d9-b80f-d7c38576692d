package com.fedex.mets.data;

import lombok.*;

import java.io.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
public class EventFlightEticData implements Serializable{
	
	public int		eventId;
	public String   initialEtic;
	public int		eticNumber;
	public String	pastDue;
	public String	flightNumber;
	public String	flightDate;
	public String	flightLegNumber;
	public String	acn;
	public String	destination;
	public String   origin;
	public String	flightStatus;
	public String	flightType;
	public String	scheduledDeparture;
	public String	actualDeparture;
	public String	totalDelay;
	public String	delayCodes;
	public String	fltIn;
	public String	arrival;
	public String	fltOut;
	public String	departure;
	public String	totalGroundTime;
	public String	flightFlag;
	
	//new variables added to determine if the flight info (or) eticNumber modified - 08/14/2003
	public boolean	eticNumberModified = false;
	public boolean	flightInfoModified = true;
}