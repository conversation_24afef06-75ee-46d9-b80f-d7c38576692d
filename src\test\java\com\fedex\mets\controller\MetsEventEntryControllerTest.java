package com.fedex.mets.controller;

import com.fedex.mets.data.WizardEventData;
import com.fedex.mets.dto.MetsResponse;
import com.fedex.mets.service.addEvent.EventEntryService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

public class MetsEventEntryControllerTest {

    @Mock
    private EventEntryService eventEntryService;

    @InjectMocks
    private MetsEventEntryController metsEventEntryController;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testAddEvent_Success() throws Exception {
        WizardEventData request = new WizardEventData();
        HashMap<String, Object> responseMap = new HashMap<>();
        responseMap.put("key", "value");

        when(eventEntryService.addEvent(any(WizardEventData.class))).thenReturn(responseMap);

        ResponseEntity<MetsResponse> response = metsEventEntryController.addEvent(request);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(responseMap, response.getBody().getData());
    }

    @Test
    public void testAddEvent_InvalidInput() throws Exception {
        WizardEventData request = new WizardEventData();

        doThrow(new IllegalArgumentException("Invalid input")).when(eventEntryService).addEvent(any(WizardEventData.class));

        ResponseEntity<MetsResponse> response = metsEventEntryController.addEvent(request);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    }

    @Test
    public void testAddEvent_InternalServerError() throws Exception {
        WizardEventData request = new WizardEventData();

        doThrow(new RuntimeException("Internal server error")).when(eventEntryService).addEvent(any(WizardEventData.class));

        ResponseEntity<MetsResponse> response = metsEventEntryController.addEvent(request);

        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
    }
}
