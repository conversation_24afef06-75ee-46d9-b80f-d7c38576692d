
package com.fedex.mets.wsdl.discrepancy;

import java.math.BigDecimal;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for PidType complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="PidType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="wisePidDetailOid" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="problemId" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="problemDesc" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="fleetCd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="avgDelay" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="shortTermDays" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="shortTermAlertThld" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="shortTermWarnThld" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="longTermDays" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="longTermAlertThld" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="longTermWarnThld" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="criticalFlg" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ppaFlg" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="rvwReqFlg" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PidType", propOrder = {
    "wisePidDetailOid",
    "problemId",
    "problemDesc",
    "fleetCd",
    "avgDelay",
    "shortTermDays",
    "shortTermAlertThld",
    "shortTermWarnThld",
    "longTermDays",
    "longTermAlertThld",
    "longTermWarnThld",
    "criticalFlg",
    "ppaFlg",
    "rvwReqFlg"
})
public class PidType {

    @XmlElement(required = true)
    protected BigDecimal wisePidDetailOid;
    @XmlElement(required = true)
    protected BigDecimal problemId;
    @XmlElement(required = true)
    protected String problemDesc;
    @XmlElement(required = true)
    protected String fleetCd;
    protected BigDecimal avgDelay;
    protected BigDecimal shortTermDays;
    protected BigDecimal shortTermAlertThld;
    protected BigDecimal shortTermWarnThld;
    protected BigDecimal longTermDays;
    protected BigDecimal longTermAlertThld;
    protected BigDecimal longTermWarnThld;
    protected String criticalFlg;
    protected String ppaFlg;
    protected String rvwReqFlg;

    /**
     * Gets the value of the wisePidDetailOid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getWisePidDetailOid() {
        return wisePidDetailOid;
    }

    /**
     * Sets the value of the wisePidDetailOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setWisePidDetailOid(BigDecimal value) {
        this.wisePidDetailOid = value;
    }

    /**
     * Gets the value of the problemId property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getProblemId() {
        return problemId;
    }

    /**
     * Sets the value of the problemId property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setProblemId(BigDecimal value) {
        this.problemId = value;
    }

    /**
     * Gets the value of the problemDesc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getProblemDesc() {
        return problemDesc;
    }

    /**
     * Sets the value of the problemDesc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setProblemDesc(String value) {
        this.problemDesc = value;
    }

    /**
     * Gets the value of the fleetCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFleetCd() {
        return fleetCd;
    }

    /**
     * Sets the value of the fleetCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFleetCd(String value) {
        this.fleetCd = value;
    }

    /**
     * Gets the value of the avgDelay property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAvgDelay() {
        return avgDelay;
    }

    /**
     * Sets the value of the avgDelay property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAvgDelay(BigDecimal value) {
        this.avgDelay = value;
    }

    /**
     * Gets the value of the shortTermDays property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getShortTermDays() {
        return shortTermDays;
    }

    /**
     * Sets the value of the shortTermDays property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setShortTermDays(BigDecimal value) {
        this.shortTermDays = value;
    }

    /**
     * Gets the value of the shortTermAlertThld property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getShortTermAlertThld() {
        return shortTermAlertThld;
    }

    /**
     * Sets the value of the shortTermAlertThld property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setShortTermAlertThld(BigDecimal value) {
        this.shortTermAlertThld = value;
    }

    /**
     * Gets the value of the shortTermWarnThld property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getShortTermWarnThld() {
        return shortTermWarnThld;
    }

    /**
     * Sets the value of the shortTermWarnThld property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setShortTermWarnThld(BigDecimal value) {
        this.shortTermWarnThld = value;
    }

    /**
     * Gets the value of the longTermDays property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getLongTermDays() {
        return longTermDays;
    }

    /**
     * Sets the value of the longTermDays property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setLongTermDays(BigDecimal value) {
        this.longTermDays = value;
    }

    /**
     * Gets the value of the longTermAlertThld property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getLongTermAlertThld() {
        return longTermAlertThld;
    }

    /**
     * Sets the value of the longTermAlertThld property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setLongTermAlertThld(BigDecimal value) {
        this.longTermAlertThld = value;
    }

    /**
     * Gets the value of the longTermWarnThld property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getLongTermWarnThld() {
        return longTermWarnThld;
    }

    /**
     * Sets the value of the longTermWarnThld property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setLongTermWarnThld(BigDecimal value) {
        this.longTermWarnThld = value;
    }

    /**
     * Gets the value of the criticalFlg property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCriticalFlg() {
        return criticalFlg;
    }

    /**
     * Sets the value of the criticalFlg property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCriticalFlg(String value) {
        this.criticalFlg = value;
    }

    /**
     * Gets the value of the ppaFlg property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPpaFlg() {
        return ppaFlg;
    }

    /**
     * Sets the value of the ppaFlg property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPpaFlg(String value) {
        this.ppaFlg = value;
    }

    /**
     * Gets the value of the rvwReqFlg property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRvwReqFlg() {
        return rvwReqFlg;
    }

    /**
     * Sets the value of the rvwReqFlg property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRvwReqFlg(String value) {
        this.rvwReqFlg = value;
    }

}
