
package com.fedex.mets.wsdl.discrepancy;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for StandardsReqmt complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="StandardsReqmt">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="modified" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="oid" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="totalSpanTimeOfTask" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="ataNbr" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="priority" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="ataSubType" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="companyPartNumber" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="description" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="modTypeNumber" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="notes" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="oilInterimFlg" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="parallelMaintenanceFlg" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="exclusiveTaskFlg" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="standardParts" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="skillCode" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="numberOfEmployeesRequired" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="timeSpanOfTask" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="overrideSpan" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="overrideTimeSpanOfTask" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="planningParts" type="{http://www.fedex.com/airops/schemas/Planning.xsd}PlanningPartType" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="planningResources" type="{http://www.fedex.com/airops/schemas/Planning.xsd}PlanningResourceType" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "StandardsReqmt", namespace = "http://www.fedex.com/airops/schemas/Planning.xsd", propOrder = {
    "modified",
    "oid",
    "totalSpanTimeOfTask",
    "ataNbr",
    "priority",
    "ataSubType",
    "companyPartNumber",
    "description",
    "modTypeNumber",
    "notes",
    "oilInterimFlg",
    "parallelMaintenanceFlg",
    "exclusiveTaskFlg",
    "standardParts",
    "skillCode",
    "numberOfEmployeesRequired",
    "timeSpanOfTask",
    "overrideSpan",
    "overrideTimeSpanOfTask",
    "planningParts",
    "planningResources"
})
@XmlSeeAlso({
    WorkRelseItem.class
})
public class StandardsReqmt {

    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd")
    protected boolean modified;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd", required = true)
    protected BigDecimal oid;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd", required = true)
    protected BigDecimal totalSpanTimeOfTask;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd", required = true)
    protected BigDecimal ataNbr;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd", required = true)
    protected BigDecimal priority;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd", required = true)
    protected String ataSubType;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd", required = true)
    protected String companyPartNumber;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd", required = true)
    protected String description;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd", required = true)
    protected String modTypeNumber;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd", required = true)
    protected String notes;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd")
    protected boolean oilInterimFlg;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd")
    protected boolean parallelMaintenanceFlg;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd")
    protected boolean exclusiveTaskFlg;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd")
    protected boolean standardParts;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd", required = true)
    protected String skillCode;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd", required = true)
    protected BigDecimal numberOfEmployeesRequired;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd", required = true)
    protected BigDecimal timeSpanOfTask;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd")
    protected boolean overrideSpan;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd", required = true)
    protected BigDecimal overrideTimeSpanOfTask;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd")
    protected List<PlanningPartType> planningParts;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd")
    protected List<PlanningResourceType> planningResources;

    /**
     * Gets the value of the modified property.
     * 
     */
    public boolean isModified() {
        return modified;
    }

    /**
     * Sets the value of the modified property.
     * 
     */
    public void setModified(boolean value) {
        this.modified = value;
    }

    /**
     * Gets the value of the oid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getOid() {
        return oid;
    }

    /**
     * Sets the value of the oid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setOid(BigDecimal value) {
        this.oid = value;
    }

    /**
     * Gets the value of the totalSpanTimeOfTask property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTotalSpanTimeOfTask() {
        return totalSpanTimeOfTask;
    }

    /**
     * Sets the value of the totalSpanTimeOfTask property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTotalSpanTimeOfTask(BigDecimal value) {
        this.totalSpanTimeOfTask = value;
    }

    /**
     * Gets the value of the ataNbr property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAtaNbr() {
        return ataNbr;
    }

    /**
     * Sets the value of the ataNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAtaNbr(BigDecimal value) {
        this.ataNbr = value;
    }

    /**
     * Gets the value of the priority property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getPriority() {
        return priority;
    }

    /**
     * Sets the value of the priority property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setPriority(BigDecimal value) {
        this.priority = value;
    }

    /**
     * Gets the value of the ataSubType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAtaSubType() {
        return ataSubType;
    }

    /**
     * Sets the value of the ataSubType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAtaSubType(String value) {
        this.ataSubType = value;
    }

    /**
     * Gets the value of the companyPartNumber property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCompanyPartNumber() {
        return companyPartNumber;
    }

    /**
     * Sets the value of the companyPartNumber property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCompanyPartNumber(String value) {
        this.companyPartNumber = value;
    }

    /**
     * Gets the value of the description property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDescription() {
        return description;
    }

    /**
     * Sets the value of the description property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDescription(String value) {
        this.description = value;
    }

    /**
     * Gets the value of the modTypeNumber property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getModTypeNumber() {
        return modTypeNumber;
    }

    /**
     * Sets the value of the modTypeNumber property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setModTypeNumber(String value) {
        this.modTypeNumber = value;
    }

    /**
     * Gets the value of the notes property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNotes() {
        return notes;
    }

    /**
     * Sets the value of the notes property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNotes(String value) {
        this.notes = value;
    }

    /**
     * Gets the value of the oilInterimFlg property.
     * 
     */
    public boolean isOilInterimFlg() {
        return oilInterimFlg;
    }

    /**
     * Sets the value of the oilInterimFlg property.
     * 
     */
    public void setOilInterimFlg(boolean value) {
        this.oilInterimFlg = value;
    }

    /**
     * Gets the value of the parallelMaintenanceFlg property.
     * 
     */
    public boolean isParallelMaintenanceFlg() {
        return parallelMaintenanceFlg;
    }

    /**
     * Sets the value of the parallelMaintenanceFlg property.
     * 
     */
    public void setParallelMaintenanceFlg(boolean value) {
        this.parallelMaintenanceFlg = value;
    }

    /**
     * Gets the value of the exclusiveTaskFlg property.
     * 
     */
    public boolean isExclusiveTaskFlg() {
        return exclusiveTaskFlg;
    }

    /**
     * Sets the value of the exclusiveTaskFlg property.
     * 
     */
    public void setExclusiveTaskFlg(boolean value) {
        this.exclusiveTaskFlg = value;
    }

    /**
     * Gets the value of the standardParts property.
     * 
     */
    public boolean isStandardParts() {
        return standardParts;
    }

    /**
     * Sets the value of the standardParts property.
     * 
     */
    public void setStandardParts(boolean value) {
        this.standardParts = value;
    }

    /**
     * Gets the value of the skillCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSkillCode() {
        return skillCode;
    }

    /**
     * Sets the value of the skillCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSkillCode(String value) {
        this.skillCode = value;
    }

    /**
     * Gets the value of the numberOfEmployeesRequired property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getNumberOfEmployeesRequired() {
        return numberOfEmployeesRequired;
    }

    /**
     * Sets the value of the numberOfEmployeesRequired property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setNumberOfEmployeesRequired(BigDecimal value) {
        this.numberOfEmployeesRequired = value;
    }

    /**
     * Gets the value of the timeSpanOfTask property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTimeSpanOfTask() {
        return timeSpanOfTask;
    }

    /**
     * Sets the value of the timeSpanOfTask property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTimeSpanOfTask(BigDecimal value) {
        this.timeSpanOfTask = value;
    }

    /**
     * Gets the value of the overrideSpan property.
     * 
     */
    public boolean isOverrideSpan() {
        return overrideSpan;
    }

    /**
     * Sets the value of the overrideSpan property.
     * 
     */
    public void setOverrideSpan(boolean value) {
        this.overrideSpan = value;
    }

    /**
     * Gets the value of the overrideTimeSpanOfTask property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getOverrideTimeSpanOfTask() {
        return overrideTimeSpanOfTask;
    }

    /**
     * Sets the value of the overrideTimeSpanOfTask property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setOverrideTimeSpanOfTask(BigDecimal value) {
        this.overrideTimeSpanOfTask = value;
    }

    /**
     * Gets the value of the planningParts property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the planningParts property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getPlanningParts().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PlanningPartType }
     * 
     * 
     */
    public List<PlanningPartType> getPlanningParts() {
        if (planningParts == null) {
            planningParts = new ArrayList<PlanningPartType>();
        }
        return this.planningParts;
    }

    /**
     * Gets the value of the planningResources property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the planningResources property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getPlanningResources().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PlanningResourceType }
     * 
     * 
     */
    public List<PlanningResourceType> getPlanningResources() {
        if (planningResources == null) {
            planningResources = new ArrayList<PlanningResourceType>();
        }
        return this.planningResources;
    }

}
