package com.fedex.mets.config;

import com.fedex.mets.wsdl.msn.shortagenotice.*;
import jakarta.xml.soap.MessageFactory;
import jakarta.xml.soap.SOAPConstants;
import jakarta.xml.soap.SOAPException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.ws.client.core.WebServiceTemplate;
import org.springframework.ws.client.support.interceptor.ClientInterceptor;
import org.springframework.ws.soap.saaj.SaajSoapMessageFactory;

@Configuration
public class MaxiSoapClientConfig {

    @Autowired
    private Environment env;

    @Bean
    public Jaxb2Marshaller app2marshaller() {
        Jaxb2Marshaller marshaller = new Jaxb2Marshaller();

        marshaller.setClassesToBeBound(
                GetShortageNoticeListRequest.class,
                GetShortageNoticeListResponse.class,
                GetMSNDetailsRequest.class,
                GetMSNDetailsResponse.class,
                GetMsnShippingInfoRequest.class,
                GetMsnShippingInfoResponse.class
        );
        return marshaller;
    }

    @Bean
    public WebServiceTemplate app2webServiceTemplate() throws SOAPException {
        WebServiceTemplate template = new WebServiceTemplate();
        template.setMarshaller(app2marshaller());
        template.setUnmarshaller(app2marshaller());
        template.setCheckConnectionForFault(true);
        MessageFactory msgFactory = MessageFactory.newInstance(SOAPConstants.SOAP_1_1_PROTOCOL);
        SaajSoapMessageFactory newSoapMessageFactory = new SaajSoapMessageFactory(msgFactory);

        template.setMessageFactory(app2messageFactory());

        template.setInterceptors(new ClientInterceptor[]{new SoapLoggingInterceptor()});
        template.setMessageFactory(newSoapMessageFactory);
        String URL=env.getProperty("maxi-service-url");
        template.setDefaultUri(URL);
        return template;
    }


    @Bean
    public SaajSoapMessageFactory app2messageFactory() {
        SaajSoapMessageFactory messageFactory = new SaajSoapMessageFactory();
        messageFactory.afterPropertiesSet();
        return messageFactory;
    }

}
