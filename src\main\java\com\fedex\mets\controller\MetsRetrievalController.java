package com.fedex.mets.controller;

import com.fedex.mets.config.RequirePermission;
import com.fedex.mets.dao.DscrpMaintTxtReq;
import com.fedex.mets.dao.ManagerDetails;
import com.fedex.mets.data.*;
import com.fedex.mets.dto.*;
import com.fedex.mets.entity.ldap.User;
import com.fedex.mets.entity.mets.EventTfNotes;
import com.fedex.mets.exceptionHandler.InternalServerErrorException;
import com.fedex.mets.service.retrieval.*;
import com.fedex.mets.util.IServerConstants;
import com.fedex.mets.wsdl.msn.shortagenotice.GetMSNDetailsResponse;
import com.fedex.mets.wsdl.msn.shortagenotice.GetMsnShippingInfoResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.concurrent.CompletableFuture;

//@CrossOrigin(origins = "*") // Allows all origins
@RestController
@RequestMapping("/api/mets/retrieval")
@Tag(name = "Mets Retrieval", description = "Endpoints for managing Mets Retrieval")
public class MetsRetrievalController {
    private static final Logger logger = LoggerFactory.getLogger(MetsRetrievalController.class);
    private static final String ACN_CACHE_DETAIL_KEY = "ACN_CACHE_DETAIL";
    @Autowired
    private EventListViewService eventListViewService;
    @Autowired
    private ReportingCategoriesService rcService;
    @Autowired
    private MOCCRegionService moccRegionService;
    @Autowired
    private EventListDetailViewService eventListDetailViewService;
    @Autowired
    private EventFlightEticInfoService eventFlightEticInfoService;
    @Autowired
    private NIWTimersService niwTimerService;
    @Autowired
    private DiscrepancyDetailService discrepancyDetailService;
    @Autowired
    private EventDiscrepanciesService eventDiscrepanciesService;
    @Autowired
    private MsnDetailService msnDetailService;
    @Autowired
    private FlightLegDetailService flightLegDetailService;

    @Autowired
    private TfNotesEmailService tubFlileEmailServive;

    @Autowired
    private AcnCacheService acnCacheService;
    @Autowired
    private UserService userService;

    @Autowired
    private WarningDaysService warningDaysService;

    @Autowired
    private EmrAndFobStatusService emrAndFobStatusService;

    @Operation(summary = "Fetch All the Managers by station ID", description = "Retrieve all the Managers to a given Station ID from the system.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved managers", content = @Content(mediaType = "application/json", schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = ManagerDetails.class
            ))),
            @ApiResponse(responseCode = "400", description = "Invalid input provided", content = @Content),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @GetMapping(path = "/managers/{station}")
    public ResponseEntity<List<ManagerDetails>> getManagers(@PathVariable String station) throws Exception {
        try {
            List<ManagerDetails> managers = eventListViewService.getManagerDetail(station);
            if (managers == null && managers.isEmpty()) {
                return ResponseEntity.ok(Collections.emptyList());
            }
            return ResponseEntity.ok(managers);
        } catch (IllegalArgumentException e) {
            logger.error("Invalid input for Manager Details station value: {}. Error: {}", station, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error fetching Manager Details for value: {}", station, e);
            return ResponseEntity.status(500).build();
        }

    }

    @Operation(summary = "Fetch all regions", description = "Retrieve all regions from the system")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved list of regions"),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @GetMapping("/regions/all")
    public ResponseEntity<List<String>> getAllregions() {
        try {
            List<String> regions = moccRegionService.getMOCCRegions();
            if (regions == null && regions.isEmpty()) {
                return ResponseEntity.ok(Collections.emptyList());
            }
            return ResponseEntity.ok(regions);
        } catch (Exception e) {
            logger.error("Error fetching Regions", e);
            return ResponseEntity.status(500).build();
        }
    }

    @Operation(summary = "Fetch All Stations by Region", description = "Retrieve all stations for a given region from the system")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved list of stations"),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @GetMapping("/stations/{region}")
    public ResponseEntity<List<String>> getStationsByRegion(@PathVariable String region) {
        try {
            List<String> stations = moccRegionService.getMOCCRegionStations(region);
            if (stations == null && stations.isEmpty()) {
                return ResponseEntity.ok(Collections.emptyList());
            }
            return ResponseEntity.ok(stations);
        } catch (IllegalArgumentException e) {
            logger.error("Invalid input for region value: {}. Error: {}", region, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error fetching stations", e);
            return ResponseEntity.status(500).build();
        }
    }

    @Operation(summary = "Fetch Detail View", description = "Retrieve all the event details for a given Aircraft from the system")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved all the event details of Aircraft", content = @Content(mediaType = "application/json", schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = DetailViewData.class
            ))),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @GetMapping("/detail-view/{acn}")
    public ResponseEntity<List<DetailViewData>> getEventDetailView(@PathVariable String acn, @RequestParam String userId) {
        try {
            List<DetailViewData> detailView = eventListDetailViewService.getEventDetailView(acn, userId);
            if (detailView == null && detailView.isEmpty()) {
                return ResponseEntity.ok(Collections.emptyList());
            }
            return ResponseEntity.ok(detailView);
        } catch (IllegalArgumentException e) {
            logger.error("Invalid input for Event Details Acn value: {}. Error: {}", acn, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error fetching event Details", e);
            return ResponseEntity.status(500).build();
        }
    }

    @Operation(summary = "Fetch flight Leg Details", description = "Retrieve all flight details categorized by current, upcoming, and past flights for a given Aircraft from the system")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved all the flight let details of Aircraft", content = @Content(mediaType = "application/json", schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = FlightDetailResponse.class
            ))),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @GetMapping("/flightLeg/{acn}")
    public ResponseEntity<FlightDetailResponse> getFlightLegDetail(@PathVariable String acn, @RequestParam String userId) {
        try {
            FlightDetailResponse flightLegs = flightLegDetailService.getFlightLegDetail(acn, userId);
            return ResponseEntity.ok(flightLegs);
        } catch (IllegalArgumentException e) {
            logger.error("Invalid input for Flight Leg Details: Acn value: {}. Error: {}", acn, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error fetching Flight Details", e);
            return ResponseEntity.status(500).build();
        }
    }

    @Operation(summary = "Fetch Tubfile Notes", description = "Retrieve all the tf notes details for a given eventId from the system")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved all the tub file notes of eventId", content = @Content(mediaType = "application/json", schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = EventTfNotes.class
            ))),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @GetMapping("/tfNotes/{eventId}")
    public ResponseEntity<List<EventTfNotes>> getTfNotes(@PathVariable String eventId) {
        try {
            List<EventTfNotes> tfNotes = eventListDetailViewService.getTFNotes(eventId);
            if (tfNotes == null && tfNotes.isEmpty()) {
                return ResponseEntity.ok(Collections.emptyList());
            }
            return ResponseEntity.ok(tfNotes);
        } catch (IllegalArgumentException e) {
            logger.error("Invalid input for Tf Notes Details: Event Id value: {}. Error: {}", eventId, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error fetching tubFileNotes", e);
            return ResponseEntity.status(500).build();
        }
    }

    @Operation(summary = "Fetch Flight Etic Info", description = "Retrieve Flight Etic details for a given eventId from the system")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved Flight Etic Info of eventId", content = @Content(mediaType = "application/json", schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = EventFlightEticData.class
            ))),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @GetMapping("/fltEtic/{eventId}")
    public ResponseEntity<EventFlightEticData> getFlightEticDetail(@PathVariable int eventId) {
        try {
            EventFlightEticData fltEtic = eventFlightEticInfoService.getFlightEticDetail(eventId);
            return ResponseEntity.ok(fltEtic);
        } catch (IllegalArgumentException e) {
            logger.error("Invalid input for Flt Etic Info: Event Id value: {}. Error: {}", eventId, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error fetching Flt Etic Info", e);
            return ResponseEntity.status(500).build();
        }
    }

    @Operation(summary = "Fetch user profile from ldap", description = "Retrieve profile details for a given userId from the ldap system")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved an userprofile for given userId", content = @Content(mediaType = "application/json", schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = User.class
            ))),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @GetMapping("/users/{uid}")
    public ResponseEntity<User> getUser(@PathVariable String uid) {
        try {
            Optional<User> user = userService.getUserByUid(uid);
            return ResponseEntity.ok(user.get());
        } catch (IllegalArgumentException e) {
            logger.error("Invalid input for get Email Address: user Id value: {}. Error: {}", uid, e.getMessage());
            return ResponseEntity.badRequest().build();
        }
        catch (Exception e) {
            logger.error("Error fetching User Info", e);
            return ResponseEntity.status(500).build();
        }
    }

    @Operation(summary = "Send an email with the TfNotes", description = "Send an email with the tf notes for a given eventId from the system")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully sent an email of eventId"),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @PostMapping("/sendEmail")
    public ResponseEntity<Boolean> sendEmail(@RequestBody EventEmailData emailData) {
        try {
            tubFlileEmailServive.sendMail(emailData);
            return ResponseEntity.ok(true);
        } catch (IllegalArgumentException e) {
            logger.error("Invalid input for sent Email: email Data: {}. Error: {}", emailData, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error sending Email", e);
            return ResponseEntity.status(500).build();
        }
    }

    @Operation(summary = "Fetch shortage notice details", description = "Retrieve shortage notice details for a given aircraft from the mach server")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved Shortage Notice Info of Aircraft", content = @Content(mediaType = "application/json", schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = ShortageNoticeData.class
            ))),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @GetMapping("/shortagenotice/{acn}")
    public ResponseEntity<List<ShortageNoticeData>> getShortageNotice(@PathVariable String acn, @RequestParam String userId) {
        try {
            List<ShortageNoticeData> shortageNoticeData = msnDetailService.getShortageNoticeDetails(acn, userId);
            return ResponseEntity.ok(shortageNoticeData);
        } catch (IllegalArgumentException e) {
            logger.error("Invalid input for shortageNoticeData: Acn value: {}. Error: {}", acn, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error fetching shortage notice Info", e);
            return ResponseEntity.status(500).build();
        }
    }

    @Operation(summary = "Fetch MSN Shipping Details", description = "Retrieve MSN Shipping Info for a given msn from the mach server")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved MSN Shipping Info", content = @Content(mediaType = "application/json", schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = GetMsnShippingInfoResponse.class
            ))),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @GetMapping("/msnShipping/{msn}")
    public ResponseEntity<GetMsnShippingInfoResponse> getMSNShippingInfo(@PathVariable String msn, @RequestParam String userId) {
        try {
            GetMsnShippingInfoResponse msnShippingDetails = msnDetailService.getMsnShippingDetails(msn, userId);
            return ResponseEntity.ok(msnShippingDetails);
        } catch (IllegalArgumentException e) {
            logger.error("Invalid input for msnShippingDetails: msn value: {}. Error: {}", msn, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error fetching msn Shipping Info", e);
            return ResponseEntity.status(500).build();
        }
    }

    @Operation(summary = "Fetch MSN Details", description = "Retrieve MSN Info for a given msn from the mach server")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved MSN  Info", content = @Content(mediaType = "application/json", schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = GetMSNDetailsResponse.class
            ))),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @GetMapping("/msnDetail/{msn}")
    public ResponseEntity<GetMSNDetailsResponse> getMSNDetail(@PathVariable String msn, @RequestParam String userId) {
        try {
            GetMSNDetailsResponse msnDetail = msnDetailService.getMSNDetails(msn, userId);
            return ResponseEntity.ok(msnDetail);
        } catch (IllegalArgumentException e) {
            logger.error("Invalid input for msn Detail: msn value: {}. Error: {}", msn, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error fetching msn Shipping Info", e);
            return ResponseEntity.status(500).build();
        }
    }

    @Operation(summary = "Fetch Event List View", description = "Retrieve all the active event list from the system")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved all the active event list", content = @Content(mediaType = "application/json", schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = ListViewData.class
            ))),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @GetMapping("/list-view")
//    @RequirePermission(transaction = "METS", action = 'B')
    public ResponseEntity<List<ListViewData>> getListDetailView() {
        try {
            List<ListViewData> listView = eventListViewService.getEventList();
            if (listView == null && listView.isEmpty()) {
                return ResponseEntity.ok(Collections.emptyList());
            }
            return ResponseEntity.ok(listView);
        } catch (Exception e) {
            logger.error("Error fetching event Details", e);
            return ResponseEntity.status(500).build();
        }
    }


    @Operation(summary = "Fetch Discrepancy Detail View", description = "Retrieve Detail view of Discrepancy for a given discrepancy number from the mach server")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved Detail Discrepancy"),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @GetMapping("/discrepancy-detail/{acn}/{ata}/{discNumber}")
    public ResponseEntity<List<String>> getDiscrepancyDetail(@PathVariable String acn, @PathVariable String ata, @PathVariable String discNumber, @RequestParam String userId) {
        try {
            List<String> discDetail = discrepancyDetailService.getDiscrepancyDetail(acn, ata, discNumber, userId);
            return ResponseEntity.ok(discDetail);
        } catch (IllegalArgumentException e) {
            logger.error("Invalid input for discrepancy detail: acn value: {}, ata value: {}, discNumber value: {}, userId value: {}. Error: {}",
                    acn, ata, discNumber, userId, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error fetching detail discrepancy Info", e);
            return ResponseEntity.status(500).build();
        }
    }

    @Operation(summary = "Fetch Open Discrepancies by EventId/Acn", description = "Retrieve all the discrepancies for a given eventId/Acn from the mach server")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved All Open Discrepancies", content = @Content(mediaType = "application/json", schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = DiscrepancyResponse.class
            ))),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @GetMapping("/open-discrepancies")
    public ResponseEntity<DiscrepancyResponse> getOpenDiscrepancies(@RequestParam(required = false) String acn, @RequestParam(required = false) String eventId, @RequestParam String userId) {
        try {
            DiscrepancyResponse response = new DiscrepancyResponse();
            if (eventId != null && userId != null) {
                int eventIdInteger = Integer.parseInt(eventId);
                if (eventIdInteger > 0) {
                    logger.info("....calling the getOpenDiscrepancies for EventId:" + eventId);
                    response = eventDiscrepanciesService.getOpenDiscrepancies(
                            eventId,
                            userId);
                } else {
                    logger.info(
                            "Event Id passed to the Server should be greater than 0.");
                }
            } else if (acn != null && userId != null && !acn.isEmpty() && !userId.isEmpty()) {
                logger.info("....calling the getDiscrepancies for ACN:" + acn);
                response = eventDiscrepanciesService.getAllOpenDsicrepancy(acn, userId);
            }
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            logger.error("Invalid input for open discrepancy details: acn value: {}, eventId value: {},userId value: {}. Error: {}",
                    acn, eventId, userId, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error fetching open discrepancy Info", e);
            return ResponseEntity.status(500).build();
        }
    }

    @Operation(summary = "Fetch Reporting Categories by eventId/eventType", description = "Retrieve all the reporting categories for a given eventId/eventType from the mets system")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved All Reporting Categories", content = @Content(mediaType = "application/json", schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = ReportCatgResponse.class
            ))),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @GetMapping("/rpt-catgories")
    public ResponseEntity<ReportCatgResponse> getRptCategories(@RequestParam(required = false) String eventId, @RequestParam(required = false) String eventType) {
        try {
            //TODO
            String accessLevel = "80";
            ReportCatgResponse response = new ReportCatgResponse();
            if (eventId != null && !eventId.isEmpty()) {
                logger.info("....calling the getReportCategories for the EventId:" + eventId);
                response = rcService.getReportCategories(eventId, accessLevel);
            } else if (eventType != null && !eventType.isEmpty()) {
                logger.info("....calling the getReportCategories for the EventType:" + eventType);
                response = rcService.getReportingCategoriesByEventType(eventType, accessLevel);
            } else {
                logger.info(
                        "One of the required parameters is not passed to the Server.");
            }
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            logger.error("Invalid input for Reporting Categories: eventId value: {}, eventType value: {}. Error: {}",
                    eventId, eventType, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error fetching reporting category Info", e);
            return ResponseEntity.status(500).build();
        }
    }


    @Operation(summary = "Fetch NIW Timers", description = "Retrieve all the not in work timer details for a given eventId or eventId & timerId")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved All NIW Timers for given eventId."),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @GetMapping("/niw-timers")
    public ResponseEntity<NiwTimersResponse> getNIWTimers(@RequestParam(required = false) String eventId, @RequestParam(required = false) String timerId) {
        try {
            NiwTimersResponse response = new NiwTimersResponse();
            if (!eventId.isEmpty() && eventId != null) {
                logger.info("....calling the getNIWTimers for the EventId:" + eventId);
                response = niwTimerService.getEventNIWTimers(eventId);
            } else {
                logger.info(
                        "One of the required parameters is not passed to the Server.");
            }
            if (eventId != null && timerId != null && !eventId.isEmpty() && !timerId.isEmpty()) {
                logger.info("....calling the getNIWTimerDetails for the EventId:" + eventId + " and TimerId:" + timerId);
                response = niwTimerService.getNIWTimerDetail(eventId, timerId);
            } else {
                logger.info(IServerConstants.ERROR,
                        "One of the required parameters is not passed to the Server.");
            }
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            logger.error("Invalid input for NIW Timers: eventId value: {}, timerId value: {}. Error: {}",
                    eventId, timerId, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error fetching niw timers Info", e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * Get the ACN cache data from the JSON file
     *
     * @return ResponseEntity containing the ACN cache data
     */
    @Operation(summary = "Get ACN cache data", description = "Retrieves the ACN cache data from the JSON file")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved ACN cache data",
                    content = @Content(schema = @Schema(implementation = HashMap.class))),
            @ApiResponse(responseCode = "404", description = "ACN cache data not found"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/acn-cache")
    public ResponseEntity<?> getAcnCacheData() {
        try {
            HashMap<String, List<?>> cacheData = acnCacheService.getAcnCacheFromFile();

            if (cacheData == null) {
                logger.warn("ACN cache data not found in JSON file");
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body("ACN cache data not found");
            }

            List<?> acnCacheDetails = cacheData.get(ACN_CACHE_DETAIL_KEY);
            if (acnCacheDetails == null || acnCacheDetails.isEmpty()) {
                logger.warn("ACN cache details not found or empty");
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body("ACN cache details not found or empty");
            }

            logger.info("Successfully retrieved ACN cache data with {} entries", acnCacheDetails.size());
            return ResponseEntity.ok(cacheData);

        } catch (Exception e) {
            logger.error("Error retrieving ACN cache data: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error retrieving ACN cache data: " + e.getMessage());
        }
    }

    /**
     * Force update of the ACN cache data
     *
     * @return ResponseEntity containing the updated ACN cache data
     */
    @Operation(summary = "Force update ACN cache", description = "Forces an update of the ACN cache data")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully updated ACN cache data",
                    content = @Content(schema = @Schema(implementation = HashMap.class))),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/acn-cache/update")
    public ResponseEntity<?> updateAcnCacheData() {
        try {
            logger.info("Forcing update of ACN cache data");

            HashMap<String, List<?>> cacheDetails = acnCacheService.getAcnCacheDetail();

            boolean saved = acnCacheService.jsonFileUtil.saveToJsonFile(
                    AcnCacheService.ACN_CACHE_FILE_PATH, cacheDetails);

            if (!saved) {
                logger.warn("Failed to save ACN cache data to JSON file");
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body("Failed to save ACN cache data to JSON file");
            }

            List<?> acnCacheDetails = cacheDetails.get(ACN_CACHE_DETAIL_KEY);
            int size = acnCacheDetails != null ? acnCacheDetails.size() : 0;

            logger.info("Successfully updated ACN cache data with {} entries", size);
            return ResponseEntity.ok(cacheDetails);

        } catch (Exception e) {
            logger.error("Error updating ACN cache data: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error updating ACN cache data: " + e.getMessage());
        }
    }
    @Operation(summary = "Fetch serviceWarningDays details", description = "Retrieve ServiceWarningDays details for a given aircraft from the mach server")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved ServiceWarningDays Info of Aircraft", content = @Content(mediaType = "application/json", schema = @io.swagger.v3.oas.annotations.media.Schema(implementation = ServiceWarningDays.class
            ))),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @GetMapping("/getFlightChecks")
    public ResponseEntity<AircraftInfoChecks> getFlightChecks(@RequestParam("acn") String
                                                                      acn, @RequestParam("userId") String userId) {
        try {
            AircraftInfoChecks aircraftInfoChecks = new AircraftInfoChecks();

            CompletableFuture<ServiceWarningDays> serviceWarningDaysFuture = CompletableFuture.supplyAsync(() -> warningDaysService.getServiceWarningDays(acn, userId));
            CompletableFuture<Map<String, String>> flightStatusAndFobMap = CompletableFuture.supplyAsync(() -> emrAndFobStatusService.getFlightStatusAndFobMap(acn, userId));

            aircraftInfoChecks.setServiceWarningDays(serviceWarningDaysFuture.get());
            aircraftInfoChecks.setEmrStatus(flightStatusAndFobMap.get().get("FlightStatus"));
            aircraftInfoChecks.setFob(flightStatusAndFobMap.get().get("Fob"));
            aircraftInfoChecks.setStatusCode(flightStatusAndFobMap.get().get("StatusCode"));
            return ResponseEntity.ok(aircraftInfoChecks);
        } catch (IllegalArgumentException e) {
            logger.error("Invalid input for serviceWarningDays: Acn value: {}. Error: {}", acn, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error fetching serviceWarningDays Info", e);
            return ResponseEntity.status(500).build();
        }
    }

    @Operation(summary = "Fetch Discrepancy maintUpdtTxt", description = "Retrieve list of maintUpdtTxt using list of (acn, ata and discNumber)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully Discrepancy maintUpdtTxt"),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @PostMapping("/discrepancy-txt")
    public ResponseEntity<List<DscrpMaintTxtResponse>> getDiscrepancyDetail(@RequestBody List<DscrpMaintTxtReq> dscrpMaintTxtReqList, @RequestParam String userId) {
        String tokenId = "";
        List<DscrpMaintTxtResponse> dscrpMaintTxtResponseList = new ArrayList<>();
        try {
            for (DscrpMaintTxtReq dsc : dscrpMaintTxtReqList) {
                DscrpMaintTxtResponse dscrpMaintTxtResponse = new DscrpMaintTxtResponse();
                dscrpMaintTxtResponse.setAta(dsc.getAta());
                dscrpMaintTxtResponse.setDscrNumber(dsc.getDiscNumber());

                List<DscrpTxt> dscrpTxtList = discrepancyDetailService.getAllDiscrepancyUpdtTxts(userId, dsc.getAcn(), dsc.getAta(), dsc.getDiscNumber(), tokenId);
                dscrpMaintTxtResponse.setDscrpTxts(dscrpTxtList);
                dscrpMaintTxtResponseList.add(dscrpMaintTxtResponse);
            }
//            List<String> discDetail = discrepancyDetailService.getDiscrepancyDetail(acn, ata, discNumber, userId);
            return ResponseEntity.ok(dscrpMaintTxtResponseList);
        } catch (IllegalArgumentException e) {
//            logger.error("Invalid input for discrepancy detail: acn value: {}, ata value: {}, discNumber value: {}, userId value: {}. Error: {}",
//                    acn, ata, discNumber, userId, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error fetching detail discrepancy Info", e);
            return ResponseEntity.status(500).build();
        }
    }
}
