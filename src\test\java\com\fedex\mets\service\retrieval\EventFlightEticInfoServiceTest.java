package com.fedex.mets.service.retrieval;

import com.fedex.mets.dao.FlightEticView;
import com.fedex.mets.data.EventFlightEticData;
import com.fedex.mets.entity.mets.EventFlightDelays;
import com.fedex.mets.entity.mets.EventFlightInfo;
import com.fedex.mets.repository.mets.EventFlightDelaysRepository;
import com.fedex.mets.repository.mets.EventFlightInfoRepository;
import com.fedex.mets.repository.mets.EventsRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

public class EventFlightEticInfoServiceTest {

    @Mock
    private EventsRepository eventsRepository;

    @Mock
    private EventFlightInfoRepository eventFlightInfoRepository;

    @Mock
    private EventFlightDelaysRepository eventFlightDelaysRepository;

    @InjectMocks
    private EventFlightEticInfoService eventFlightEticInfoService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testGetFlightEticDetail_Success() {
        int eventId = 1;

        String flightEtic = "1,InitialEtic,123";
        FlightEticView flightEticView = new FlightEticView();
        flightEticView.setFlightNumber("Flight123");
        flightEticView.setFlightDate(Timestamp.valueOf("2025-04-11 12:00:00"));
        flightEticView.setFlightLeg("Leg1");
        flightEticView.setFlightACN("ACN123");
        flightEticView.setFlightDestination("Destination");
        flightEticView.setFlightStatus("Status");
        flightEticView.setFlightSchedDeptDateTime(Timestamp.valueOf("2025-04-11 10:00:00"));
        flightEticView.setFlightActualDeptDateTime(Timestamp.valueOf("2025-04-11 10:30:00"));
        flightEticView.setFlightTotalDelay(30);
        flightEticView.setDescription("Description");

        EventFlightInfo flightInfoIn = new EventFlightInfo();
        flightInfoIn.setFlightNumber("FlightIn");
        flightInfoIn.setFlightDate(Timestamp.valueOf("2025-04-11 12:00:00"));
        flightInfoIn.setFlightActualArrivalDateTime(Timestamp.valueOf("2025-04-11 12:00:00"));

        EventFlightInfo flightInfoOut = new EventFlightInfo();
        flightInfoOut.setFlightNumber("FlightOut");
        flightInfoOut.setFlightDate(Timestamp.valueOf("2025-04-11 12:00:00"));
        flightInfoOut.setFlightActualDeptDateTime(Timestamp.valueOf("2025-04-11 14:00:00"));

        EventFlightDelays flightDelay = new EventFlightDelays();
        flightDelay.setDelayCode("D1");
        flightDelay.setDelayTime(30);

        List<EventFlightDelays> flightDelaysList = new ArrayList<>();
        flightDelaysList.add(flightDelay);

        when(eventsRepository.getFlightEtic(anyInt())).thenReturn(flightEtic);
        when(eventFlightInfoRepository.getFligthEticData(anyInt())).thenReturn(flightEticView);
        when(eventFlightInfoRepository.findByEventIdAndFltFlagI(anyInt())).thenReturn(flightInfoIn);
        when(eventFlightInfoRepository.findByEventIdAndFltFlagOAndFltFlagB(anyInt())).thenReturn(flightInfoOut);
        when(eventFlightDelaysRepository.getAllByEventId(anyInt())).thenReturn(flightDelaysList);

        EventFlightEticData result = eventFlightEticInfoService.getFlightEticDetail(eventId);

        assertEquals(eventId, result.getEventId());
        assertEquals("InitialEtic", result.getInitialEtic());
        assertEquals(123, result.getEticNumber());
        assertEquals("Flight123", result.getFlightNumber());
        assertEquals("2025-04-11 12:00:00.0", result.getFlightDate());
        assertEquals("Leg1", result.getFlightLegNumber());
        assertEquals("ACN123", result.getAcn());
        assertEquals("Destination", result.getDestination());
        assertEquals("Status", result.getFlightStatus());
        assertEquals("2025-04-11 10:00:00.0", result.getScheduledDeparture());
        assertEquals("2025-04-11 10:30:00.0", result.getActualDeparture());
        assertEquals("30", result.getTotalDelay());
        assertEquals("Description", result.getFlightType());
        assertEquals("FlightIn/11", result.getFltIn());
        assertEquals("2025-04-11 12:00:00.0", result.getArrival());
        assertEquals("FlightOut/11", result.getFltOut());
        assertEquals("2025-04-11 14:00:00.0", result.getDeparture());
        assertEquals("00:30 (D1), ", result.getDelayCodes());
    }

    @Test
    public void testGetFlightEticDetail_Exception() {
        int eventId = 1;

        doThrow(new RuntimeException("Internal server error")).when(eventsRepository).getFlightEtic(anyInt());

        EventFlightEticData result = eventFlightEticInfoService.getFlightEticDetail(eventId);

        assertEquals(null, result.getFlightNumber());
        assertEquals(null, result.getFlightDate());
        assertEquals(null, result.getFlightLegNumber());
        assertEquals(null, result.getAcn());
        assertEquals(null, result.getDestination());
        assertEquals(null, result.getFlightStatus());
        assertEquals(null, result.getScheduledDeparture());
        assertEquals(null, result.getActualDeparture());
        assertEquals(null, result.getTotalDelay());
        assertEquals(null, result.getFlightType());
        assertEquals(null, result.getFltIn());
        assertEquals(null, result.getArrival());
        assertEquals(null, result.getFltOut());
        assertEquals(null, result.getDeparture());
        assertEquals(null, result.getDelayCodes());
    }
}
