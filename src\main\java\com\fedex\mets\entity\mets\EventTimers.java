package com.fedex.mets.entity.mets;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;
@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "EVENT_TIMERS")
public class EventTimers{

    @EmbeddedId
    private EventTimersPk eventTimersPk;

    @Column(name = "TIMER_START_DT_TM")
    private Timestamp timerStartDtTm;

    @Column(name = "TIMER_STOP_DT_TM")
    private Timestamp timerStopDtTm;

    @Column(name = "TIMER_ID", nullable = false)
    private String timerId;

    @Column(name = "LAST_UPDATE_DT_TM")
    private Timestamp lastUpdateDtTm;
}