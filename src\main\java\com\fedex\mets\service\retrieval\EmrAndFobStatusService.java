package com.fedex.mets.service.retrieval;

import com.fedex.mets.config.EmrSoapClientConfig;
import com.fedex.mets.config.OktaTokenGenService;
import com.fedex.mets.repository.rampview.RvAircraftRepository;
import com.fedex.mets.wsdl.emrStatus.AuthSourceSysType;
import com.fedex.mets.wsdl.emrStatus.GetLatestFlightStatusRequest;
import com.fedex.mets.wsdl.emrStatus.GetLatestFlightStatusResponse;
import com.fedex.mets.wsdl.emrStatus.SessionType;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;


@SuppressWarnings("unused")
@Service
@RequiredArgsConstructor
public class EmrAndFobStatusService {

    @Autowired
    private RvAircraftRepository rvAircraftRepository;

    private static final Logger logger = LoggerFactory.getLogger(EmrAndFobStatusService.class);

    @Autowired
    private EmrSoapClientConfig wb;

    @Autowired
    private OktaTokenGenService oktaTokenGenService;


    @SuppressWarnings("unchecked")
    public Map<String, String> getFlightStatusAndFobMap(
            String acn,
            String userId) {

        logger.info("Getting FlightStatusAndFob details for Aircraft >" + acn);
        Map<String, String> flightStatusAndFob = new HashMap<>();
        try {
            String tokenId = this.oktaTokenGenService.generateToken();
            GetLatestFlightStatusResponse response = getFlightStatus(
                    acn, userId, tokenId);
            flightStatusAndFob.put("FlightStatus", response.getStatusInfo().get(0).getFlightReadiness().value());
            flightStatusAndFob.put("StatusCode",response.getStatusInfo().get(0).getFlightStatus().getStatusCode());

        } catch (Exception e) {
            logger.warn(
                    "ERROR MetsRetrieval Servlet getFlightStatusAndFobMap() e >> "
                            + e.getMessage(), e);
        }
        logger.info("Fob value is: " + rvAircraftRepository.getFob(acn));
        flightStatusAndFob.put("Fob", flightStatusAndFob.put("Fob", rvAircraftRepository.getFob(acn) != null ? String.valueOf(rvAircraftRepository.getFob(acn)) : "N/A"));
        return flightStatusAndFob;
    }


    public GetLatestFlightStatusResponse getFlightStatus(
            String acn, String userId, String tokenId) throws Exception {

        logger.info("..............getLatestFlightStatusRequest for acn:" + acn);

        GetLatestFlightStatusRequest getLatestFlightStatusRequest = new GetLatestFlightStatusRequest();

        SessionType sessionType = new SessionType();
        sessionType.setUserId(userId);
        sessionType.setToken(tokenId);
        sessionType.setAuthSourceSysName(AuthSourceSysType.LDAP);
        getLatestFlightStatusRequest.getAcns().add(acn);
        getLatestFlightStatusRequest.setIsGetPersistedStatus(false);
        getLatestFlightStatusRequest.setSession(sessionType);
        GetLatestFlightStatusResponse resp = (GetLatestFlightStatusResponse) wb.app3webServiceTemplate().marshalSendAndReceive(getLatestFlightStatusRequest);
        return resp;

    }
}
