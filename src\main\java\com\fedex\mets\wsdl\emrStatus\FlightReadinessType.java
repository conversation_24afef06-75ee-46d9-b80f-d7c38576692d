
package com.fedex.mets.wsdl.emrStatus;

import jakarta.xml.bind.annotation.XmlEnum;
import jakarta.xml.bind.annotation.XmlEnumValue;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for flightReadinessType.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="flightReadinessType">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="BLOCKED"/>
 *     &lt;enumeration value="FLT READY"/>
 *     &lt;enumeration value="FLT READY ACK"/>
 *     &lt;enumeration value="IN WORK"/>
 *     &lt;enumeration value="MEL"/>
 *     &lt;enumeration value="TP CHK"/>
 *     &lt;enumeration value="CAT3"/>
 *     &lt;enumeration value="OIL"/>
 *     &lt;enumeration value="SVC CHK 2"/>
 *     &lt;enumeration value="AWR SIGNOFF"/>
 *     &lt;enumeration value="SEC CHK"/>
 *     &lt;enumeration value="PDSC"/>
 *     &lt;enumeration value="NOT SCHD"/>
 *     &lt;enumeration value="NOT AT STA"/>
 *     &lt;enumeration value="IN TRAN"/>
 *     &lt;enumeration value="ERROR(S)"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 * 
 */
@XmlType(name = "flightReadinessType")
@XmlEnum
public enum FlightReadinessType {

    BLOCKED("BLOCKED"),
    @XmlEnumValue("FLT READY")
    FLT_READY("FLT READY"),
    @XmlEnumValue("FLT READY ACK")
    FLT_READY_ACK("FLT READY ACK"),
    @XmlEnumValue("IN WORK")
    IN_WORK("IN WORK"),
    MEL("MEL"),
    @XmlEnumValue("TP CHK")
    TP_CHK("TP CHK"),
    @XmlEnumValue("CAT3")
    CAT_3("CAT3"),
    OIL("OIL"),
    @XmlEnumValue("SVC CHK 2")
    SVC_CHK_2("SVC CHK 2"),
    @XmlEnumValue("AWR SIGNOFF")
    AWR_SIGNOFF("AWR SIGNOFF"),
    @XmlEnumValue("SEC CHK")
    SEC_CHK("SEC CHK"),
    PDSC("PDSC"),
    @XmlEnumValue("NOT SCHD")
    NOT_SCHD("NOT SCHD"),
    @XmlEnumValue("NOT AT STA")
    NOT_AT_STA("NOT AT STA"),
    @XmlEnumValue("IN TRAN")
    IN_TRAN("IN TRAN"),
    @XmlEnumValue("ERROR(S)")
    ERROR_S("ERROR(S)");
    private final String value;

    FlightReadinessType(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static FlightReadinessType fromValue(String v) {
        for (FlightReadinessType c: FlightReadinessType.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
