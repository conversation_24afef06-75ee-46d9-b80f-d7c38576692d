
package com.fedex.mets.wsdl.discrepancy;

import java.math.BigDecimal;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for PlanningResourceType complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="PlanningResourceType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="modified" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="oid" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="resourceCode" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="resourceQty" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="resourceReqFlg" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PlanningResourceType", namespace = "http://www.fedex.com/airops/schemas/Planning.xsd", propOrder = {
    "modified",
    "oid",
    "resourceCode",
    "resourceQty",
    "resourceReqFlg"
})
public class PlanningResourceType {

    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd")
    protected boolean modified;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd", required = true)
    protected BigDecimal oid;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd", required = true)
    protected String resourceCode;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd", required = true)
    protected BigDecimal resourceQty;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd")
    protected boolean resourceReqFlg;

    /**
     * Gets the value of the modified property.
     * 
     */
    public boolean isModified() {
        return modified;
    }

    /**
     * Sets the value of the modified property.
     * 
     */
    public void setModified(boolean value) {
        this.modified = value;
    }

    /**
     * Gets the value of the oid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getOid() {
        return oid;
    }

    /**
     * Sets the value of the oid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setOid(BigDecimal value) {
        this.oid = value;
    }

    /**
     * Gets the value of the resourceCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getResourceCode() {
        return resourceCode;
    }

    /**
     * Sets the value of the resourceCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setResourceCode(String value) {
        this.resourceCode = value;
    }

    /**
     * Gets the value of the resourceQty property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getResourceQty() {
        return resourceQty;
    }

    /**
     * Sets the value of the resourceQty property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setResourceQty(BigDecimal value) {
        this.resourceQty = value;
    }

    /**
     * Gets the value of the resourceReqFlg property.
     * 
     */
    public boolean isResourceReqFlg() {
        return resourceReqFlg;
    }

    /**
     * Sets the value of the resourceReqFlg property.
     * 
     */
    public void setResourceReqFlg(boolean value) {
        this.resourceReqFlg = value;
    }

}
