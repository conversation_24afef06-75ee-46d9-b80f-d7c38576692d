package com.fedex.mets.service.retrieval;

import com.fedex.mets.data.EventEmailData;
import com.fedex.mets.entity.mets.EventTfNotes;
import com.fedex.mets.entity.mets.EventTfNotesPk;
import com.fedex.mets.repository.mets.EventTfNotesRepository;
import com.fedex.mets.util.ServerDateHelper;
import jakarta.mail.internet.MimeMessage;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class TfNotesEmailService {

    private static final Logger logger = LoggerFactory.getLogger(TfNotesEmailService.class);
    @Autowired
    private EventTfNotesRepository tfNotesRepo;

    @Autowired
    private JavaMailSender mailSender;


    /**
     * The following sendMail() is used to send the Email .
     * @params  EventEmailData eventEmailData.
     * @return boolean result.
     */
    public boolean sendMail(
            EventEmailData eventEmailData)
            {
        boolean result = false;

        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setSubject(eventEmailData.getEmailSubject());
            helper.setText(eventEmailData.getEmailBody(), true);
            helper.setFrom(eventEmailData.getEmailFrom());
            helper.setTo(eventEmailData.getEmailTo());

            logger.info("Before sending the E-mail");

            mailSender.send(message);

            result= true;
            logger.info("After sending the E - mail >>" + result);

            if (result) {
                String tfNotes =
                        "An E-mail was sent To: "
                                + eventEmailData.getEmailTo()
                                + " with Subject: "
                                + eventEmailData.getEmailSubject()
                                + " From: "
                                + eventEmailData.getEmailFrom();

                boolean tfNoteInserted =
                        addTFNotes(eventEmailData, tfNotes);
                logger.info(
                        "After the E - mail Tub File Note inserted >>"
                                + tfNoteInserted);
            }
        } catch (Exception mail) {
            result=false;
            logger.warn(
                    " ERROR Tub File Notes sendMail() >> " + mail.getMessage());
        }

        return result;
    }
    /**
     The following method is a private/sub method to support the addEvent method, to add a Tub File Note to the database
     @ params int eventId, String tfNote, Timestamp UpdatedTime.
     @ return boolean result
     */
    public boolean addTFNotes(EventEmailData eventEmailData, String tfNote) {
        boolean result = false;
        int noteId = 0;

        try {
            noteId = tfNotesRepo.getMaxNoteId(eventEmailData.getEmailEventId()) != null ? tfNotesRepo.getMaxNoteId(eventEmailData.getEmailEventId()) : 0;
            noteId += 1;
            logger.info("noteId after incrementing ========================" + noteId);
        } catch (Exception e) {
            logger.warn(
                    " ERROR Event TF Notes addTFNotes e ** >>" + e.getMessage());
        }

        if (noteId > 0) {
            java.util.Date currentDate = ServerDateHelper.getDate_UTC();
            java.sql.Timestamp tfNotesDateTime =
                    new java.sql.Timestamp(currentDate.getTime() + 1000);

            EventTfNotes eventTfNotes = new EventTfNotes();
            eventTfNotes.setEventTfNotesPk(new EventTfNotesPk(eventEmailData.getEmailEventId(), tfNotesDateTime));
            eventTfNotes.setEditedFlag("N");
            eventTfNotes.setNoteId(noteId);
            eventTfNotes.setTfNote(tfNote);
            eventTfNotes.setNoteType(0);
            eventTfNotes.setChangeType(0);
            eventTfNotes.setLastUpdateDtTm(tfNotesDateTime);
            eventTfNotes.setEmpNum(eventEmailData.getEmpId());
            eventTfNotes.setEmpName(eventEmailData.getEmpName());
            eventTfNotes.setEmpDepartment(eventEmailData.getDepartment());
            tfNotesRepo.save(eventTfNotes);
            result = true;
        }
        return result;
    }

}
