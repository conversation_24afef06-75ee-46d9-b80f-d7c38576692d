package com.fedex.mets.entity.mss;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Date;
import java.sql.Time;
import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "FORTE_LEG")
public class ForteLeg {
    @Id
    @Column(name="OID")
    private int oid;

    @Column(name = "FLT_NBR")
    private String fltNbr;

    @Column(name = "FLT_ZULU_DT")
    private Timestamp fltZuluDate;

    @Column(name = "LEG_NBR")
    private String legNbr;

    @Column(name = "TAIL_NBR")
    private String tailNbr;

    @Column(name = "IATA_DEST_CD")
    private String iataDestCd;

    @Column(name = "LEG_STAT_DESC")
    private String legStatDesc;

    @Column(name = "LEG_TYPE_CD")
    private String legTypeCd;

    @Column(name = "SCHED_OUT_ZULU_TMSTP")
    private Timestamp schedOutZuluTmstp;

    @Column(name = "ACTL_OUT_ZULU_TMSTP")
    private Timestamp actlOutZuluTmstp;

    @Column(name = "TOT_DELAY_QTY")
    private String totDelayQty;

    @Column(name = "DELAY_QTY_1")
    private String delayQty1;

    @Column(name = "DELAY_QTY_2")
    private String delayQty2;

    @Column(name = "DELAY_QTY_3")
    private String delayQty3;

    @Column(name = "DELAY_QTY_4")
    private String delayQty4;

    @Column(name = "DELAY_QTY_5")
    private String delayQty5;

    @Column(name = "DELAY_QTY_6")
    private String delayQty6;

    @Column(name = "DELAY_QTY_7")
    private String delayQty7;

    @Column(name = "DELAY_QTY_8")
    private String delayQty8;

    @Column(name = "DELAY_QTY_9")
    private String delayQty9;

    @Column(name = "DELAY_QTY_10")
    private String delayQty10;

    @Column(name = "DELAY_CD_1")
    private String delayCd1;

    @Column(name = "DELAY_CD_2")
    private String delayCd2;

    @Column(name = "DELAY_CD_3")
    private String delayCd3;

    @Column(name = "DELAY_CD_4")
    private String delayCd4;

    @Column(name = "DELAY_CD_5")
    private String delayCd5;

    @Column(name = "DELAY_CD_6")
    private String delayCd6;

    @Column(name = "DELAY_CD_7")
    private String delayCd7;

    @Column(name = "DELAY_CD_8")
    private String delayCd8;

    @Column(name = "DELAY_CD_9")
    private String delayCd9;

    @Column(name = "DELAY_CD_10")
    private String delayCd10;

    @Column(name = "IATA_ORIG_CD")
    private String iataOrigCd;

    @Column(name = "SCHED_IN_ZULU_TMSTP")
    private Timestamp schedInZuluTmstp;

    @Column(name = "ACTL_IN_ZULU_TMSTP")
    private Timestamp actlInZuluTmstp;

    @Column(name="FLT_RRT_CD")
    private String fltRrtCd;

}

