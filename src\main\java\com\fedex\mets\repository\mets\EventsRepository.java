package com.fedex.mets.repository.mets;

import com.fedex.mets.dao.*;
import com.fedex.mets.entity.mets.Events;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.sql.Clob;
import java.sql.Timestamp;
import java.util.List;

@Repository
public interface EventsRepository extends JpaRepository<Events,Integer> {

    @Query(value="Select * from EVENTS where EVENT_ID=:id",nativeQuery = true)
    public Events getEventsByEventId(@Param("id") int id);

    @Query(value="Select type from EVENTS where EVENT_ID=:id",nativeQuery = true)
    public String getEventType(@Param("id") int id);

    @Query(value="select MAX(EVENT_ID) from EVENTS",nativeQuery = true)
    public int getMaxEvent();

    @Query(value="Select EVENT_ID, STATUS from EVENTS where ACN=:acn and TYPE='OOS' and ACTIVE_EVENT='Y'",nativeQuery = true)
    public String getActiveOOS(@Param("acn") String acn);

    @Query(value= "select count(*) from EVENTS where ACN=:acn and TYPE='OOS' and ACTIVE_EVENT='Y'",nativeQuery = true)
    public Integer getActiveEventCount(@Param("acn") String acn);

    @Query(value= "select count(*) from EVENTS where EVENT_ID=:eventId and ACTIVE_EVENT='Y'",nativeQuery = true)
    public Integer getActiveEventsCount(@Param("eventId") int acn);

    @Query(value= "select count(*) from EVENTS where ACN=:acn and EVENT_ID=:eventId and STATUS='UP'",nativeQuery = true)
    public Integer getActiveUpEventsCount(@Param("acn") String acn,@Param("eventId") int eventId);

    @Modifying
    @Transactional
    @Query("UPDATE Events e SET e.activeEvent = :status WHERE e.eventId = :eventId")
    public void updateActiveEventStatus(@Param("status") String status,@Param("eventId") int eventId);

    @Query("SELECT new com.fedex.mets.dao.EventListView(a.eventOwnerGroupId) FROM Events a ")
    List<EventListView> getListViewData();

    @Query("SELECT new com.fedex.mets.dao.EventListView(a.eventOwnerGroupId, a.eventId, a.type, " +
            "a.startDateTime, a.endDateTime, a.acn, a.fleetDesc, " +
            "a.station, a.status, a.eticDateTime, a.eticText,nvl(a.curComment, a.origComment) as currentComment, " +
            "a.lastUpdateDateTime, " +
            "a.lastUpdatedBy, a.createdDateTime, a.createdBy, b.requestStatus, b.changeType, b.lastUpdateDtTm, b.newStatus, b.newEticText,b.newComment ," +
            "b.newEticDtTm," +
            "a.acOwnerGroupId, d.groupTitle," +
            "a.activeEvent, a.ost, b.newOst, a.eticRsnCd, b.newEticRsnCd, " +
            "a.eticRsnComment, b.newEticRsnComment) " +
            "FROM Events a " +
            "JOIN ChangeRequest b ON a.eventId = b.eventId " +
            "JOIN GroupDict d ON a.acOwnerGroupId = d.groupId " +
            "WHERE a.activeEvent = 'Y' " +
            "ORDER BY a.acn")
    public List<EventListView> getListViewList();

    @Query(value="Select count(*) from EVENTS where EVENT_ID=:eventId and LAST_UPDATE_DT_TM > to_date(:lastUpdatedTime,'mm/dd/yy hh24:mi:ss')",nativeQuery = true)
    public Integer getCountOfUpdatedRecords(@Param("eventId") int eventId,@Param("lastUpdatedTime") String lastUpdatedTime);

    @Query(value= "select count(*) from EVENTS where ACN=:acn and TYPE='OOS' and ACTIVE_EVENT='Y' and STATUS <> 'UP'",nativeQuery = true)
    public Integer getActiveOOSCount(@Param("acn") String acn);

    @Query(value="SELECT NVL(DBMS_LOB.SUBSTR(MGR_NOTES, 4000), ' ') FROM EVENTS WHERE EVENT_ID=:eventId",nativeQuery = true)
    public Clob findMgrNotesByEventId(@Param("eventId") int eventId);

    @Query(value="select count(*) from EVENTS where ACN=:acn and TYPE='OOS' and ACTIVE_EVENT='Y'",nativeQuery = true)
    int getCountOfActiveOOSEvents(@Param("acn") String acn);



    @Query(value="Select EVENT_ID, ETIC_INITIAL, ETIC_NUM from EVENTS where EVENT_ID=:eventId",nativeQuery = true)
    public String getFlightEtic(@Param("eventId") int eventId);



    @Query("SELECT new com.fedex.mets.dao.EventDetailView(a.eventId, a.type," +
            "a.startDateTime, a.endDateTime, a.acn, a.fleetDesc, " +
            "a.station, a.status, a.eticDateTime, a.eticText, " +
            "nvl(a.curComment,a.origComment) as currentOrigComment, \n" +
            "a.lastUpdateDateTime, " +
            "a.lastUpdatedBy, a.createdDateTime, a.createdBy,a.primaryContact," +
            "a.acOwnerGroupId,a.eventOwnerGroupId,b.listOrder,c.requestStatus,c.changeType,a.origComment, NVL(a.mgrNotes, ' ') as mgrNotes," +
            "c.newStatus, a.cancelled,c.newEticText,nvl(c.newComment, c.oldComment) as newOldComment," +
            "c.newEticDtTm,a.fedexIdNumber,a.ost," +
            "a.eticRsnCd,a.eticRsnComment,a.memDeskContact) \n"+
            "FROM Events a \n" +
            "JOIN EventDict b ON a.type=b.type \n" +
            "JOIN ChangeRequest c ON a.eventId=c.eventId \n" +
            "WHERE a.activeEvent = 'Y' \n" +
            "AND a.acn=:acn order by b.listOrder")
    public List<EventDetailView> getDetailListView(@Param("acn") String acn);


    @Query("select new com.fedex.mets.dao.ActiveEvents(a.eventId, a.acn, a.type, " +
            "a.startDateTime, a.station, a.status, nvl(a.curComment,a.origComment) as newOldComment," +
            "b.newStatus, b.requestStatus, a.endDateTime) \n" +
            "from Events a " +
            "JOIN  ChangeRequest b on a.eventId = b.eventId \n" +
            "where a.acn =:acn and a.activeEvent = 'Y'")
    public List<ActiveEvents> findActiveEvents(@Param("acn") String acn);

    @Query("select new com.fedex.mets.dao.DoaEvents(a.eventId, a.station, a.startDateTime, a.status, " +
            "a.curComment, b.doaFleetNumber, b.doaFleetDate, b.doaFleetLeg) from Events a " +
            "JOIN EventDoa b on a.eventId=b.eventId where a.acn=:acn " +
            "and a.activeEvent='Y' and a.type='DOA'")
    public List<DoaEvents> getDOAEvents(@Param("acn") String acn);

    @Query("select new com.fedex.mets.dao.UnReviewedEvents(a.eventId, a.acn, a.type, a.startDateTime, a.station, " +
            "a.status, a.curComment, b.requestStatus) from Events a " +
            "LEFT JOIN ChangeRequest b on a.eventId=b.eventId where a.acn=:acn " +
            "and a.activeEvent='Y' and b.requestStatus='C' and b.newStatus='UP'")
    public List<UnReviewedEvents> findUnReviewedEvents(@Param("acn") String acn);


    @Query(value="Select ACTIVE_EVENT from EVENTS where EVENT_ID=:eventId",nativeQuery = true)
    public String getActiveEvent(@Param("eventId") int eventId);

    @Modifying
    @Transactional
    @Query(value = "update Events set END_DT_TM=:endDateTime,LAST_UPDATE_DT_TM=:lastUpdateDateTime \n" +
            ",LAST_UPDATE_BY=:lastUpdatedBy, PRIMARY_CONTACT=:primaryContact\n" +
            ", MEM_DESK_CONTACT=:memDeskContact where EVENT_ID=:eventId",nativeQuery = true)
    public void updateChangeEvent(@Param("endDateTime") String endDateTime,@Param("lastUpdateDateTime") Timestamp lastUpdateDateTime,
                                  @Param("lastUpdatedBy") String lastUpdatedBy,@Param("primaryContact") String primaryContact,
                                  @Param("memDeskContact") String memDeskContact,@Param("eventId") int eventId);

    @Query(value="select ETIC_DT_TM, ETIC_TEXT, CUR_COMMENT, STATUS, OST, ETIC_RSN_CD, ETIC_RSN_COMMENT from EVENTS where ACN=:acn and TYPE='DOA' and \n" +
            " ACTIVE_EVENT='Y' and START_DT_TM=(select min(START_DT_TM) from EVENTS where ACN=:acn \n"+
            "and TYPE='DOA' and ACTIVE_EVENT='Y')",nativeQuery = true)
    public String getEarliestDOAEvent(@Param("acn") String acn);
    
    @Query(value="select count(*) from EVENTS where ACN=:acn and EVENT_ID=:eventId and TYPE='OOS'",nativeQuery = true)
    int getCountOfOOSEventsByEventId(@Param("acn") String acn, @Param("eventId") int eventId);
    
    @Query(value="Select * from EVENTS where EVENT_ID=:id and ACTIVE_EVENT='Y'",nativeQuery = true)
    public Events getActiveEventsByEventId(@Param("id") int id);


}
