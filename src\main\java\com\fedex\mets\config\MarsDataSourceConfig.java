package com.fedex.mets.config;

import com.fedex.mets.util.DecryptionUtil;
import jakarta.persistence.EntityManagerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        basePackages = "com.fedex.mets.repository.mars", // Repositories for mars
        entityManagerFactoryRef = "marsEntityManagerFactory",
        transactionManagerRef = "marsTransactionManager"
)
public class MarsDataSourceConfig {

    @Value("${spring.datasource.mars.password}")
    private String encryptedPassword;

    @Value("${spring.datasource.mars.jdbc-url}")
    private String url;

    @Value("${spring.datasource.mars.username}")
    private String username;

    @Value("${spring.datasource.mars.driver-class-name}")
    private String driverClassName;

    @Bean(name = "marsDataSource")
    public DataSource marsDataSource() throws Exception {
        String decryptedPassword = DecryptionUtil.decrypt(encryptedPassword, "z76yf8ScxNFLZMbxC1YVRQ==");
        return DataSourceBuilder.create().username(username).url(url).driverClassName(driverClassName)
                .password(decryptedPassword).build();
    }

    @Bean(name = "marsEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean marsEntityManagerFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("marsDataSource") DataSource dataSource) {
        return builder
                .dataSource(dataSource)
                .packages("com.fedex.mets.entity.mars") // Entities for mars
                .persistenceUnit("mars") // Persistence unit name for mars
                .build();
    }

    @Bean(name = "marsTransactionManager")
    public PlatformTransactionManager marsTransactionManager(
            @Qualifier("marsEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        return new JpaTransactionManager(entityManagerFactory);
    }
}
