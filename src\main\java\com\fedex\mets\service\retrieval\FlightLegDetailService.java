package com.fedex.mets.service.retrieval;

import com.fedex.mets.config.MachSoapClientConfig;
import com.fedex.mets.config.OktaTokenGenService;
import com.fedex.mets.dao.FlightDetail;
import com.fedex.mets.dto.FlightDetailResponse;
import com.fedex.mets.wsdl.discrepancy.AuthSourceSysType;
import com.fedex.mets.wsdl.discrepancy.SessionType;
import com.fedex.mets.wsdl.flightSearch.GetCurrentFlightLegRequest;
import com.fedex.mets.wsdl.flightSearch.GetCurrentFlightLegResponse;
import com.fedex.mets.wsdl.flightSearch.GetFlightLegDetailsRequest;
import com.fedex.mets.wsdl.flightSearch.GetFlightLegDetailsResponse;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.xml.datatype.DatatypeConfigurationException;
import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class FlightLegDetailService {

    private static final Logger logger = LoggerFactory.getLogger(FlightLegDetailService.class);

    @Autowired
    private MachSoapClientConfig wb;

    @Autowired
    private OktaTokenGenService oktaTokenGenService;

    /**
     * Private method to get the Flight DETAILS for a ACN.
     *
     * @return hashTable containing List of flights.
     * @params String acn.
     */
    @SuppressWarnings("unchecked")

    public FlightDetailResponse getFlightLegDetail(String acn,String userId) throws Exception {
        FlightDetailResponse response = new FlightDetailResponse();
        String tokenId=this.oktaTokenGenService.generateToken();
        logger.info("generated token:"+tokenId);
        try {
            GetFlightLegDetailsRequest getFlightLegDetailsRequest = new GetFlightLegDetailsRequest();
            SessionType sessionType = new SessionType();
            sessionType.setUserId(userId);
            sessionType.setToken(tokenId);
            sessionType.setAuthSourceSysName(AuthSourceSysType.LDAP);
            getFlightLegDetailsRequest.setSession(sessionType);

            getFlightLegDetailsRequest.setAcn(acn);
            LocalDate currentDate = LocalDate.now();
            LocalDate startDate = currentDate.minusDays(10);
            LocalDate futureDate = currentDate.plusDays(5);

            XMLGregorianCalendar xmlStartDate = toXMLGregorianCalendar(startDate);
            XMLGregorianCalendar xmlFutureDate = toXMLGregorianCalendar(futureDate);

            getFlightLegDetailsRequest.setStartDate(xmlStartDate);
            getFlightLegDetailsRequest.setEndDate(xmlFutureDate);
            getFlightLegDetailsRequest.setGetDelayLeg(false);
            logger.info("calling the service");
            GetFlightLegDetailsResponse resp = (GetFlightLegDetailsResponse) wb.app1webServiceTemplate().marshalSendAndReceive(getFlightLegDetailsRequest);
            logger.info("getAircraftFlightDetails SOAP Response" + resp.getStatus().isSuccessful());
            List<FlightDetail> fltDetailList=new ArrayList<>();
               if (resp.getStatus().isSuccessful()) {
                   for ( int i=0;i<resp.getFlightLegs().size(); i++) {
                       FlightDetail flightDetail = new FlightDetail();
                       flightDetail.setFlightNumber(resp.getFlightLegs().get(i).getFlightNumber());
                       flightDetail.setLegNumber(resp.getFlightLegs().get(i).getLegNumber());
                       flightDetail.setEquipmentCode(resp.getFlightLegs().get(i).getEquipmentCode());
                       flightDetail.setLegOrigin(resp.getFlightLegs().get(i).getLegOrigin());
                       flightDetail.setLegDestination(resp.getFlightLegs().get(i).getLegDestination());
                       flightDetail.setGroundTimeMinutes(resp.getFlightLegs().get(i).getGroundTimeMinutes());
                       flightDetail.setLegDepartureTime(resp.getFlightLegs().get(i).getLegDepartureTime());
                       flightDetail.setLegArrivalTime(resp.getFlightLegs().get(i).getLegArrivalTime());
                       flightDetail.setFlightDate(resp.getFlightLegs().get(i).getFlightDate());
                       flightDetail.setStatus(resp.getFlightLegs().get(i).getStatus());
                       fltDetailList.add(flightDetail);
                   }
                   getCurrentFlightLegDetail(acn,userId,response,tokenId,fltDetailList);
                } else {
                    response=null;
                }

            // Sort the HashMap by LegDepartureTime using XMLGregorianCalendar's compareTo method


        } catch (Exception e) {
            logger.warn("ERROR Exception .>> " + e.getMessage());
            throw new Exception(e.getMessage());
        }
        return response;
    }
    public FlightDetailResponse getCurrentFlightLegDetail(String acn,String userId,FlightDetailResponse response,String tokenId,List<FlightDetail> flightDetails) throws Exception {
        tokenId=this.oktaTokenGenService.generateToken();
        try {
            GetCurrentFlightLegRequest getFlightLegDetailsRequest = new GetCurrentFlightLegRequest();
            SessionType sessionType = new SessionType();
            sessionType.setUserId(userId);
            sessionType.setToken(tokenId);
            sessionType.setAuthSourceSysName(AuthSourceSysType.LDAP);
            getFlightLegDetailsRequest.setSession(sessionType);

            getFlightLegDetailsRequest.setAircraftNbr(acn);
            getFlightLegDetailsRequest.setGetLastGroundLeg(false);
            logger.info("calling the getCurrentFlightLegDetail");
            GetCurrentFlightLegResponse resp = (GetCurrentFlightLegResponse) wb.app1webServiceTemplate().marshalSendAndReceive(getFlightLegDetailsRequest);
            logger.info("getCurrentFlightLegDetail SOAP Response" + resp.getStatus().isSuccessful());
            List<FlightDetail> currentFlightDetail=new ArrayList<>();
            if (resp.getStatus().isSuccessful()) {
                    FlightDetail curFlightDetail = new FlightDetail();
                    curFlightDetail.setFlightNumber(resp.getCurrentLeg().getFlightNumber());
                    curFlightDetail.setLegNumber(resp.getCurrentLeg().getLegNumber());
                    curFlightDetail.setEquipmentCode(resp.getCurrentLeg().getEquipmentCode());
                    curFlightDetail.setLegOrigin(resp.getCurrentLeg().getLegOrigin());
                    curFlightDetail.setLegDestination(resp.getCurrentLeg().getLegDestination());
                    curFlightDetail.setGroundTimeMinutes(resp.getCurrentLeg().getGroundTimeMinutes());
                    curFlightDetail.setLegDepartureTime(resp.getCurrentLeg().getLegDepartureTime());
                    curFlightDetail.setLegArrivalTime(resp.getCurrentLeg().getLegArrivalTime());
                    curFlightDetail.setFlightDate(resp.getCurrentLeg().getFlightDate());
                    curFlightDetail.setStatus(resp.getCurrentLeg().getStatus());
                    LocalDateTime currentDate = LocalDateTime.now();
                    XMLGregorianCalendar xmlCurrentDate = toXMLGregorianCalendarTime(currentDate);
                    if(xmlCurrentDate.compare(resp.getCurrentLeg().getLegDepartureTime())<0) {
                        curFlightDetail.setStation(resp.getCurrentLeg().getLegOrigin());
                    }
                    else if(xmlCurrentDate.compare(resp.getCurrentLeg().getLegDepartureTime()) > 0 && xmlCurrentDate.compare(resp.getCurrentLeg().getLegArrivalTime()) < 0) {
                        curFlightDetail.setStation(resp.getCurrentLeg().getLegDestination());
                    }
                    else if(xmlCurrentDate.compare(resp.getCurrentLeg().getLegArrivalTime())>0){
                        if(resp.getCurrentLeg().getStatus()!=null && resp.getCurrentLeg().getStatus().equals("CLOSED"))
                        {
                            curFlightDetail.setStation(resp.getCurrentLeg().getLegDestination());
                        }
                        else {
                            // Find the flight that matches the input departure time
                            Optional<FlightDetail> matchingFlight = flightDetails.stream()
                                    .filter(flight -> flight.getLegDepartureTime().equals(resp.getCurrentLeg().getNextLegDepartureTime()))
                                    .findFirst();

                            // Retrieve the location from the matching flight
                            if (matchingFlight.isPresent()) {
                                FlightDetail flight = matchingFlight.get();
                                String legDestination = flight.getLegDestination();
                                if (xmlCurrentDate.compare(flight.getLegDepartureTime()) > 0 && resp.getCurrentLeg().getStatus().equals("OPEN")) {
                                    curFlightDetail.setStation(legDestination);
                                }
                            } else {
                                logger.info("No matching flight found for the given departure time.");
                            }
                        }
                    }
                    currentFlightDetail.add(curFlightDetail);

                    // Sort the list by legDepartureTime using XMLGregorianCalendar's compare method and removing current flight
                    //add order parameter in a single line
                    AtomicInteger counter = new AtomicInteger(1);
                    flightDetails =  flightDetails.stream()
                            .sorted((fd1, fd2) -> fd1.getLegDepartureTime().compare(fd2.getLegDepartureTime()))
                            .filter(flight -> !(flight.getLegDepartureTime().equals(resp.getCurrentLeg().getLegDepartureTime()) &&
                                    flight.getFlightNumber().equals(resp.getCurrentLeg().getFlightNumber())))
//                            .peek(flight -> flight.setOrder(counter.getAndIncrement()))
                            .collect(Collectors.toList());

                // Print the partitioned flight details
                // Partition the list into "upcoming flights" and "past flights"
                Map<Boolean, List<FlightDetail>> partitionedFlights = flightDetails.stream()
                        .collect(Collectors.partitioningBy(flight -> flight.getLegDepartureTime().compare(resp.getCurrentLeg().getLegDepartureTime()) > 0));

                List<FlightDetail> upcomingFlights = partitionedFlights.get(true);
                List<FlightDetail> pastFlights = partitionedFlights.get(false);

                // Create the final HashMap
                response.setCurrentFlight(curFlightDetail);
                response.setUpcomingFlights(upcomingFlights);
                response.setPastFlights(pastFlights);
            } else {
                response=null;
            }

        } catch (Exception e) {
            logger.warn("ERROR Exception .>> " + e.getMessage());
            throw new Exception(e.getMessage());
        }
        return response;
    }

    private XMLGregorianCalendar toXMLGregorianCalendar(LocalDate date) throws DatatypeConfigurationException {
        GregorianCalendar gregorianCalendar = GregorianCalendar.from(date.atStartOfDay(ZoneId.systemDefault()));
        return DatatypeFactory.newInstance().newXMLGregorianCalendar(gregorianCalendar);
    }

    private XMLGregorianCalendar toXMLGregorianCalendarTime(LocalDateTime dateTime) throws DatatypeConfigurationException {
        GregorianCalendar gregorianCalendar = GregorianCalendar.from(dateTime.atZone(ZoneId.systemDefault()));
        return DatatypeFactory.newInstance().newXMLGregorianCalendar(gregorianCalendar);
    }
}
