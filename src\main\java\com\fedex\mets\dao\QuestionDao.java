package com.fedex.mets.dao;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Optional;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class QuestionDao {

    int questionId;
    String questionTxt;
    String questionGrp;
    List<AnswerDao> answers;
    <PERSON><PERSON><PERSON> answered;
    Boolean required;
}
