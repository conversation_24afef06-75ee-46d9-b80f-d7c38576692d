
package com.fedex.mets.wsdl.msn.shortagenotice;

import java.util.ArrayList;
import java.util.List;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for shortageNoticeShipperType complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="shortageNoticeShipperType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="eventNbr" type="{http://www.w3.org/2001/XMLSchema}long"/>
 *         &lt;element name="date" type="{http://www.w3.org/2001/XMLSchema}date"/>
 *         &lt;element name="shippingType" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="airline" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="flight" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="etd" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="eta" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="waybill" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="fromStation" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="fromDept" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="toStation" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="toDept" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ict" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="sensawareDeviceNbr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="additionalInfo" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pfmAirbill" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="etaChanged" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="type" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="isn" type="{http://www.w3.org/2001/XMLSchema}long"/>
 *         &lt;element name="quantity" type="{http://www.w3.org/2001/XMLSchema}long"/>
 *         &lt;element name="location" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="foisEta" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="key" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="events" type="{http://fedex.com/airops/maxi/services/jaxws/material}shortageNoticeShipperEventsType" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "shortageNoticeShipperType", namespace = "http://fedex.com/airops/maxi/services/jaxws/material", propOrder = {
    "eventNbr",
    "date",
    "shippingType",
    "airline",
    "flight",
    "etd",
    "eta",
    "waybill",
    "fromStation",
    "fromDept",
    "toStation",
    "toDept",
    "ict",
    "sensawareDeviceNbr",
    "additionalInfo",
    "pfmAirbill",
    "etaChanged",
    "type",
    "isn",
    "quantity",
    "location",
    "foisEta",
    "key",
    "events"
})
public class ShortageNoticeShipperType {

    protected long eventNbr;
    @XmlElement(required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar date;
    @XmlElement(required = true)
    protected String shippingType;
    @XmlElement(required = true)
    protected String airline;
    @XmlElement(required = true)
    protected String flight;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar etd;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar eta;
    @XmlElement(required = true)
    protected String waybill;
    @XmlElement(required = true)
    protected String fromStation;
    @XmlElement(required = true)
    protected String fromDept;
    @XmlElement(required = true)
    protected String toStation;
    @XmlElement(required = true)
    protected String toDept;
    @XmlElement(required = true)
    protected String ict;
    @XmlElement(required = true)
    protected String sensawareDeviceNbr;
    @XmlElement(required = true)
    protected String additionalInfo;
    @XmlElement(required = true)
    protected String pfmAirbill;
    protected boolean etaChanged;
    @XmlElement(required = true)
    protected String type;
    protected long isn;
    protected long quantity;
    @XmlElement(required = true)
    protected String location;
    @XmlElement(required = true)
    protected String foisEta;
    @XmlElement(required = true)
    protected String key;
    protected List<ShortageNoticeShipperEventsType> events;

    /**
     * Gets the value of the eventNbr property.
     * 
     */
    public long getEventNbr() {
        return eventNbr;
    }

    /**
     * Sets the value of the eventNbr property.
     * 
     */
    public void setEventNbr(long value) {
        this.eventNbr = value;
    }

    /**
     * Gets the value of the date property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDate() {
        return date;
    }

    /**
     * Sets the value of the date property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDate(XMLGregorianCalendar value) {
        this.date = value;
    }

    /**
     * Gets the value of the shippingType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getShippingType() {
        return shippingType;
    }

    /**
     * Sets the value of the shippingType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setShippingType(String value) {
        this.shippingType = value;
    }

    /**
     * Gets the value of the airline property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAirline() {
        return airline;
    }

    /**
     * Sets the value of the airline property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAirline(String value) {
        this.airline = value;
    }

    /**
     * Gets the value of the flight property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFlight() {
        return flight;
    }

    /**
     * Sets the value of the flight property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFlight(String value) {
        this.flight = value;
    }

    /**
     * Gets the value of the etd property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getEtd() {
        return etd;
    }

    /**
     * Sets the value of the etd property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setEtd(XMLGregorianCalendar value) {
        this.etd = value;
    }

    /**
     * Gets the value of the eta property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getEta() {
        return eta;
    }

    /**
     * Sets the value of the eta property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setEta(XMLGregorianCalendar value) {
        this.eta = value;
    }

    /**
     * Gets the value of the waybill property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWaybill() {
        return waybill;
    }

    /**
     * Sets the value of the waybill property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWaybill(String value) {
        this.waybill = value;
    }

    /**
     * Gets the value of the fromStation property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFromStation() {
        return fromStation;
    }

    /**
     * Sets the value of the fromStation property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFromStation(String value) {
        this.fromStation = value;
    }

    /**
     * Gets the value of the fromDept property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFromDept() {
        return fromDept;
    }

    /**
     * Sets the value of the fromDept property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFromDept(String value) {
        this.fromDept = value;
    }

    /**
     * Gets the value of the toStation property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getToStation() {
        return toStation;
    }

    /**
     * Sets the value of the toStation property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setToStation(String value) {
        this.toStation = value;
    }

    /**
     * Gets the value of the toDept property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getToDept() {
        return toDept;
    }

    /**
     * Sets the value of the toDept property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setToDept(String value) {
        this.toDept = value;
    }

    /**
     * Gets the value of the ict property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIct() {
        return ict;
    }

    /**
     * Sets the value of the ict property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIct(String value) {
        this.ict = value;
    }

    /**
     * Gets the value of the sensawareDeviceNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSensawareDeviceNbr() {
        return sensawareDeviceNbr;
    }

    /**
     * Sets the value of the sensawareDeviceNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSensawareDeviceNbr(String value) {
        this.sensawareDeviceNbr = value;
    }

    /**
     * Gets the value of the additionalInfo property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAdditionalInfo() {
        return additionalInfo;
    }

    /**
     * Sets the value of the additionalInfo property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAdditionalInfo(String value) {
        this.additionalInfo = value;
    }

    /**
     * Gets the value of the pfmAirbill property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPfmAirbill() {
        return pfmAirbill;
    }

    /**
     * Sets the value of the pfmAirbill property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPfmAirbill(String value) {
        this.pfmAirbill = value;
    }

    /**
     * Gets the value of the etaChanged property.
     * 
     */
    public boolean isEtaChanged() {
        return etaChanged;
    }

    /**
     * Sets the value of the etaChanged property.
     * 
     */
    public void setEtaChanged(boolean value) {
        this.etaChanged = value;
    }

    /**
     * Gets the value of the type property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getType() {
        return type;
    }

    /**
     * Sets the value of the type property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setType(String value) {
        this.type = value;
    }

    /**
     * Gets the value of the isn property.
     * 
     */
    public long getIsn() {
        return isn;
    }

    /**
     * Sets the value of the isn property.
     * 
     */
    public void setIsn(long value) {
        this.isn = value;
    }

    /**
     * Gets the value of the quantity property.
     * 
     */
    public long getQuantity() {
        return quantity;
    }

    /**
     * Sets the value of the quantity property.
     * 
     */
    public void setQuantity(long value) {
        this.quantity = value;
    }

    /**
     * Gets the value of the location property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLocation() {
        return location;
    }

    /**
     * Sets the value of the location property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLocation(String value) {
        this.location = value;
    }

    /**
     * Gets the value of the foisEta property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFoisEta() {
        return foisEta;
    }

    /**
     * Sets the value of the foisEta property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFoisEta(String value) {
        this.foisEta = value;
    }

    /**
     * Gets the value of the key property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKey() {
        return key;
    }

    /**
     * Sets the value of the key property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKey(String value) {
        this.key = value;
    }

    /**
     * Gets the value of the events property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the events property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getEvents().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link ShortageNoticeShipperEventsType }
     * 
     * 
     */
    public List<ShortageNoticeShipperEventsType> getEvents() {
        if (events == null) {
            events = new ArrayList<ShortageNoticeShipperEventsType>();
        }
        return this.events;
    }

}
