package com.fedex.mets.entity.mars;


import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "EMPDETAIL")
public class EmpDetail {

    @Id
    @Column(name = "EMPNUM", nullable = false)
    private String empNum;

    @Column(name = "EMPFIRSTNAME")
    private String empFirstName;

    @Column(name = "EMPMIDDLEINITIAL")
    private String empMiddleInitial;

    @Column(name = "EMPLASTNAME")
    private String empLastName;

    @Column(name = "JOBTITLE")
    private String jobTitle;

    @Column(name = "EMPVPORG")
    private String empVpOrg;

    @Column(name = "EMPSTATIONID")
    private String empStationId;

    @Column(name = "EMPDEPTID")
    private String empDeptId;

    @Column(name = "SECTOR_ID")
    private String sectorId;

    @Column(name = "SECTOR_DESC")
    private String sectorDesc;

    @Column(name = "REGION_ID")
    private String regionId;

    @Column(name = "REGION_DESC")
    private String regionDesc;

    @Column(name = "EMPWORKADDRESS1")
    private String empWorkAddress1;

    @Column(name = "EMPWORKADDRESS2")
    private String empWorkAddress2;

    @Column(name = "EMPWORKCITY")
    private String empWorkCity;

    @Column(name = "EMPWORKSTATE")
    private String empWorkState;

    @Column(name = "EMPWORKZIP")
    private String empWorkZip;

    @Column(name = "EMPCOUNTRYCODE")
    private String empCountryCode;

    @Column(name = "EMPCOUNTRYNAME")
    private String empCountryName;

    @Column(name = "EMPWORKPHONE")
    private String empWorkPhone;

    @Column(name = "EMPHOMEPHONE")
    private String empHomePhone;

    @Column(name = "EMPWORKFAXPUBLIC")
    private String empWorkFaxPublic;

    @Column(name = "EMPPAGERNUMBER")
    private String empPagerNumber;

    @Column(name = "PAGER_UPDATE_FLAG")
    private String pagerUpdateFlag;

    @Column(name = "EMPCELLPHONENUMBER")
    private String empCellPhoneNumber;

    @Column(name = "CELL_UPDATE_FLAG")
    private String cellUpdateFlag;

    @Column(name = "EMPCOMAILSTA")
    private String empComailSta;

    @Column(name = "EMPMANAGERNUM")
    private String empManagerNum;

    @Column(name = "MGR_NAME")
    private String mgrName;

    @Column(name = "LASTUPDATED")
    private Timestamp lastUpdated;

    @Column(name = "DISTRICT_ID")
    private String districtId;

    @Column(name = "EMPCOMAILZIP")
    private String empComailZip;

    @Column(name = "EMPTYPE")
    private String empType;

    @Column(name = "EMPJOBTITLE")
    private String empJobTitle;

    @Column(name = "EMPORGCODE")
    private String empOrgCode;

    @Column(name = "EMPWORKFAXPRIVATE")
    private String empWorkFaxPrivate;

    @Column(name = "EMPCOSTCENTER")
    private String empCostCenter;

    @Column(name = "EMPSTATUS")
    private String empStatus;

    @Column(name = "EMPDEPARTMENTNAME")
    private String empDepartmentName;

    @Column(name = "LASTUPDATEDBY")
    private String lastUpdatedBy;

    @Column(name = "MOCC_SPD_DIAL")
    private String moccSpdDial;

    @Column(name = "EMPEMAILADDRESS")
    private String empEmailAddress;

    @Column(name = "EMPNO")
    private Long empNo;

    @Column(name = "DISTRICT_DESC")
    private String districtDesc;

    @Column(name = "EMPMGTLEVEL")
    private int empMgtLevel;

    @Column(name = "DEPT_DESC")
    private String deptDesc;

    @Column(name = "EMPJOBCODE")
    private String empJobCode;

    @Column(name = "TOOL_ACCESS_LEVEL_CD")
    private String toolAccessLevelCd;

    @Column(name = "EMPJOBFAMILY")
    private Long empJobFamily;

    @Column(name = "EMPSENIORITYNUM")
    private Long empSeniorityNum;

    @Column(name = "CURRENT_SECTOR")
    private String currentSector;

    @Column(name = "NORMAL_SECTOR")
    private String normalSector;
}
