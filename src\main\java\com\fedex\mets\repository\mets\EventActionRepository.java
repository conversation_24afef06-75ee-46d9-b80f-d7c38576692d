package com.fedex.mets.repository.mets;

import com.fedex.mets.entity.mets.EventAction;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface EventActionRepository extends JpaRepository<EventAction, Integer> {
    @Query(value = "select * from EVENT_ACTION where EVENT_ID=:eventId", nativeQuery = true)
    public EventAction findByPrimaryKey(@Param("eventId") int eventId);

    @Modifying
    @Transactional
    @Query(value = "delete from EVENT_ACTION where EVENT_ID=:eventId", nativeQuery = true)
    public void deleteByPrimaryKey(@Param("eventId") int eventId);
}
