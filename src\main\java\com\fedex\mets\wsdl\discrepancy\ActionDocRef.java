
package com.fedex.mets.wsdl.discrepancy;

import java.math.BigDecimal;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ActionDocRef complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ActionDocRef">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="userUpdateType" type="{http://www.fedex.com/airops/schemas/EnumTypes.xsd}UserUpdateTypeEnum"/>
 *         &lt;element name="DscrpActionDocRefOid" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="actDocRefOid" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="reqFlag" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="docReference" type="{http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd}DocRef"/>
 *         &lt;element name="actionDetail" type="{http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd}ActionUsedDetail"/>
 *         &lt;element name="reviewNotes" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ActionDocRef", propOrder = {
    "userUpdateType",
    "dscrpActionDocRefOid",
    "actDocRefOid",
    "reqFlag",
    "docReference",
    "actionDetail",
    "reviewNotes"
})
public class ActionDocRef {

    @XmlElement(required = true)
    protected UserUpdateTypeEnum userUpdateType;
    @XmlElement(name = "DscrpActionDocRefOid", required = true)
    protected BigDecimal dscrpActionDocRefOid;
    @XmlElement(required = true)
    protected BigDecimal actDocRefOid;
    @XmlElement(required = true)
    protected String reqFlag;
    @XmlElement(required = true)
    protected DocRef docReference;
    @XmlElement(required = true)
    protected ActionUsedDetail actionDetail;
    @XmlElement(required = true)
    protected String reviewNotes;

    /**
     * Gets the value of the userUpdateType property.
     * 
     * @return
     *     possible object is
     *     {@link UserUpdateTypeEnum }
     *     
     */
    public UserUpdateTypeEnum getUserUpdateType() {
        return userUpdateType;
    }

    /**
     * Sets the value of the userUpdateType property.
     * 
     * @param value
     *     allowed object is
     *     {@link UserUpdateTypeEnum }
     *     
     */
    public void setUserUpdateType(UserUpdateTypeEnum value) {
        this.userUpdateType = value;
    }

    /**
     * Gets the value of the dscrpActionDocRefOid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getDscrpActionDocRefOid() {
        return dscrpActionDocRefOid;
    }

    /**
     * Sets the value of the dscrpActionDocRefOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setDscrpActionDocRefOid(BigDecimal value) {
        this.dscrpActionDocRefOid = value;
    }

    /**
     * Gets the value of the actDocRefOid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getActDocRefOid() {
        return actDocRefOid;
    }

    /**
     * Sets the value of the actDocRefOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setActDocRefOid(BigDecimal value) {
        this.actDocRefOid = value;
    }

    /**
     * Gets the value of the reqFlag property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReqFlag() {
        return reqFlag;
    }

    /**
     * Sets the value of the reqFlag property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReqFlag(String value) {
        this.reqFlag = value;
    }

    /**
     * Gets the value of the docReference property.
     * 
     * @return
     *     possible object is
     *     {@link DocRef }
     *     
     */
    public DocRef getDocReference() {
        return docReference;
    }

    /**
     * Sets the value of the docReference property.
     * 
     * @param value
     *     allowed object is
     *     {@link DocRef }
     *     
     */
    public void setDocReference(DocRef value) {
        this.docReference = value;
    }

    /**
     * Gets the value of the actionDetail property.
     * 
     * @return
     *     possible object is
     *     {@link ActionUsedDetail }
     *     
     */
    public ActionUsedDetail getActionDetail() {
        return actionDetail;
    }

    /**
     * Sets the value of the actionDetail property.
     * 
     * @param value
     *     allowed object is
     *     {@link ActionUsedDetail }
     *     
     */
    public void setActionDetail(ActionUsedDetail value) {
        this.actionDetail = value;
    }

    /**
     * Gets the value of the reviewNotes property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReviewNotes() {
        return reviewNotes;
    }

    /**
     * Sets the value of the reviewNotes property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReviewNotes(String value) {
        this.reviewNotes = value;
    }

}
