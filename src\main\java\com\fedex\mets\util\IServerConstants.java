package com.fedex.mets.util;

public interface IServerConstants {
	/************************** keys for values read from the Client ***********************************/
	public static final String MODE =					"MODE";
	public static final String ACN =					"ACN";
    public static final String EVENT_ID=				"EVENT_ID";
	public static final String STATION=					"STATION";
	/************************** String values used for MODE & FLAG*******************************/
	//for METsRetrieval Servlet
	public static final String LIST_VIEW =				"LIST_VIEW";
	public static final String DETAIL_VIEW =			"DETAIL_VIEW";
	public static final String REPORT_CATEGORIES =		"REPORT_CATEGORIES";
	public static final String TF_NOTES =				"TF_NOTES";
	public static final String NIW_TIMERS =				"NIW_TIMERS";
	public static final String TEST_FLIGHT =			"TEST_FLIGHT";
	public static final String EVENT_DISCREPANCIES =	"EVENT_DISCREPANCIES";
	public static final String EVENT_FLIGHT_ETIC =		"EVENT_FLIGHT_ETIC";
	public static final String FLIGHT_SEARCH =			"FLIGHT_SEARCH";
	public static final String EVENT_ROAD_TRIP =		"EVENT_ROAD_TRIP";
	public static final String EVENT_DOA =				"EVENT_DOA";
	public static final String DISCREPANCY_DETAIL =		"DISCREPANCY_DETAIL";
	public static final String SEND_MAIL =				"SEND_MAIL";//for mailing function on Server Side.
	public static final String WLM_NIW_TIMERS =			"WLM_NIW_TIMERS";//for updating NIW TIMER from WorkLoad Management application.
	public static final String WLM_TF_NOTES =			"WLM_TF_NOTES";//for Adding TubFile Notes from WorkLoad Management application.
	public static final String MGR_DETAILS = 			"MGR_DETAILS";//for finding the Manager Details for a given station.
	public static final String MGR_DETAILS_RESULT = 	"MGR_DETAILS_RESULT";//for finding the Manager Details for a given station.
	public static final String REGION_LIST_VIEW =		"REGION_LIST_VIEW";//for finding the Region list
	public static final String REGION_STATION_DETAILS_VIEW ="REGION_STATION_DETAILS_VIEW";//for finding the Region list

	/************************** String values used for Update Functionality******************************/
	public static final String ADD =					"ADD";//add a new Record
	public static final String DELETE =					"DELETE";//delete an existing Record
	public static final String EDIT =					"EDIT";//update an exisitng Record
	public static final String START =					"START";//Start a Timer
	public static final String CANCEL =					"CANCEL";//to cancel a DOA Event
	public static final String CLOSE =					"CLOSE";//to Close a DOA Event
	public static final String VIEW =					"VIEW";//to view NIW Timers
	public static final String UPDATE =					"UPDATE";//to view NIW Timer Details
	public static final String EVENT_UPDATE =			"EVENT_UPDATE";//added on 01/08/2003 for Updates on Event Detail view
	public static final String SHORTAGE_NOTICE_DETAILS=  "SHORTAGE_NOTICE_DETAILS"; // //to view Shortage Notice Details
	public static final String MSN_DETAILS=  "MSN_DETAILS"; // //to view MSN Details
	public static final String MSN_SHIPPING_INFO=  "MSN_SHIPPING_INFO"; // //to view MSN Details

	/******************************************************************************************/
	public static final String ERROR =							"ERROR";
	public static final String CURRENT_TIME =					"CURRENT_TIME";
	public static final String LIST_VIEW_OBJECT =				"LIST_VIEW_OBJECT";
	public static final String DETAIL_VIEW_OBJECT =				"DETAIL_VIEW_OBJECT";
	public static final String TF_NOTES_OBJECT =				"TF_NOTES_OBJECT";
	public static final String REPORT_CATEGORIES_KEY_VALUES =	"REPORT_CATEGORIES_KEY_VALUES";
	public static final String EVENT_ACTIVE_NIW_TIMERS =		"EVENT_ACTIVE_NIW_TIMERS";
	public static final String EVENT_NIW_TIMERS =				"EVENT_NIW_TIMERS";
	public static final String TIMER_DETAILS =					"TIMER_DETAILS";
	public static final String TEST_FLIGHT_DETAILS =			"TEST_FLIGHT_DETAILS";
	public static final String EVENT_LINKED_DISCREPANCIES =		"EVENT_LINKED_DISCREPANCIES";
	public static final String DISCREPANCIES =					"DISCREPANCIES";
	public static final String FLIGHT_ETIC_DETAIL =				"FLIGHT_ETIC_DETAIL";
	public static final String FLIGHT_SEARCH_DETAIL =			"FLIGHT_SEARCH_DETAIL";
	public static final String ROAD_TRIP_DETAILS =				"ROAD_TRIP_DETAILS";
	public static final String DOA_DETAILS =					"DOA_DETAILS";
	public static final String DISCREPANCY_DETAILS =			"DISCREPANCY_DETAILS";
	public static final String OVERLAP_TIMER_DETAILS =			"OVERLAP_TIMER_DETAILS";
	public static final String UPDATED_TIMER_DETAILS =			"UPDATED_TIMER_DETAILS";
	public static final String REGIONS_LIST =					"REGIONS_LIST";
	public static final String REGION_STATION_LIST =			"REGION_STATION_LIST";

	/************************** values used in MET ADD/CHANGE/REVIEW/CLOSE/CONVERT EVENT FUNCTIONALITY ***********************************/
	public static final String WIZARD_EVENT_DATA=		"ADDED_EVENT_DATA";
	public static final String CHANGE_EVENT=			"CHANGE_EVENT";//to Change an Event.
	public static final String VALIDATE_FLIGHT=			"VALIDATE_FLIGHT";//to validate the Flight being added.
	public static final String CONVERTABLE_EVENTS=		"CONVERTABLE_EVENTS";//Events that are still active in the database and should be converted.
	public static final String ACTION_REQUIRED_EVENTS=	"ACTION_REQUIRED_EVENTS";//Events that are still active/past/convertable in the database.
	public static final String UNREVIEWED_EVENTS=		"UNREVIEWED_EVENTS";//Events that has a closed OOS Event requiring Duty Manager.
	public static final String OVERRIDABLE_EVENTS=		"OVERRIDABLE_EVENTS";//Events that are still active && called UP but did not receive Confirmation back from SUPER(added on 05-01-2003).

	public static final String SUCCESS= "SUCCESS";//FLAG to inform Client that the Event was successfully added/closed/changed/reviewed.
	public static final String ADD_EVENT_FLAG= "ADD_EVENT_FLAG";//FLAG to inform Client that there are no active events for the given ACN.

	public static final String REVIEWED_EVENT=		"REVIEWED_EVENT";//Events that has been reviewed.



	/*********************** values used for JMS message on Detail view updates **************************/
	public static final String REPORTING_CATEGORY_UPDATE ="Reporting Categories";
	public static final String NIW_TIMER_UPDATE = "NIW Timers";
	public static final String DISCREPANCY_UPDATE = "Discrepancies";
	public static final String FLIGHT_ETIC_UPDATE = "Flight/ETIC Info";
	public static final String TEST_FLIGHT_UPDATE ="Test Flight Form";
	public static final String DOA_UPDATE = "DOA Form";
	public static final String EVENT_DETAIL_UPDATE = "Detail Updated";//added on 01/08/2003 for publishing Updates on Event Detail view
	public static final String LIST_VIEW_EVENT_UPDATE = "List View Event Updated";//added on 09/16/2003 for publishing Updates on List View(for details changed on Detail View)


	/*********************** values USED/DECLARED specifically for FORTE INTERFACE **************************/
	public static final String FLIGHT_NUMBER				= "FLIGHT_NUMBER";
	public static final String FLIGHT_DATE					= "FLIGHT_DATE";
	public static final String FLIGHT_LEG_NUMBER			= "FLIGHT_LEG_NUMBER";
	public static final String FLIGHT_DETAILS				= "FLIGHT_DETAILS";//this is the mode set for Forte request
	public static final String VALID_FLIGHT					= "VALID_FLIGHT";
	public static final String FLIGHT_DETAILS_RESULTS		= "FLIGHT_DETAILS_RESULTS";//this is the key set for Forte Request Results.
	public static final String FLIGHT_LEG_DETAILS			= "FLIGHT_LEG_DETAILS";


	public static final String	CONTINUE_ADDING_EVENT		= "CONTINUE_ADDING_EVENT";//added to check for overridable events when user selects continue adding event.

	public static final String	IS_TIMER_PUBLISH_REQUIRED	= "IS_TIMER_PUBLISH_REQUIRED";//added to publish the Timer Update (06-09-2003)
	
	public static final String 	SUPER_UPDATE_SENDBUFFER  	= "SUPER_UPDATE_SENDBUFFER";  // added to avoid race condition where GDI returns SUPER confirmation before CHANGE_REQUEST table is updated

	public static final String	CONTINUE_EVENT_TRANSATCION	= "CONTINUE_EVENT_TRANSATCION";//added to allow the event transaction(10-15-2003).
	public static final String	TRANSATCION_ERROR_EVENTS	= "TRANSATCION_ERROR_EVENTS";//added to warn about the event transactions(10-15-2003).
	
	public static final String VALIDATE_START_DATE_TIME			= "VALIDATE_START_DATE_TIME";
	public static final String START_DATE_TIME					= "START_DATE_TIME";	
}