
package com.fedex.mets.wsdl.discrepancy;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;

/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;extension base="{http://www.fedex.com/airops/schemas/Common.xsd}GenericRequest">
 *       &lt;sequence>
 *         &lt;element name="detailDiscrepancyFilter" type="{http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd}detailDiscrepancyQueryFilter" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "detailDiscrepancyFilter"
})
@XmlRootElement(name = "getDetailAircraftDiscrepancyRequest",namespace="http:///www.fedex.com/airops/schemas/Mach")
public class GetDetailAircraftDiscrepancyRequest
    extends GenericRequest
{

    protected DetailDiscrepancyQueryFilter detailDiscrepancyFilter;

    /**
     * Gets the value of the detailDiscrepancyFilter property.
     * 
     * @return
     *     possible object is
     *     {@link DetailDiscrepancyQueryFilter }
     *     
     */
    public DetailDiscrepancyQueryFilter getDetailDiscrepancyFilter() {
        return detailDiscrepancyFilter;
    }

    /**
     * Sets the value of the detailDiscrepancyFilter property.
     * 
     * @param value
     *     allowed object is
     *     {@link DetailDiscrepancyQueryFilter }
     *     
     */
    public void setDetailDiscrepancyFilter(DetailDiscrepancyQueryFilter value) {
        this.detailDiscrepancyFilter = value;
    }

}
