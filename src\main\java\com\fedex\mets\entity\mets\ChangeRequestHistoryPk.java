package com.fedex.mets.entity.mets;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Embeddable
public class ChangeRequestHistoryPk {
    @Column(name = "EVENT_ID")
    private Integer eventId;

    @Column(name = "ACN")
    private String acn;

    @Column(name = "CREATED_DT_TM")
    private Timestamp createdDtTm;
}
