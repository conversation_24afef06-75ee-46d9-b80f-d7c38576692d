package com.fedex.mets.dto;

import com.fedex.mets.dao.FlightDetail;
import com.fedex.mets.data.EventNIWTimersDataEntity;
import com.fedex.mets.data.NIWTimersDataEntity;
import com.fedex.mets.entity.mets.EventTimers;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@Schema(description = "Response containing flight details")
public class NiwTimersResponse {

    @Schema(description = "List of timer details")
    List<EventTimers> timerDataList;

    @Schema(description = "List of active timer details")
    List<EventNIWTimersDataEntity> eventActiveTimerDataList;

    @Schema(description = "List of event timers details")
    List<EventNIWTimersDataEntity> eventTimerDataList;
    public NiwTimersResponse() {
        this.timerDataList = new ArrayList<>();
        this.eventActiveTimerDataList = new ArrayList<>();
        this.eventTimerDataList = new ArrayList<>();
    }
}
