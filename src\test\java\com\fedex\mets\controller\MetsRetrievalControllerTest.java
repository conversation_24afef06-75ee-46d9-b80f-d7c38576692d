package com.fedex.mets.controller;

import com.fedex.mets.dao.AcnCacheDetail;
import com.fedex.mets.dao.ManagerDetails;
import com.fedex.mets.data.*;
import com.fedex.mets.dto.*;
import com.fedex.mets.entity.ldap.User;
import com.fedex.mets.entity.mets.EventTfNotes;
import com.fedex.mets.service.retrieval.*;
import com.fedex.mets.util.JsonFileUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

public class MetsRetrievalControllerTest {

    @Mock
    private EventListViewService eventListViewService;

    @Mock
    private ReportingCategoriesService rcService;

    @Mock
    private MOCCRegionService moccRegionService;

    @Mock
    private EventListDetailViewService eventListDetailViewService;

    @Mock
    private EventFlightEticInfoService eventFlightEticInfoService;

    @Mock
    private NIWTimersService niwTimerService;

    @Mock
    private DiscrepancyDetailService discrepancyDetailService;

    @Mock
    private EventDiscrepanciesService eventDiscrepanciesService;

    @Mock
    private MsnDetailService msnDetailService;

    @Mock
    private FlightLegDetailService flightLegDetailService;

    @Mock
    private TfNotesEmailService tubFlileEmailServive;

    @Mock
    private UserService userService;
    @Mock
    private AcnCacheService acnCacheService;

    @Mock
    private JsonFileUtil jsonFileUtil;
    @InjectMocks
    private MetsRetrievalController metsRetrievalController;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        acnCacheService.jsonFileUtil = jsonFileUtil;
    }

    @Test
    public void testGetManagers_Success() throws Exception {
        String station = "Station1";
        List<ManagerDetails> managers = Collections.singletonList(new ManagerDetails());

        when(eventListViewService.getManagerDetail(anyString())).thenReturn(managers);

        ResponseEntity<List<ManagerDetails>> response = metsRetrievalController.getManagers(station);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(managers, response.getBody());
    }

    @Test
    public void testGetManagers_InvalidInput() throws Exception {
        String station = "InvalidStation";

        doThrow(new IllegalArgumentException("Invalid input")).when(eventListViewService).getManagerDetail(anyString());

        ResponseEntity<List<ManagerDetails>> response = metsRetrievalController.getManagers(station);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    }

    @Test
    public void testGetManagers_InternalServerError() throws Exception {
        String station = "Station1";

        doThrow(new RuntimeException("Internal server error")).when(eventListViewService).getManagerDetail(anyString());

        ResponseEntity<List<ManagerDetails>> response = metsRetrievalController.getManagers(station);

        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
    }

    @Test
    public void testGetAllRegions_Success() throws Exception {
        List<String> regions = Collections.singletonList("Region1");

        when(moccRegionService.getMOCCRegions()).thenReturn(regions);

        ResponseEntity<List<String>> response = metsRetrievalController.getAllregions();

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(regions, response.getBody());
    }

    @Test
    public void testGetAllRegions_InternalServerError() throws Exception {
        doThrow(new RuntimeException("Internal server error")).when(moccRegionService).getMOCCRegions();

        ResponseEntity<List<String>> response = metsRetrievalController.getAllregions();

        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
    }

    @Test
    public void testGetStationsByRegion_Success() throws Exception {
        String region = "Region1";
        List<String> stations = Collections.singletonList("Station1");

        when(moccRegionService.getMOCCRegionStations(anyString())).thenReturn(stations);

        ResponseEntity<List<String>> response = metsRetrievalController.getStationsByRegion(region);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(stations, response.getBody());
    }

    @Test
    public void testGetStationsByRegion_InvalidInput() throws Exception {
        String region = "InvalidRegion";

        doThrow(new IllegalArgumentException("Invalid input")).when(moccRegionService).getMOCCRegionStations(anyString());

        ResponseEntity<List<String>> response = metsRetrievalController.getStationsByRegion(region);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    }

    @Test
    public void testGetStationsByRegion_InternalServerError() throws Exception {
        String region = "Region1";

        doThrow(new RuntimeException("Internal server error")).when(moccRegionService).getMOCCRegionStations(anyString());

        ResponseEntity<List<String>> response = metsRetrievalController.getStationsByRegion(region);

        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
    }

    @Test
    public void testGetEventDetailView_Success() throws Exception {
        String acn = "ACN123";
        String userId = "User1";
        List<DetailViewData> detailView = Collections.singletonList(new DetailViewData());

        when(eventListDetailViewService.getEventDetailView(anyString(), anyString())).thenReturn(detailView);

        ResponseEntity<List<DetailViewData>> response = metsRetrievalController.getEventDetailView(acn, userId);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(detailView, response.getBody());
    }

    @Test
    public void testGetEventDetailView_InvalidInput() throws Exception {
        String acn = "InvalidACN";
        String userId = "User1";

        doThrow(new IllegalArgumentException("Invalid input")).when(eventListDetailViewService).getEventDetailView(anyString(), anyString());

        ResponseEntity<List<DetailViewData>> response = metsRetrievalController.getEventDetailView(acn, userId);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    }

    @Test
    public void testGetEventDetailView_InternalServerError() throws Exception {
        String acn = "ACN123";
        String userId = "User1";

        doThrow(new RuntimeException("Internal server error")).when(eventListDetailViewService).getEventDetailView(anyString(), anyString());

        ResponseEntity<List<DetailViewData>> response = metsRetrievalController.getEventDetailView(acn, userId);

        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
    }


    @Test
    public void testGetAcnCacheData_Success() {
        // Prepare test data
        HashMap<String, List<?>> mockCacheData = new HashMap<>();
        List<AcnCacheDetail> acnDetailList = new ArrayList<>();

        AcnCacheDetail detail1 = new AcnCacheDetail();
        detail1.setAcn("ACN123");
        detail1.setFleetCode("B777");
        detail1.setStatus("A");
        acnDetailList.add(detail1);

        AcnCacheDetail detail2 = new AcnCacheDetail();
        detail2.setAcn("ACN456");
        detail2.setFleetCode("B767");
        detail2.setStatus("A");
        acnDetailList.add(detail2);

        mockCacheData.put("ACN_CACHE_DETAIL", acnDetailList);

        // Mock service method
        when(acnCacheService.getAcnCacheFromFile()).thenReturn(mockCacheData);

        // Call controller method
        ResponseEntity<?> response = metsRetrievalController.getAcnCacheData();

        // Verify response
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(mockCacheData, response.getBody());
    }

    @Test
    public void testGetAcnCacheData_NotFound() {
        // Mock service method to return null
        when(acnCacheService.getAcnCacheFromFile()).thenReturn(null);

        // Call controller method
        ResponseEntity<?> response = metsRetrievalController.getAcnCacheData();

        // Verify response
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertEquals("ACN cache data not found", response.getBody());
    }

    @Test
    public void testGetAcnCacheData_EmptyCache() {
        // Prepare empty cache data
        HashMap<String, List<?>> emptyCacheData = new HashMap<>();
        emptyCacheData.put("ACN_CACHE_DETAIL", new ArrayList<>());

        // Mock service method
        when(acnCacheService.getAcnCacheFromFile()).thenReturn(emptyCacheData);

        // Call controller method
        ResponseEntity<?> response = metsRetrievalController.getAcnCacheData();

        // Verify response
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertEquals("ACN cache details not found or empty", response.getBody());
    }

    @Test
    public void testGetAcnCacheData_Exception() {
        // Mock service method to throw exception
        when(acnCacheService.getAcnCacheFromFile()).thenThrow(new RuntimeException("Test exception"));

        // Call controller method
        ResponseEntity<?> response = metsRetrievalController.getAcnCacheData();

        // Verify response
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertEquals("Error retrieving ACN cache data: Test exception", response.getBody());
    }

    @Test
    public void testUpdateAcnCacheData_Success() throws Exception {
        // Prepare test data
        HashMap<String, List<?>> mockCacheData = new HashMap<>();
        List<AcnCacheDetail> acnDetailList = new ArrayList<>();

        AcnCacheDetail detail1 = new AcnCacheDetail();
        detail1.setAcn("ACN123");
        detail1.setFleetCode("B777");
        detail1.setStatus("A");
        acnDetailList.add(detail1);

        AcnCacheDetail detail2 = new AcnCacheDetail();
        detail2.setAcn("ACN456");
        detail2.setFleetCode("B767");
        detail2.setStatus("A");
        acnDetailList.add(detail2);

        mockCacheData.put("ACN_CACHE_DETAIL", acnDetailList);

        // Mock service methods
        when(acnCacheService.getAcnCacheDetail()).thenReturn(mockCacheData);
        when(jsonFileUtil.saveToJsonFile(eq(AcnCacheService.ACN_CACHE_FILE_PATH), any())).thenReturn(true);

        // Call controller method
        ResponseEntity<?> response = metsRetrievalController.updateAcnCacheData();

        // Verify response
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(mockCacheData, response.getBody());
    }

    @Test
    public void testUpdateAcnCacheData_SaveFailure() throws Exception {
        // Prepare test data
        HashMap<String, List<?>> mockCacheData = new HashMap<>();
        mockCacheData.put("ACN_CACHE_DETAIL", new ArrayList<AcnCacheDetail>());

        // Mock service methods
        when(acnCacheService.getAcnCacheDetail()).thenReturn(mockCacheData);
        when(jsonFileUtil.saveToJsonFile(eq(AcnCacheService.ACN_CACHE_FILE_PATH), any())).thenReturn(false);

        // Call controller method
        ResponseEntity<?> response = metsRetrievalController.updateAcnCacheData();

        // Verify response
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertEquals("Failed to save ACN cache data to JSON file", response.getBody());
    }

    @Test
    public void testUpdateAcnCacheData_Exception() throws Exception {
        // Mock service method to throw exception
        when(acnCacheService.getAcnCacheDetail()).thenThrow(new RuntimeException("Test exception"));

        // Call controller method
        ResponseEntity<?> response = metsRetrievalController.updateAcnCacheData();

        // Verify response
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertEquals("Error updating ACN cache data: Test exception", response.getBody());
    }
}
