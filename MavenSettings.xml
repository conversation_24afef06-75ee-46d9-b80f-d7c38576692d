<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0" 
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 
                              https://maven.apache.org/xsd/settings-1.0.0.xsd">
  
  <!-- Local Repository: Uncomment and change path if needed -->
  <!--
  <localRepository>/path/to/local/repo</localRepository>
  -->

  <!-- Interactive Mode -->
  <interactiveMode>true</interactiveMode>

  <!-- Offline Mode -->
  <offline>false</offline>

  <!-- Proxy Settings -->
  <proxies>
    <!-- Example Proxy -->
    <!--
    <proxy>
      <id>example-proxy</id>
      <active>true</active>
      <protocol>http</protocol>
      <host>proxy.example.com</host>
      <port>8080</port>
      <username>proxy-username</username>
      <password>proxy-password</password>
      <nonProxyHosts>*.example.com|localhost</nonProxyHosts>
    </proxy>
    -->
  </proxies>

  <!-- Servers: Credentials for remote repositories -->
  <servers>
    <!-- Example: Private Repository Server -->
    <!--
    <server>
      <id>my-private-repo</id>
      <username>your-username</username>
      <password>your-password</password>
    </server>
    -->
  </servers>

  <!-- Mirrors: Redirect repositories to other locations -->
  <mirrors>
    <!-- Example: Use Maven Central -->
    <mirror>
      <id>central-mirror</id>
      <mirrorOf>central</mirrorOf>
      <url>https://repo.maven.apache.org/maven2</url>
    </mirror>
    
    <!-- Example: Redirect everything to a custom repository -->
    <!--
    <mirror>
      <id>my-repo-mirror</id>
      <mirrorOf>*</mirrorOf>
      <url>https://my.custom.repo</url>
    </mirror>
    -->
  </mirrors>

  <!-- Profiles: Custom build settings -->
  <profiles>
    <!-- Example Profile -->
	<profile>
	  <id>nexus</id>
		<repositories>
			<repository>
				<id>5231-aosd-workbench</id>
				<name>5231-aosd-workbench</name>
				<url>https://nexus.prod.cloud.fedex.com:8443/nexus/content/repositories/5231-aosd-workbench/</url>
				<releases>
					<enabled>true</enabled>
				</releases>
			</repository>
			<repository>
					<id>central</id>
					<url>https://nexus.prod.cloud.fedex.com:8443/nexus/repository/maven-central/
					</url>
					<releases>
						<enabled>true</enabled>
					</releases>
					<snapshots>
						<enabled>true</enabled>
					</snapshots>
				</repository>
				
				<repository>
					<id>oracle</id>
					<url>https://nexus.prod.cloud.fedex.com:8443/nexus/repository/oracle/
					</url>
				</repository>
				<repository>
					<id>6949-CIS</id>
					<url>https://nexus.prod.cloud.fedex.com:8443/nexus/content/repositories/6949-CIS/</url>
					<snapshots>
						<enabled>true</enabled>
					</snapshots>
				</repository>
				<repository>
					<id>540-AOSD-FOCUS</id>
					<name>ENVOY</name>
					<url>https://nexus.prod.cloud.fedex.com:8443/nexus/content/repositories/release
					</url>
				</repository>
		</repositories>
		<pluginRepositories>
			<pluginRepository>
				<id>central</id>
				<url>https://nexus.prod.cloud.fedex.com:8443/nexus/repository/maven-central/</url>
			</pluginRepository>
		</pluginRepositories>
	</profile>
    <profile>
      <id>default-profile</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <repositories>
        <!-- Maven Central -->
        <repository>
          <id>central</id>
          <url>https://repo.maven.apache.org/maven2</url>
          <releases>
            <enabled>true</enabled>
          </releases>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
        </repository>
        
        <!-- Example: Custom Repository -->
        <!--
        <repository>
          <id>my-private-repo</id>
          <url>https://my.private.repo/repository/maven-releases</url>
          <releases>
            <enabled>true</enabled>
          </releases>
          <snapshots>
            <enabled>true</enabled>
          </snapshots>
        </repository>
        -->
      </repositories>
      <pluginRepositories>
        <!-- Example: Custom Plugin Repository -->
        <!--
        <pluginRepository>
          <id>plugin-repo</id>
          <url>https://my.private.repo/repository/maven-plugins</url>
          <releases>
            <enabled>true</enabled>
          </releases>
          <snapshots>
            <enabled>true</enabled>
          </snapshots>
        </pluginRepository>
        -->
      </pluginRepositories>
    </profile>
  </profiles>

  <!-- Active Profiles -->
  <activeProfiles>
    <activeProfile>default-profile</activeProfile>
	<activeProfile>nexus</activeProfile>
  </activeProfiles>
</settings>
