
package com.fedex.mets.wsdl.msn.shortagenotice;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="session" type="{http://fedex.com/airops/maxi/services/jaxws}sessionType"/>
 *         &lt;element name="header" type="{http://fedex.com/airops/maxi/services/jaxws}HeaderType"/>
 *         &lt;element name="criteria" type="{http://fedex.com/airops/maxi/services/jaxws/shortagenotice}shortageNoticeSearchCriteriaType"/>
 *         &lt;element name="displayHotItems" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="displayMyQueue" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="msnSearch" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "session",
    "header",
    "criteria",
    "displayHotItems",
    "displayMyQueue",
    "msnSearch"
})
@XmlRootElement(name = "getShortageNoticeListRequest", namespace = "http://fedex.com/airops/maxi/services/jaxws/shortagenotice")
public class GetShortageNoticeListRequest {

    @XmlElement(namespace = "http://fedex.com/airops/maxi/services/jaxws/shortagenotice", required = true)
    protected SessionType session;

    @XmlElement(namespace = "http://fedex.com/airops/maxi/services/jaxws/shortagenotice", required = true)
    protected HeaderType header;
    @XmlElement(namespace = "http://fedex.com/airops/maxi/services/jaxws/shortagenotice", required = true)
    protected ShortageNoticeSearchCriteriaType criteria;
    @XmlElement(namespace = "http://fedex.com/airops/maxi/services/jaxws/shortagenotice")
    protected boolean displayHotItems;
    @XmlElement(namespace = "http://fedex.com/airops/maxi/services/jaxws/shortagenotice")
    protected boolean displayMyQueue;
    @XmlElement(namespace = "http://fedex.com/airops/maxi/services/jaxws/shortagenotice")
    protected boolean msnSearch;

    /**
     * Gets the value of the session property.
     * 
     * @return
     *     possible object is
     *     {@link SessionType }
     *     
     */
    public SessionType getSession() {
        return session;
    }

    /**
     * Sets the value of the session property.
     * 
     * @param value
     *     allowed object is
     *     {@link SessionType }
     *     
     */
    public void setSession(SessionType value) {
        this.session = value;
    }

    /**
     * Gets the value of the header property.
     * 
     * @return
     *     possible object is
     *     {@link HeaderType }
     *     
     */
    public HeaderType getHeader() {
        return header;
    }

    /**
     * Sets the value of the header property.
     * 
     * @param value
     *     allowed object is
     *     {@link HeaderType }
     *     
     */
    public void setHeader(HeaderType value) {
        this.header = value;
    }

    /**
     * Gets the value of the criteria property.
     * 
     * @return
     *     possible object is
     *     {@link ShortageNoticeSearchCriteriaType }
     *     
     */
    public ShortageNoticeSearchCriteriaType getCriteria() {
        return criteria;
    }

    /**
     * Sets the value of the criteria property.
     * 
     * @param value
     *     allowed object is
     *     {@link ShortageNoticeSearchCriteriaType }
     *     
     */
    public void setCriteria(ShortageNoticeSearchCriteriaType value) {
        this.criteria = value;
    }

    /**
     * Gets the value of the displayHotItems property.
     * 
     */
    public boolean isDisplayHotItems() {
        return displayHotItems;
    }

    /**
     * Sets the value of the displayHotItems property.
     * 
     */
    public void setDisplayHotItems(boolean value) {
        this.displayHotItems = value;
    }

    /**
     * Gets the value of the displayMyQueue property.
     * 
     */
    public boolean isDisplayMyQueue() {
        return displayMyQueue;
    }

    /**
     * Sets the value of the displayMyQueue property.
     * 
     */
    public void setDisplayMyQueue(boolean value) {
        this.displayMyQueue = value;
    }

    /**
     * Gets the value of the msnSearch property.
     * 
     */
    public boolean isMsnSearch() {
        return msnSearch;
    }

    /**
     * Sets the value of the msnSearch property.
     * 
     */
    public void setMsnSearch(boolean value) {
        this.msnSearch = value;
    }

}
