package com.fedex.mets.entity.rampview;


import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Date;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "RV_REGION_STATION_FULL")
public class RvRegionStationFull {

    @Id
    @Column(name = "REGION_ID", nullable = false)
    private Long regionId;

    @Id
    @Column(name = "STATION_CD", nullable = false)
    private String stationCd;

    @Column(name = "MAINT_ACTION")
    private Character maintAction;

    @Column(name = "MAINT_USER_CD")
    private String maintUserCd;

    @Column(name = "MAINT_DTM")
    private Date maintDtm;

    @Column(name = "MAINT_VERSION")
    private Long maintVersion;

    @Column(name = "MAINT_FUNCTION")
    private String maintFunction;
}