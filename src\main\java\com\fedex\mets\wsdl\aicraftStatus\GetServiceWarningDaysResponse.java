package com.fedex.mets.wsdl.aicraftStatus;

import com.fedex.mets.wsdl.discrepancy.GenericResponse;
import jakarta.xml.bind.annotation.*;

import javax.xml.datatype.XMLGregorianCalendar;
import java.util.ArrayList;
import java.util.List;



/**
 * <p>Java class for anonymous complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;extension base="{http://www.fedex.com/airops/schemas/Common.xsd}GenericResponse">
 *       &lt;sequence>
 *         &lt;element name="acn" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="serviceWarningDays" type="{http:///www.fedex.com/airops/schemas/Mach}ServiceWarningDays" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="legDepartureTime" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "acn",
        "serviceWarningDays",
        "legDepartureTime"
})
@XmlRootElement(name = "getServiceWarningDaysResponse",namespace="http:///www.fedex.com/airops/schemas/Mach")
public class GetServiceWarningDaysResponse
        extends GenericResponse
{

    @XmlElement(required = true)
    protected String acn;
    protected List<ServiceWarningDays> serviceWarningDays;
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar legDepartureTime;

    /**
     * Gets the value of the acn property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getAcn() {
        return acn;
    }

    /**
     * Sets the value of the acn property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setAcn(String value) {
        this.acn = value;
    }

    /**
     * Gets the value of the serviceWarningDays property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the serviceWarningDays property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getServiceWarningDays().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link ServiceWarningDays }
     *
     *
     */
    public List<ServiceWarningDays> getServiceWarningDays() {
        if (serviceWarningDays == null) {
            serviceWarningDays = new ArrayList<ServiceWarningDays>();
        }
        return this.serviceWarningDays;
    }

    /**
     * Gets the value of the legDepartureTime property.
     *
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *
     */
    public XMLGregorianCalendar getLegDepartureTime() {
        return legDepartureTime;
    }

    /**
     * Sets the value of the legDepartureTime property.
     *
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *
     */
    public void setLegDepartureTime(XMLGregorianCalendar value) {
        this.legDepartureTime = value;
    }

}