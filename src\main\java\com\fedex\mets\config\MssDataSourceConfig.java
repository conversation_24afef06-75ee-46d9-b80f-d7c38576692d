package com.fedex.mets.config;

import com.fedex.mets.util.DecryptionUtil;
import jakarta.persistence.EntityManagerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        basePackages = "com.fedex.mets.repository.mss", // Repositories for Mets
        entityManagerFactoryRef = "mssEntityManagerFactory",
        transactionManagerRef = "mssTransactionManager"
)
public class MssDataSourceConfig {

    @Value("${spring.datasource.mss.password}")
    private String encryptedPassword;

    @Value("${spring.datasource.mss.jdbc-url}")
    private String url;

    @Value("${spring.datasource.mss.username}")
    private String username;

    @Value("${spring.datasource.mss.driver-class-name}")
    private String driverClassName;


    @Bean(name = "mssDataSource")
    public DataSource mssDataSource() throws Exception {
        String decryptedPassword = DecryptionUtil.decrypt(encryptedPassword, "z76yf8ScxNFLZMbxC1YVRQ==");
        return DataSourceBuilder.create().username(username).url(url).driverClassName(driverClassName)
                .password(decryptedPassword).build();
    }

    @Bean(name = "mssEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean mssEntityManagerFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("mssDataSource") DataSource dataSource) {
        return builder
                .dataSource(dataSource)
                .packages("com.fedex.mets.entity.mss") // Entities for Mets
                .persistenceUnit("mss") // Persistence unit name for Mets
                .build();
    }

    @Bean(name = "mssTransactionManager")
    public PlatformTransactionManager mssTransactionManager(
            @Qualifier("mssEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        return new JpaTransactionManager(entityManagerFactory);
    }
}
