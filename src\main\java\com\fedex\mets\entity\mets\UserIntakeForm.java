package com.fedex.mets.entity.mets;


import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.sql.Timestamp;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "user_intake_form")
public class UserIntakeForm {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "user_intake_form_seq")
    @SequenceGenerator(name = "user_intake_form_seq", sequenceName = "USER_INTAKE_FORM_SEQ", allocationSize = 1)
    @Column(name="USER_INTAKE_FORM_ID")
    private int userIntakeFormId;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "intake_form_id")
    public IntakeForm intakeForm;
    @Column(name = "intake_form_nm")
    public String intakeFormNm;

    @Column(name = "role_id")
    private int roleId;

    @Column(name = "event_type_id")
    private int eventId;
    //Need to update the Below field name to fleetType as we are storing the Fleet values in dssAuthCode field
    @Column(name="DSS_AUTH_CD")
    private String dssAuthCode;

    @JsonFormat(
            shape = JsonFormat.Shape.STRING,
            pattern = "yyyy-MM-dd HH:mm:ss.SSS",
            timezone = "UTC")
    @CreatedDate
    @Column(name="created_tmstp")
    private Timestamp createdTMSTP;

    @JsonFormat(
            shape = JsonFormat.Shape.STRING,
            pattern = "yyyy-MM-dd HH:mm:ss.SSS",
            timezone = "UTC")
    @LastModifiedDate
    @Column(name="updated_tmstp")
    private Timestamp updatedTMSTP;


}
