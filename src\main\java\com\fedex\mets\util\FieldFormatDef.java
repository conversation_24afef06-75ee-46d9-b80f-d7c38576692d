package com.fedex.mets.util;

import java.util.List;

public class FieldFormatDef {

	private String  name;
	private String  value;
	private int     length;
	
	private boolean allignLeft = true;
	
	private List<String>  valueList;
	
	public List<String> getValueList() {
		return valueList;
	}

	public void setValueList(List<String> valueList) {
		this.valueList = valueList;
	}

	public FieldFormatDef(String name, int length) {
		super();
		this.name = name;
		this.length = length;
	}

	public FieldFormatDef(String name, int length, boolean allignLeft) {
		this(name, length);
		this.allignLeft = allignLeft;
	}
	
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}
	public int getLength() {
		return length;
	}
	public void setLength(int length) {
		this.length = length;
	}
	public boolean isAllignLeft() {
		return allignLeft;
	}
	
	@Override
	public String toString() {
		return "FieldFormatDef [name=" + name + ", value=" + value
				+ ", length=" + length + ", allignLeft=" + allignLeft + "]";
	}
	
}

