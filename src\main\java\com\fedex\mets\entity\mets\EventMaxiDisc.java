package com.fedex.mets.entity.mets;

import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "EVENT_MAXI_DISC")
public class EventMaxiDisc {

    @EmbeddedId
    private EventMaxiDiscPk eventMaxiDisckPk;

    @Column(name = "TYPE")
    private String type;
}