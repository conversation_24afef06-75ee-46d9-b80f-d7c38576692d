package com.fedex.mets.entity.ldap;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.ldap.odm.annotations.Attribute;
import org.springframework.ldap.odm.annotations.Entry;
import org.springframework.ldap.odm.annotations.Id;

import javax.naming.Name;

@Getter
@Setter
@Data
@Entry(objectClasses = {"inetOrgPerson", "organizationalPerson", "person", "top"})
public final class User {

    @Id
    @JsonIgnore
    private Name id;

    @Attribute(name = "uid")
    private String uid;

    @Attribute(name = "cn")
    private String name;

    @Attribute(name = "mail")
    private String mail;

    @Attribute(name = "title")
    private String title;

    @Attribute(name = "departmentname")
    private String departmentname;



}