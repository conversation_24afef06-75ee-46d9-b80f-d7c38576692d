package com.fedex.mets.entity.mets;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "TIMERS")
public class Timers {

    @Id
    @Column(name = "TIMER_ID", nullable = false)
    private String timerId;

    @Column(name = "TIMER_NAME", nullable = false)
    private String timerName;

    @Column(name = "TIMER_DESC")
    private String timerDesc;

    @Column(name = "ACTIVE_CATG", nullable = false)
    private String activeCatg;

    @Column(name = "SYSTEM_CATG", nullable = false)
    private String systemCatg;

    @Column(name = "LIST_ORDER")
    private Integer listOrder;

    @Column(name = "LAST_UPDATE_DT_TM")
    private Timestamp lastUpdateDtTm;
}