
package com.fedex.mets.wsdl.aicraftStatus;


import jakarta.xml.bind.annotation.XmlEnum;
import jakarta.xml.bind.annotation.XmlEnumValue;
import jakarta.xml.bind.annotation.XmlType;

/**
 * <p>Java class for WarningDaysType.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="WarningDaysType">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="LLM Timer"/>
 *     &lt;enumeration value="PDSC"/>
 *     &lt;enumeration value="A Check"/>
 *     &lt;enumeration value="B Check"/>
 *     &lt;enumeration value="C Check"/>
 *     &lt;enumeration value="Service Chk"/>
 *     &lt;enumeration value="Max Power"/>
 *     &lt;enumeration value="Tire/Daily"/>
 *     &lt;enumeration value="APU In-Flt Start"/>
 *     &lt;enumeration value="Security Chk"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 * 
 */
@XmlType(name = "WarningDaysType", namespace = "http://www.fedex.com/airops/schemas/EnumTypes.xsd")
@XmlEnum
public enum WarningDaysType {

    @XmlEnumValue("LLM Timer")
    LLM_TIMER("LLM Timer"),
    PDSC("PDSC"),
    @XmlEnumValue("A Check")
    A_CHECK("A Check"),
    @XmlEnumValue("B Check")
    B_CHECK("B Check"),
    @XmlEnumValue("C Check")
    C_CHECK("C Check"),
    @XmlEnumValue("Service Chk")
    SERVICE_CHK("Service Chk"),
    @XmlEnumValue("Max Power")
    MAX_POWER("Max Power"),
    @XmlEnumValue("Tire/Daily")
    TIRE_DAILY("Tire/Daily"),
    @XmlEnumValue("APU In-Flt Start")
    APU_IN_FLT_START("APU In-Flt Start"),
    @XmlEnumValue("Security Chk")
    SECURITY_CHK("Security Chk");
    private final String value;

    WarningDaysType(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static WarningDaysType fromValue(String v) {
        for (WarningDaysType c: WarningDaysType.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
