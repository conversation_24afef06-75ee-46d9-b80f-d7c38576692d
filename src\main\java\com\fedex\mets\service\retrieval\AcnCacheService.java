package com.fedex.mets.service.retrieval;

import com.fedex.mets.config.MachSoapClientConfig;
import com.fedex.mets.config.OktaTokenGenService;
import com.fedex.mets.dao.AcnCacheDetail;
import com.fedex.mets.util.JsonFileUtil;
import com.fedex.mets.wsdl.acnCache.CacheDataTypeEnum;
import com.fedex.mets.wsdl.acnCache.GetCacheDataRequest;
import com.fedex.mets.wsdl.acnCache.GetCacheDataResponse;
import com.fedex.mets.wsdl.discrepancy.AuthSourceSysType;
import com.fedex.mets.wsdl.discrepancy.SessionType;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@RequiredArgsConstructor
public class AcnCacheService {

    private static final Logger logger = LoggerFactory.getLogger(AcnCacheService.class);
    public static final String ACN_CACHE_FILE_PATH = "acn_cache/acn_cache_data.json";
    public static final String ACN_CACHE_DETAIL_KEY = "ACN_CACHE_DETAIL";

    @Autowired
    private MachSoapClientConfig wb;

    @Autowired
    private OktaTokenGenService oktaTokenGenService;

    @Autowired
    public JsonFileUtil jsonFileUtil;

    @Scheduled(cron = "0 0 */6 * * *")
    public void updateAcnCache() {
    	logger.info("Running scheduled job to update acn cache...");

    	try {
    		HashMap<String, List<?>> cacheDetails = getAcnCacheDetail();

    		// Save the cache data to a JSON file
    		boolean saved = jsonFileUtil.saveToJsonFile(ACN_CACHE_FILE_PATH, cacheDetails);

    		if (saved) {
    		    logger.info("ACN Cache updated successfully with {} entries and saved to JSON file",
    			    cacheDetails.get(ACN_CACHE_DETAIL_KEY) != null ?
    			    ((List<?>)cacheDetails.get(ACN_CACHE_DETAIL_KEY)).size() : 0);
    		} else {
    		    logger.warn("ACN Cache updated but failed to save to JSON file");
    		}

        	logger.info("Scheduled job completed.");

    	} catch(Exception e) {
        	logger.error("Scheduled job failed: {}", e.getMessage(), e);
            // Don't rethrow the exception as it will stop the scheduler
    	}
    }

    /**
     * Private method to get the ACN Cache.
     *
     * @return hashTable containing List of ACN.
     * @params String acn.
     */
    public HashMap<String, List<?>> getAcnCacheDetail() throws Exception {
    	HashMap<String, List<?>> hm = new HashMap<>();
        String tokenId=this.oktaTokenGenService.generateToken();
        try {
        	GetCacheDataRequest getAcnCacheRequest = new GetCacheDataRequest();
            SessionType sessionType = new SessionType();
            sessionType.setUserId("12345");
            sessionType.setToken(tokenId);
            sessionType.setAuthSourceSysName(AuthSourceSysType.LDAP);
            getAcnCacheRequest.setSession(sessionType);

            getAcnCacheRequest.setCacheDataType(CacheDataTypeEnum.ACN);

            logger.info("calling the service");
            GetCacheDataResponse resp = (GetCacheDataResponse) wb.app1webServiceTemplate().marshalSendAndReceive(getAcnCacheRequest);
            logger.info("getAcnCacheDetails SOAP Response" + resp.getStatus().isSuccessful());
            List<AcnCacheDetail> acnDetailList=new ArrayList<>();
               if (resp.getStatus().isSuccessful()) {
                   for ( int i=0;i<resp.getAcnAircraftDetails().size(); i++) {
                	   AcnCacheDetail acnDetail = new AcnCacheDetail();
                       acnDetail.setAcn(resp.getAcnAircraftDetails().get(i).getAcn());
                       acnDetail.setFleetCode(resp.getAcnAircraftDetails().get(i).getFleetModelCd());
                       acnDetail.setStatus(resp.getAcnAircraftDetails().get(i).getStatus());

                       if(acnDetail.getStatus() == null || !acnDetail.getStatus().equals("I")){
                            acnDetailList.add(acnDetail);
                       }
                   }
                   hm.put(ACN_CACHE_DETAIL_KEY, acnDetailList);
                } else {
                   hm.put(ACN_CACHE_DETAIL_KEY, null);
                }

        } catch (Exception e) {
        	hm.put(ACN_CACHE_DETAIL_KEY, null);
            logger.warn("ERROR Exception .>> " + e.getMessage());
            throw new Exception(e.getMessage());
        }
        return hm;
    }

    /**
     * Get the ACN cache data from the JSON file
     *
     * @return the cached ACN data, or null if the file doesn't exist or an error occurred
     */
    @SuppressWarnings("unchecked")
    public HashMap<String, List<?>> getAcnCacheFromFile() {
        return jsonFileUtil.readFromJsonFile(ACN_CACHE_FILE_PATH, HashMap.class);
    }
}





