package com.fedex.mets.entity.mets;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "EVENT_MSNS")
public class EventMsns {

    @Id
    @Column(name = "EVENT_ID", nullable = false)
    private Long eventId;

    @Column(name = "ATA", nullable = false)
    private String ata;

    @Column(name = "DISC_NUM", nullable = false)
    private String discNum;

    @Column(name = "MSN", nullable = false)
    private Long msn;
}
