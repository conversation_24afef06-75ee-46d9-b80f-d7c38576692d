package com.fedex.mets.entity.mets;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "rpt_catg_level_1")
public class RptCatgLevel1 {

    @Id
    @Column(name = "LEVEL_1_ID", nullable = false)
    private String level1Id;

    @Column(name = "LEVEL_1_NAME", nullable = false)
    private String level1Name;

    @Column(name = "DESCRIPTION")
    private String description;

    @Column(name = "ADD_PROMPT")
    private String addPrompt;

    @Column(name = "REQ_ADD_PROMPT")
    private String reqAddPrompt;

    @Column(name = "CLOSE_PROMPT")
    private String closePrompt;

    @Column(name = "REQ_CLOSE_PROMPT")
    private String reqClosePrompt;

    @Column(name = "MULTISELECT")
    private String multiselect;

    @Column(name = "LIST_ORDER")
    private Integer listOrder;

    @Column(name = "ACTIVE_CATG", nullable = false)
    private String activeCatg;

    @Column(name = "GROUP_ID", nullable = false)
    private String groupId;

    @Column(name = "LAST_UPDATE_DT_TM")
    private Timestamp lastUpdateDtTm;
}