package com.fedex.mets.controller;

import com.fedex.mets.data.WizardEventData;
import com.fedex.mets.dto.MetsResponse;
import com.fedex.mets.service.closeEvent.EventCloseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;

@RestController
@RequestMapping("/api/mets")
@Tag(name = "METS CLOSE EVENT", description = "Endpoint for closing an event.")
public class MetsCloseEventController {
    private static final Logger logger = LoggerFactory.getLogger(MetsCloseEventController.class);

    @Autowired
    private EventCloseService eventCloseService;

    @Operation(summary = "Closing an event.", description = "Closing an event in METS system.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully closed an event."),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @PostMapping(path = "/closeEvent")
    public ResponseEntity<MetsResponse> closeEvent(@RequestBody WizardEventData request) throws Exception {
        MetsResponse closeEventResponse = new MetsResponse();
        HashMap hashmap = new HashMap();
        String userId = request.getUserId();
        String tokenId = request.getTokenId();
        try {
            if (request != null) {
                if (!userId.isEmpty() && !tokenId.isEmpty()) {
                    logger.info(".....calling the closeEvent");
                    hashmap = eventCloseService.closeEvent(request);
                    if (hashmap != null) {
                        closeEventResponse.setData(hashmap);
                    } else {
                        return ResponseEntity.status(500).build();
                    }
                } else {
                    logger.info("One or more essential parameters are missing from the request.");
                }
            }
            return ResponseEntity.ok(closeEventResponse);
        } catch (IllegalArgumentException e) {
            logger.error("Invalid input for close Event: Wizard Event: {}. Error: {}", request, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error closing an event", e);
            return ResponseEntity.status(500).build();
        }
    }
}
