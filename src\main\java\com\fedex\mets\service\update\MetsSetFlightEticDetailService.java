package com.fedex.mets.service.update;

import com.fedex.mets.data.EventFlightEticData;
import com.fedex.mets.data.EventFlightEticDataEntity;
import com.fedex.mets.data.MetsEventUpdateEntity;
import com.fedex.mets.entity.mets.EventFlightDelays;
import com.fedex.mets.entity.mets.EventFlightInfo;
import com.fedex.mets.entity.mets.EventFltInfoPk;
import com.fedex.mets.entity.mets.Events;
import com.fedex.mets.entity.mss.ForteLeg;
import com.fedex.mets.repository.mets.EventFlightDelaysRepository;
import com.fedex.mets.repository.mets.EventFlightInfoRepository;
import com.fedex.mets.repository.mets.EventsRepository;
import com.fedex.mets.repository.mss.ForteLegRepository;
import com.fedex.mets.service.retrieval.EventFlightEticInfoService;
import com.fedex.mets.util.IServerConstants;
import com.fedex.mets.util.ServerDateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.rmi.RemoteException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class MetsSetFlightEticDetailService {

    private static final Logger logger = LoggerFactory.getLogger(MetsSetFlightEticDetailService.class);
    @Autowired
    EventFlightInfoRepository eventFlightInfoRepository;

    @Autowired
    EventsRepository eventsRepository;

    @Autowired
    EventFlightDelaysRepository eventFlightDelaysRepository;

    @Autowired
    ForteLegRepository forteLegRepository;

    @Autowired
    EventFlightEticInfoService eventFlightEticInfoService;


    public Map<String, Object> setFlightEticDetail(MetsEventUpdateEntity request, Map<String, Object> hashMap,
                                                   EventFlightEticDataEntity eventFlightEticData, String flag, String userId, String tokenId, Boolean eventActive) throws Exception {
        // TODO Token Validation
        boolean resultFromBean = false, isEventActive = true;
        EventFlightEticData elements = new EventFlightEticData();
        if (eventActive != null) {
            isEventActive = eventActive;
        }
        int eventId = eventFlightEticData.getEventId();
        if (flag.equalsIgnoreCase("EDIT")) {
            resultFromBean = editEventFlightEtic(eventFlightEticData, userId, tokenId, isEventActive);
        }
        if (resultFromBean) {

            elements = eventFlightEticInfoService.getFlightEticDetail(eventId);
            if (elements != null) {
                hashMap.put(IServerConstants.FLIGHT_ETIC_DETAIL, elements);
            } else {
                hashMap.put(
                        IServerConstants.ERROR,
                        "No Records Found for the Event Id " + eventId);
            }
        }
        return hashMap;
    }

    /**
     * The following editEventFlightEtic() is used to edit ETIC Info of a particular event.
     *
     * @return boolean result.
     * @params EventFlightEticData eventFlightEticData, String userId, String tokenId.
     */
    public boolean editEventFlightEtic(EventFlightEticDataEntity eventFlightEticData, String userId, String tokenId, Boolean eventActive) throws RemoteException {
        boolean result = false;
        boolean updateEventDelays = false;

        // TODO Token Validation and Check the Access Level
        EventFlightInfo eventFlightInfoO = null; //with flightFlag 'O' only
        EventFlightInfo eventFlightInfoB = null; //with flightFlag 'B' both
        EventFlightInfo eventFlightInfoA = null;//with flightFlag 'A' affected
        int eventId = 0;
        String flightNumber = "",
                flightDate = "",
                flightLegNumber = "";
        eventId = eventFlightEticData.getEventId();
        flightNumber = eventFlightEticData.getFlightNumber();
        //Flight date is used to get the lookup date which is used in the forte leg query
        flightDate = eventFlightEticData.getFlightDate();
        flightLegNumber = eventFlightEticData.getFlightLegNumber();
        if (eventFlightEticData.isFlightInfoModified()) {
            if (flightNumber == null) {
                try {
                    // TODO Implement deleteAffectedFlight() method check is this need or not
//					result = deleteAffectedFlight(eventId);
                } catch (Exception delete) {
                    throw new RemoteException(delete.getMessage());
                }
            } else if (flightNumber != null && flightNumber.trim().length() == 0) {
                try {
                    // TODO Implement deleteAffectedFlight() method
//					result = deleteAffectedFlight(eventId);
                } catch (Exception delete) {
                    throw new RemoteException(delete.getMessage());
                }
            } else {
                ArrayList<String> flightDelayCodes = new ArrayList<>();
                ArrayList<Integer> flightDelayTimes = new ArrayList<>();
                String strFlightNumber = "",
                        strFlightLegNumber = "",
                        strFlightACN = "",
                        strFlightDestination = "",
                        strFlightStatus = "",
                        strFlightType = "",
                        strFlightOrigniation = "",
                        strTotalDelay = "";
                Timestamp schDepartureTime = null,
                        actDepartureTime = null,
                        schArrivalTime = null,
                        actArrivalTime = null;
                Timestamp dateFlightDate = null;
//                String strLookupDate = ServerDateHelper.getLookUpFormatHH_MM(flightDate);
                //TODO NEED TO PASS FLIGHT NUMBER and FORTE LEG and STRLOOKUPDATE to ForteLeg query
                ForteLeg forteLeg = forteLegRepository.findByFltNbrAndFltZuluDtAndLegNbr();
                //TODO NEED TO check strFlightNumber and strFlightACN values as strFlightACN should be getting the forteLeg.getFltNbr() and strFlightNumber should be getting the forteLeg.getTailNbr()
                strFlightNumber = forteLeg.getFltNbr();
                if (strFlightNumber == null) {
                    strFlightNumber = flightNumber;
                }
                strFlightLegNumber = forteLeg.getLegNbr();
                if (strFlightLegNumber == null) {
                    strFlightLegNumber = flightLegNumber;
                }
                dateFlightDate = forteLeg.getFltZuluDate();
                //TODO need to uncomment the below line
//                if (dateFlightDate == null) {
//                    dateFlightDate = ServerDateHelper.getConvertedSQLDate(flightDate);
//                }
                strFlightACN = forteLeg.getTailNbr();
                strFlightDestination = forteLeg.getIataDestCd();
                strFlightStatus = forteLeg.getLegStatDesc();
                strFlightType = forteLeg.getLegTypeCd();
                schDepartureTime = forteLeg.getSchedOutZuluTmstp();
                actDepartureTime = forteLeg.getActlOutZuluTmstp();
                strTotalDelay = forteLeg.getTotDelayQty();
                strFlightOrigniation = forteLeg.getIataOrigCd();
                schArrivalTime = forteLeg.getSchedInZuluTmstp();
                actArrivalTime = forteLeg.getActlInZuluTmstp();
                //Below Try block is to dynamically enter the delay codes and delay times into the list
                try {
                    for (int i = 1; i <= 10; i++) {
                        String methodName = "getDelayCd" + i;
                        String methodNameTime = "getDelayQty" + i;
                        Method method = ForteLeg.class.getMethod(methodName);
                        Method methodTime = ForteLeg.class.getMethod(methodNameTime);
                        Object value = method.invoke(forteLeg);
                        Object valueTime = methodTime.invoke(forteLeg);
                        if (value != null) {
                            flightDelayCodes.add(value.toString());
                        }
                        if (valueTime != null) {
                            flightDelayTimes.add(Integer.parseInt(valueTime.toString()));
                        }
                    }
                } catch (Exception e) {
                    logger.error("ERROR MetsUpdate Servlet setFlightEticDetail() exec1 >> " + e.getMessage());
                    e.printStackTrace();
                }
                try {
                    eventFlightInfoO = eventFlightInfoRepository.getEventFlightInfo(eventId, 'O');
                    eventFlightInfoA = eventFlightInfoRepository.getEventFlightInfo(eventId, 'A');
                    eventFlightInfoB = eventFlightInfoRepository.getEventFlightInfo(eventId, 'B');
                } catch (Exception remote) {
                    // TODO create global exception class
                    logger.error("ERROR MetsUpdate Servlet setFlightEticDetail() exec1 >> " + remote.getMessage());
                    throw new RemoteException(remote.getMessage());
                    //return false;
                }
                					/*
					Case 1: no records for event id with flight flags of "A" or "O" or "B"
					insert record into table with flt flag = "A" with all appropriate flight info
					*/
                if (eventFlightInfoO == null
                        && eventFlightInfoA == null
                        && eventFlightInfoB == null) {
                    try {
                        EventFlightInfo newData = new EventFlightInfo();

                        newData.setEventFltInfoPk(new EventFltInfoPk(eventId, 'A'));
                        newData.setFlightNumber(strFlightNumber);
                        newData.setFlightDate(dateFlightDate);
                        newData.setFlightLeg(strFlightLegNumber);
                        newData.setFlightACN(strFlightACN);
                        newData.setFlightDestination(strFlightDestination);
                        newData.setFlightStatus(strFlightStatus);
                        newData.setFlightType(strFlightType);
                        newData.setFlightSchedDeptDateTime(schDepartureTime);
                        newData.setFlightActualDeptDateTime(actDepartureTime);
                        newData.setFlightOrigin(strFlightOrigniation);
                        newData.setFlightSchedArrivalDateTime(schArrivalTime);
                        newData.setFlightActualArrivalDateTime(actArrivalTime);


                        if (strTotalDelay != null && strTotalDelay.trim().length() > 0) {
                            int totalDelayTime = Integer.parseInt(strTotalDelay.trim());
                            newData.setFlightTotalDelay(totalDelayTime);
                        } else {
                            newData.setFlightTotalDelay(0);
                        }

                        eventFlightInfoRepository.save(newData);
                        updateEventDelays = true;

                        result = true;
                    } catch (Exception e) {
                        logger.error("ERROR editEventFlightEtic() eventFlightInfoA >> >> " + e.getMessage());
                        throw new RemoteException(e.getMessage());
                    }
                }
                /*
					Case 2: no record with flight flag = "A" in table for event id but record with flt flag = "O" exists
					if record with flt flag = "O" has the same values for flt #, flt date, flt leg as the new values then change the flt flag = "B"
					on the existing record and update any other info on the same record (e.g. departure date/time, etc.)
					else if record with flt flag="O" and the { flt# / flt date / flt leg } are different then insert a new record with flt flag "A"
					*/
                else if (eventFlightInfoA == null && eventFlightInfoO != null) {
                    EventFlightInfo eventFlightInfoRecordO = eventFlightInfoRepository.getEventFlightInfoByFltNumAndEventIdAndFlag(eventId, 'O', strFlightNumber, strFlightLegNumber);
                    if (eventFlightInfoRecordO != null) {
                        try {
                            eventFlightInfoRepository.delete(eventFlightInfoRecordO);
                            EventFlightInfo newEventFlightInfoData = new EventFlightInfo();
                            newEventFlightInfoData.setFlightNumber(strFlightNumber);
                            newEventFlightInfoData.setFlightDate(dateFlightDate);
                            newEventFlightInfoData.setFlightLeg(strFlightLegNumber);
                            newEventFlightInfoData.setFlightACN(strFlightACN);
                            newEventFlightInfoData.setFlightDestination(strFlightDestination);
                            newEventFlightInfoData.setFlightStatus(strFlightStatus);
                            newEventFlightInfoData.setFlightType(strFlightType);
                            newEventFlightInfoData.setFlightSchedDeptDateTime(schDepartureTime);
                            newEventFlightInfoData.setFlightActualDeptDateTime(actDepartureTime);
                            newEventFlightInfoData.setFlightOrigin(strFlightOrigniation);
                            newEventFlightInfoData.setFlightSchedArrivalDateTime(schArrivalTime);
                            newEventFlightInfoData.setFlightActualArrivalDateTime(actArrivalTime);
                            newEventFlightInfoData.setEventFltInfoPk(new EventFltInfoPk(eventId, 'B'));
                            if (strTotalDelay != null && strTotalDelay.trim().length() > 0) {
                                int totalDelayTime = Integer.parseInt(strTotalDelay.trim());
                                newEventFlightInfoData.setFlightTotalDelay(totalDelayTime);
                            } else {
                                newEventFlightInfoData.setFlightTotalDelay(0);
                            }

                            eventFlightInfoRepository.save(newEventFlightInfoData);
                            updateEventDelays = true;

                            result = true;
                        } catch (Exception e) {
                            logger.error("ERROR editEventFlightEtic() eventFlightO>> >> " + e.getMessage());
                            throw new RemoteException(e.getMessage());
                        }
                    } else {
                        try {
                            EventFlightInfo newData = new EventFlightInfo();

//                            newData.setEventId(eventId);
                            newData.setEventFltInfoPk(new EventFltInfoPk(eventId, 'A'));
                            newData.setFlightNumber(strFlightNumber);
                            newData.setFlightDate(dateFlightDate);
                            newData.setFlightLeg(strFlightLegNumber);
                            newData.setFlightACN(strFlightACN);
                            newData.setFlightDestination(strFlightDestination);
                            newData.setFlightStatus(strFlightStatus);
                            newData.setFlightType(strFlightType);
                            newData.setFlightSchedDeptDateTime(schDepartureTime);
                            newData.setFlightActualDeptDateTime(actDepartureTime);
                            newData.setFlightOrigin(strFlightOrigniation);
                            newData.setFlightSchedArrivalDateTime(schArrivalTime);
                            newData.setFlightActualArrivalDateTime(actArrivalTime);

//                            newData.setFlightFlag('A');

                            if (strTotalDelay != null && strTotalDelay.trim().length() > 0) {
                                int totalDelayTime = Integer.parseInt(strTotalDelay.trim());
                                newData.setFlightTotalDelay(totalDelayTime);
                            } else {
                                newData.setFlightTotalDelay(0);
                            }

                            eventFlightInfoRepository.save(newData);
                            updateEventDelays = true;

                            result = true;
                        } catch (Exception e) {
                            logger.error("ERROR editEventFlightEtic() eventFlightA>> >> " + e.getMessage());
                            throw new RemoteException(e.getMessage());
                        }
                    }
                }
            		/*
					Case 3: no record with flight flag = "A" in table for event id but record with flt flag = "B" exists
					if record with flt flag = "B" has the same values for flt #, flt date, flt leg as the new values then just
					update any other info on the same record (e.g. departure date/time, etc.)
					else if record with flt flag="B" and the { flt# / flt date / flt leg } are different then insert a new record with flt flag "A",
					and change the flt flag value of the existing record from "B" to "O"
					*/
                else if (eventFlightInfoA == null && eventFlightInfoB != null) {
                    if (eventFlightInfoB.getFlightNumber().equalsIgnoreCase(strFlightNumber)
                            && eventFlightInfoB.getFlightDate().compareTo(dateFlightDate) == 0
                            && eventFlightInfoB.getFlightLeg().equalsIgnoreCase(strFlightLegNumber)
                            && eventFlightInfoB.getEventFltInfoPk().getEventId() == eventId
                    ) {
                        try {
                            eventFlightInfoB.setFlightNumber(strFlightNumber);
                            eventFlightInfoB.setFlightDate(dateFlightDate);
                            eventFlightInfoB.setFlightLeg(strFlightLegNumber);
                            eventFlightInfoB.setFlightACN(strFlightACN);
                            eventFlightInfoB.setFlightDestination(strFlightDestination);
                            eventFlightInfoB.setFlightStatus(strFlightStatus);
                            eventFlightInfoB.setFlightType(strFlightType);
                            eventFlightInfoB.setFlightSchedDeptDateTime(schDepartureTime);
                            eventFlightInfoB.setFlightActualDeptDateTime(actDepartureTime);
                            eventFlightInfoB.setFlightOrigin(strFlightOrigniation);
                            eventFlightInfoB.setFlightSchedArrivalDateTime(schArrivalTime);
                            eventFlightInfoB.setFlightActualArrivalDateTime(actArrivalTime);

                            if (strTotalDelay != null && strTotalDelay.trim().length() > 0) {
                                int totalDelayTime = Integer.parseInt(strTotalDelay.trim());
                                eventFlightInfoB.setFlightTotalDelay(totalDelayTime);
                            } else {
                                eventFlightInfoB.setFlightTotalDelay(0);
                            }
                            eventFlightInfoRepository.save(eventFlightInfoB);
                            updateEventDelays = true;

                            result = true;
                        } catch (Exception e) {
                            logger.error(" ERROR editEventFlightEtic() outbound>> >> " + e.getMessage());
                            throw new RemoteException(e.getMessage());
                        }
                    } else {
                        try {
                            EventFlightInfo newData = new EventFlightInfo();

//                            newData.setEventId(eventId);
                            newData.setFlightNumber(strFlightNumber);
                            newData.setFlightDate(dateFlightDate);
                            newData.setFlightLeg(strFlightLegNumber);
                            newData.setFlightACN(strFlightACN);
                            newData.setFlightDestination(strFlightDestination);
                            newData.setFlightStatus(strFlightStatus);
                            newData.setFlightType(strFlightType);
                            newData.setFlightSchedDeptDateTime(schDepartureTime);
                            newData.setFlightActualDeptDateTime(actDepartureTime);
                            newData.setFlightOrigin(strFlightOrigniation);
                            newData.setFlightSchedArrivalDateTime(schArrivalTime);
                            newData.setFlightActualArrivalDateTime(actArrivalTime);
                            newData.setEventFltInfoPk(new EventFltInfoPk(eventId, 'A'));
//                            newData.setFlightFlag('A');

                            if (strTotalDelay != null && strTotalDelay.trim().length() > 0) {
                                int totalDelayTime = Integer.parseInt(strTotalDelay.trim());
                                newData.setFlightTotalDelay(totalDelayTime);
                            } else {
                                newData.setFlightTotalDelay(0);
                            }

                            eventFlightInfoRepository.save(newData);
                            updateEventDelays = true;

//                            eventFlightInfoB.setFlightFlag('O');
                            eventFlightInfoRepository.save(eventFlightInfoB);

                            result = true;
                        } catch (Exception create) {
                            logger.error(
                                    " ERROR editEventFlightEtic()  eventFlightInfoA ***-- >>"
                                            + create.getMessage());

                            throw new RemoteException(create.getMessage());
                        }
                    }
                }
                /*
					Case 4: record with flight flag = "A" and flt flag = "O" exists for the event id
					if record with flt flag = "O" has the same values for flt #, flt date, flt leg as the new values then change
					flt flag from "O" to "B" on the "O" record (+ update any other info. on that record) and delete the existing flt flag = "A" record
					else if record with flt flag="O" and the { flt# / flt date / flt leg } are different then update the existing  flt flag "A" record
					*/
                else if (eventFlightInfoA != null && eventFlightInfoO != null) {
                    if (eventFlightInfoO.getFlightNumber().equalsIgnoreCase(strFlightNumber)
                            && eventFlightInfoO.getFlightDate().compareTo(dateFlightDate) == 0
                            && eventFlightInfoO.getFlightLeg().equalsIgnoreCase(strFlightLegNumber)
                            && eventFlightInfoO.getEventFltInfoPk().getEventId() == eventId
                    ) {
                        try {
                            eventFlightInfoRepository.delete(eventFlightInfoO);
                            EventFlightInfo eventFlightInfo = new EventFlightInfo();
                            eventFlightInfo.setFlightNumber(strFlightNumber);
                            eventFlightInfo.setFlightDate(dateFlightDate);
                            eventFlightInfo.setFlightLeg(strFlightLegNumber);
                            eventFlightInfo.setFlightACN(strFlightACN);
                            eventFlightInfo.setFlightDestination(strFlightDestination);
                            eventFlightInfo.setFlightStatus(strFlightStatus);
                            eventFlightInfo.setFlightType(strFlightType);
                            eventFlightInfo.setFlightSchedDeptDateTime(schDepartureTime);
                            eventFlightInfo.setFlightActualDeptDateTime(actDepartureTime);
                            eventFlightInfo.setFlightOrigin(strFlightOrigniation);
                            eventFlightInfo.setFlightSchedArrivalDateTime(schArrivalTime);
                            eventFlightInfo.setFlightActualArrivalDateTime(actArrivalTime);
                            eventFlightInfo.setEventFltInfoPk(new EventFltInfoPk(eventId, 'B'));
                            if (strTotalDelay != null && strTotalDelay.trim().length() > 0) {
                                int totalDelayTime = Integer.parseInt(strTotalDelay.trim());
                                eventFlightInfo.setFlightTotalDelay(totalDelayTime);
                            } else {
                                eventFlightInfo.setFlightTotalDelay(0);
                            }

//                            eventFlightInfoO.setFlightFlag('B');

                            eventFlightInfoRepository.save(eventFlightInfo);
                            updateEventDelays = true;

                            eventFlightInfoRepository.delete(eventFlightInfoA);

                            result = true;
                        } catch (Exception e) {
                            logger.error(
                                    " ERROR editEventFlightEtic()  eventFlightInfoA * removeFlight *-- >>");
                        }
                    } else {
                        try {
                            eventFlightInfoA.setFlightNumber(strFlightNumber);
                            eventFlightInfoA.setFlightDate(dateFlightDate);
                            eventFlightInfoA.setFlightLeg(strFlightLegNumber);
                            eventFlightInfoA.setFlightACN(strFlightACN);
                            eventFlightInfoA.setFlightDestination(strFlightDestination);
                            eventFlightInfoA.setFlightStatus(strFlightStatus);
                            eventFlightInfoA.setFlightType(strFlightType);
                            eventFlightInfoA.setFlightSchedDeptDateTime(schDepartureTime);
                            eventFlightInfoA.setFlightActualDeptDateTime(actDepartureTime);
                            eventFlightInfoA.setFlightOrigin(strFlightOrigniation);
                            eventFlightInfoA.setFlightSchedArrivalDateTime(schArrivalTime);
                            eventFlightInfoA.setFlightActualArrivalDateTime(actArrivalTime);

                            if (strTotalDelay != null && strTotalDelay.trim().length() > 0) {
                                int totalDelayTime = Integer.parseInt(strTotalDelay.trim());
                                eventFlightInfoA.setFlightTotalDelay(totalDelayTime);
                            } else {
                                eventFlightInfoA.setFlightTotalDelay(0);
                            }

                            eventFlightInfoRepository.save(eventFlightInfoA);
                            updateEventDelays = true;

                            result = true;
                        } catch (Exception e) {
                            logger.error(
                                    " ERROR editEventFlightEtic()  eventFlightInfoA create-- >>"
                                            + e.getMessage());
                            throw new RemoteException(e.getMessage());
                        }
                    }
                }
                				/*
					Case 5: record with flight flag = "A" and no record with flt flag = "O" exists for the event id
					for this case, is the record with flt flag = "B" not considered at all ? irrespective of records (found/not found) in database,
					the record with flt flag "A" is updated with the new values ??
					there should never be a case where there is a flt flag ="B" and flt flag="O" for the same event id; also should never be a case
					where there is a flt flag ="A" and flt flg="B" for the same event id; else messed up and saved something incorrectly!!!!
					just update all info. on the existing flt flag = "A" record.
					*/
                else if (eventFlightInfoA != null && eventFlightInfoO == null) {
                    //data Object with flightFlag 'A' affected

                    eventFlightInfoA.setFlightNumber(strFlightNumber);
                    eventFlightInfoA.setFlightDate(dateFlightDate);
                    eventFlightInfoA.setFlightLeg(strFlightLegNumber);
                    eventFlightInfoA.setFlightACN(strFlightACN);
                    eventFlightInfoA.setFlightDestination(strFlightDestination);
                    eventFlightInfoA.setFlightStatus(strFlightStatus);
                    eventFlightInfoA.setFlightType(strFlightType);
                    eventFlightInfoA.setFlightSchedDeptDateTime(schDepartureTime);
                    eventFlightInfoA.setFlightActualDeptDateTime(actDepartureTime);
                    eventFlightInfoA.setFlightOrigin(strFlightOrigniation);
                    eventFlightInfoA.setFlightSchedArrivalDateTime(schArrivalTime);
                    eventFlightInfoA.setFlightActualArrivalDateTime(actArrivalTime);

                    if (strTotalDelay != null && strTotalDelay.trim().length() > 0) {
                        int totalDelayTime = Integer.parseInt(strTotalDelay.trim());
                        eventFlightInfoA.setFlightTotalDelay(totalDelayTime);
                    } else {
                        eventFlightInfoA.setFlightTotalDelay(0);
                    }
                    eventFlightInfoRepository.save(eventFlightInfoA);

                    updateEventDelays = true;

                    result = true;
                }
                /*To update the Event_flt_delays table...*/

                if (updateEventDelays) {
                    List<EventFlightDelays> eventFlightDelaysList = eventFlightDelaysRepository.getAllByEventId(eventId);
                    if (eventFlightDelaysList.size() > 0) {
                        eventFlightDelaysList.stream().forEach(eventFlightDelays -> {
                            eventFlightDelaysRepository.delete(eventFlightDelays);
                        });
                    }
                    for (int i = 0; i < flightDelayCodes.size(); i++) {
                        if (flightDelayCodes.get(i) != null && flightDelayCodes.get(i).trim().length() > 0) {
                            EventFlightDelays eventFlightDelays = new EventFlightDelays();
                            eventFlightDelays.setEventId(eventId);
                            eventFlightDelays.setDelayCode(flightDelayCodes.get(i));
                            eventFlightDelays.setDelayTime(flightDelayTimes.get(i));
                            eventFlightDelaysRepository.save(eventFlightDelays);
                        }
                    }

                }

            }
        }
        /*		New method added to update the Initial Etic Value in the Events Table. (08-14-2003)		*/
        if (eventFlightEticData.isEticNumberModified()) {
            Events events = eventsRepository.getEventsByEventId(eventId);
            Timestamp updatedTimeStamp = ServerDateHelper.getTimeStamp();
            Timestamp eticInitialTimeStamp = null;
            if (eventFlightEticData.getInitialEtic() != null
                    && eventFlightEticData.getInitialEtic().trim().length() > 0) {
                eticInitialTimeStamp =
                        ServerDateHelper.getConvertedTimestamp(
                                eventFlightEticData.getInitialEtic());
            }
            if (events != null) {
                events.setEticNumber(eventFlightEticData.getEticNumber());
                events.setEticInitial(eticInitialTimeStamp);
                events.setLastUpdateDateTime(updatedTimeStamp);
                events.setLastUpdatedBy(userId);
                eventsRepository.save(events);
                result = true;
            }
        }

        return result;
    }
}
