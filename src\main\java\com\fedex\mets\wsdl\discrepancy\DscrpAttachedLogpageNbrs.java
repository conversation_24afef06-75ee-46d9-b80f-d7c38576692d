
package com.fedex.mets.wsdl.discrepancy;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for DscrpAttachedLogpageNbrs complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="DscrpAttachedLogpageNbrs">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="aircraftNbr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ataNbr" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="dscrpNbr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="logpageNbrs" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "DscrpAttachedLogpageNbrs", propOrder = {
    "aircraftNbr",
    "ataNbr",
    "dscrpNbr",
    "logpageNbrs"
})
public class DscrpAttachedLogpageNbrs {

    @XmlElement(required = true)
    protected String aircraftNbr;
    @XmlElement(required = true)
    protected BigDecimal ataNbr;
    @XmlElement(required = true)
    protected String dscrpNbr;
    protected List<String> logpageNbrs;

    /**
     * Gets the value of the aircraftNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAircraftNbr() {
        return aircraftNbr;
    }

    /**
     * Sets the value of the aircraftNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAircraftNbr(String value) {
        this.aircraftNbr = value;
    }

    /**
     * Gets the value of the ataNbr property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAtaNbr() {
        return ataNbr;
    }

    /**
     * Sets the value of the ataNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAtaNbr(BigDecimal value) {
        this.ataNbr = value;
    }

    /**
     * Gets the value of the dscrpNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDscrpNbr() {
        return dscrpNbr;
    }

    /**
     * Sets the value of the dscrpNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDscrpNbr(String value) {
        this.dscrpNbr = value;
    }

    /**
     * Gets the value of the logpageNbrs property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the logpageNbrs property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getLogpageNbrs().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link String }
     * 
     * 
     */
    public List<String> getLogpageNbrs() {
        if (logpageNbrs == null) {
            logpageNbrs = new ArrayList<String>();
        }
        return this.logpageNbrs;
    }

}
