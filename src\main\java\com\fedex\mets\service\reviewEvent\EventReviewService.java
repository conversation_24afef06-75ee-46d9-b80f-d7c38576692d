package com.fedex.mets.service.reviewEvent;

import com.fedex.mets.data.EventDiscrepancyListData;
import com.fedex.mets.data.ReportCategoriesKeyValueData;
import com.fedex.mets.data.WizardEventData;
import com.fedex.mets.entity.mets.*;
import com.fedex.mets.repository.mets.*;
import com.fedex.mets.util.ETICHelper;
import com.fedex.mets.util.IServerConstants;
import com.fedex.mets.util.ServerDateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;

@Service
public class EventReviewService {
    private static final Logger logger = LoggerFactory.getLogger(EventReviewService.class);
    Events events = null;
    ChangeRequest changeRequest = null;
    ChangeRequest newChangeRequestData = null;
    ChangeRequestHistory changeRequestHistory = null;
    ChangeRequestHistory newChangeRequestHistoryData = null;
    ChangeRequestLog changeRequestLog = null;
    ChangeRequestLog newChangeRequestLogData = null;
    EventTypeHistory eventTypeHistory = null;
    @Autowired
    private ChangeRequestRepository changeRequestRepository;
    @Autowired
    private EventsRepository eventsRepository;
    @Autowired
    private ChangeRequestHistoryRepository changeRequestHistoryRepository;
    @Autowired
    private ChangeRequestLogRepository changeRequestLogRepository;
    @Autowired
    private EventTypeHistoryRepository eventTypeHistoryRepository;
    @Autowired
    private EventTfNotesRepository eventTfNotesRepository;
    @Autowired
    private EventRepCatgRepository eventRepCatgRepository;
    @Autowired
    private EventMaxiDiscRepository eventMaxiDiscRepository;
    @Autowired
    private EventMaxiDwningItmRepository eventMaxiDwningItmRepository;
    @Autowired
    private EventTimersRepository eventsTimerRepository;
    @Autowired
    private SuperUpdateHelper superUpdateHelper;

    public HashMap reviewEvent(WizardEventData wizardEventData) throws Exception {
        HashMap hashmap = new HashMap();
        HashMap returnHashMap = new HashMap();
        String strEticDateTime = wizardEventData.getNewEticDateTime();
        int activeEvent = 0;
        int activeDOAEvent = 0;
        String doaEticText = "", doaEticComment = "", doaStatus = "", doaOST = "", doaEticRsnCd = "", doaEticRsnComment = "";
        java.sql.Timestamp eticDateTimeStamp = null, reviewedDateTimeStamp = null, doaEticDateTime = null;

        try {
            String _activeEvent = "";
            _activeEvent = eventsRepository.getActiveEvent(wizardEventData.getEventId());
            if (null != _activeEvent && _activeEvent.trim().equalsIgnoreCase("N")) {
                logger.info("ERROR ##   Event is No longer active to continue with review process.");
                returnHashMap.put(IServerConstants.ERROR, "Event is No longer active to continue with review process.");
                return returnHashMap;
            } else {
                logger.info("Event is active to continue with review process.");
            }
        } catch (Exception e) {
            logger.error("Error in review Event method: " + e.getMessage());
            hashmap.put("error", e.getMessage());
        }
        try {
            if (strEticDateTime != null) {
                eticDateTimeStamp = ServerDateHelper.getConvertedTimestamp(strEticDateTime);
                wizardEventData.setNewEticDateTime(String.valueOf(eticDateTimeStamp));
            }
            reviewedDateTimeStamp = ServerDateHelper.getTimeStamp();
            wizardEventData.setReviewedDateTime(reviewedDateTimeStamp);

        } catch (Exception convert) {
            logger.info("ERROR Review Event reviewEvent() convert time stamp >> " + convert.getMessage());
            returnHashMap = null;
            return returnHashMap;
        }
        try {
            String strLookupDate = ServerDateHelper.getLookUpFormat(wizardEventData.getChangeRequestLastUpdated());
            logger.info("strLookupDate >> " + strLookupDate);
            activeEvent = changeRequestRepository.getActiveEventCountByEventStatusUp(wizardEventData.getEventId(), strLookupDate);
            if (activeEvent == 0) {
                logger.info("ERROR ##   This alert has already been reviewed.");
                hashmap.put(IServerConstants.ERROR, "This alert has already been reviewed.");
                return hashmap;
            }
            String doaEventResult = eventsRepository.getEarliestDOAEvent(wizardEventData.getACN());
            String[] doaEvent = doaEventResult != null ? doaEventResult.split(",") : null;
            if (doaEventResult != null && doaEventResult.trim().length() > 0) {
                doaEticDateTime = Timestamp.valueOf(doaEvent[0]);
                doaEticText = doaEvent[1];
                doaEticComment = doaEvent[2];
                doaStatus = doaEvent[3];
                doaOST = doaEvent[4];
                doaEticRsnCd = doaEvent[5];
                doaEticRsnComment = doaEvent[6];
                if (doaStatus != null && doaStatus.trim().length() > 0) {
                    activeDOAEvent = activeDOAEvent + 1;
                }
                logger.info("Existing active DOA event====== " + activeDOAEvent);
            }
        } catch (Exception e) {
            logger.error("Error in EventReviewService: ", e.getMessage());
            returnHashMap = null;
            return returnHashMap;
        }
        try {
            events = eventsRepository.getEventsByEventId(wizardEventData.getEventId());
            if (events != null) {
                logger.info("before updating the Change Request for the event " + wizardEventData.getEventId());
                java.sql.Timestamp changeRequestCreatedTime = null, changeRequestOldEticTime = null;
                String strOldEticComment = "", strOldEticText = "", strOldStatus = "", strOldOST = "", strOldEticRsnCd = "", strOldEticRsnComment = "";

                changeRequest = changeRequestRepository.getChangeRequestByAcn(wizardEventData.getACN());
                if (changeRequest != null) {
                    //if the submitChange flag is false the record should be deleted from the database. else update the record.
                    if (!wizardEventData.isSubmitChange()) {
                        changeRequestCreatedTime = changeRequest.getCreatedDtTm();
                        changeRequestRepository.delete(changeRequest);
                        changeRequest = null;
                        logger.info("After removing the Change request record as the change is not submitted after reviewing.");
                        /*The following is to find the Record in the ChangeRequestHistory table and UPDATE the existing record Request Status.**/
                        try {
                            logger.info("changeRequestCreatedTime >> " + changeRequestCreatedTime);
                            changeRequestHistory = changeRequestHistoryRepository.getChangeRequestHistory(wizardEventData.getEventId(), wizardEventData.getACN(), changeRequestCreatedTime);
                            if (changeRequestHistory != null) {
                                changeRequestHistoryRepository.updateRequestStatus(wizardEventData.getReviewedDateTime(), wizardEventData.getEventId(), wizardEventData.getACN());
                                logger.info("Updated the Change Request History record Request Status as 'R' as the change is not submitted after reviewing.");
                            }
                        } catch (Exception changeHist) {
                            logger.error(
                                    "ERROR Review Event reviewEvent() Could not find the record in ChangeRequest History table for EventId "
                                            + wizardEventData.getEventId()
                                            + " and ACN "
                                            + wizardEventData.getACN()
                                            + " and Created Date Time >>"
                                            + changeRequestCreatedTime
                                            + " >> "
                                            + changeHist.getMessage());
                        }
                        /*Creating a new Record in the Log Table**/
                        changeRequestLog = new ChangeRequestLog();
                        ChangeRequestLogPk changeRequestLogPk = new ChangeRequestLogPk();
                        changeRequestLogPk.setEventId(wizardEventData.getEventId());
                        changeRequestLogPk.setStatusChangedDtTm(wizardEventData.getReviewedDateTime());
                        changeRequestLogPk.setCreatedDtTm(changeRequestCreatedTime);
                        changeRequestLog.setChangeRequestLogPk(changeRequestLogPk);
                        changeRequestLog.setOldRequestStatus("U");
                        changeRequestLog.setNewRequestStatus("R");
                        try {
                            changeRequestLogRepository.save(changeRequestLog);
                            logger.info("Created a new record in the Change Request Log table as the change is not submitted after reviewing.");
                        } catch (Exception changeReqLog) {
                            logger.warn("ERROR Unable to create record in changeReqLog table" + changeReqLog.getMessage());
                            events = null;
                        }

                        /*The following is to create a new Record in the EventTypeHistory table.**/
                        try {
                            if (wizardEventData.getAlertType().equals("NEW")) {
                                if (!wizardEventData.isSubmitChange()) {
                                    eventTypeHistory = new EventTypeHistory();
                                    eventTypeHistory.setEventId(wizardEventData.getEventId());
                                    eventTypeHistory.setChangeDateTime(wizardEventData.getReviewedDateTime());
                                    eventTypeHistory.setOldType("OOS");
                                    eventTypeHistory.setNewType("TRK");
                                    eventTypeHistory.setChangedBy(wizardEventData.getUserId());
                                    eventTypeHistoryRepository.save(eventTypeHistory);
                                    logger.info("Created a new record in the EventTypeHistory table as the change is not submitted after reviewing.");
                                }
                            }
                        } catch (Exception eventHistory) {
                            logger.warn("ERROR Review Event reviewEvent() Could not add Event History for the event " + wizardEventData.getEventId() + "\n"
                                    + eventHistory.getMessage());
                            events = null;
                            return returnHashMap;
                        }
                    } else {
                                /*If the type of alert being reviewed is a status alert & the value of "Submit Changes"  is "Yes" &
                                the new status = "UP" & there is an active  "DOA" event on the aircraft then do the following
                                (if more than one DOA event then pick the one with the earliest arriving inbound flight (EVENT_DOA table)

                                In this case want to send the ETIC, Status, Comment from the active DOA event to Super (not the UP status)
                                    a) CHANGE_REQUEST tables
                                        i) Delete the existing change request record
                                        ii) Insert  new records in the CHANGE_REQUEST and CHANGE_REQUEST_HIST
                                        (since this is actually a new change request not just changing the status of an existing change request)**/
                        if (wizardEventData.getAlertType().equals("STATUS")
                                && wizardEventData.getChangeRequestNewStatus().trim().equals("UP")
                                && wizardEventData.isSubmitChange()) {
                            if (activeDOAEvent > 0) {
                                java.sql.Timestamp changeRequestCreatedDtTm = null;
                                //******There is/are Active DOA Events on the Aircraft.
                                logger.info("Creating new Records in the Change request record as There is/are Active DOA Events on the Aircraft");
                                try {
                                    changeRequest = changeRequestRepository.getChangeRequestByEventId(String.valueOf(wizardEventData.getEventId()));
                                    changeRequestCreatedDtTm = changeRequest.getCreatedDtTm();
                                    changeRequestOldEticTime = changeRequest.getNewEticDtTm();
                                    //OLD_ETIC_DT_TM of OOS event being reviewed
                                    strOldEticText = changeRequest.getNewEticText();
                                    //OLD_ETIC_TEXT	of OOS event being reviewed
                                    strOldEticComment = changeRequest.getNewComment();
                                    //OLD_COMMENT	of OOS event being reviewed
                                    strOldStatus = changeRequest.getNewStatus();
                                    //OLD_STATUS	of OOS event being reviewed
                                    strOldOST = changeRequest.getNewOst();
                                    //OLD_OST	of OOS event being reviewed
                                    strOldEticRsnCd = changeRequest.getNewEticRsnCd();
                                    strOldEticRsnComment = changeRequest.getNewEticRsnComment();
                                    changeRequestRepository.delete(changeRequest);
                                    changeRequest = null;
                                } catch (Exception ch) {
                                    logger.warn("ERROR Review Event reviewEvent() ch " + ch.getMessage());
                                }

                                //*****************creating a new record in the change request table*********************
                                newChangeRequestData =
                                        generateNewDOAChangeRequestData(
                                                wizardEventData,
                                                doaEticDateTime,
                                                strOldEticText,
                                                strOldEticComment,
                                                strOldStatus,
                                                changeRequestOldEticTime,
                                                doaEticText,
                                                doaEticComment,
                                                doaStatus,
                                                strOldOST,
                                                doaOST,
                                                strOldEticRsnCd,
                                                doaEticRsnCd,
                                                strOldEticRsnComment,
                                                doaEticRsnComment);
                                logger.info(" Setting the Change type to wizardDataObject >>>>>>>>>>>>>>>>>" + newChangeRequestData.getChangeType());
                                wizardEventData.setChangeType(newChangeRequestData.getChangeType());
                                wizardEventData.setNewStatus(newChangeRequestData.getNewStatus());
                                wizardEventData.setNewEticInfo(newChangeRequestData.getNewEticText());
                                wizardEventData.setNewEticComment(newChangeRequestData.getNewComment());

                                //******************creating a new record in the change request history table********************
                                newChangeRequestHistoryData =
                                        generateNewDOAChangeRequestHistoryData(
                                                wizardEventData,
                                                doaEticDateTime,
                                                strOldEticText,
                                                strOldEticComment,
                                                strOldStatus,
                                                changeRequestOldEticTime,
                                                doaEticText,
                                                doaEticComment,
                                                doaStatus,
                                                strOldOST,
                                                doaOST,
                                                strOldEticRsnCd,
                                                doaEticRsnCd,
                                                strOldEticRsnComment,
                                                doaEticRsnComment);
                                //********************creating a new record in the change request log table.************************
                                newChangeRequestLogData = generateChangeRequestLogData(wizardEventData, changeRequestCreatedDtTm);
                            } else {
                                java.sql.Timestamp changeRequestOldCreatedDtTm = null;
                                //******There is/are NO Active DOA Events on the Aircraft.
                                logger.info("Creating new Records in the Change request record as There is/are **NO** Active DOA Events on the Aircraft");
                                try {
                                    changeRequest = changeRequestRepository.getChangeRequestByEventId(String.valueOf(wizardEventData.getEventId()));
                                    changeRequestOldEticTime = changeRequest.getOldEticDtTm();
                                    //OLD_ETIC_DT_TM of OOS event being reviewed
                                    strOldEticText = changeRequest.getOldEticText();
                                    //OLD_ETIC_TEXT	of OOS event being reviewed
                                    strOldEticComment = changeRequest.getOldComment();
                                    //OLD_COMMENT	of OOS event being reviewed
                                    strOldStatus = changeRequest.getOldStatus();
                                    //OLD_STATUS	of OOS event being reviewed
                                    changeRequestOldCreatedDtTm = changeRequest.getCreatedDtTm();
                                    //OLD_CreatedDtTm of Event being reviewed
                                    strOldOST = changeRequest.getOldOst();
                                    changeRequestRepository.delete(changeRequest);
                                    changeRequest = null;
                                } catch (Exception ch) {
                                    logger.warn("ERROR Review Event reviewEvent() ch **** " + ch.getMessage());
                                }

                                //*****************creating a new record in the change request table*********************
                                newChangeRequestData =
                                        generateNewChangeRequestData(
                                                wizardEventData,
                                                eticDateTimeStamp,
                                                strOldEticText,
                                                strOldEticComment,
                                                strOldStatus,
                                                changeRequestOldEticTime,
                                                changeRequestOldCreatedDtTm,
                                                strOldOST,
                                                strOldEticRsnCd,
                                                strOldEticRsnComment);

                                logger.info(" Setting the Change type to wizardDataObject " + newChangeRequestData.getChangeType());
                                wizardEventData.setChangeType(newChangeRequestData.getChangeType());

                                /*The following is to update the existing record. in the ChangeRequestHistory table.*/
                                try {
                                    logger.info("changeRequestOldCreatedDtTm >> " + changeRequestOldCreatedDtTm);
                                    ChangeRequestHistoryPk primaryKey = new ChangeRequestHistoryPk(wizardEventData.getEventId(), wizardEventData.getACN(), changeRequestOldCreatedDtTm);
                                    changeRequestHistory = changeRequestHistoryRepository.findById(primaryKey).orElse(null);

                                    if (changeRequestHistory != null) {
                                        changeRequestHistory.setNewStatus(wizardEventData.getNewStatus());
                                        changeRequestHistory.setLastUpdateDtTm(wizardEventData.getReviewedDateTime());
                                        changeRequestHistory.setRequestStatus("S");
                                    } else {
                                        //******************creating a new record in the change request history table********************
                                        newChangeRequestHistoryData =
                                                generateNewChangeRequestHistoryData(
                                                        wizardEventData,
                                                        eticDateTimeStamp,
                                                        strOldEticText,
                                                        strOldEticComment,
                                                        strOldStatus,
                                                        changeRequestOldEticTime,
                                                        changeRequestOldCreatedDtTm,
                                                        strOldOST,
                                                        strOldEticRsnCd,
                                                        strOldEticRsnComment);
                                    }
                                } catch (Exception changeHist) {
                                    logger.warn(
                                            "ERROR Review Event reviewEvent() Could not find the record in ChangeRequest History table for EventId "
                                                    + wizardEventData.getEventId()
                                                    + " and ACN "
                                                    + wizardEventData.getACN()
                                                    + " and Created Date Time >>"
                                                    + changeRequestOldCreatedDtTm);
                                }

                                //********************creating a new record in the change request log table.************************
                                try {
                                    newChangeRequestLogData = generateChangeRequestLogData(wizardEventData, newChangeRequestData.getCreatedDtTm());
                                } catch (Exception changeReqLog) {
                                    logger.warn("ERROR Review Event reviewEvent() changeReqLog " + changeReqLog.getMessage());
                                    events = null;
                                }
                            } //end of no Active DOA Events.
                        } //end of if(wizardEventData.getAlertType().equals("STATUS") &&
                        else {
                            /************* IF MOCC User Reviewing an Event whoes AlertType is other than "STATUS" *************************
                             Then update the Change Request table with the Values that the MOCC User had modified or updated****************/
                            logger.info("Updating the Change request,change request history records as the MOCC User Reviewing an Event whoes AlertType is other than STATUS. && UP");
                            logger.info("wizardEventData.getAlertType() " + wizardEventData.getAlertType());
                            changeRequest = changeRequestRepository.findById(wizardEventData.getACN()).orElse(null);
                            changeRequestCreatedTime = changeRequest.getCreatedDtTm();
                            changeRequest.setLastUpdateDtTm(wizardEventData.getReviewedDateTime());
                            changeRequest.setNewStatus(wizardEventData.getNewStatus());
                            changeRequest.setRequestStatus("S");

                            if (wizardEventData.getNewEticInfo() != null) {
                                logger.info("= >>> NewEticText in change Request table >" + wizardEventData.getNewEticInfo());
                                changeRequest.setNewEticText(
                                        wizardEventData.getNewEticInfo().trim());
                            } else {
                                changeRequest.setNewEticText("");
                            }

                            changeRequest.setNewOst(wizardEventData.getNewOST());
                            changeRequest.setNewEticRsnCd(wizardEventData.getNewEticRsnCd());
                            changeRequest.setNewEticRsnComment(wizardEventData.getNewEticRsnComment());

                            if (eticDateTimeStamp != null)
                                changeRequest.setNewEticDtTm(eticDateTimeStamp);

                            if (!wizardEventData.getChangeRequestNewStatus().trim().equals("UP")) {
                                if (wizardEventData.getNewEticComment() != null
                                        && wizardEventData.getNewEticComment().trim().length() > 0) {
                                    changeRequest.setNewComment(
                                            wizardEventData.getNewEticComment().trim());
                                } else {
                                    if (changeRequest.getOldComment() != null)
                                        changeRequest.setNewComment(
                                                changeRequest.getOldComment());
                                }
                            } else {
                                changeRequest.setNewComment("");
                            }

                            //the Client will set if ETIC, Comment or Status is changed while review.
                            changeRequest.setChangeType(wizardEventData.getReviewChangeType());
                            /*The following is  to update the existing record in the ChangeRequestHistory table**/
                            try {
                                logger.info("changeRequestCreatedTime >> " + changeRequestCreatedTime);
                                ChangeRequestHistoryPk primaryKey =
                                        new ChangeRequestHistoryPk(
                                                wizardEventData.getEventId(),
                                                wizardEventData.getACN(),
                                                changeRequestCreatedTime);
                                changeRequestHistory = changeRequestHistoryRepository.findById(primaryKey).orElse(null);
                            } catch (Exception changeHist) {
                                logger.warn(
                                        "ERROR Review Event reviewEvent() Could not find the record in ChangeRequest History table for EventId "
                                                + wizardEventData.getEventId()
                                                + " and ACN "
                                                + wizardEventData.getACN()
                                                + " and Created Date Time >>"
                                                + changeRequestCreatedTime);
                            }
                            try {
                                if (changeRequestHistory != null) {
                                    logger.info("wizardEventData.getAlertType() " + wizardEventData.getAlertType());
                                    logger.info("updating existing record in the Hisotry table after reviewing.");

                                    changeRequestHistory.setNewStatus(wizardEventData.getNewStatus());
                                    changeRequestHistory.setLastUpdateDtTm(wizardEventData.getReviewedDateTime());
                                    changeRequestHistory.setRequestStatus("S");

                                    if (wizardEventData.getNewEticInfo() != null) {
                                        logger.info("= >>> NewEticText in History table >" + wizardEventData.getNewEticInfo());
                                        changeRequestHistory.setNewEticText(wizardEventData.getNewEticInfo().trim());
                                    }

                                    if (eticDateTimeStamp != null) {
                                        changeRequestHistory.setNewEticDtTm(eticDateTimeStamp);
                                    }

                                    if (!wizardEventData.getChangeRequestNewStatus().trim().equals("UP")) {
                                        if (wizardEventData.getNewEticComment() != null
                                                && wizardEventData.getNewEticComment().trim().length()
                                                > 0) {
                                            changeRequestHistory.setNewComment(wizardEventData.getNewEticComment().trim());
                                        } else {
                                            changeRequestHistory.setNewComment(changeRequestHistory.getOldComment());
                                        }
                                    } else {
                                        changeRequestHistory.setNewComment("");
                                    }

                                    changeRequestHistory.setNewOst(wizardEventData.getNewOST());
                                    changeRequestHistory.setNewEticRsnCd(wizardEventData.getNewEticRsnCd());
                                    changeRequestHistory.setNewEticRsnComment(wizardEventData.getNewEticRsnComment());

                                    //the Client will set if ETIC, Comment or Status is changed while review.
                                    changeRequestHistory.setChangeType(wizardEventData.getReviewChangeType());
                                }
                            } catch (Exception changeReqHist) {
                                logger.warn("ERROR in reviewEvent() Could not add Change Request History for the event " + changeReqHist.getMessage());
                                events = null;
                            }

                            /*The following is to create a new Record in the ChangeRequestLog table.**/
                            changeRequestLog = generateChangeRequestLogData(wizardEventData, changeRequestCreatedTime);

                        } //end of else IF MOCC User Reviewing an Event whoes AlertType is other than "STATUS"
                    } //end of else if wizardEventData.getSubmitChange()
                } //end of if changeRequest != null
            } //end of if events != null
        } catch (Exception chRequest) {
            logger.warn("ERROR Review Event reviewEvent() chRequest ****---- " + chRequest.getMessage());
            returnHashMap = null;
            return returnHashMap;
        }
        /*The follwing is to add a TF_Notes to the database.**/
        try {
            if (events != null && activeEvent > 0) {
                logger.info("before adding the TF_Notes for the event " + wizardEventData.getEventId());
                boolean tfNotesAdded = addTFNotes(wizardEventData);
                if (!tfNotesAdded) {
                    try {
                        events = null;
                    } catch (Exception rem) {
                        logger.error("Could not add TF_Notes for the event " + rem.getMessage());
                        return returnHashMap;
                    }
                    return returnHashMap;
                }
            }
        } catch (Exception tfNote) {
            logger.warn("ERROR Review Event reviewEvent() tfNote ****---- " + tfNote.getMessage());
            return returnHashMap;
        }

        /*The following is to update Reporting categories edited by the client.**/
        try {
            if (events != null
                    && wizardEventData.getReportingCategoriesKeys() != null
                    && wizardEventData.getReportingCategoriesKeys().size() > 0) {
                try {
                    for (int rep = 0; rep < wizardEventData.getReportingCategoriesKeys().size(); rep++) {
                        ReportCategoriesKeyValueData data = wizardEventData.getReportingCategoriesKeys().get(rep);
                        data.setEventId(wizardEventData.getEventId());
                        boolean modified = data.getIsModified();
                        if (modified) {
                            setReportingCategories(data, wizardEventData.getReviewedDateTime());
                        }
                    }
                } catch (Exception exec) {
                    logger.warn("ERROR Review Event reviewEvent() setReportingCategories ****---- " + exec.getMessage());
                }
            }
        } catch (Exception repCategories) {
            logger.warn("ERROR reviewEvent() Could not add Reporting Categories for the event" + repCategories.getMessage());
            return returnHashMap;
        }

        /*The following is to Start a NIW Timer started by the client.**/
        try {
            if (events != null && wizardEventData.getTimerId() != null) {
                boolean resultFromBean = false;
                String strEventId = "" + wizardEventData.getEventId();
                String strTimerId = wizardEventData.getTimerId();
                String timerStartDateTime = wizardEventData.getTimerStartDateTime();

                logger.info("before starting the NIW timer for Event Id "
                        + wizardEventData.getEventId()
                        + " and Timer id "
                        + wizardEventData.getTimerId());
                resultFromBean =
                        startNIWTimer(
                                strEventId,
                                strTimerId,
                                timerStartDateTime,
                                wizardEventData.getReviewedDateTime());
                logger.info("Timer Id Started >>>>>>" + resultFromBean);

                if (resultFromBean) {
                    returnHashMap.put(IServerConstants.IS_TIMER_PUBLISH_REQUIRED, "TIMER_PUBLISH_REQUIRED");
                }
            }
        } catch (Exception timers) {
            logger.warn("ERROR reviewEvent() Could not Start the Timer:" + timers.getMessage());
        }

        /*The following is to add/delete a Linked Discrepancy**/
        try {
            if (events != null && wizardEventData.getDiscrepancyList() != null && wizardEventData.getDiscrepancyList().size() > 0) {
                try {
                    logger.info("before adding the linked discrepancy for the event " + wizardEventData.getEventId() + " and size of the list is " + wizardEventData.getDiscrepancyList().size());
                    for (int disc = 0; disc < wizardEventData.getDiscrepancyList().size(); disc++) {
                        EventDiscrepancyListData data =
                                wizardEventData.getDiscrepancyList().get(disc);
                        data.setEventId(wizardEventData.getEventId());

                        boolean modified = data.isModified();
                        boolean linkedDiscrepancy = data.isLink();

                        if (modified) {
                            if (linkedDiscrepancy) {
                                addEventDiscrepancyData(data);
                            } else {
                                deleteEventDiscrepancyData(data);
                            }
                        }
                    }
                } catch (Exception exec) {
                    logger.warn("ERROR reviewEvent() Could not add/delete the linked discrepancy for the event:" + exec.getMessage());
                    return returnHashMap;
                }
            }
        } catch (Exception discrepnacy) {
            logger.warn("ERROR Review Event reviewEvent() discrepnacy ****---- " + discrepnacy.getMessage());
        }

        try {
            if (events != null) {
                Events eventsData = eventsRepository.findById(wizardEventData.getEventId()).orElse(null);

                if (!wizardEventData.isSubmitChange() && eventsData.getEndDateTime() != null)
                    eventsData.setEndDateTime(null);

                if (wizardEventData.getAlertType().equals("NEW")) {
                    if (!wizardEventData.isSubmitChange()) {
                        eventsData.setType("TRK");
                        eventsData.setStatus("TRK");
                        eventsData.setEticDateTime(null);
                        eventsData.setEticText("");
                        eventsData.setEticNumber(0);
                        eventsData.setEticInitial(null);
                        eventsData.setLastUpdateDateTime(wizardEventData.getReviewedDateTime());
                        eventsData.setLastUpdatedBy(wizardEventData.getUserId());

                        if (null != wizardEventData.getResMgrId() && wizardEventData.getResMgrId().trim().length() > 0) {
                            eventsData.setLastUpdatedBy(wizardEventData.getResMgrId());
                        }

                        if (null != wizardEventData.getMemDeskContact() && wizardEventData.getMemDeskContact().trim().length() > 0) {
                            eventsData.setMemDeskContact(wizardEventData.getMemDeskContact());
                        }
                    }
                } else if (
                        wizardEventData.getAlertType().equals("STATUS") && wizardEventData.isSubmitChange() && eventsData.getStatus().equals("UP")) {
                    eventsData.setLastUpdateDateTime(wizardEventData.getReviewedDateTime());
                    eventsData.setLastUpdatedBy(wizardEventData.getUserId());

                    if (null != wizardEventData.getResMgrId() && wizardEventData.getResMgrId().trim().length() > 0) {
                        //changes made to update Responsible Manager Id 06/29/2012
                        eventsData.setLastUpdatedBy(wizardEventData.getResMgrId());
                    }

                    if (null != wizardEventData.getMemDeskContact() && wizardEventData.getMemDeskContact().trim().length() > 0) {
                        eventsData.setMemDeskContact(wizardEventData.getMemDeskContact());
                    }
                } else if (wizardEventData.getAlertType().equals("STATUS") && wizardEventData.isSubmitChange()) {
                    eventsData.setLastUpdateDateTime(wizardEventData.getReviewedDateTime());
                    eventsData.setLastUpdatedBy(wizardEventData.getUserId());

                    if (null != wizardEventData.getResMgrId() && wizardEventData.getResMgrId().trim().length() > 0) {
                        eventsData.setLastUpdatedBy(wizardEventData.getResMgrId());
                    }
                    if (null != wizardEventData.getMemDeskContact() && wizardEventData.getMemDeskContact().trim().length() > 0) {
                        eventsData.setMemDeskContact(wizardEventData.getMemDeskContact());
                    }
                }
                eventsRepository.save(eventsData);
                logger.info("After updating the record in the Events table. for event Id----->" + wizardEventData.getEventId());

                //*********************** Creating new Records in the Change Request,History & Log tables******************************************
                try {
                    if (newChangeRequestLogData != null) {
                        changeRequestLog = changeRequestLogRepository.save(newChangeRequestLogData);
                        logger.info("After creating a new reord for the ChangeRequest log for event Id----->" + wizardEventData.getEventId());
                    }
                } catch (Exception log) {
                    logger.warn("ERROR Review Event reviewEvent() log ****---- " + log.getMessage());
                }

                try {
                    if (newChangeRequestData != null) {
                        changeRequest = changeRequestRepository.save(newChangeRequestData);
                        logger.info("After creating a new reord for the ChangeRequest for event Id----->" + wizardEventData.getEventId());
                        if (changeRequest != null
                                && newChangeRequestData.getRequestStatus().equals("S")) {
                            boolean isSuperUpdateRequired = false;
                            try {
                                isSuperUpdateRequired =
                                        superUpdateHelper.superUpdateRequired(
                                                wizardEventData.getACN(),
                                                newChangeRequestData.getNewStatus(),
                                                wizardEventData.getNewEticDateTime(),
                                                newChangeRequestData.getNewEticText(),
                                                newChangeRequestData.getNewComment());
                            } catch (Exception updateRequired) {
                                logger.warn("ERROR Review Event reviewEvent() updateRequired ****---- " + updateRequired.getMessage());
                                updateRequired.printStackTrace();
                            }

                            try {
                                if (isSuperUpdateRequired) {

                                    // added to avoid race condition where GDI returns SUPER confirmation before CHANGE_REQUEST table is updated
                                    //TODO jms related implementation
//                                        returnHashMap.put(IServerConstants.SUPER_UPDATE_SENDBUFFER, generateSendBufferData(
//                                                wizardEventData,
//                                                newChangeRequestData));
                                    returnHashMap.put(IServerConstants.SUPER_UPDATE_SENDBUFFER, "As super update required generate send buffer data and send them to super");
                                } else {
                                            /*As SUPER_EQUIPMENT data and Change Request Data matches, Auto Confirmation should take place.
                                            Update Change Request Table, Events table and insert a record in TF_Notes table.**/
                                    boolean isMetsDataUpdated =
                                            updateMetsDataRecords(
                                                    changeRequest,
                                                    changeRequestHistory,
                                                    changeRequestLog,
                                                    events);
                                    logger.info("is Auto Confirmation DONE >>" + isMetsDataUpdated);
                                    returnHashMap.put(IServerConstants.SUCCESS, "SuccessfullyREVIEWED_EVENT reviewed status alert and updated the Change Request and Event tables.");
                                    returnHashMap.put(IServerConstants.REVIEWED_EVENT, events);
                                }
                            } catch (Exception superUpdate) {
                                logger.warn("ERROR Review Event reviewEvent() superUpdate ****---- " + superUpdate.getMessage());
                            }
                        }
                        logger.info("After creating a new reord for the ChangeRequest for event Id----->" + wizardEventData.getEventId());
                    }
                } catch (Exception log) {
                    logger.warn("ERROR Review Event reviewEvent() log 2 ****---- " + log.getMessage());
                }

                try {
                    if (newChangeRequestHistoryData != null) {
                        changeRequestHistory = changeRequestHistoryRepository.save(newChangeRequestHistoryData);
                        logger.info("After creating a new reord for the ChangeRequest History for event Id----->" + wizardEventData.getEventId());
                    }
                } catch (Exception log) {
                    logger.warn("ERROR Review Event reviewEvent() changeRequestHistory ****---- " + log.getMessage());
                }
                //************************ End of creating new records for Change Request, History & Log tables.************************************
                try {
                    if (changeRequestLog != null) {
                        changeRequestLog =
                                changeRequestLogRepository.save(changeRequestLog);
                        logger.info("After creating the ChangeRequest log for event Id----->" + wizardEventData.getEventId());
                    }
                } catch (Exception log) {
                    logger.warn("ERROR Review Event reviewEvent() changeRequestLog ****---- " + log.getMessage());
                }

                try {
                    if (changeRequestHistory != null) {
                        changeRequestHistoryRepository.save(changeRequestHistory);
                        logger.info("After updating ChangeRequestHistory for event Id----->"
                                + wizardEventData.getEventId());
                    }
                } catch (Exception hist) {
                    logger.warn("ERROR Review Event reviewEvent() changeRequestHistory ****---- " + hist.getMessage());
                }

                try {
                    if (changeRequest != null) {
                        if (changeRequest != null
                                && changeRequest.getRequestStatus().equals("S")) {
                            boolean isSuperUpdateRequired = false;
                            try {
                                isSuperUpdateRequired =
                                        superUpdateHelper.superUpdateRequired(
                                                wizardEventData.getACN(),
                                                changeRequest.getNewStatus(),
                                                wizardEventData.getNewEticDateTime(),
                                                changeRequest.getNewEticText(),
                                                changeRequest.getNewComment());
                            } catch (Exception updateRequired) {
                                logger.warn(
                                        "ERROR Review Event reviewEvent() updateRequired ****---- "
                                                + updateRequired.getMessage());
                            }
                            try {
                                if (isSuperUpdateRequired) {

                                    // added to avoid race condition where GDI returns SUPER confirmation before CHANGE_REQUEST table is updated
                                    //TODO jms related implementation
//                                        returnHashMap.put(IServerConstants.SUPER_UPDATE_SENDBUFFER, generateSendBufferData(
//                                                wizardEventData,
//                                                changeRequestData));
                                    returnHashMap.put(IServerConstants.SUPER_UPDATE_SENDBUFFER, "As super update required generate send buffer data and send them to super");


                                } else {
                                        /*As SUPER_EQUIPMENT data and Change Request Data matches, Auto Confirmation should take place.
                                            Update Change Request Table, Events table and insert a record in TF_Notes table.**/

                                    boolean isMetsDataUpdated =
                                            updateMetsDataRecords(
                                                    changeRequest,
                                                    changeRequestHistory,
                                                    changeRequestLog,
                                                    events);
                                    logger.info("is Auto Confirmation DONE >>" + isMetsDataUpdated);
                                    returnHashMap.put(IServerConstants.SUCCESS, "Successfully reviewed Event and updated the Change Request and Event tables.");
                                    returnHashMap.put(IServerConstants.REVIEWED_EVENT, events);
                                }
                            } catch (Exception superUpdate) {
                                logger.warn("ERROR Review Event reviewEvent() superUpdate ****---- " + superUpdate.getMessage());
                            }
                        }
                        logger.info("After updating the ChangeRequest for event Id----->" + wizardEventData.getEventId());
                    }
                } catch (Exception req) {
                    logger.warn("ERROR Review Event reviewEvent() req ****---- " + req.getMessage());
                }
                try {
                    if (eventTypeHistory != null) {
                        eventTypeHistoryRepository.save(eventTypeHistory);
                    }
                } catch (Exception typeHist) {
                    logger.warn("ERROR Review Event reviewEvent() typeHist ****---- " + typeHist.getMessage());
                }
            }
        } catch (Exception close) {
            logger.warn("ERROR Review Event reviewEvent() close ****---- " + close.getMessage());
        }
        return returnHashMap;
    }

    private ChangeRequest generateNewDOAChangeRequestData(
            WizardEventData wizardEventData,
            java.sql.Timestamp doaEticDateTime,
            String strOldEticText,
            String strOldEticComment,
            String strOldStatus,
            java.sql.Timestamp changeRequestOldEticTime,
            String doaEticText,
            String doaEticComment,
            String doaStatus,
            String strOldOST,
            String doaOST,
            String strOldEticRsnCd,
            String doaOldEticRsnCd,
            String strOldEticRsnComment,
            String doaOldEticRsnComment) {
        ChangeRequest newChangeRequestData = new ChangeRequest();

        newChangeRequestData.setEventId(wizardEventData.getEventId());
        newChangeRequestData.setAcn(wizardEventData.getACN());
        newChangeRequestData.setCreatedDtTm(wizardEventData.getReviewedDateTime());

        if (strOldEticText != null && strOldEticText.startsWith("M")) {
            logger.info("= >>> ERROR Me OldEticText in table >" + strOldEticText);
        }
        logger.info("= >>> OldEticText in change Request table >" + strOldEticText);
        newChangeRequestData.setOldEticText(strOldEticText);
        newChangeRequestData.setOldComment(strOldEticComment);
        newChangeRequestData.setOldStatus(strOldStatus);
        newChangeRequestData.setOldEticDtTm(changeRequestOldEticTime);
        newChangeRequestData.setOldOst(strOldOST);
        newChangeRequestData.setOldEticRsnCd(strOldEticRsnCd);
        newChangeRequestData.setOldEticRsnComment(strOldEticRsnComment);

        logger.info("= >>> NewEticText in change Request table >" + doaEticText);
        newChangeRequestData.setNewEticText(doaEticText);
        newChangeRequestData.setNewComment(doaEticComment);
        newChangeRequestData.setNewStatus(doaStatus);
        newChangeRequestData.setNewOst(doaOST);
        newChangeRequestData.setNewEticRsnCd(doaOldEticRsnCd);
        newChangeRequestData.setNewEticRsnComment(doaOldEticRsnComment);

        newChangeRequestData.setRequestStatus("S");
        newChangeRequestData.setLastUpdateDtTm(wizardEventData.getReviewedDateTime());
        newChangeRequestData.setEnteredInError("N");
        newChangeRequestData.setChangeType(7);
        if (doaEticDateTime != null) {
            newChangeRequestData.setNewEticDtTm(doaEticDateTime);
        }
        return newChangeRequestData;
    }

    private ChangeRequestHistory generateNewDOAChangeRequestHistoryData(
            WizardEventData wizardEventData,
            java.sql.Timestamp doaEticDateTime,
            String strOldEticText,
            String strOldEticComment,
            String strOldStatus,
            java.sql.Timestamp changeRequestOldEticTime,
            String doaEticText,
            String doaEticComment,
            String doaStatus,
            String oldOST,
            String doaOST,
            String oldEticRsnCd,
            String doaEticRsnCd,
            String oldEticRsnComment,
            String doaEticRsnComment) {
        ChangeRequestHistory newChangeRequestHistoryData = new ChangeRequestHistory();

        ChangeRequestHistoryPk changeRequestHistoryKey = new ChangeRequestHistoryPk();
        changeRequestHistoryKey.setEventId(wizardEventData.getEventId());
        changeRequestHistoryKey.setCreatedDtTm(wizardEventData.getReviewedDateTime());
        changeRequestHistoryKey.setAcn(wizardEventData.getACN());
        newChangeRequestHistoryData.setChangeRequestHistoryPk(changeRequestHistoryKey);


        if (strOldEticText != null && strOldEticText.startsWith("M")) {
            logger.info("= >>> ERROR Me OldEticText in History table >" + strOldEticText);
        }
        logger.info("= >>> OldEticText in History table >" + strOldEticText);
        newChangeRequestHistoryData.setOldEticText(strOldEticText);
        newChangeRequestHistoryData.setOldComment(strOldEticComment);
        newChangeRequestHistoryData.setOldStatus(strOldStatus);
        newChangeRequestHistoryData.setOldEticDtTm(changeRequestOldEticTime);
        newChangeRequestHistoryData.setOldOst(oldOST);
        newChangeRequestHistoryData.setOldEticRsnCd(oldEticRsnCd);
        newChangeRequestHistoryData.setOldEticRsnComment(oldEticRsnComment);

        logger.info("= >>> NewEticText in History table >" + doaEticText);
        newChangeRequestHistoryData.setNewEticText(doaEticText);
        newChangeRequestHistoryData.setNewComment(doaEticComment);
        newChangeRequestHistoryData.setNewStatus(doaStatus);
        newChangeRequestHistoryData.setNewOst(doaOST);
        newChangeRequestHistoryData.setNewEticRsnCd(doaEticRsnCd);
        newChangeRequestHistoryData.setNewEticRsnComment(doaEticRsnComment);

        newChangeRequestHistoryData.setRequestStatus("S");
        newChangeRequestHistoryData.setLastUpdateDtTm(wizardEventData.getReviewedDateTime());
        newChangeRequestHistoryData.setEnteredInError("N");
        newChangeRequestHistoryData.setChangeType(7);

        if (doaEticDateTime != null) {
            newChangeRequestHistoryData.setNewEticDtTm(doaEticDateTime);
        }
        return newChangeRequestHistoryData;
    }

    private ChangeRequest generateNewChangeRequestData(
            WizardEventData wizardEventData, java.sql.Timestamp eticDateTimeStamp, String strOldEticText,
            String strOldEticComment, String strOldStatus, java.sql.Timestamp changeRequestOldEticTime,
            java.sql.Timestamp changeRequestOldCreatedDtTm, String strOldOST, String strOldEticRsnCd, String strOldEticRsnComment) {
        ChangeRequest newChangeRequestData = new ChangeRequest();

        newChangeRequestData.setEventId(wizardEventData.getEventId());
        newChangeRequestData.setAcn(wizardEventData.getACN());
        newChangeRequestData.setCreatedDtTm(changeRequestOldCreatedDtTm);

        if (strOldEticText != null && strOldEticText.startsWith("M")) {
            logger.info("= >>> ERROR Me OldEticText in table >" + strOldEticText);
        }
        logger.info("= >>> OldEticText in change Request table >" + strOldEticText);
        newChangeRequestData.setOldEticText(strOldEticText);
        newChangeRequestData.setOldComment(strOldEticComment);
        newChangeRequestData.setOldStatus(strOldStatus);
        newChangeRequestData.setOldEticDtTm(changeRequestOldEticTime);
        newChangeRequestData.setOldOst(strOldOST);
        newChangeRequestData.setOldEticRsnCd(strOldEticRsnCd);
        newChangeRequestData.setOldEticRsnComment(strOldEticRsnComment);

        if (wizardEventData.getNewEticInfo() != null) {
            logger.info("= >>> NewEticText in change Request table >" + wizardEventData.getNewEticInfo());
            newChangeRequestData.setNewEticText(
                    wizardEventData.getNewEticInfo().trim());
        } else
            newChangeRequestData.setNewEticText("");
        newChangeRequestData.setNewStatus(wizardEventData.getNewStatus());
        newChangeRequestData.setNewEticDtTm(eticDateTimeStamp);

        if (!wizardEventData.getChangeRequestNewStatus().trim().equals("UP")) {
            if (wizardEventData.getNewEticComment() != null && wizardEventData.getNewEticComment().trim().length() > 0) {
                newChangeRequestData.setNewComment(wizardEventData.getNewEticComment().trim());
            } else {
                newChangeRequestData.setNewComment(strOldEticComment);
            }
        } else {
            newChangeRequestData.setNewComment("");
        }

        newChangeRequestData.setNewOst(wizardEventData.getNewOST());
        newChangeRequestData.setNewEticRsnCd(wizardEventData.getNewEticRsnCd());
        newChangeRequestData.setNewEticRsnComment(wizardEventData.getNewEticRsnComment());
        newChangeRequestData.setRequestStatus("S");
        newChangeRequestData.setLastUpdateDtTm(wizardEventData.getReviewedDateTime());
        newChangeRequestData.setEnteredInError("N");
        newChangeRequestData.setChangeType(7);

        return newChangeRequestData;
    }

    private ChangeRequestHistory generateNewChangeRequestHistoryData(
            WizardEventData wizardEventData, java.sql.Timestamp eticDateTimeStamp,
            String strOldEticText, String strOldEticComment,
            String strOldStatus, java.sql.Timestamp changeRequestOldEticTime,
            java.sql.Timestamp changeRequestOldCreatedDtTm, String strOldOST,
            String strOldEticRsnCd, String strOldEticRsnComment) {
        ChangeRequestHistory newChangeRequestHistoryData = new ChangeRequestHistory();
        ChangeRequestHistoryPk changeRequestHistoryKey = new ChangeRequestHistoryPk();

        changeRequestHistoryKey.setEventId(wizardEventData.getEventId());
        changeRequestHistoryKey.setAcn(wizardEventData.getACN());
        changeRequestHistoryKey.setCreatedDtTm(changeRequestOldCreatedDtTm);
        newChangeRequestHistoryData.setChangeRequestHistoryPk(changeRequestHistoryKey);

        if (strOldEticText != null && strOldEticText.startsWith("M")) {
            logger.info(
                    "= >>> ERROR Me OldEticText in History table >" + strOldEticText);
        }
        logger.info("= >>> OldEticText in History table >" + strOldEticText);
        newChangeRequestHistoryData.setOldEticText(strOldEticText);
        newChangeRequestHistoryData.setOldComment(strOldEticComment);
        newChangeRequestHistoryData.setOldStatus(strOldStatus);
        newChangeRequestHistoryData.setOldEticDtTm(changeRequestOldEticTime);
        newChangeRequestHistoryData.setOldOst(strOldOST);
        newChangeRequestHistoryData.setOldEticRsnCd(strOldEticRsnCd);
        newChangeRequestHistoryData.setOldEticRsnComment(strOldEticRsnComment);

        if (wizardEventData.getNewEticInfo() != null) {
            logger.info("= >>> NewEticText in History table >" + wizardEventData.getNewEticInfo());
            newChangeRequestHistoryData.setNewEticText(
                    wizardEventData.getNewEticInfo().trim());
        }
        newChangeRequestHistoryData.setNewStatus(wizardEventData.getNewStatus());

        if (eticDateTimeStamp != null)
            newChangeRequestHistoryData.setNewEticDtTm(eticDateTimeStamp);

        if (!wizardEventData.getChangeRequestNewStatus().trim().equals("UP")) {
            if (wizardEventData.getNewEticComment() != null && wizardEventData.getNewEticComment().trim().length() > 0) {
                newChangeRequestHistoryData.setNewComment(wizardEventData.getNewEticComment().trim());
            } else {
                newChangeRequestHistoryData.setNewComment(strOldEticComment);
            }
        } else {
            newChangeRequestHistoryData.setNewComment("");
        }

        newChangeRequestHistoryData.setNewOst(wizardEventData.getNewOST());
        newChangeRequestHistoryData.setNewEticRsnCd(wizardEventData.getNewEticRsnCd());
        newChangeRequestHistoryData.setNewEticRsnComment(wizardEventData.getNewEticRsnComment());
        newChangeRequestHistoryData.setRequestStatus("S");
        newChangeRequestHistoryData.setLastUpdateDtTm(wizardEventData.getReviewedDateTime());
        newChangeRequestHistoryData.setEnteredInError("N");
        newChangeRequestHistoryData.setChangeType(7);

        return newChangeRequestHistoryData;
    }

    private ChangeRequestLog generateChangeRequestLogData(
            WizardEventData wizardData,
            java.sql.Timestamp changeRequestCreatedTimeStamp) {
        ChangeRequestLog changeRequestLogData = new ChangeRequestLog();

        ChangeRequestLogPk changeRequestLogPk = new ChangeRequestLogPk();
        changeRequestLogPk.setEventId(wizardData.getEventId());
        changeRequestLogPk.setCreatedDtTm(changeRequestCreatedTimeStamp);
        changeRequestLogPk.setStatusChangedDtTm(wizardData.getReviewedDateTime());
        changeRequestLogData.setChangeRequestLogPk(changeRequestLogPk);
        changeRequestLogData.setOldRequestStatus("U");
        changeRequestLogData.setNewRequestStatus("S");
        return changeRequestLogData;
    }

    private boolean addTFNotes(WizardEventData wizardEventData) {
        boolean result = false;
        EventTfNotes eventTFNotes = null;

        try {
            //the following is to add a record to the EVENT_TF_NOTES table.
            int noteId = 0;
            int activeEvents = 0;
            try {
                noteId = eventTfNotesRepository.getMaxNoteId(wizardEventData.getEventId());
                noteId = noteId + 1;
                logger.info("after incrementing noteId====== " + noteId);
                activeEvents = eventsRepository.getActiveEventCount(wizardEventData.getACN());
                logger.info("activeEvents====== " + activeEvents);
            } catch (Exception n) {
                logger.warn("ERROR Review Event addTFNotes() increment note id ****---- " + n.getMessage());
            }
            if (noteId > 0) {
                String strEticComment = "", strETICForm = "", strStatus = "", strTFNotesString = "";
                if (wizardEventData.getNewEticDateTime() == null && wizardEventData.getNewEticInfo() != null) {
                    strETICForm = wizardEventData.getNewEticInfo();
                }
                if (wizardEventData.getNewEticDateTime() != null && wizardEventData.getNewEticInfo() != null) {
                    strETICForm = ETICHelper.getETICFormat(wizardEventData.getNewEticDateTime(), wizardEventData.getNewEticInfo());
                }
                if (wizardEventData.getNewEticDateTime() != null && wizardEventData.getNewEticInfo() == null) {
                    strETICForm = ETICHelper.getETICFormat(wizardEventData.getNewEticDateTime(), null);
                }

                if (wizardEventData.getNewEticComment() != null && wizardEventData.getNewEticComment().trim().length() > 0)
                    strEticComment = wizardEventData.getNewEticComment();

                if (strETICForm != null && strETICForm.trim().length() > 0)
                    strETICForm = strETICForm;

                if (wizardEventData.getNewStatus() != null && wizardEventData.getNewStatus().length() > 0)
                    strStatus = wizardEventData.getNewStatus();

                if (wizardEventData.getReviewChangeType() == 1)
                    strTFNotesString = strEticComment;
                else if (wizardEventData.getReviewChangeType() == 2)
                    strTFNotesString = strETICForm;
                else if (wizardEventData.getReviewChangeType() == 3)
                    strTFNotesString = strETICForm + ", " + strEticComment;
                else if (wizardEventData.getReviewChangeType() == 4)
                    strTFNotesString = strStatus;
                else if (wizardEventData.getReviewChangeType() == 5)
                    strTFNotesString = strStatus + ", " + strEticComment;
                else if (wizardEventData.getReviewChangeType() == 6)
                    strTFNotesString = strStatus + ", " + strETICForm;
                else if (wizardEventData.getReviewChangeType() >= 7) {
                    if (strStatus != null && strStatus.trim().equalsIgnoreCase("UP"))
                        strTFNotesString = strStatus;
                    else
                        strTFNotesString =
                                strStatus + ", " + strETICForm + ", " + strEticComment;
                }

                EventTfNotes eventTFNotesData = new EventTfNotes();
                EventTfNotesPk eventTfNotesPk = new EventTfNotesPk();
                eventTfNotesPk.setEventId(wizardEventData.getEventId());
                java.sql.Timestamp tfNotesDateTm = ServerDateHelper.getTimeStamp();
                eventTfNotesPk.setTfDtTm(tfNotesDateTm);
                eventTFNotesData.setEventTfNotesPk(eventTfNotesPk);
                eventTFNotesData.setEmpNum(wizardEventData.getUserId());
                eventTFNotesData.setEmpName(wizardEventData.getEmployeeName());
                eventTFNotesData.setEmpDepartment(wizardEventData.getEmpDepartment());
                eventTFNotesData.setEditedFlag("N");
                eventTFNotesData.setNoteId(noteId);

                if (wizardEventData.isSubmitChange()) {
                    //if the User had submitted the changes
                    if (wizardEventData.getAlertType().equals("NEW")) {
                        eventTFNotesData.setTfNote("Posted New OOS: " + strTFNotesString);
                        eventTFNotesData.setChangeType(15);
                    } else {
                        if (wizardEventData.getReviewChangeType() >= 4 && wizardEventData.getReviewChangeType() <= 7) {
                            eventTFNotesData.setTfNote("Posted New Status: " + strTFNotesString);
                        } else if (wizardEventData.getReviewChangeType() == 2 || wizardEventData.getReviewChangeType() == 3) {
                            eventTFNotesData.setTfNote("Posted New ETIC: " + strTFNotesString);
                        } else if (wizardEventData.getReviewChangeType() == 1) {
                            eventTFNotesData.setTfNote("Posted New Comment: " + strTFNotesString);
                        }
                    }
                    eventTFNotesData.setChangeType(wizardEventData.getReviewChangeType());
                    eventTFNotesData.setNoteType(3);
                } else { //if the User did not want to submit the changes.
                    if (wizardEventData.getAlertType().equals("NEW")) {
                        eventTFNotesData.setTfNote("Reviewed - OOS to TRK");
                        eventTFNotesData.setChangeType(15);
                    } else if (wizardEventData.getAlertType().equals("STATUS")) {
                        eventTFNotesData.setTfNote("Reviewed - No Status Change");
                        eventTFNotesData.setChangeType(wizardEventData.getReviewChangeType());
                    } else if (wizardEventData.getAlertType().equals("ETIC")) {
                        eventTFNotesData.setTfNote("Reviewed - No ETIC Change");
                        eventTFNotesData.setChangeType(wizardEventData.getReviewChangeType());
                    } else if (wizardEventData.getAlertType().equals("COM")) {
                        eventTFNotesData.setTfNote("Reviewed - No Comment Change");
                        eventTFNotesData.setChangeType(wizardEventData.getReviewChangeType());
                    }
                    eventTFNotesData.setNoteType(2);
                }
                eventTFNotesData.setLastUpdateDtTm(wizardEventData.getReviewedDateTime());

                try {
                    eventTfNotesRepository.save(eventTFNotesData);
                    result = true;
                } catch (Exception tfNotes) {
                    logger.warn(
                            "ERROR Review Event addTFNotes() tfNotes ****---- "
                                    + tfNotes.getMessage());
                }
                //the following is to add the TF_Notes that the Client had added.
                if (wizardEventData.getTfNotesList() != null && wizardEventData.getTfNotesList().size() > 0) {
                    for (int tf = 0; tf < wizardEventData.getTfNotesList().size(); tf++) {

                        //creating a new createdTimeStamp as the TFDateTime is the primary Key to the table.
                        java.sql.Timestamp tfNotesDateTime =
                                ServerDateHelper.getTimeStamp();
                        if (tfNotesDateTime.equals(wizardEventData.getReviewedDateTime())) {
                            java.util.Date currentDate = ServerDateHelper.getDate_UTC();
                            tfNotesDateTime = new java.sql.Timestamp(currentDate.getTime() + 1000);
                            logger.info("incremented tfNotesDateTime >>>>>>>>>>>>>> " + tfNotesDateTime);
                        }
                        String clientData = wizardEventData.getTfNotesList().get(tf);
                        EventTfNotes eventTFNotesData1 = new EventTfNotes();
                        EventTfNotesPk eventTfNotesPk1 = new EventTfNotesPk();
                        eventTfNotesPk1.setEventId(wizardEventData.getEventId());
                        eventTfNotesPk1.setTfDtTm(tfNotesDateTime);
                        eventTFNotesData1.setEventTfNotesPk(eventTfNotesPk1);

                        eventTFNotesData1.setEmpNum(wizardEventData.getUserId());
                        eventTFNotesData1.setEmpName(wizardEventData.getEmployeeName());
                        eventTFNotesData1.setEmpDepartment(wizardEventData.getEmpDepartment());
                        eventTFNotesData1.setEditedFlag("N");
                        eventTFNotesData1.setNoteId(noteId);
                        eventTFNotesData1.setTfNote(clientData);
                        eventTFNotesData1.setNoteType(0);

                        if (wizardEventData.isSubmitChange()) {
                            //if the User had submitted the changes
                            if (wizardEventData.getAlertType().equals("NEW")) {
                                eventTFNotesData1.setChangeType(15);
                            } else {
                                eventTFNotesData1.setChangeType(
                                        wizardEventData.getReviewChangeType());
                            }
                            eventTFNotesData.setNoteType(3);
                        } else { //if the User did not want to submit the changes.
                            if (wizardEventData.getAlertType().equals("NEW")) {
                                eventTFNotesData1.setChangeType(15);
                            } else {
                                eventTFNotesData1.setChangeType(wizardEventData.getReviewChangeType());
                            }
                            eventTFNotesData.setNoteType(2);
                        }
                        eventTFNotesData1.setLastUpdateDtTm(wizardEventData.getReviewedDateTime());
                        try {
                            eventTfNotesRepository.save(eventTFNotesData1);
                            result = true;
                        } catch (Exception tfNotes) {
                            logger.warn("ERROR Review Event addTFNotes() tfNotes 1 ****---- " + tfNotes.getMessage());
                        }
                    } //end of for tfNotesList
                } //end of if tfNotesList !=null
            } //end of if noteId >0
        } catch (Exception notes) {
            logger.warn("ERROR Review Event addTFNotes() notes------ " + notes.getMessage());
        }
        return result;
    }

    private boolean setReportingCategories(ReportCategoriesKeyValueData reportCategoriesKeyValueData, java.sql.Timestamp closedDateTime) {
        logger.info("== setReportingCategories().. ==>");
        boolean result = false;
        int eventId = 0;
        String levelOneId = "", levelTwoId = "", updatedLevelOneId = "", updatedLevelTwoId = "";

        eventId = reportCategoriesKeyValueData.getEventId();
        levelOneId = reportCategoriesKeyValueData.getLevelOneId();
        levelTwoId = reportCategoriesKeyValueData.getLevelTwoId();
        updatedLevelOneId = reportCategoriesKeyValueData.getUpdatedLevelOneId();
        updatedLevelTwoId = reportCategoriesKeyValueData.getUpdatedLevelTwoId();

        EventRepCatg data = new EventRepCatg();
        try {
            EventRepCatgPk pk = new EventRepCatgPk();
            pk.setEventId(eventId);
            pk.setLevel1Id(levelOneId);
            pk.setLevel2Id(levelTwoId);
            data = eventRepCatgRepository.findById(pk).orElse(null);
        } catch (Exception e) {
            logger.info("The reporting category for eventId " + eventId + " and level_1_Id " + levelOneId + " could not be found");
        }
        try {
            if (data != null) {
                //update an existing record.
                if (updatedLevelTwoId == null || updatedLevelTwoId.trim().length() == 0) {
                    try {
                        logger.info("deleting the record as updated level 2  is " + updatedLevelTwoId);
                        eventRepCatgRepository.delete(data);
                        result = true;
                    } catch (Exception rem) {
                        logger.warn("ERROR Review Event setReportingCategories remove >> " + rem.getMessage());
                    }
                } else {
                    logger.info("updating the record as updated level 2  is " + updatedLevelTwoId);
                    EventRepCatg repCatdata = new EventRepCatg();
                    EventRepCatgPk pk = new EventRepCatgPk();
                    pk.setEventId(eventId);
                    pk.setLevel1Id(updatedLevelOneId);
                    pk.setLevel2Id(updatedLevelTwoId);
                    repCatdata.setEventRepCatgPk(pk);
                    repCatdata.setLastUpdatedDtTm(closedDateTime);
                    try {
                        eventRepCatgRepository.delete(data);
                        eventRepCatgRepository.save(repCatdata);
                        result = true;
                    } catch (Exception u) {
                        logger.warn("ERROR Review Event setReportingCategories u >> " + u.getMessage());
                    }
                }
            } else {
                //create a new record
                EventRepCatg repCatgdata = new EventRepCatg();
                EventRepCatgPk pk = new EventRepCatgPk();
                pk.setEventId(eventId);
                pk.setLevel1Id(updatedLevelOneId);
                pk.setLevel2Id(updatedLevelTwoId);
                repCatgdata.setEventRepCatgPk(pk);
                repCatgdata.setLastUpdatedDtTm(closedDateTime);

                if (updatedLevelTwoId != null && updatedLevelTwoId.trim().length() > 0)
                    eventRepCatgRepository.save(repCatgdata);
                result = true;
            }
        } catch (Exception update) {
            logger.warn("ERROR Review Event setReportingCategories() create/update ****---- " + update.getMessage());
        }
        return result;
    }

    private boolean startNIWTimer(String eventId, String timerId, String timerStartDateTime, java.sql.Timestamp createdDateTime) {
        logger.info("startNIWTimer.. ==========");
        List<EventTimers> NIWTimerList = null;
        boolean result = false;
        try {
            NIWTimerList =
                    eventsTimerRepository.findActiveTimers(Integer.parseInt(eventId));
        } catch (Exception e) {
            logger.info("Could not find active timer.... " + e.getMessage());
        }
        //stop the existing timer first
        try {
            logger.info("Stoping the current NIW Timer");
            EventTimers eventTimers = null;

            for (int i = 0; i < NIWTimerList.size(); i++) {
                eventTimers = (EventTimers) NIWTimerList.get(i);
                java.sql.Timestamp stopDateTime = null;

                //as the start date time is supplied from the client. else take the current time on the server.
                if (timerStartDateTime != null) {
                    stopDateTime = ServerDateHelper.getConvertedTimestamp(timerStartDateTime);
                    logger.info("stopDateTime  after converting -------" + stopDateTime);
                } else {
                    String strCurrentTimeStamp = ServerDateHelper.getCurrentTimeStamp();
                    stopDateTime = ServerDateHelper.getConvertedTimestamp(strCurrentTimeStamp);
                    logger.info("stopDateTime  after converting -------" + stopDateTime);
                }

                if (stopDateTime != null) {
                    EventTimers eventData = eventsTimerRepository.findById(new EventTimersPk(eventTimers.getEventTimersPk().getEventId(), eventTimers.getEventTimersPk().getCreationDtTm())).orElse(null);
                    eventData.setTimerStartDtTm(stopDateTime);
                    eventData.setLastUpdateDtTm(stopDateTime);
                    eventsTimerRepository.save(eventData);
                    result = true;
                }
            }
        } catch (Exception e) {
            logger.warn("ERROR Review Event startNIWTimer eeeee >> " + e.getMessage());
            result = false;
        }
        //start new timer
        try {
            java.sql.Timestamp startDateTimeStamp = null;

            if (timerStartDateTime != null) {
                startDateTimeStamp = ServerDateHelper.getConvertedTimestamp(timerStartDateTime);
                logger.info("startDateTimeStamp  after converting -------" + startDateTimeStamp);
            }

            if (startDateTimeStamp != null) {
                EventTimers eventTimerData = new EventTimers();
                EventTimersPk eventTimerPk = new EventTimersPk();
                eventTimerPk.setEventId(Integer.parseInt(eventId));
                eventTimerPk.setCreationDtTm(createdDateTime);
                eventTimerData.setEventTimersPk(eventTimerPk);
                eventTimerData.setTimerId(timerId);
                eventTimerData.setTimerStartDtTm(startDateTimeStamp);
                eventTimerData.setLastUpdateDtTm(createdDateTime);
                EventTimers eventTimers = eventsTimerRepository.save(eventTimerData);

                if (eventTimers != null)
                    result = true;
            }
        } catch (Exception ee) {
            logger.warn("ERROR Review Event startNIWTimer() create ****---- " + ee.getMessage());
            result = false;
        }
        return result;
    }

    private boolean addEventDiscrepancyData(EventDiscrepancyListData discrepancyData) {
        logger.info("in the addEventDiscrepancyListData of the ..........");
        boolean result = false;
        int eventId = 0;
        String ata = "", discNumber = "", eventType = "";
        boolean isDowningItem = false;

        eventId = discrepancyData.getEventId();
        ata = discrepancyData.getAta();
        //to convert back to 4 digit char(eliminate '-' from the string);
        ata = ata.substring(0, 2) + "" + ata.substring(3, 5);
        discNumber = discrepancyData.getNumber();
        eventType = discrepancyData.getEventType();
        isDowningItem = discrepancyData.isDowningItem();
        try {
            EventMaxiDisc eventMaxiDiscrepancy = null;
            EventMaxiDiscPk pkey = new EventMaxiDiscPk((long) eventId, ata, discNumber);
            try {
                eventMaxiDiscrepancy = eventMaxiDiscRepository.findById(pkey).orElse(null);
            } catch (Exception norec) {
                logger.info("error in finding the record in EventMaxiDisc table" + norec.getMessage());
            }
            // If we're simply updating the discrepancy as a downing item, it may already exist in the DB
            if (eventMaxiDiscrepancy == null) {
                EventMaxiDisc data = new EventMaxiDisc();
                EventMaxiDiscPk pk = new EventMaxiDiscPk((long) eventId, ata, discNumber);
                data.setEventMaxiDisckPk(pk);
                data.setType(eventType);
                eventMaxiDiscRepository.save(data);
            }
            if (eventMaxiDiscrepancy != null)
                result = true;
        } catch (Exception update) {
            logger.warn(
                    "ERROR Review Event setReportingCategories() addEventDiscrepancyData ****---- "
                            + update.getMessage());
        }
        if (isDowningItem) {
            try {
                // Remove current Downing Item if it exists
                try {
                    eventMaxiDwningItmRepository.deleteById((long) eventId);
                } catch (Exception c) {
                    logger.warn(" ERROR Event Discrepancies addEventDiscrepancyData() remove prior downing item >> " + c.getMessage());
                }
                EventMaxiDwningItm data = new EventMaxiDwningItm();
                data.setEventId((long) eventId);
                data.setAta(ata);
                data.setDiscNum(discNumber);
                eventMaxiDwningItmRepository.save(data);
                result = result && data != null;
            } catch (Exception create) {
                logger.warn(" ERROR Event Discrepancies addEventDiscrepancyDowningItemData() create >> " + create.getMessage());
            }
        }
        return result;
    }

    public boolean deleteEventDiscrepancyData(EventDiscrepancyListData discrepancyData) {
        logger.info("in the deleteEventDiscrepancyData of the EventDiscrepanciesUpdateSessionBean bean..........");
        boolean result = false;
        int eventId = 0;
        String ata = "", discNumber = "", eventType = "";
        eventId = discrepancyData.getEventId();
        ata = discrepancyData.getAta();
        EventMaxiDisc eventMaxiDiscrepancy = null;

        //to convert back to 4 digit char(eliminate '-' from the string);
        ata = ata.substring(0, 2) + "" + ata.substring(3, 5);

        discNumber = discrepancyData.getNumber();
        try {
            EventMaxiDiscPk primaryKey = null;
            try {
                primaryKey = new EventMaxiDiscPk((long) eventId, ata, discNumber);
            } catch (Exception pk) {
                return false;
            }
            if (primaryKey != null) {
                eventMaxiDiscrepancy =
                        eventMaxiDiscRepository.findById(primaryKey).orElse(null);
            }
        } catch (Exception e) {
            logger.warn("ERROR Review Event deleteEventDiscrepancyData eeeee >> " + e.getMessage());
        }
        try {
            if (eventMaxiDiscrepancy != null) {
                eventMaxiDiscRepository.delete(eventMaxiDiscrepancy);
                result = true;
            }
        } catch (Exception update) {
            logger.warn("ERROR Review Event deleteEventDiscrepancyData() update ****---- " + update.getMessage());
        }
        return result;
    }

    /**
     * private method to update METS Data as SUPER_Equipment Data matches with Change Request created by the Client.
     *
     * @ params	ChangeRequest changeRequest, ChangeRequestHistory changeRequestHistory, ChangeRequestLog changeRequestLog, Events events
     * @ return	result
     */
    private boolean updateMetsDataRecords(ChangeRequest changeRequest, ChangeRequestHistory changeRequestHistory, ChangeRequestLog changeRequestLog, Events events) {
        boolean isMetsUpdated = false;
        try {
            java.util.Date currentDate = ServerDateHelper.getDate_UTC();
            java.sql.Timestamp incrementedReviewedDateTime = new java.sql.Timestamp(currentDate.getTime() + 5000);
            if (changeRequest != null) {
                ChangeRequest superChangeRequestData = null;
                try {
                    logger.info(" Before confirming Change Request Data");
                    superChangeRequestData = changeRequestRepository.findById(changeRequest.getAcn()).orElse(null);
                    superChangeRequestData.setRequestStatus("C");
                    changeRequestRepository.save(superChangeRequestData);
                } catch (Exception changeRequestError) {
                    logger.warn("ERROR Review Event updateMetsDataRecords() changeRequestError ****---- " + changeRequestError.getMessage());
                    events = null;
                }
                try {
                    ChangeRequestHistory superChangeRequestHistoryData = changeRequestHistoryRepository.findById(changeRequestHistory.getChangeRequestHistoryPk()).orElse(null);
                    superChangeRequestHistoryData.setRequestStatus("C");
                    changeRequestHistoryRepository.save(superChangeRequestHistoryData);
                } catch (Exception changeRequestHistoryError) {
                    logger.warn("ERROR Review Event updateMetsDataRecords() changeRequestHistoryError ****---- " + changeRequestHistoryError.getMessage());
                }
                try {
                    ChangeRequestLog superChangeRequestLogData = new ChangeRequestLog();
                    ChangeRequestLogPk changeRequestLogPk = new ChangeRequestLogPk();
                    changeRequestLogPk.setEventId(superChangeRequestData.getEventId());
                    changeRequestLogPk.setStatusChangedDtTm(incrementedReviewedDateTime);
                    changeRequestLogPk.setCreatedDtTm(incrementedReviewedDateTime);
                    superChangeRequestLogData.setChangeRequestLogPk(changeRequestLogPk);
                    superChangeRequestLogData.setNewRequestStatus("C");
                    changeRequestLogRepository.save(superChangeRequestLogData);
                } catch (Exception changeReqLog) {
                    logger.warn("ERROR Review Event updateMetsDataRecords() changeReqLog ****---- " + changeReqLog.getMessage());
                }
                if (events != null) {
                    try {
                        logger.info(" Before confirming Events Data");
                        Events superConfirmationData = eventsRepository.findById(events.getEventId()).orElse(null);
                        superConfirmationData.setStatus(superChangeRequestData.getNewStatus());
                        superConfirmationData.setEticDateTime(superChangeRequestData.getNewEticDtTm());
                        superConfirmationData.setEticText(superChangeRequestData.getNewEticText());
                        superConfirmationData.setCurComment(superChangeRequestData.getNewComment());
                        superConfirmationData.setLastUpdateDateTime(incrementedReviewedDateTime);
                        superConfirmationData.setLastUpdatedBy("SUPER");
                        superConfirmationData.setOst(superChangeRequestData.getNewOst());
                        superConfirmationData.setEticRsnCd(superChangeRequestData.getNewEticRsnCd());
                        superConfirmationData.setEticRsnComment(superChangeRequestData.getNewEticRsnComment());
                        events = eventsRepository.save(superConfirmationData);
                    } catch (Exception eventsUpdated) {
                        logger.warn("ERROR Review Event updateMetsDataRecords() eventsUpdated ****---- " + eventsUpdated.getMessage());
                        events = null;
                    }
                }
                if (events != null) {
                    try {
                        logger.info(" Before inserting confirmation in TF Notes table");
                        String strEticComment = "", strETICForm = "", strStatus = "", strTFNotesString = "";
                        String strChangeRequestEticDateTime = null;
                        if (superChangeRequestData.getNewEticDtTm() != null)
                            strChangeRequestEticDateTime = "" + superChangeRequestData.getNewEticDtTm();

                        if (strChangeRequestEticDateTime == null && superChangeRequestData.getNewEticText() != null) {
                            strETICForm = superChangeRequestData.getNewEticText();
                        }
                        if (strChangeRequestEticDateTime != null && superChangeRequestData.getNewEticText() != null) {
                            strETICForm = ETICHelper.getETICFormat(
                                    strChangeRequestEticDateTime,
                                    superChangeRequestData.getNewEticText());
                        }
                        if (strChangeRequestEticDateTime != null && superChangeRequestData.getNewEticText() == null) {
                            strETICForm = ETICHelper.getETICFormat(strChangeRequestEticDateTime, null);
                        }

                        if (superChangeRequestData.getNewComment() != null && superChangeRequestData.getNewComment().trim().length() > 0)
                            strEticComment = superChangeRequestData.getNewComment();

                        if (strETICForm != null && strETICForm.trim().length() > 0)
                            strETICForm = strETICForm;

                        if (superChangeRequestData.getNewStatus() != null && superChangeRequestData.getNewStatus().length() > 0)
                            strStatus = superChangeRequestData.getNewStatus();

                        if (superChangeRequestData.getChangeType() == 1)
                            strTFNotesString = "Confirm New Comment: " + strEticComment;
                        else if (superChangeRequestData.getChangeType() == 2)
                            strTFNotesString = "Confirm New ETIC: " + strETICForm;
                        else if (superChangeRequestData.getChangeType() == 3)
                            strTFNotesString = "Confirm New ETIC: " + strETICForm + ", " + strEticComment;
                        else if (superChangeRequestData.getChangeType() == 4)
                            strTFNotesString = "Confirm New Status: " + strStatus;
                        else if (superChangeRequestData.getChangeType() == 5)
                            strTFNotesString = "Confirm New Status: " + strStatus + ", " + strEticComment;
                        else if (superChangeRequestData.getChangeType() == 6)
                            strTFNotesString = "Confirm New Status: " + strStatus + ", " + strETICForm;
                        else if (superChangeRequestData.getChangeType() >= 7) {
                            if (strStatus != null && strStatus.trim().equalsIgnoreCase("UP"))
                                strTFNotesString = "Confirm New Status: " + strStatus;
                            else
                                strTFNotesString = "Confirm New Status: " + strStatus + ", " + strETICForm + ", " + strEticComment;
                        }
                        try {
                            boolean tfNoteAdded =
                                    addTFNotes(
                                            superChangeRequestData.getEventId(),
                                            strTFNotesString,
                                            incrementedReviewedDateTime);
                        } catch (Exception tfNote) {
                            logger.warn("ERROR Review Event updateMetsDataRecords() tfNote ****---- " + tfNote.getMessage());
                        }
                        isMetsUpdated = true;
                    } catch (Exception tfNoteInserted) {
                        logger.warn("ERROR Review Event tfNoteInserted  >> " + tfNoteInserted.getMessage());
                        isMetsUpdated = false;
                    }
                }
            }
        } catch (Exception pk) {
            logger.warn(
                    "ERROR Review Event updateMetsDataRecords() pk ****---- "
                            + pk.getMessage());
            isMetsUpdated = false;
        }

        return isMetsUpdated;
    }

    private boolean addTFNotes(int eventId, String tfNote, java.sql.Timestamp updatedTimeStamp) {
        boolean result = false;
        int noteId = 0;
        try {
            noteId = eventTfNotesRepository.getMaxNoteId(eventId);
            noteId = noteId + 2;
            logger.info("after incrementing noteId====== " + noteId);
        } catch (Exception e) {
            logger.warn("ERROR Review Event addTFNotes() incrementing note id ---- " + e.getMessage());
        }
        if (noteId > 0) {
            logger.info(" tfNote while inserting the record ======" + tfNote + "<<");
            EventTfNotes eventTFNotesData = new EventTfNotes();
            EventTfNotesPk eventTfNotesPk = new EventTfNotesPk();
            eventTfNotesPk.setEventId(eventId);
            eventTfNotesPk.setTfDtTm(updatedTimeStamp);
            eventTFNotesData.setEventTfNotesPk(eventTfNotesPk);
            eventTFNotesData.setEditedFlag("N");
            eventTFNotesData.setNoteId(noteId);
            eventTFNotesData.setTfNote(tfNote);
            eventTFNotesData.setNoteType(4);
            eventTFNotesData.setLastUpdateDtTm(updatedTimeStamp);
            try {
                if (eventTFNotesData != null) {
                    eventTfNotesRepository.save(eventTFNotesData);
                    result = true;
                }
            } catch (Exception tfNotes) {
                logger.warn(
                        "ERROR Review Event addTFNotes() tfNotes ---- "
                                + tfNotes.getMessage());
            }
        }
        return result;
    }
}
