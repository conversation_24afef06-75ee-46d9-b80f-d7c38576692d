
package com.fedex.mets.wsdl.discrepancy;

import java.math.BigDecimal;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for PlannedItemAsgmtType complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="PlannedItemAsgmtType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="plannedItemOid" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="plannedItemAsgmtOid" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="seqNbr" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="logAddTm" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="intentCd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="grpStatDept" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="grpStartTime" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="grpDepartDateTime" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="grpFltIn" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="grpFltOut" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="scheduledBy" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="status" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="reasonCode" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="warnings" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="manHoursExpended" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="planUnplanInd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="maintType" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="priorityCd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="aircraftNbr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ataNbr" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="discrpNbr" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="discrepancyOid" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="oilInterimFlg" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="flightNbr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="fightDt" type="{http://www.w3.org/2001/XMLSchema}date"/>
 *         &lt;element name="legNbr" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="startDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="station" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="dept" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="departDateTime" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="wriTotalTaskTimeMins" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="wriSpanTimeDurationMins" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="nbrOfAmtReq" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="flightDirectionInd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="lateAssignment" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="deleteReasonCd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="deleteReasonText" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="updatedUserId" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="nextScheduled" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="geoConflictInd" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PlannedItemAsgmtType", propOrder = {
    "plannedItemOid",
    "plannedItemAsgmtOid",
    "seqNbr",
    "logAddTm",
    "intentCd",
    "grpStatDept",
    "grpStartTime",
    "grpDepartDateTime",
    "grpFltIn",
    "grpFltOut",
    "scheduledBy",
    "status",
    "reasonCode",
    "warnings",
    "manHoursExpended",
    "planUnplanInd",
    "maintType",
    "priorityCd",
    "aircraftNbr",
    "ataNbr",
    "discrpNbr",
    "discrepancyOid",
    "oilInterimFlg",
    "flightNbr",
    "fightDt",
    "legNbr",
    "startDate",
    "station",
    "dept",
    "departDateTime",
    "wriTotalTaskTimeMins",
    "wriSpanTimeDurationMins",
    "nbrOfAmtReq",
    "flightDirectionInd",
    "lateAssignment",
    "deleteReasonCd",
    "deleteReasonText",
    "updatedUserId",
    "nextScheduled",
    "geoConflictInd"
})
@XmlSeeAlso({
    AutoSchedPlanItem.class
})
public class PlannedItemAsgmtType {

    @XmlElement(required = true)
    protected BigDecimal plannedItemOid;
    @XmlElement(required = true)
    protected BigDecimal plannedItemAsgmtOid;
    @XmlElement(required = true)
    protected BigDecimal seqNbr;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar logAddTm;
    @XmlElement(required = true)
    protected String intentCd;
    @XmlElement(required = true)
    protected String grpStatDept;
    @XmlElement(required = true)
    protected String grpStartTime;
    @XmlElement(required = true)
    protected String grpDepartDateTime;
    @XmlElement(required = true)
    protected String grpFltIn;
    @XmlElement(required = true)
    protected String grpFltOut;
    @XmlElement(required = true)
    protected String scheduledBy;
    @XmlElement(required = true)
    protected String status;
    @XmlElement(required = true)
    protected String reasonCode;
    @XmlElement(required = true)
    protected String warnings;
    @XmlElement(required = true)
    protected BigDecimal manHoursExpended;
    @XmlElement(required = true)
    protected String planUnplanInd;
    @XmlElement(required = true)
    protected String maintType;
    @XmlElement(required = true)
    protected String priorityCd;
    @XmlElement(required = true)
    protected String aircraftNbr;
    @XmlElement(required = true)
    protected BigDecimal ataNbr;
    @XmlElement(required = true)
    protected BigDecimal discrpNbr;
    @XmlElement(required = true)
    protected BigDecimal discrepancyOid;
    protected boolean oilInterimFlg;
    @XmlElement(required = true)
    protected String flightNbr;
    @XmlElement(required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar fightDt;
    @XmlElement(required = true)
    protected BigDecimal legNbr;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar startDate;
    @XmlElement(required = true)
    protected String station;
    @XmlElement(required = true)
    protected String dept;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar departDateTime;
    @XmlElement(required = true)
    protected BigDecimal wriTotalTaskTimeMins;
    @XmlElement(required = true)
    protected BigDecimal wriSpanTimeDurationMins;
    @XmlElement(required = true)
    protected BigDecimal nbrOfAmtReq;
    @XmlElement(required = true)
    protected String flightDirectionInd;
    protected boolean lateAssignment;
    @XmlElement(required = true)
    protected String deleteReasonCd;
    @XmlElement(required = true)
    protected String deleteReasonText;
    @XmlElement(required = true)
    protected BigDecimal updatedUserId;
    protected boolean nextScheduled;
    protected boolean geoConflictInd;

    /**
     * Gets the value of the plannedItemOid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getPlannedItemOid() {
        return plannedItemOid;
    }

    /**
     * Sets the value of the plannedItemOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setPlannedItemOid(BigDecimal value) {
        this.plannedItemOid = value;
    }

    /**
     * Gets the value of the plannedItemAsgmtOid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getPlannedItemAsgmtOid() {
        return plannedItemAsgmtOid;
    }

    /**
     * Sets the value of the plannedItemAsgmtOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setPlannedItemAsgmtOid(BigDecimal value) {
        this.plannedItemAsgmtOid = value;
    }

    /**
     * Gets the value of the seqNbr property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getSeqNbr() {
        return seqNbr;
    }

    /**
     * Sets the value of the seqNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setSeqNbr(BigDecimal value) {
        this.seqNbr = value;
    }

    /**
     * Gets the value of the logAddTm property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLogAddTm() {
        return logAddTm;
    }

    /**
     * Sets the value of the logAddTm property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLogAddTm(XMLGregorianCalendar value) {
        this.logAddTm = value;
    }

    /**
     * Gets the value of the intentCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIntentCd() {
        return intentCd;
    }

    /**
     * Sets the value of the intentCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIntentCd(String value) {
        this.intentCd = value;
    }

    /**
     * Gets the value of the grpStatDept property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGrpStatDept() {
        return grpStatDept;
    }

    /**
     * Sets the value of the grpStatDept property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGrpStatDept(String value) {
        this.grpStatDept = value;
    }

    /**
     * Gets the value of the grpStartTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGrpStartTime() {
        return grpStartTime;
    }

    /**
     * Sets the value of the grpStartTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGrpStartTime(String value) {
        this.grpStartTime = value;
    }

    /**
     * Gets the value of the grpDepartDateTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGrpDepartDateTime() {
        return grpDepartDateTime;
    }

    /**
     * Sets the value of the grpDepartDateTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGrpDepartDateTime(String value) {
        this.grpDepartDateTime = value;
    }

    /**
     * Gets the value of the grpFltIn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGrpFltIn() {
        return grpFltIn;
    }

    /**
     * Sets the value of the grpFltIn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGrpFltIn(String value) {
        this.grpFltIn = value;
    }

    /**
     * Gets the value of the grpFltOut property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGrpFltOut() {
        return grpFltOut;
    }

    /**
     * Sets the value of the grpFltOut property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGrpFltOut(String value) {
        this.grpFltOut = value;
    }

    /**
     * Gets the value of the scheduledBy property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getScheduledBy() {
        return scheduledBy;
    }

    /**
     * Sets the value of the scheduledBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setScheduledBy(String value) {
        this.scheduledBy = value;
    }

    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStatus(String value) {
        this.status = value;
    }

    /**
     * Gets the value of the reasonCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReasonCode() {
        return reasonCode;
    }

    /**
     * Sets the value of the reasonCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReasonCode(String value) {
        this.reasonCode = value;
    }

    /**
     * Gets the value of the warnings property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWarnings() {
        return warnings;
    }

    /**
     * Sets the value of the warnings property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWarnings(String value) {
        this.warnings = value;
    }

    /**
     * Gets the value of the manHoursExpended property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getManHoursExpended() {
        return manHoursExpended;
    }

    /**
     * Sets the value of the manHoursExpended property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setManHoursExpended(BigDecimal value) {
        this.manHoursExpended = value;
    }

    /**
     * Gets the value of the planUnplanInd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPlanUnplanInd() {
        return planUnplanInd;
    }

    /**
     * Sets the value of the planUnplanInd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPlanUnplanInd(String value) {
        this.planUnplanInd = value;
    }

    /**
     * Gets the value of the maintType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMaintType() {
        return maintType;
    }

    /**
     * Sets the value of the maintType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMaintType(String value) {
        this.maintType = value;
    }

    /**
     * Gets the value of the priorityCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPriorityCd() {
        return priorityCd;
    }

    /**
     * Sets the value of the priorityCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPriorityCd(String value) {
        this.priorityCd = value;
    }

    /**
     * Gets the value of the aircraftNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAircraftNbr() {
        return aircraftNbr;
    }

    /**
     * Sets the value of the aircraftNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAircraftNbr(String value) {
        this.aircraftNbr = value;
    }

    /**
     * Gets the value of the ataNbr property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAtaNbr() {
        return ataNbr;
    }

    /**
     * Sets the value of the ataNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAtaNbr(BigDecimal value) {
        this.ataNbr = value;
    }

    /**
     * Gets the value of the discrpNbr property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getDiscrpNbr() {
        return discrpNbr;
    }

    /**
     * Sets the value of the discrpNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setDiscrpNbr(BigDecimal value) {
        this.discrpNbr = value;
    }

    /**
     * Gets the value of the discrepancyOid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getDiscrepancyOid() {
        return discrepancyOid;
    }

    /**
     * Sets the value of the discrepancyOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setDiscrepancyOid(BigDecimal value) {
        this.discrepancyOid = value;
    }

    /**
     * Gets the value of the oilInterimFlg property.
     * 
     */
    public boolean isOilInterimFlg() {
        return oilInterimFlg;
    }

    /**
     * Sets the value of the oilInterimFlg property.
     * 
     */
    public void setOilInterimFlg(boolean value) {
        this.oilInterimFlg = value;
    }

    /**
     * Gets the value of the flightNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFlightNbr() {
        return flightNbr;
    }

    /**
     * Sets the value of the flightNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFlightNbr(String value) {
        this.flightNbr = value;
    }

    /**
     * Gets the value of the fightDt property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getFightDt() {
        return fightDt;
    }

    /**
     * Sets the value of the fightDt property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setFightDt(XMLGregorianCalendar value) {
        this.fightDt = value;
    }

    /**
     * Gets the value of the legNbr property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getLegNbr() {
        return legNbr;
    }

    /**
     * Sets the value of the legNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setLegNbr(BigDecimal value) {
        this.legNbr = value;
    }

    /**
     * Gets the value of the startDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getStartDate() {
        return startDate;
    }

    /**
     * Sets the value of the startDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setStartDate(XMLGregorianCalendar value) {
        this.startDate = value;
    }

    /**
     * Gets the value of the station property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStation() {
        return station;
    }

    /**
     * Sets the value of the station property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStation(String value) {
        this.station = value;
    }

    /**
     * Gets the value of the dept property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDept() {
        return dept;
    }

    /**
     * Sets the value of the dept property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDept(String value) {
        this.dept = value;
    }

    /**
     * Gets the value of the departDateTime property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDepartDateTime() {
        return departDateTime;
    }

    /**
     * Sets the value of the departDateTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDepartDateTime(XMLGregorianCalendar value) {
        this.departDateTime = value;
    }

    /**
     * Gets the value of the wriTotalTaskTimeMins property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getWriTotalTaskTimeMins() {
        return wriTotalTaskTimeMins;
    }

    /**
     * Sets the value of the wriTotalTaskTimeMins property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setWriTotalTaskTimeMins(BigDecimal value) {
        this.wriTotalTaskTimeMins = value;
    }

    /**
     * Gets the value of the wriSpanTimeDurationMins property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getWriSpanTimeDurationMins() {
        return wriSpanTimeDurationMins;
    }

    /**
     * Sets the value of the wriSpanTimeDurationMins property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setWriSpanTimeDurationMins(BigDecimal value) {
        this.wriSpanTimeDurationMins = value;
    }

    /**
     * Gets the value of the nbrOfAmtReq property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getNbrOfAmtReq() {
        return nbrOfAmtReq;
    }

    /**
     * Sets the value of the nbrOfAmtReq property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setNbrOfAmtReq(BigDecimal value) {
        this.nbrOfAmtReq = value;
    }

    /**
     * Gets the value of the flightDirectionInd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFlightDirectionInd() {
        return flightDirectionInd;
    }

    /**
     * Sets the value of the flightDirectionInd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFlightDirectionInd(String value) {
        this.flightDirectionInd = value;
    }

    /**
     * Gets the value of the lateAssignment property.
     * 
     */
    public boolean isLateAssignment() {
        return lateAssignment;
    }

    /**
     * Sets the value of the lateAssignment property.
     * 
     */
    public void setLateAssignment(boolean value) {
        this.lateAssignment = value;
    }

    /**
     * Gets the value of the deleteReasonCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDeleteReasonCd() {
        return deleteReasonCd;
    }

    /**
     * Sets the value of the deleteReasonCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDeleteReasonCd(String value) {
        this.deleteReasonCd = value;
    }

    /**
     * Gets the value of the deleteReasonText property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDeleteReasonText() {
        return deleteReasonText;
    }

    /**
     * Sets the value of the deleteReasonText property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDeleteReasonText(String value) {
        this.deleteReasonText = value;
    }

    /**
     * Gets the value of the updatedUserId property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getUpdatedUserId() {
        return updatedUserId;
    }

    /**
     * Sets the value of the updatedUserId property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setUpdatedUserId(BigDecimal value) {
        this.updatedUserId = value;
    }

    /**
     * Gets the value of the nextScheduled property.
     * 
     */
    public boolean isNextScheduled() {
        return nextScheduled;
    }

    /**
     * Sets the value of the nextScheduled property.
     * 
     */
    public void setNextScheduled(boolean value) {
        this.nextScheduled = value;
    }

    /**
     * Gets the value of the geoConflictInd property.
     * 
     */
    public boolean isGeoConflictInd() {
        return geoConflictInd;
    }

    /**
     * Sets the value of the geoConflictInd property.
     * 
     */
    public void setGeoConflictInd(boolean value) {
        this.geoConflictInd = value;
    }

}
