package com.fedex.mets.entity.mets;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.sql.Timestamp;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name="intake_form")
public class IntakeForm {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "intake_form_seq")
    @SequenceGenerator(name = "intake_form_seq", sequenceName = "INTAKE_FORM_SEQ", allocationSize = 1)
    @Column(name = "intake_form_id")
    private int intakeFormId;

    @ManyToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinTable(name = "FORM_QUESTION_TABLE",
            joinColumns = {
                    @JoinColumn(name = "form_id", referencedColumnName = "intake_form_id")
            },
            inverseJoinColumns = {
                    @JoinColumn(name = "question_id", referencedColumnName = "question_id")
            }
    )
    private List<Question> questions;


    @Column(name="created_by")
    public String createdBy;

    @JsonFormat(
            shape = JsonFormat.Shape.STRING,
            pattern = "yyyy-MM-dd HH:mm:ss.SSS",
            timezone = "UTC")
    @CreatedDate
    @Column(name="created_tmstp")
    private Timestamp createdTMSTP;

    @JsonFormat(
            shape = JsonFormat.Shape.STRING,
            pattern = "yyyy-MM-dd HH:mm:ss.SSS",
            timezone = "UTC")
    @LastModifiedDate
    @Column(name="updated_tmstp")
    private Timestamp updatedTMSTP;

}
