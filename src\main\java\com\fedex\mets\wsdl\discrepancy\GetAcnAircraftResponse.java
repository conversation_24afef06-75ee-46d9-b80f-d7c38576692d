
package com.fedex.mets.wsdl.discrepancy;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;extension base="{http://www.fedex.com/airops/schemas/Common.xsd}GenericResponse">
 *       &lt;sequence>
 *         &lt;element name="acnAircraft" type="{http:///www.fedex.com/airops/schemas/Mach}acnAircraftDetail"/>
 *         &lt;element name="efvsCoOrdinate" type="{http://www.fedex.com/airops/schemas/AcnDiscrepancyUpdate.xsd}EfvsCoOrdinateType"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "acnAircraft",
    "efvsCoOrdinate"
})
@XmlRootElement(name = "getAcnAircraftResponse",namespace="http:///www.fedex.com/airops/schemas/Mach")
public class GetAcnAircraftResponse
    extends GenericResponse
{

    @XmlElement(required = true)
    protected AcnAircraftDetail acnAircraft;
    @XmlElement(required = true)
    protected EfvsCoOrdinateType efvsCoOrdinate;

    /**
     * Gets the value of the acnAircraft property.
     * 
     * @return
     *     possible object is
     *     {@link AcnAircraftDetail }
     *     
     */
    public AcnAircraftDetail getAcnAircraft() {
        return acnAircraft;
    }

    /**
     * Sets the value of the acnAircraft property.
     * 
     * @param value
     *     allowed object is
     *     {@link AcnAircraftDetail }
     *     
     */
    public void setAcnAircraft(AcnAircraftDetail value) {
        this.acnAircraft = value;
    }

    /**
     * Gets the value of the efvsCoOrdinate property.
     * 
     * @return
     *     possible object is
     *     {@link EfvsCoOrdinateType }
     *     
     */
    public EfvsCoOrdinateType getEfvsCoOrdinate() {
        return efvsCoOrdinate;
    }

    /**
     * Sets the value of the efvsCoOrdinate property.
     * 
     * @param value
     *     allowed object is
     *     {@link EfvsCoOrdinateType }
     *     
     */
    public void setEfvsCoOrdinate(EfvsCoOrdinateType value) {
        this.efvsCoOrdinate = value;
    }

}
