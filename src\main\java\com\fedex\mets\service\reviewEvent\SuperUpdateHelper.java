package com.fedex.mets.service.reviewEvent;

import com.fedex.mets.repository.cache.SuperEquipmentRepository;
import com.fedex.mets.util.ETICHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SuperUpdateHelper {
	private static final Logger logger = LoggerFactory.getLogger(SuperUpdateHelper.class);

	@Autowired
	private SuperEquipmentRepository superEquipmentRepository;

	/**
	* Method to Check the if Super Update is required.
	* @ params  String strAcn, String strNewStatus, String strNewEticDateTime,
				String strNewEticText, String strNewComment.
	* @ return boolean updateRequired.
	*/
	public boolean superUpdateRequired(
		String strAcn,
		String strNewStatus,
		String strNewEticDateTime,
		String strNewEticText,
		String strNewComment)
		throws Exception {
		boolean updateRequired = false;

		java.sql.Timestamp eticDateTime = null, eticDateTimeStamp = null;
		String strEticText = "", strOperationalStatus = "", strComment = "";

		String strEticTextToSuper = "";

		if (strNewEticDateTime == null && strNewEticText != null) {
			strEticTextToSuper = strNewEticText;
		}

		if (strNewEticDateTime != null && strNewEticText != null) {
			strEticTextToSuper =
				ETICHelper.getETICFormat(strNewEticDateTime, strNewEticText);
		}

		try {
			String  superEquipment = superEquipmentRepository.getSuperUpdateRequiredDetails(strAcn);
			String[] dataInfo = superEquipment!=null?superEquipment.split(","):null;
			if(dataInfo!=null){
				strEticText = dataInfo[0];
				strOperationalStatus = dataInfo[1];
				strComment = dataInfo[2];
			}
		} catch (Exception equipment) {
			equipment.printStackTrace();
		}
		logger.debug(" Super_Equipment >> eticDateTime.." + eticDateTime);
		logger.debug(" Super_Equipment >> strEticText.." + strEticText);
		logger.debug(
			" Super_Equipment >> strOperationalStatus.." + strOperationalStatus);
		logger.debug(" Super_Equipment >> strComment.." + strComment);
		logger.debug(" ");
		logger.debug(" wizardEventData >> strNewEticDateTime.." + strNewEticDateTime);
		logger.debug(" wizardEventData >> strEticTextToSuper.." + strEticTextToSuper);
		logger.debug(" wizardEventData >> strNewStatus.." + strNewStatus);
		logger.debug(" wizardEventData >> strNewComment.." + strNewComment);

		if (strOperationalStatus != null
			&& strOperationalStatus.trim().length() > 0) {
			if (!strOperationalStatus.equalsIgnoreCase(strNewStatus)) {
				logger.debug(" .. 1 ..");
				logger.debug(" As Status does not match SUPER_EQUIPMENT update is required");
				updateRequired = true;
			}

			if (strComment != null
				&& !(strComment.trim().equalsIgnoreCase(strNewComment))) {
				logger.debug(" .. 2 ..");
				logger.debug(" As Comment does not match SUPER_EQUIPMENT update is required");
				updateRequired = true;
			}

			if (eticDateTime != null && !(eticDateTime.equals(eticDateTimeStamp))) {
				logger.debug(" .. 3 ..");
				logger.debug(" As eticDateTime does not match SUPER_EQUIPMENT update is required");
				updateRequired = true;
				//			}else if(eticDateTime==null){

				//           Changed the above condition because there can be conditions where
				//           the user does not change the date but changes the info to a 2 char
				//           value e.g. "WA"
			} else {
				if (strEticText != null
					&& !(strEticText.trim().equalsIgnoreCase(strEticTextToSuper))) {
					logger.debug(" .. 4 ..");
					logger.debug(" As strEticTextToSuper does not match SUPER_EQUIPMENT update is required");
					updateRequired = true;
				} else if (
					strEticText == null
						&& (strEticTextToSuper != null
							&& strEticTextToSuper.trim().length() > 0)) {
					logger.debug(" .. 5 ..");
					logger.debug(" As strEticText is null && strEticTextToSuper does not match SUPER_EQUIPMENT update is required");
					updateRequired = true;
				}
			}
		}else if (strOperationalStatus == null
		|| strOperationalStatus.trim().length() == 0){
			//new condition added so the update can be sent to super even if a record is not found.
			logger.debug(" .. 6 ..");
			logger.debug(" As no record found in SUPER_EQUIPMENT update is required");
			updateRequired = true;
		}
		logger.debug(
			"SUPER_EQUIPMENT update is required --> "
				+ updateRequired);
		return updateRequired;
	}


}