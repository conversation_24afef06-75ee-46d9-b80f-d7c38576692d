
package com.fedex.mets.wsdl.msn.shortagenotice;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for shortageNoticeType complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="shortageNoticeType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="shortageNotcIsn" type="{http://www.w3.org/2001/XMLSchema}long"/>
 *         &lt;element name="applCode" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="transName" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="intentCode" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="transTm" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="transUserId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="terminalId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="addTm" type="{http://www.w3.org/2001/XMLSchema}date"/>
 *         &lt;element name="addUserId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="constructTime" type="{http://www.w3.org/2001/XMLSchema}date"/>
 *         &lt;element name="moduleName" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="msn" type="{http://www.w3.org/2001/XMLSchema}long"/>
 *         &lt;element name="manufPartNbr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="statusCd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="followUpCd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="coPartNbr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="aircraft" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="cityLoc" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="bohDiscrpCd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="enterDt9C" type="{http://www.w3.org/2001/XMLSchema}long"/>
 *         &lt;element name="inByName" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="spec2KMpn" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="refIpc" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="refTypeNbr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="cpnQty" type="{http://www.w3.org/2001/XMLSchema}long"/>
 *         &lt;element name="ataChap" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ataSubChap" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="dscrpNbr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="dscrpOid" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ctgStatusCd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="enterTm" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="requestedById" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="requestedByName" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="requestedByPhone" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="authorizedBy" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="planApprovBy" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="planNotMetPrtCd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="resDt" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="resTime" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="resTotHr" type="{http://www.w3.org/2001/XMLSchema}long"/>
 *         &lt;element name="resStaff" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="estResTime" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="estResDt" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="resFailureCd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="svcLvlGoal" type="{http://www.w3.org/2001/XMLSchema}long"/>
 *         &lt;element name="shprPrtdCode" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="planMetInd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="materialInLoc" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="cpnUnitOfMeasure" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="cpnDescription" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="cpnNoun" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="shiptoSta" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="datePartNeedBy" type="{http://www.w3.org/2001/XMLSchema}long"/>
 *         &lt;element name="timePartNeedBy" type="{http://www.w3.org/2001/XMLSchema}long"/>
 *         &lt;element name="shiptoBoh" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="codeLocControl" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="qtyContAtBoh" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="qtyReqPo" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="qtyOrderPurchase" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="qtySoOpen" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="qtyBoOpen" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="qtyBohServ" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="qtyItServ" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="qtyBohUnserv" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="qtyItUnserv" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="avgAmuGlobal" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="woNbr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="taskNbr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="seqNonRtnNbr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="shiptoDept" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="um" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="primeMpn" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="dangerousGoodFlg" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="bomFlg" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="alloc" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="crossRefCpn" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="crossRefType" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="assigneeId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="assigneeName" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="assigneePhone" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="fleet" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="fakInfo" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="poNbr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="poFsc" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="poQty" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="tsp" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="printerId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="mpnHasMultipleCpns" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="charterPo" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="charterFsc" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="consumable" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="accountGroup" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="oem" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="msnCpnCreateFlag" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="interchangeCpn" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="turnOverFlag" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pickupFlag" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="bohQty" type="{http://www.w3.org/2001/XMLSchema}long" minOccurs="0"/>
 *         &lt;element name="requiredByDateTime" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "shortageNoticeType", namespace = "http://fedex.com/airops/maxi/services/jaxws/material", propOrder = {
    "shortageNotcIsn",
    "applCode",
    "transName",
    "intentCode",
    "transTm",
    "transUserId",
    "terminalId",
    "addTm",
    "addUserId",
    "constructTime",
    "moduleName",
    "msn",
    "manufPartNbr",
    "statusCd",
    "followUpCd",
    "coPartNbr",
    "aircraft",
    "cityLoc",
    "bohDiscrpCd",
    "enterDt9C",
    "inByName",
    "spec2KMpn",
    "refIpc",
    "refTypeNbr",
    "cpnQty",
    "ataChap",
    "ataSubChap",
    "dscrpNbr",
    "dscrpOid",
    "ctgStatusCd",
    "enterTm",
    "requestedById",
    "requestedByName",
    "requestedByPhone",
    "authorizedBy",
    "planApprovBy",
    "planNotMetPrtCd",
    "resDt",
    "resTime",
    "resTotHr",
    "resStaff",
    "estResTime",
    "estResDt",
    "resFailureCd",
    "svcLvlGoal",
    "shprPrtdCode",
    "planMetInd",
    "materialInLoc",
    "cpnUnitOfMeasure",
    "cpnDescription",
    "cpnNoun",
    "shiptoSta",
    "datePartNeedBy",
    "timePartNeedBy",
    "shiptoBoh",
    "codeLocControl",
    "qtyContAtBoh",
    "qtyReqPo",
    "qtyOrderPurchase",
    "qtySoOpen",
    "qtyBoOpen",
    "qtyBohServ",
    "qtyItServ",
    "qtyBohUnserv",
    "qtyItUnserv",
    "avgAmuGlobal",
    "woNbr",
    "taskNbr",
    "seqNonRtnNbr",
    "shiptoDept",
    "um",
    "primeMpn",
    "dangerousGoodFlg",
    "bomFlg",
    "alloc",
    "crossRefCpn",
    "crossRefType",
    "assigneeId",
    "assigneeName",
    "assigneePhone",
    "fleet",
    "fakInfo",
    "poNbr",
    "poFsc",
    "poQty",
    "tsp",
    "printerId",
    "mpnHasMultipleCpns",
    "charterPo",
    "charterFsc",
    "consumable",
    "accountGroup",
    "oem",
    "msnCpnCreateFlag",
    "interchangeCpn",
    "turnOverFlag",
    "pickupFlag",
    "bohQty",
    "requiredByDateTime"
})
public class ShortageNoticeType {

    protected long shortageNotcIsn;
    @XmlElement(required = true)
    protected String applCode;
    @XmlElement(required = true)
    protected String transName;
    @XmlElement(required = true)
    protected String intentCode;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar transTm;
    @XmlElement(required = true)
    protected String transUserId;
    @XmlElement(required = true)
    protected String terminalId;
    @XmlElement(required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar addTm;
    @XmlElement(required = true)
    protected String addUserId;
    @XmlElement(required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar constructTime;
    @XmlElement(required = true)
    protected String moduleName;
    protected long msn;
    @XmlElement(required = true)
    protected String manufPartNbr;
    @XmlElement(required = true)
    protected String statusCd;
    @XmlElement(required = true)
    protected String followUpCd;
    @XmlElement(required = true)
    protected String coPartNbr;
    @XmlElement(required = true)
    protected String aircraft;
    @XmlElement(required = true)
    protected String cityLoc;
    @XmlElement(required = true)
    protected String bohDiscrpCd;
    protected long enterDt9C;
    @XmlElement(required = true)
    protected String inByName;
    @XmlElement(required = true)
    protected String spec2KMpn;
    @XmlElement(required = true)
    protected String refIpc;
    @XmlElement(required = true)
    protected String refTypeNbr;
    protected long cpnQty;
    @XmlElement(required = true)
    protected String ataChap;
    @XmlElement(required = true)
    protected String ataSubChap;
    @XmlElement(required = true)
    protected String dscrpNbr;
    @XmlElement(required = true)
    protected String dscrpOid;
    @XmlElement(required = true)
    protected String ctgStatusCd;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar enterTm;
    @XmlElement(required = true)
    protected String requestedById;
    @XmlElement(required = true)
    protected String requestedByName;
    @XmlElement(required = true)
    protected String requestedByPhone;
    @XmlElement(required = true)
    protected String authorizedBy;
    @XmlElement(required = true)
    protected String planApprovBy;
    @XmlElement(required = true)
    protected String planNotMetPrtCd;
    @XmlElement(required = true)
    protected String resDt;
    @XmlElement(required = true)
    protected String resTime;
    protected long resTotHr;
    @XmlElement(required = true)
    protected String resStaff;
    @XmlElement(required = true)
    protected String estResTime;
    @XmlElement(required = true)
    protected String estResDt;
    @XmlElement(required = true)
    protected String resFailureCd;
    protected long svcLvlGoal;
    @XmlElement(required = true)
    protected String shprPrtdCode;
    @XmlElement(required = true)
    protected String planMetInd;
    @XmlElement(required = true)
    protected String materialInLoc;
    @XmlElement(required = true)
    protected String cpnUnitOfMeasure;
    @XmlElement(required = true)
    protected String cpnDescription;
    @XmlElement(required = true)
    protected String cpnNoun;
    @XmlElement(required = true)
    protected String shiptoSta;
    protected long datePartNeedBy;
    protected long timePartNeedBy;
    @XmlElement(required = true)
    protected String shiptoBoh;
    @XmlElement(required = true)
    protected String codeLocControl;
    @XmlElement(required = true)
    protected String qtyContAtBoh;
    @XmlElement(required = true)
    protected String qtyReqPo;
    @XmlElement(required = true)
    protected String qtyOrderPurchase;
    @XmlElement(required = true)
    protected String qtySoOpen;
    @XmlElement(required = true)
    protected String qtyBoOpen;
    @XmlElement(required = true)
    protected String qtyBohServ;
    @XmlElement(required = true)
    protected String qtyItServ;
    @XmlElement(required = true)
    protected String qtyBohUnserv;
    @XmlElement(required = true)
    protected String qtyItUnserv;
    @XmlElement(required = true)
    protected String avgAmuGlobal;
    @XmlElement(required = true)
    protected String woNbr;
    @XmlElement(required = true)
    protected String taskNbr;
    @XmlElement(required = true)
    protected String seqNonRtnNbr;
    @XmlElement(required = true)
    protected String shiptoDept;
    @XmlElement(required = true)
    protected String um;
    @XmlElement(required = true)
    protected String primeMpn;
    @XmlElement(required = true)
    protected String dangerousGoodFlg;
    @XmlElement(required = true)
    protected String bomFlg;
    @XmlElement(required = true)
    protected String alloc;
    @XmlElement(required = true)
    protected String crossRefCpn;
    @XmlElement(required = true)
    protected String crossRefType;
    @XmlElement(required = true)
    protected String assigneeId;
    @XmlElement(required = true)
    protected String assigneeName;
    @XmlElement(required = true)
    protected String assigneePhone;
    @XmlElement(required = true)
    protected String fleet;
    @XmlElement(required = true)
    protected String fakInfo;
    @XmlElement(required = true)
    protected String poNbr;
    @XmlElement(required = true)
    protected String poFsc;
    @XmlElement(required = true)
    protected String poQty;
    @XmlElement(required = true)
    protected String tsp;
    @XmlElement(required = true)
    protected String printerId;
    protected boolean mpnHasMultipleCpns;
    @XmlElement(required = true)
    protected String charterPo;
    @XmlElement(required = true)
    protected String charterFsc;
    @XmlElement(required = true)
    protected String consumable;
    @XmlElement(required = true)
    protected String accountGroup;
    @XmlElement(required = true)
    protected String oem;
    protected boolean msnCpnCreateFlag;
    @XmlElement(required = true)
    protected String interchangeCpn;
    @XmlElement(required = true)
    protected String turnOverFlag;
    @XmlElement(required = true)
    protected String pickupFlag;
    protected Long bohQty;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar requiredByDateTime;

    /**
     * Gets the value of the shortageNotcIsn property.
     * 
     */
    public long getShortageNotcIsn() {
        return shortageNotcIsn;
    }

    /**
     * Sets the value of the shortageNotcIsn property.
     * 
     */
    public void setShortageNotcIsn(long value) {
        this.shortageNotcIsn = value;
    }

    /**
     * Gets the value of the applCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getApplCode() {
        return applCode;
    }

    /**
     * Sets the value of the applCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setApplCode(String value) {
        this.applCode = value;
    }

    /**
     * Gets the value of the transName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTransName() {
        return transName;
    }

    /**
     * Sets the value of the transName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTransName(String value) {
        this.transName = value;
    }

    /**
     * Gets the value of the intentCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIntentCode() {
        return intentCode;
    }

    /**
     * Sets the value of the intentCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIntentCode(String value) {
        this.intentCode = value;
    }

    /**
     * Gets the value of the transTm property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getTransTm() {
        return transTm;
    }

    /**
     * Sets the value of the transTm property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setTransTm(XMLGregorianCalendar value) {
        this.transTm = value;
    }

    /**
     * Gets the value of the transUserId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTransUserId() {
        return transUserId;
    }

    /**
     * Sets the value of the transUserId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTransUserId(String value) {
        this.transUserId = value;
    }

    /**
     * Gets the value of the terminalId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTerminalId() {
        return terminalId;
    }

    /**
     * Sets the value of the terminalId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTerminalId(String value) {
        this.terminalId = value;
    }

    /**
     * Gets the value of the addTm property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getAddTm() {
        return addTm;
    }

    /**
     * Sets the value of the addTm property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setAddTm(XMLGregorianCalendar value) {
        this.addTm = value;
    }

    /**
     * Gets the value of the addUserId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAddUserId() {
        return addUserId;
    }

    /**
     * Sets the value of the addUserId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAddUserId(String value) {
        this.addUserId = value;
    }

    /**
     * Gets the value of the constructTime property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getConstructTime() {
        return constructTime;
    }

    /**
     * Sets the value of the constructTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setConstructTime(XMLGregorianCalendar value) {
        this.constructTime = value;
    }

    /**
     * Gets the value of the moduleName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getModuleName() {
        return moduleName;
    }

    /**
     * Sets the value of the moduleName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setModuleName(String value) {
        this.moduleName = value;
    }

    /**
     * Gets the value of the msn property.
     * 
     */
    public long getMsn() {
        return msn;
    }

    /**
     * Sets the value of the msn property.
     * 
     */
    public void setMsn(long value) {
        this.msn = value;
    }

    /**
     * Gets the value of the manufPartNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getManufPartNbr() {
        return manufPartNbr;
    }

    /**
     * Sets the value of the manufPartNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setManufPartNbr(String value) {
        this.manufPartNbr = value;
    }

    /**
     * Gets the value of the statusCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStatusCd() {
        return statusCd;
    }

    /**
     * Sets the value of the statusCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStatusCd(String value) {
        this.statusCd = value;
    }

    /**
     * Gets the value of the followUpCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFollowUpCd() {
        return followUpCd;
    }

    /**
     * Sets the value of the followUpCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFollowUpCd(String value) {
        this.followUpCd = value;
    }

    /**
     * Gets the value of the coPartNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCoPartNbr() {
        return coPartNbr;
    }

    /**
     * Sets the value of the coPartNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCoPartNbr(String value) {
        this.coPartNbr = value;
    }

    /**
     * Gets the value of the aircraft property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAircraft() {
        return aircraft;
    }

    /**
     * Sets the value of the aircraft property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAircraft(String value) {
        this.aircraft = value;
    }

    /**
     * Gets the value of the cityLoc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCityLoc() {
        return cityLoc;
    }

    /**
     * Sets the value of the cityLoc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCityLoc(String value) {
        this.cityLoc = value;
    }

    /**
     * Gets the value of the bohDiscrpCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBohDiscrpCd() {
        return bohDiscrpCd;
    }

    /**
     * Sets the value of the bohDiscrpCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBohDiscrpCd(String value) {
        this.bohDiscrpCd = value;
    }

    /**
     * Gets the value of the enterDt9C property.
     * 
     */
    public long getEnterDt9C() {
        return enterDt9C;
    }

    /**
     * Sets the value of the enterDt9C property.
     * 
     */
    public void setEnterDt9C(long value) {
        this.enterDt9C = value;
    }

    /**
     * Gets the value of the inByName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInByName() {
        return inByName;
    }

    /**
     * Sets the value of the inByName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInByName(String value) {
        this.inByName = value;
    }

    /**
     * Gets the value of the spec2KMpn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSpec2KMpn() {
        return spec2KMpn;
    }

    /**
     * Sets the value of the spec2KMpn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSpec2KMpn(String value) {
        this.spec2KMpn = value;
    }

    /**
     * Gets the value of the refIpc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRefIpc() {
        return refIpc;
    }

    /**
     * Sets the value of the refIpc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRefIpc(String value) {
        this.refIpc = value;
    }

    /**
     * Gets the value of the refTypeNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRefTypeNbr() {
        return refTypeNbr;
    }

    /**
     * Sets the value of the refTypeNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRefTypeNbr(String value) {
        this.refTypeNbr = value;
    }

    /**
     * Gets the value of the cpnQty property.
     * 
     */
    public long getCpnQty() {
        return cpnQty;
    }

    /**
     * Sets the value of the cpnQty property.
     * 
     */
    public void setCpnQty(long value) {
        this.cpnQty = value;
    }

    /**
     * Gets the value of the ataChap property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAtaChap() {
        return ataChap;
    }

    /**
     * Sets the value of the ataChap property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAtaChap(String value) {
        this.ataChap = value;
    }

    /**
     * Gets the value of the ataSubChap property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAtaSubChap() {
        return ataSubChap;
    }

    /**
     * Sets the value of the ataSubChap property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAtaSubChap(String value) {
        this.ataSubChap = value;
    }

    /**
     * Gets the value of the dscrpNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDscrpNbr() {
        return dscrpNbr;
    }

    /**
     * Sets the value of the dscrpNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDscrpNbr(String value) {
        this.dscrpNbr = value;
    }

    /**
     * Gets the value of the dscrpOid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDscrpOid() {
        return dscrpOid;
    }

    /**
     * Sets the value of the dscrpOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDscrpOid(String value) {
        this.dscrpOid = value;
    }

    /**
     * Gets the value of the ctgStatusCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCtgStatusCd() {
        return ctgStatusCd;
    }

    /**
     * Sets the value of the ctgStatusCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCtgStatusCd(String value) {
        this.ctgStatusCd = value;
    }

    /**
     * Gets the value of the enterTm property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getEnterTm() {
        return enterTm;
    }

    /**
     * Sets the value of the enterTm property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setEnterTm(XMLGregorianCalendar value) {
        this.enterTm = value;
    }

    /**
     * Gets the value of the requestedById property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRequestedById() {
        return requestedById;
    }

    /**
     * Sets the value of the requestedById property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRequestedById(String value) {
        this.requestedById = value;
    }

    /**
     * Gets the value of the requestedByName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRequestedByName() {
        return requestedByName;
    }

    /**
     * Sets the value of the requestedByName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRequestedByName(String value) {
        this.requestedByName = value;
    }

    /**
     * Gets the value of the requestedByPhone property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRequestedByPhone() {
        return requestedByPhone;
    }

    /**
     * Sets the value of the requestedByPhone property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRequestedByPhone(String value) {
        this.requestedByPhone = value;
    }

    /**
     * Gets the value of the authorizedBy property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAuthorizedBy() {
        return authorizedBy;
    }

    /**
     * Sets the value of the authorizedBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAuthorizedBy(String value) {
        this.authorizedBy = value;
    }

    /**
     * Gets the value of the planApprovBy property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPlanApprovBy() {
        return planApprovBy;
    }

    /**
     * Sets the value of the planApprovBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPlanApprovBy(String value) {
        this.planApprovBy = value;
    }

    /**
     * Gets the value of the planNotMetPrtCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPlanNotMetPrtCd() {
        return planNotMetPrtCd;
    }

    /**
     * Sets the value of the planNotMetPrtCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPlanNotMetPrtCd(String value) {
        this.planNotMetPrtCd = value;
    }

    /**
     * Gets the value of the resDt property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getResDt() {
        return resDt;
    }

    /**
     * Sets the value of the resDt property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setResDt(String value) {
        this.resDt = value;
    }

    /**
     * Gets the value of the resTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getResTime() {
        return resTime;
    }

    /**
     * Sets the value of the resTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setResTime(String value) {
        this.resTime = value;
    }

    /**
     * Gets the value of the resTotHr property.
     * 
     */
    public long getResTotHr() {
        return resTotHr;
    }

    /**
     * Sets the value of the resTotHr property.
     * 
     */
    public void setResTotHr(long value) {
        this.resTotHr = value;
    }

    /**
     * Gets the value of the resStaff property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getResStaff() {
        return resStaff;
    }

    /**
     * Sets the value of the resStaff property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setResStaff(String value) {
        this.resStaff = value;
    }

    /**
     * Gets the value of the estResTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEstResTime() {
        return estResTime;
    }

    /**
     * Sets the value of the estResTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEstResTime(String value) {
        this.estResTime = value;
    }

    /**
     * Gets the value of the estResDt property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEstResDt() {
        return estResDt;
    }

    /**
     * Sets the value of the estResDt property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEstResDt(String value) {
        this.estResDt = value;
    }

    /**
     * Gets the value of the resFailureCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getResFailureCd() {
        return resFailureCd;
    }

    /**
     * Sets the value of the resFailureCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setResFailureCd(String value) {
        this.resFailureCd = value;
    }

    /**
     * Gets the value of the svcLvlGoal property.
     * 
     */
    public long getSvcLvlGoal() {
        return svcLvlGoal;
    }

    /**
     * Sets the value of the svcLvlGoal property.
     * 
     */
    public void setSvcLvlGoal(long value) {
        this.svcLvlGoal = value;
    }

    /**
     * Gets the value of the shprPrtdCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getShprPrtdCode() {
        return shprPrtdCode;
    }

    /**
     * Sets the value of the shprPrtdCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setShprPrtdCode(String value) {
        this.shprPrtdCode = value;
    }

    /**
     * Gets the value of the planMetInd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPlanMetInd() {
        return planMetInd;
    }

    /**
     * Sets the value of the planMetInd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPlanMetInd(String value) {
        this.planMetInd = value;
    }

    /**
     * Gets the value of the materialInLoc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMaterialInLoc() {
        return materialInLoc;
    }

    /**
     * Sets the value of the materialInLoc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMaterialInLoc(String value) {
        this.materialInLoc = value;
    }

    /**
     * Gets the value of the cpnUnitOfMeasure property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCpnUnitOfMeasure() {
        return cpnUnitOfMeasure;
    }

    /**
     * Sets the value of the cpnUnitOfMeasure property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCpnUnitOfMeasure(String value) {
        this.cpnUnitOfMeasure = value;
    }

    /**
     * Gets the value of the cpnDescription property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCpnDescription() {
        return cpnDescription;
    }

    /**
     * Sets the value of the cpnDescription property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCpnDescription(String value) {
        this.cpnDescription = value;
    }

    /**
     * Gets the value of the cpnNoun property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCpnNoun() {
        return cpnNoun;
    }

    /**
     * Sets the value of the cpnNoun property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCpnNoun(String value) {
        this.cpnNoun = value;
    }

    /**
     * Gets the value of the shiptoSta property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getShiptoSta() {
        return shiptoSta;
    }

    /**
     * Sets the value of the shiptoSta property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setShiptoSta(String value) {
        this.shiptoSta = value;
    }

    /**
     * Gets the value of the datePartNeedBy property.
     * 
     */
    public long getDatePartNeedBy() {
        return datePartNeedBy;
    }

    /**
     * Sets the value of the datePartNeedBy property.
     * 
     */
    public void setDatePartNeedBy(long value) {
        this.datePartNeedBy = value;
    }

    /**
     * Gets the value of the timePartNeedBy property.
     * 
     */
    public long getTimePartNeedBy() {
        return timePartNeedBy;
    }

    /**
     * Sets the value of the timePartNeedBy property.
     * 
     */
    public void setTimePartNeedBy(long value) {
        this.timePartNeedBy = value;
    }

    /**
     * Gets the value of the shiptoBoh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getShiptoBoh() {
        return shiptoBoh;
    }

    /**
     * Sets the value of the shiptoBoh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setShiptoBoh(String value) {
        this.shiptoBoh = value;
    }

    /**
     * Gets the value of the codeLocControl property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodeLocControl() {
        return codeLocControl;
    }

    /**
     * Sets the value of the codeLocControl property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodeLocControl(String value) {
        this.codeLocControl = value;
    }

    /**
     * Gets the value of the qtyContAtBoh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getQtyContAtBoh() {
        return qtyContAtBoh;
    }

    /**
     * Sets the value of the qtyContAtBoh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setQtyContAtBoh(String value) {
        this.qtyContAtBoh = value;
    }

    /**
     * Gets the value of the qtyReqPo property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getQtyReqPo() {
        return qtyReqPo;
    }

    /**
     * Sets the value of the qtyReqPo property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setQtyReqPo(String value) {
        this.qtyReqPo = value;
    }

    /**
     * Gets the value of the qtyOrderPurchase property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getQtyOrderPurchase() {
        return qtyOrderPurchase;
    }

    /**
     * Sets the value of the qtyOrderPurchase property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setQtyOrderPurchase(String value) {
        this.qtyOrderPurchase = value;
    }

    /**
     * Gets the value of the qtySoOpen property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getQtySoOpen() {
        return qtySoOpen;
    }

    /**
     * Sets the value of the qtySoOpen property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setQtySoOpen(String value) {
        this.qtySoOpen = value;
    }

    /**
     * Gets the value of the qtyBoOpen property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getQtyBoOpen() {
        return qtyBoOpen;
    }

    /**
     * Sets the value of the qtyBoOpen property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setQtyBoOpen(String value) {
        this.qtyBoOpen = value;
    }

    /**
     * Gets the value of the qtyBohServ property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getQtyBohServ() {
        return qtyBohServ;
    }

    /**
     * Sets the value of the qtyBohServ property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setQtyBohServ(String value) {
        this.qtyBohServ = value;
    }

    /**
     * Gets the value of the qtyItServ property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getQtyItServ() {
        return qtyItServ;
    }

    /**
     * Sets the value of the qtyItServ property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setQtyItServ(String value) {
        this.qtyItServ = value;
    }

    /**
     * Gets the value of the qtyBohUnserv property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getQtyBohUnserv() {
        return qtyBohUnserv;
    }

    /**
     * Sets the value of the qtyBohUnserv property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setQtyBohUnserv(String value) {
        this.qtyBohUnserv = value;
    }

    /**
     * Gets the value of the qtyItUnserv property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getQtyItUnserv() {
        return qtyItUnserv;
    }

    /**
     * Sets the value of the qtyItUnserv property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setQtyItUnserv(String value) {
        this.qtyItUnserv = value;
    }

    /**
     * Gets the value of the avgAmuGlobal property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAvgAmuGlobal() {
        return avgAmuGlobal;
    }

    /**
     * Sets the value of the avgAmuGlobal property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAvgAmuGlobal(String value) {
        this.avgAmuGlobal = value;
    }

    /**
     * Gets the value of the woNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWoNbr() {
        return woNbr;
    }

    /**
     * Sets the value of the woNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWoNbr(String value) {
        this.woNbr = value;
    }

    /**
     * Gets the value of the taskNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTaskNbr() {
        return taskNbr;
    }

    /**
     * Sets the value of the taskNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTaskNbr(String value) {
        this.taskNbr = value;
    }

    /**
     * Gets the value of the seqNonRtnNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSeqNonRtnNbr() {
        return seqNonRtnNbr;
    }

    /**
     * Sets the value of the seqNonRtnNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSeqNonRtnNbr(String value) {
        this.seqNonRtnNbr = value;
    }

    /**
     * Gets the value of the shiptoDept property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getShiptoDept() {
        return shiptoDept;
    }

    /**
     * Sets the value of the shiptoDept property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setShiptoDept(String value) {
        this.shiptoDept = value;
    }

    /**
     * Gets the value of the um property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUm() {
        return um;
    }

    /**
     * Sets the value of the um property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUm(String value) {
        this.um = value;
    }

    /**
     * Gets the value of the primeMpn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPrimeMpn() {
        return primeMpn;
    }

    /**
     * Sets the value of the primeMpn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPrimeMpn(String value) {
        this.primeMpn = value;
    }

    /**
     * Gets the value of the dangerousGoodFlg property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDangerousGoodFlg() {
        return dangerousGoodFlg;
    }

    /**
     * Sets the value of the dangerousGoodFlg property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDangerousGoodFlg(String value) {
        this.dangerousGoodFlg = value;
    }

    /**
     * Gets the value of the bomFlg property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBomFlg() {
        return bomFlg;
    }

    /**
     * Sets the value of the bomFlg property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBomFlg(String value) {
        this.bomFlg = value;
    }

    /**
     * Gets the value of the alloc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAlloc() {
        return alloc;
    }

    /**
     * Sets the value of the alloc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAlloc(String value) {
        this.alloc = value;
    }

    /**
     * Gets the value of the crossRefCpn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCrossRefCpn() {
        return crossRefCpn;
    }

    /**
     * Sets the value of the crossRefCpn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCrossRefCpn(String value) {
        this.crossRefCpn = value;
    }

    /**
     * Gets the value of the crossRefType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCrossRefType() {
        return crossRefType;
    }

    /**
     * Sets the value of the crossRefType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCrossRefType(String value) {
        this.crossRefType = value;
    }

    /**
     * Gets the value of the assigneeId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAssigneeId() {
        return assigneeId;
    }

    /**
     * Sets the value of the assigneeId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAssigneeId(String value) {
        this.assigneeId = value;
    }

    /**
     * Gets the value of the assigneeName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAssigneeName() {
        return assigneeName;
    }

    /**
     * Sets the value of the assigneeName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAssigneeName(String value) {
        this.assigneeName = value;
    }

    /**
     * Gets the value of the assigneePhone property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAssigneePhone() {
        return assigneePhone;
    }

    /**
     * Sets the value of the assigneePhone property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAssigneePhone(String value) {
        this.assigneePhone = value;
    }

    /**
     * Gets the value of the fleet property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFleet() {
        return fleet;
    }

    /**
     * Sets the value of the fleet property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFleet(String value) {
        this.fleet = value;
    }

    /**
     * Gets the value of the fakInfo property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFakInfo() {
        return fakInfo;
    }

    /**
     * Sets the value of the fakInfo property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFakInfo(String value) {
        this.fakInfo = value;
    }

    /**
     * Gets the value of the poNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPoNbr() {
        return poNbr;
    }

    /**
     * Sets the value of the poNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPoNbr(String value) {
        this.poNbr = value;
    }

    /**
     * Gets the value of the poFsc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPoFsc() {
        return poFsc;
    }

    /**
     * Sets the value of the poFsc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPoFsc(String value) {
        this.poFsc = value;
    }

    /**
     * Gets the value of the poQty property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPoQty() {
        return poQty;
    }

    /**
     * Sets the value of the poQty property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPoQty(String value) {
        this.poQty = value;
    }

    /**
     * Gets the value of the tsp property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTsp() {
        return tsp;
    }

    /**
     * Sets the value of the tsp property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTsp(String value) {
        this.tsp = value;
    }

    /**
     * Gets the value of the printerId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPrinterId() {
        return printerId;
    }

    /**
     * Sets the value of the printerId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPrinterId(String value) {
        this.printerId = value;
    }

    /**
     * Gets the value of the mpnHasMultipleCpns property.
     * 
     */
    public boolean isMpnHasMultipleCpns() {
        return mpnHasMultipleCpns;
    }

    /**
     * Sets the value of the mpnHasMultipleCpns property.
     * 
     */
    public void setMpnHasMultipleCpns(boolean value) {
        this.mpnHasMultipleCpns = value;
    }

    /**
     * Gets the value of the charterPo property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCharterPo() {
        return charterPo;
    }

    /**
     * Sets the value of the charterPo property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCharterPo(String value) {
        this.charterPo = value;
    }

    /**
     * Gets the value of the charterFsc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCharterFsc() {
        return charterFsc;
    }

    /**
     * Sets the value of the charterFsc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCharterFsc(String value) {
        this.charterFsc = value;
    }

    /**
     * Gets the value of the consumable property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getConsumable() {
        return consumable;
    }

    /**
     * Sets the value of the consumable property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setConsumable(String value) {
        this.consumable = value;
    }

    /**
     * Gets the value of the accountGroup property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAccountGroup() {
        return accountGroup;
    }

    /**
     * Sets the value of the accountGroup property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAccountGroup(String value) {
        this.accountGroup = value;
    }

    /**
     * Gets the value of the oem property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOem() {
        return oem;
    }

    /**
     * Sets the value of the oem property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOem(String value) {
        this.oem = value;
    }

    /**
     * Gets the value of the msnCpnCreateFlag property.
     * 
     */
    public boolean isMsnCpnCreateFlag() {
        return msnCpnCreateFlag;
    }

    /**
     * Sets the value of the msnCpnCreateFlag property.
     * 
     */
    public void setMsnCpnCreateFlag(boolean value) {
        this.msnCpnCreateFlag = value;
    }

    /**
     * Gets the value of the interchangeCpn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInterchangeCpn() {
        return interchangeCpn;
    }

    /**
     * Sets the value of the interchangeCpn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInterchangeCpn(String value) {
        this.interchangeCpn = value;
    }

    /**
     * Gets the value of the turnOverFlag property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTurnOverFlag() {
        return turnOverFlag;
    }

    /**
     * Sets the value of the turnOverFlag property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTurnOverFlag(String value) {
        this.turnOverFlag = value;
    }

    /**
     * Gets the value of the pickupFlag property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPickupFlag() {
        return pickupFlag;
    }

    /**
     * Sets the value of the pickupFlag property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPickupFlag(String value) {
        this.pickupFlag = value;
    }

    /**
     * Gets the value of the bohQty property.
     * 
     * @return
     *     possible object is
     *     {@link Long }
     *     
     */
    public Long getBohQty() {
        return bohQty;
    }

    /**
     * Sets the value of the bohQty property.
     * 
     * @param value
     *     allowed object is
     *     {@link Long }
     *     
     */
    public void setBohQty(Long value) {
        this.bohQty = value;
    }

    /**
     * Gets the value of the requiredByDateTime property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getRequiredByDateTime() {
        return requiredByDateTime;
    }

    /**
     * Sets the value of the requiredByDateTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setRequiredByDateTime(XMLGregorianCalendar value) {
        this.requiredByDateTime = value;
    }

}
