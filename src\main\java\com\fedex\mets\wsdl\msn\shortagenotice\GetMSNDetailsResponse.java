
package com.fedex.mets.wsdl.msn.shortagenotice;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="status" type="{http://fedex.com/airops/maxi/services/jaxws}resultType"/>
 *         &lt;element name="header" type="{http://fedex.com/airops/maxi/services/jaxws}HeaderType"/>
 *         &lt;element name="msnDetails" type="{http://fedex.com/airops/maxi/services/jaxws/shortagenotice}msnDetailsType"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "status",
    "header",
    "msnDetails"
})
@XmlRootElement(name = "getMSNDetailsResponse", namespace = "http://fedex.com/airops/maxi/services/jaxws/shortagenotice")
public class GetMSNDetailsResponse {

    @XmlElement(namespace = "http://fedex.com/airops/maxi/services/jaxws/shortagenotice", required = true)
    protected ResultType status;
    @XmlElement(namespace = "http://fedex.com/airops/maxi/services/jaxws/shortagenotice", required = true)
    protected HeaderType header;
    @XmlElement(namespace = "http://fedex.com/airops/maxi/services/jaxws/shortagenotice", required = true)
    protected MsnDetailsType msnDetails;

    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link ResultType }
     *     
     */
    public ResultType getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link ResultType }
     *     
     */
    public void setStatus(ResultType value) {
        this.status = value;
    }

    /**
     * Gets the value of the header property.
     * 
     * @return
     *     possible object is
     *     {@link HeaderType }
     *     
     */
    public HeaderType getHeader() {
        return header;
    }

    /**
     * Sets the value of the header property.
     * 
     * @param value
     *     allowed object is
     *     {@link HeaderType }
     *     
     */
    public void setHeader(HeaderType value) {
        this.header = value;
    }

    /**
     * Gets the value of the msnDetails property.
     * 
     * @return
     *     possible object is
     *     {@link MsnDetailsType }
     *     
     */
    public MsnDetailsType getMsnDetails() {
        return msnDetails;
    }

    /**
     * Sets the value of the msnDetails property.
     * 
     * @param value
     *     allowed object is
     *     {@link MsnDetailsType }
     *     
     */
    public void setMsnDetails(MsnDetailsType value) {
        this.msnDetails = value;
    }

}
