
package com.fedex.mets.wsdl.flightSearch;

import java.math.BigDecimal;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for FlightLeg complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="FlightLeg">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="foisDataOid" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="aircraftNbr" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="FlightNumber" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="FlightDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="LegNumber" type="{http://www.w3.org/2001/XMLSchema}long" minOccurs="0"/>
 *         &lt;element name="Status" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="TailNumber" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="EquipmentCode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="FlightOrigin" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="FlightDestination" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="LegOriginCountryCd" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="LegDestCountryCd" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="LegOrigin" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="LegDestination" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="TotalCycles" type="{http://www.w3.org/2001/XMLSchema}long" minOccurs="0"/>
 *         &lt;element name="tsn" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="LegDepartureTime" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="LegActualDepartureTime" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="LegEstDepartureTime" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="LegSchedDepartureTime" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="FlightDurationMinutes" type="{http://www.w3.org/2001/XMLSchema}long" minOccurs="0"/>
 *         &lt;element name="LegArrivalTime" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="LegActualArrivalTime" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="LegEstArrivalTime" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="LegSchedArrivalTime" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="GroundTimeMinutes" type="{http://www.w3.org/2001/XMLSchema}long" minOccurs="0"/>
 *         &lt;element name="NextLegDepartureTime" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="NextLegActualDepartureTime" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="NextLegEstDepartureTime" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="NextLegSchedDepartureTime" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="nextLegArrivalGateId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="arrivalGateId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="departureGateId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="EtopsIndicator" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="GeographicalConflict" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="MaintenanceStatus" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pilotEmpNbr" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pilotName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="cycles" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="LegPhaseCode" type="{http://www.w3.org/2001/XMLSchema}long" minOccurs="0"/>
 *         &lt;element name="ReRouteCode" type="{http://www.w3.org/2001/XMLSchema}long" minOccurs="0"/>
 *         &lt;element name="LegTypeCd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="delayMins" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "FlightLeg", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd", propOrder = {
    "foisDataOid",
    "aircraftNbr",
    "flightNumber",
    "flightDate",
    "legNumber",
    "status",
    "tailNumber",
    "equipmentCode",
    "flightOrigin",
    "flightDestination",
    "legOriginCountryCd",
    "legDestCountryCd",
    "legOrigin",
    "legDestination",
    "totalCycles",
    "tsn",
    "legDepartureTime",
    "legActualDepartureTime",
    "legEstDepartureTime",
    "legSchedDepartureTime",
    "flightDurationMinutes",
    "legArrivalTime",
    "legActualArrivalTime",
    "legEstArrivalTime",
    "legSchedArrivalTime",
    "groundTimeMinutes",
    "nextLegDepartureTime",
    "nextLegActualDepartureTime",
    "nextLegEstDepartureTime",
    "nextLegSchedDepartureTime",
    "nextLegArrivalGateId",
    "arrivalGateId",
    "departureGateId",
    "etopsIndicator",
    "geographicalConflict",
    "maintenanceStatus",
    "pilotEmpNbr",
    "pilotName",
    "cycles",
    "legPhaseCode",
    "reRouteCode",
    "legTypeCd",
    "delayMins"
})
@XmlSeeAlso({
    AutoSchedFlightLeg.class
})
public class FlightLeg {

    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    protected BigDecimal foisDataOid;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    protected String aircraftNbr;
    @XmlElement(name = "FlightNumber", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    protected String flightNumber;
    @XmlElement(name = "FlightDate", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar flightDate;
    @XmlElement(name = "LegNumber", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    protected Long legNumber;
    @XmlElement(name = "Status", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    protected String status;
    @XmlElement(name = "TailNumber", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    protected String tailNumber;
    @XmlElement(name = "EquipmentCode", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    protected String equipmentCode;
    @XmlElement(name = "FlightOrigin", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    protected String flightOrigin;
    @XmlElement(name = "FlightDestination", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    protected String flightDestination;
    @XmlElement(name = "LegOriginCountryCd", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    protected String legOriginCountryCd;
    @XmlElement(name = "LegDestCountryCd", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    protected String legDestCountryCd;
    @XmlElement(name = "LegOrigin", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    protected String legOrigin;
    @XmlElement(name = "LegDestination", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    protected String legDestination;
    @XmlElement(name = "TotalCycles", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    protected Long totalCycles;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    protected BigDecimal tsn;
    @XmlElement(name = "LegDepartureTime", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar legDepartureTime;
    @XmlElement(name = "LegActualDepartureTime", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar legActualDepartureTime;
    @XmlElement(name = "LegEstDepartureTime", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar legEstDepartureTime;
    @XmlElement(name = "LegSchedDepartureTime", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar legSchedDepartureTime;
    @XmlElement(name = "FlightDurationMinutes", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    protected Long flightDurationMinutes;
    @XmlElement(name = "LegArrivalTime", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar legArrivalTime;
    @XmlElement(name = "LegActualArrivalTime", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar legActualArrivalTime;
    @XmlElement(name = "LegEstArrivalTime", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar legEstArrivalTime;
    @XmlElement(name = "LegSchedArrivalTime", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar legSchedArrivalTime;
    @XmlElement(name = "GroundTimeMinutes", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    protected Long groundTimeMinutes;
    @XmlElement(name = "NextLegDepartureTime", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar nextLegDepartureTime;
    @XmlElement(name = "NextLegActualDepartureTime", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar nextLegActualDepartureTime;
    @XmlElement(name = "NextLegEstDepartureTime", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar nextLegEstDepartureTime;
    @XmlElement(name = "NextLegSchedDepartureTime", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar nextLegSchedDepartureTime;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd", required = true)
    protected String nextLegArrivalGateId;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd", required = true)
    protected String arrivalGateId;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd", required = true)
    protected String departureGateId;
    @XmlElement(name = "EtopsIndicator", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    protected String etopsIndicator;
    @XmlElement(name = "GeographicalConflict", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    protected String geographicalConflict;
    @XmlElement(name = "MaintenanceStatus", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    protected String maintenanceStatus;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    protected String pilotEmpNbr;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    protected String pilotName;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    protected BigDecimal cycles;
    @XmlElement(name = "LegPhaseCode", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    protected Long legPhaseCode;
    @XmlElement(name = "ReRouteCode", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    protected Long reRouteCode;
    @XmlElement(name = "LegTypeCd", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd", required = true)
    protected String legTypeCd;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    protected BigDecimal delayMins;

    /**
     * Gets the value of the foisDataOid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getFoisDataOid() {
        return foisDataOid;
    }

    /**
     * Sets the value of the foisDataOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setFoisDataOid(BigDecimal value) {
        this.foisDataOid = value;
    }

    /**
     * Gets the value of the aircraftNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAircraftNbr() {
        return aircraftNbr;
    }

    /**
     * Sets the value of the aircraftNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAircraftNbr(String value) {
        this.aircraftNbr = value;
    }

    /**
     * Gets the value of the flightNumber property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFlightNumber() {
        return flightNumber;
    }

    /**
     * Sets the value of the flightNumber property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFlightNumber(String value) {
        this.flightNumber = value;
    }

    /**
     * Gets the value of the flightDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getFlightDate() {
        return flightDate;
    }

    /**
     * Sets the value of the flightDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setFlightDate(XMLGregorianCalendar value) {
        this.flightDate = value;
    }

    /**
     * Gets the value of the legNumber property.
     * 
     * @return
     *     possible object is
     *     {@link Long }
     *     
     */
    public Long getLegNumber() {
        return legNumber;
    }

    /**
     * Sets the value of the legNumber property.
     * 
     * @param value
     *     allowed object is
     *     {@link Long }
     *     
     */
    public void setLegNumber(Long value) {
        this.legNumber = value;
    }

    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStatus(String value) {
        this.status = value;
    }

    /**
     * Gets the value of the tailNumber property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTailNumber() {
        return tailNumber;
    }

    /**
     * Sets the value of the tailNumber property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTailNumber(String value) {
        this.tailNumber = value;
    }

    /**
     * Gets the value of the equipmentCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEquipmentCode() {
        return equipmentCode;
    }

    /**
     * Sets the value of the equipmentCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEquipmentCode(String value) {
        this.equipmentCode = value;
    }

    /**
     * Gets the value of the flightOrigin property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFlightOrigin() {
        return flightOrigin;
    }

    /**
     * Sets the value of the flightOrigin property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFlightOrigin(String value) {
        this.flightOrigin = value;
    }

    /**
     * Gets the value of the flightDestination property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFlightDestination() {
        return flightDestination;
    }

    /**
     * Sets the value of the flightDestination property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFlightDestination(String value) {
        this.flightDestination = value;
    }

    /**
     * Gets the value of the legOriginCountryCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLegOriginCountryCd() {
        return legOriginCountryCd;
    }

    /**
     * Sets the value of the legOriginCountryCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLegOriginCountryCd(String value) {
        this.legOriginCountryCd = value;
    }

    /**
     * Gets the value of the legDestCountryCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLegDestCountryCd() {
        return legDestCountryCd;
    }

    /**
     * Sets the value of the legDestCountryCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLegDestCountryCd(String value) {
        this.legDestCountryCd = value;
    }

    /**
     * Gets the value of the legOrigin property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLegOrigin() {
        return legOrigin;
    }

    /**
     * Sets the value of the legOrigin property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLegOrigin(String value) {
        this.legOrigin = value;
    }

    /**
     * Gets the value of the legDestination property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLegDestination() {
        return legDestination;
    }

    /**
     * Sets the value of the legDestination property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLegDestination(String value) {
        this.legDestination = value;
    }

    /**
     * Gets the value of the totalCycles property.
     * 
     * @return
     *     possible object is
     *     {@link Long }
     *     
     */
    public Long getTotalCycles() {
        return totalCycles;
    }

    /**
     * Sets the value of the totalCycles property.
     * 
     * @param value
     *     allowed object is
     *     {@link Long }
     *     
     */
    public void setTotalCycles(Long value) {
        this.totalCycles = value;
    }

    /**
     * Gets the value of the tsn property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTsn() {
        return tsn;
    }

    /**
     * Sets the value of the tsn property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTsn(BigDecimal value) {
        this.tsn = value;
    }

    /**
     * Gets the value of the legDepartureTime property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLegDepartureTime() {
        return legDepartureTime;
    }

    /**
     * Sets the value of the legDepartureTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLegDepartureTime(XMLGregorianCalendar value) {
        this.legDepartureTime = value;
    }

    /**
     * Gets the value of the legActualDepartureTime property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLegActualDepartureTime() {
        return legActualDepartureTime;
    }

    /**
     * Sets the value of the legActualDepartureTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLegActualDepartureTime(XMLGregorianCalendar value) {
        this.legActualDepartureTime = value;
    }

    /**
     * Gets the value of the legEstDepartureTime property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLegEstDepartureTime() {
        return legEstDepartureTime;
    }

    /**
     * Sets the value of the legEstDepartureTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLegEstDepartureTime(XMLGregorianCalendar value) {
        this.legEstDepartureTime = value;
    }

    /**
     * Gets the value of the legSchedDepartureTime property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLegSchedDepartureTime() {
        return legSchedDepartureTime;
    }

    /**
     * Sets the value of the legSchedDepartureTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLegSchedDepartureTime(XMLGregorianCalendar value) {
        this.legSchedDepartureTime = value;
    }

    /**
     * Gets the value of the flightDurationMinutes property.
     * 
     * @return
     *     possible object is
     *     {@link Long }
     *     
     */
    public Long getFlightDurationMinutes() {
        return flightDurationMinutes;
    }

    /**
     * Sets the value of the flightDurationMinutes property.
     * 
     * @param value
     *     allowed object is
     *     {@link Long }
     *     
     */
    public void setFlightDurationMinutes(Long value) {
        this.flightDurationMinutes = value;
    }

    /**
     * Gets the value of the legArrivalTime property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLegArrivalTime() {
        return legArrivalTime;
    }

    /**
     * Sets the value of the legArrivalTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLegArrivalTime(XMLGregorianCalendar value) {
        this.legArrivalTime = value;
    }

    /**
     * Gets the value of the legActualArrivalTime property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLegActualArrivalTime() {
        return legActualArrivalTime;
    }

    /**
     * Sets the value of the legActualArrivalTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLegActualArrivalTime(XMLGregorianCalendar value) {
        this.legActualArrivalTime = value;
    }

    /**
     * Gets the value of the legEstArrivalTime property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLegEstArrivalTime() {
        return legEstArrivalTime;
    }

    /**
     * Sets the value of the legEstArrivalTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLegEstArrivalTime(XMLGregorianCalendar value) {
        this.legEstArrivalTime = value;
    }

    /**
     * Gets the value of the legSchedArrivalTime property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLegSchedArrivalTime() {
        return legSchedArrivalTime;
    }

    /**
     * Sets the value of the legSchedArrivalTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLegSchedArrivalTime(XMLGregorianCalendar value) {
        this.legSchedArrivalTime = value;
    }

    /**
     * Gets the value of the groundTimeMinutes property.
     * 
     * @return
     *     possible object is
     *     {@link Long }
     *     
     */
    public Long getGroundTimeMinutes() {
        return groundTimeMinutes;
    }

    /**
     * Sets the value of the groundTimeMinutes property.
     * 
     * @param value
     *     allowed object is
     *     {@link Long }
     *     
     */
    public void setGroundTimeMinutes(Long value) {
        this.groundTimeMinutes = value;
    }

    /**
     * Gets the value of the nextLegDepartureTime property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getNextLegDepartureTime() {
        return nextLegDepartureTime;
    }

    /**
     * Sets the value of the nextLegDepartureTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setNextLegDepartureTime(XMLGregorianCalendar value) {
        this.nextLegDepartureTime = value;
    }

    /**
     * Gets the value of the nextLegActualDepartureTime property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getNextLegActualDepartureTime() {
        return nextLegActualDepartureTime;
    }

    /**
     * Sets the value of the nextLegActualDepartureTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setNextLegActualDepartureTime(XMLGregorianCalendar value) {
        this.nextLegActualDepartureTime = value;
    }

    /**
     * Gets the value of the nextLegEstDepartureTime property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getNextLegEstDepartureTime() {
        return nextLegEstDepartureTime;
    }

    /**
     * Sets the value of the nextLegEstDepartureTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setNextLegEstDepartureTime(XMLGregorianCalendar value) {
        this.nextLegEstDepartureTime = value;
    }

    /**
     * Gets the value of the nextLegSchedDepartureTime property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getNextLegSchedDepartureTime() {
        return nextLegSchedDepartureTime;
    }

    /**
     * Sets the value of the nextLegSchedDepartureTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setNextLegSchedDepartureTime(XMLGregorianCalendar value) {
        this.nextLegSchedDepartureTime = value;
    }

    /**
     * Gets the value of the nextLegArrivalGateId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getNextLegArrivalGateId() {
        return nextLegArrivalGateId;
    }

    /**
     * Sets the value of the nextLegArrivalGateId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setNextLegArrivalGateId(String value) {
        this.nextLegArrivalGateId = value;
    }

    /**
     * Gets the value of the arrivalGateId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getArrivalGateId() {
        return arrivalGateId;
    }

    /**
     * Sets the value of the arrivalGateId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setArrivalGateId(String value) {
        this.arrivalGateId = value;
    }

    /**
     * Gets the value of the departureGateId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDepartureGateId() {
        return departureGateId;
    }

    /**
     * Sets the value of the departureGateId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDepartureGateId(String value) {
        this.departureGateId = value;
    }

    /**
     * Gets the value of the etopsIndicator property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEtopsIndicator() {
        return etopsIndicator;
    }

    /**
     * Sets the value of the etopsIndicator property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEtopsIndicator(String value) {
        this.etopsIndicator = value;
    }

    /**
     * Gets the value of the geographicalConflict property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGeographicalConflict() {
        return geographicalConflict;
    }

    /**
     * Sets the value of the geographicalConflict property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGeographicalConflict(String value) {
        this.geographicalConflict = value;
    }

    /**
     * Gets the value of the maintenanceStatus property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMaintenanceStatus() {
        return maintenanceStatus;
    }

    /**
     * Sets the value of the maintenanceStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMaintenanceStatus(String value) {
        this.maintenanceStatus = value;
    }

    /**
     * Gets the value of the pilotEmpNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPilotEmpNbr() {
        return pilotEmpNbr;
    }

    /**
     * Sets the value of the pilotEmpNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPilotEmpNbr(String value) {
        this.pilotEmpNbr = value;
    }

    /**
     * Gets the value of the pilotName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPilotName() {
        return pilotName;
    }

    /**
     * Sets the value of the pilotName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPilotName(String value) {
        this.pilotName = value;
    }

    /**
     * Gets the value of the cycles property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getCycles() {
        return cycles;
    }

    /**
     * Sets the value of the cycles property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setCycles(BigDecimal value) {
        this.cycles = value;
    }

    /**
     * Gets the value of the legPhaseCode property.
     * 
     * @return
     *     possible object is
     *     {@link Long }
     *     
     */
    public Long getLegPhaseCode() {
        return legPhaseCode;
    }

    /**
     * Sets the value of the legPhaseCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link Long }
     *     
     */
    public void setLegPhaseCode(Long value) {
        this.legPhaseCode = value;
    }

    /**
     * Gets the value of the reRouteCode property.
     * 
     * @return
     *     possible object is
     *     {@link Long }
     *     
     */
    public Long getReRouteCode() {
        return reRouteCode;
    }

    /**
     * Sets the value of the reRouteCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link Long }
     *     
     */
    public void setReRouteCode(Long value) {
        this.reRouteCode = value;
    }

    /**
     * Gets the value of the legTypeCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLegTypeCd() {
        return legTypeCd;
    }

    /**
     * Sets the value of the legTypeCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLegTypeCd(String value) {
        this.legTypeCd = value;
    }

    /**
     * Gets the value of the delayMins property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getDelayMins() {
        return delayMins;
    }

    /**
     * Sets the value of the delayMins property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setDelayMins(BigDecimal value) {
        this.delayMins = value;
    }

}
