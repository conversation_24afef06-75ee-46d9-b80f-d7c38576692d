
package com.fedex.mets.wsdl.discrepancy;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ActionUsedDetail complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ActionUsedDetail">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="activeFlg" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="usedAddedFlg" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="usedToCompWorkFlg" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="addedUsedId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ActionUsedDetail", propOrder = {
    "activeFlg",
    "usedAddedFlg",
    "usedToCompWorkFlg",
    "addedUsedId"
})
public class ActionUsedDetail {

    protected Boolean activeFlg;
    protected Boolean usedAddedFlg;
    protected Boolean usedToCompWorkFlg;
    protected String addedUsedId;

    /**
     * Gets the value of the activeFlg property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isActiveFlg() {
        return activeFlg;
    }

    /**
     * Sets the value of the activeFlg property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setActiveFlg(Boolean value) {
        this.activeFlg = value;
    }

    /**
     * Gets the value of the usedAddedFlg property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isUsedAddedFlg() {
        return usedAddedFlg;
    }

    /**
     * Sets the value of the usedAddedFlg property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setUsedAddedFlg(Boolean value) {
        this.usedAddedFlg = value;
    }

    /**
     * Gets the value of the usedToCompWorkFlg property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isUsedToCompWorkFlg() {
        return usedToCompWorkFlg;
    }

    /**
     * Sets the value of the usedToCompWorkFlg property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setUsedToCompWorkFlg(Boolean value) {
        this.usedToCompWorkFlg = value;
    }

    /**
     * Gets the value of the addedUsedId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAddedUsedId() {
        return addedUsedId;
    }

    /**
     * Sets the value of the addedUsedId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAddedUsedId(String value) {
        this.addedUsedId = value;
    }

}
