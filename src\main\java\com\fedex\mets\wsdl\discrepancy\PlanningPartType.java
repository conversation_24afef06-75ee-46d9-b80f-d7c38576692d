
package com.fedex.mets.wsdl.discrepancy;

import java.math.BigDecimal;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for PlanningPartType complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="PlanningPartType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="modified" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="oid" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="cpn" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="mpn" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="partQty" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="partReqFlg" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PlanningPartType", namespace = "http://www.fedex.com/airops/schemas/Planning.xsd", propOrder = {
    "modified",
    "oid",
    "cpn",
    "mpn",
    "partQty",
    "partReqFlg"
})
public class PlanningPartType {

    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd")
    protected boolean modified;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd", required = true)
    protected BigDecimal oid;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd", required = true)
    protected String cpn;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd", required = true)
    protected String mpn;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd", required = true)
    protected BigDecimal partQty;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/Planning.xsd")
    protected boolean partReqFlg;

    /**
     * Gets the value of the modified property.
     * 
     */
    public boolean isModified() {
        return modified;
    }

    /**
     * Sets the value of the modified property.
     * 
     */
    public void setModified(boolean value) {
        this.modified = value;
    }

    /**
     * Gets the value of the oid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getOid() {
        return oid;
    }

    /**
     * Sets the value of the oid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setOid(BigDecimal value) {
        this.oid = value;
    }

    /**
     * Gets the value of the cpn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCpn() {
        return cpn;
    }

    /**
     * Sets the value of the cpn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCpn(String value) {
        this.cpn = value;
    }

    /**
     * Gets the value of the mpn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMpn() {
        return mpn;
    }

    /**
     * Sets the value of the mpn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMpn(String value) {
        this.mpn = value;
    }

    /**
     * Gets the value of the partQty property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getPartQty() {
        return partQty;
    }

    /**
     * Sets the value of the partQty property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setPartQty(BigDecimal value) {
        this.partQty = value;
    }

    /**
     * Gets the value of the partReqFlg property.
     * 
     */
    public boolean isPartReqFlg() {
        return partReqFlg;
    }

    /**
     * Sets the value of the partReqFlg property.
     * 
     */
    public void setPartReqFlg(boolean value) {
        this.partReqFlg = value;
    }

}
