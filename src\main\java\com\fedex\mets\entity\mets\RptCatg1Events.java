package com.fedex.mets.entity.mets;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Date;
import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "rpt_catg_1_events")
public class RptCatg1Events {

    @Id
    @Column(name = "LEVEL_1_ID", nullable = false)
    private String level1Id;

    @Column(name = "LEVEL_1_NAME")
    private String level1Name;

    @Column(name = "TYPE", nullable = false)
    private String type;

    @Column(name = "FLAG")
    private String flag;

    @Column(name = "LAST_UPDATE_DT_TM")
    private Timestamp lastUpdateDtTm;
}