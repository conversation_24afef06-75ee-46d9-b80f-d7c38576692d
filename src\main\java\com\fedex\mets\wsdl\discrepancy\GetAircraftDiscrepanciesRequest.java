
package com.fedex.mets.wsdl.discrepancy;


import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;extension base="{http://www.fedex.com/airops/schemas/Common.xsd}GenericRequest">
 *       &lt;sequence>
 *         &lt;element name="requestType" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="discrepancyQueryFilter" type="{http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd}discrepancyQueryFilter"/>
 *         &lt;element name="getLogpage" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "requestType",
    "discrepancyQueryFilter",
    "getLogpage"
})
@XmlRootElement(name = "getAircraftDiscrepanciesRequest",namespace="http:///www.fedex.com/airops/schemas/Mach")
public class GetAircraftDiscrepanciesRequest
    extends GenericRequest
{

    protected String requestType;
    @XmlElement(required = true)
    protected DiscrepancyQueryFilter discrepancyQueryFilter;
    protected boolean getLogpage;

    /**
     * Gets the value of the requestType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRequestType() {
        return requestType;
    }

    /**
     * Sets the value of the requestType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRequestType(String value) {
        this.requestType = value;
    }

    /**
     * Gets the value of the discrepancyQueryFilter property.
     * 
     * @return
     *     possible object is
     *     {@link DiscrepancyQueryFilter }
     *     
     */
    public DiscrepancyQueryFilter getDiscrepancyQueryFilter() {
        return discrepancyQueryFilter;
    }

    /**
     * Sets the value of the discrepancyQueryFilter property.
     * 
     * @param value
     *     allowed object is
     *     {@link DiscrepancyQueryFilter }
     *     
     */
    public void setDiscrepancyQueryFilter(DiscrepancyQueryFilter value) {
        this.discrepancyQueryFilter = value;
    }

    /**
     * Gets the value of the getLogpage property.
     * 
     */
    public boolean isGetLogpage() {
        return getLogpage;
    }

    /**
     * Sets the value of the getLogpage property.
     * 
     */
    public void setGetLogpage(boolean value) {
        this.getLogpage = value;
    }

}
