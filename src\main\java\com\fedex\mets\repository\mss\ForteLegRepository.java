package com.fedex.mets.repository.mss;

import com.fedex.mets.entity.mss.ForteLeg;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ForteLegRepository extends JpaRepository<ForteLeg,Integer> {

    @Query(value="select * from FORTE_LEG where ROWNUM<10",nativeQuery = true)
    public List<ForteLeg> getAll();

    @Query(value="select * from FORTE_LEG WHERE FLT_NBR='1362' AND FLT_ZULU_DT= TO_DATE('03/19/2024','mm/dd/yy hh24:mi:ss') AND LEG_NBR='02' and FLT_RRT_CD='0'",nativeQuery = true)
    public ForteLeg findByFltNbrAndFltZuluDtAndLegNbr();


    @Query(value="Select * from FORTE_LEG" +
            " where IATA_ORIG_CD=:station" +
            " and EQUIP_CD=:equipmentCode" +
            " and ((SCHED_OUT_ZULU_TMSTP between to_date(:strFromDate,'mm/dd/yy hh24:mi') " +
            " and to_date(:strToDate,'mm/dd/yy hh24:mi')))",nativeQuery = true)
    public List<ForteLeg> getFlightSearchList(@Param("station") String station,
                                              @Param("equipmentCode")String equipmentCode,
                                              @Param("strFromDate") String strFromDate,
                                              @Param("strToDate") String strToDate);


    @Query(value="Select IATA_DEST_CD from FORTE_LEG where FLT_NBR=:flightNumber \n" +
            "and FLT_ZULU_DT = TO_DATE(:flightDate,'mm/dd/yy hh24:mi') \n" +
            "and LEG_NBR=:flightLegNumber\n" +
            "and FLT_RRT_CD='0'",nativeQuery = true)
    public String getIATADestCd(@Param("flightNumber") String flightNumber,
                                @Param("flightDate") String flightDate,
                                @Param("flightLegNumber") String flightLegNumber);


    /**
     * The following getLegStatusDepartureTime() is used to retreive the Flight Status & Departure Time for a particular Flight.
     * @params String flightNumber, flightDate, flightLegNumber.
     * @return List of containing LegStatus & timestampDepartureTime.
     */
    @Query(value="select LEG_STAT_DESC, SCHED_OUT_ZULU_TMSTP from FORTE_LEG where FLT_NBR=:flightNumber \n" +
            "and FLT_ZULU_DT = TO_DATE(:flightDate,'mm/dd/yy hh24:mi:ss') \n" +
            "and LEG_NBR=:flightLegNumber\n" +
            "and FLT_RRT_CD='0'",nativeQuery = true)
    public String getLegStatusDepartureTime(@Param("flightNumber") String flightNumber,
                                @Param("flightDate") String flightDate,
                                @Param("flightLegNumber") String flightLegNumber);


    @Query(value="select OID,FLT_NBR, FLT_ZULU_DT, LEG_NBR, TAIL_NBR, IATA_DEST_CD, LEG_STAT_DESC, " +
            "LEG_TYPE_CD, SCHED_OUT_ZULU_TMSTP, ACTL_OUT_ZULU_TMSTP, TOT_DELAY_QTY, " +
            "DELAY_QTY_1, DELAY_QTY_2, DELAY_QTY_3, DELAY_QTY_4, DELAY_QTY_5, " +
            "DELAY_QTY_6, DELAY_QTY_7, DELAY_QTY_8, DELAY_QTY_9, DELAY_QTY_10, " +
            "DELAY_CD_1, DELAY_CD_2, DELAY_CD_3, DELAY_CD_4, " +
            "DELAY_CD_5, DELAY_CD_6, DELAY_CD_7, DELAY_CD_8, " +
            "DELAY_CD_9, DELAY_CD_10, IATA_ORIG_CD, SCHED_IN_ZULU_TMSTP, " +
            "ACTL_IN_ZULU_TMSTP,FLT_RRT_CD from FORTE_LEG WHERE  FLT_NBR=:flightNumber\n" +
            "AND FLT_ZULU_DT= TO_DATE(:flightDate,'mm/dd/yy hh24:mi:ss') " +
            "AND LEG_NBR=:flightLegNumber\n" +
            "and FLT_RRT_CD='0'",nativeQuery = true)
    public ForteLeg getFlightDetails(@Param("flightNumber") String flightNumber,
                                           @Param("flightDate") String flightDate,
                                           @Param("flightLegNumber") String flightLegNumber);

}
