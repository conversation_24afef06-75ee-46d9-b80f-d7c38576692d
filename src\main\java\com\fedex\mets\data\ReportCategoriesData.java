package com.fedex.mets.data;

import lombok.*;

import java.io.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
public class ReportCategoriesData implements Serializable{
	public String levelOneName;
	public String levelOneDescription;
	public String levelOneAddPrompt;
	public String levelOneRequiredAddPrompt;
	public String levelOneClosePrompt;
	public String levelOneRequiredClosePrompt;
	public String levelOneMultiSelect;
	public int	  levelOneListOrder;
	public String levelOneId;
	public String levelOneActiveCategory;
	public String levelOneGroupId;

	public String levelTwoName;
	public String levelTwoDescription;
	public String levelTwoId;
	public String	  levelTwoListOrder;
}