package com.fedex.mets.data;
import java.util.Calendar;
import java.util.TimeZone;
import java.util.GregorianCalendar;
import java.text.SimpleDateFormat;

public class ETICData extends GregorianCalendar {
  private Calendar m_eticTime;
  private String m_eticInfo;

  public String toString(){
      StringBuffer strBuffer = new StringBuffer();
      if(m_eticTime == null) {
          if(m_eticInfo != null)
              strBuffer.append(m_eticInfo);
      } else {
          SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd/HHmm");
          df.setTimeZone(TimeZone.getTimeZone("UTC"));
          strBuffer.append(df.format(m_eticTime.getTime()));
      }
      return strBuffer.toString();
  }
}