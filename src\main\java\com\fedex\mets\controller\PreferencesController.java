package com.fedex.mets.controller;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.fedex.mets.dao.PreferenceInfo;
import com.fedex.mets.dao.ScreenPreference;
import com.fedex.mets.entity.mets.UserPreference;
import com.fedex.mets.repository.mets.PreferencesRepository;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping(path = "/api/mets/preferences")
//@CrossOrigin("*")
@Tag(name = "Preferences", description = "Endpoint to save and retrieve user preferences.")
public class PreferencesController {

    @Autowired
    PreferencesRepository preferencesRepository;

//    @PostMapping("/setPreferences")
//    public void storeUserEntity(@RequestParam("empid") int empId,@RequestParam("empName") String name,@RequestBody ScreenPreference screenPreference) throws SQLException, IOException {
//        byte[] userDetailsBytes = sqlBlobSerializer.serialize(screenPreference);
//        Blob userDetailsBlob = new javax.sql.rowset.serial.SerialBlob(userDetailsBytes);
//        System.out.println("The user details blob is: " + userDetailsBlob);
//        boolean present=preferencesRepository.existsById(empId);
//        LocalDateTime now = LocalDateTime.now();
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//        String formattedNow = now.format(formatter);
//        if(present){
//            UserPreference userEntity = preferencesRepository.findById(empId).orElse(null);
//            assert userEntity != null;
//            userEntity.setPref(userDetailsBlob);
//            userEntity.setUpdatedAt(Timestamp.valueOf(formattedNow));
//            preferencesRepository.save(userEntity);
//        }else{
//        UserPreference userEntity = new UserPreference(name, empId, userDetailsBlob,Timestamp.valueOf(formattedNow), Timestamp.valueOf(formattedNow));
//        preferencesRepository.save(userEntity);
//        }
//    }

    @PostMapping("/setPreferences")
    public void storeUserEntity(@RequestParam("empNbr") int empId, @RequestParam("empName") String name, @RequestBody ScreenPreference screenPreference) throws SQLException, IOException {
        String json = new ObjectMapper().writeValueAsString(screenPreference);
        boolean present = preferencesRepository.existsById(empId);
        LocalDateTime now = LocalDateTime.now();
        Timestamp timestampNow = Timestamp.valueOf(now);
        if (present) {
            UserPreference userEntity = preferencesRepository.findById(empId).orElse(null);
            assert userEntity != null;
            userEntity.setPreferenceJson(json);
            userEntity.setUpdatedTMSTP(timestampNow);
            preferencesRepository.save(userEntity);
        } else {
            UserPreference userEntity = new UserPreference(name, empId, json, timestampNow, timestampNow);
            preferencesRepository.save(userEntity);
        }
    }

    public ScreenPreference getScreenPref(ScreenPreference screenPreference) {
//        PreferenceInfo preferenceInfo=new PreferenceInfo();
//        preferenceInfo.setPreferenceName("Theme");
//        preferenceInfo.setPreferenceValues(List.of("Dark","Light"));
//        List<PreferenceInfo> preferenceInfoList=List.of(preferenceInfo);
//        ScreenPreference screenPreference = new ScreenPreference();
//        screenPreference.setScreenName("Home");
//        screenPreference.setPreferenceInfoList(preferenceInfoList);
        return screenPreference;
    }

    @GetMapping("/getPreferences")
    public ScreenPreference retrieveUserEntity(@RequestParam("id") int id) throws IOException, SQLException {
//        UserPreference userEntity = preferencesRepository.getPreferences(id);
//        System.out.println("The user entity is: " + preferencesRepository.findByEmpId(id));
        ObjectMapper objectMapper = new ObjectMapper();
        ScreenPreference sc = new ScreenPreference();

        String preferenceJson = preferencesRepository.getPreferences(id);

        if (preferenceJson != null && !preferenceJson.isEmpty()) {
            sc = objectMapper.readValue(preferenceJson, ScreenPreference.class);
        } else {
            sc.setScreenName("Home");
            List<PreferenceInfo> preferenceInfoList=new ArrayList<>();
            List<String> empty=new ArrayList<>();
            List<String> emptyBool=new ArrayList<>();
            emptyBool.add("false");
            preferenceInfoList.add(new PreferenceInfo("Table",empty));
            preferenceInfoList.add(new PreferenceInfo("Fleet",empty));
            preferenceInfoList.add(new PreferenceInfo("Event",empty));
            preferenceInfoList.add(new PreferenceInfo("sideNavClosed",emptyBool));
            preferenceInfoList.add(new PreferenceInfo("isOptionsClosed",emptyBool));
            preferenceInfoList.add(new PreferenceInfo("Region",empty));
            preferenceInfoList.add(new PreferenceInfo("Station",empty));
            preferenceInfoList.add(new PreferenceInfo("PowerPlantChecked",emptyBool));
            sc.setPreferenceInfo(preferenceInfoList);
        }
        return sc;
    }


}
