package com.fedex.mets.repository.mets;

import com.fedex.mets.entity.mets.UserIntakeForm;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;


@Repository
public interface UserIntakeFormRepository extends JpaRepository<UserIntakeForm, Integer> {

    @Query(value = "select * from user_intake_form where role_id=:roleId and event_type_id=:eventId", nativeQuery = true)
    public UserIntakeForm getUserIntakeForm(@Param("roleId") int roleId, @Param("eventId") int eventId);

    @Query(value = "select * from user_intake_form where role_id=:roleId and event_type_id=:eventId and DSS_AUTH_CD=:dssCode", nativeQuery = true)
    public UserIntakeForm findUserIntakeFormByRoleIdAndEventIdAndDssAuthCode(@Param("roleId") int roleId, @Param("eventId") int eventId,@Param("dssCode") String dssCode);
}
