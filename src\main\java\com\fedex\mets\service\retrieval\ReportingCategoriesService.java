package com.fedex.mets.service.retrieval;

import com.fedex.mets.dao.ReportCategoryKeyValues;
import com.fedex.mets.dao.ReportingCategoryValues;
import com.fedex.mets.data.ReportCategoriesData;
import com.fedex.mets.dao.ReportCategoriesActiveKeyValueData;
import com.fedex.mets.dto.MetsRequest;
import com.fedex.mets.dto.MetsResponse;
import com.fedex.mets.dto.ReportCatgResponse;
import com.fedex.mets.repository.mets.EventRepCatgRepository;
import com.fedex.mets.repository.mets.EventsRepository;
import com.fedex.mets.repository.mets.GroupDictRepository;
import com.fedex.mets.repository.mets.RptCatgLevel1Repository;
import com.fedex.mets.util.IServerConstants;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@SuppressWarnings("unchecked")
@Service
@RequiredArgsConstructor
public class ReportingCategoriesService {

    private static final Logger logger = LoggerFactory.getLogger(ReportingCategoriesService.class);

    @Autowired
    private EventsRepository eventsRepository;
    @Autowired
    private GroupDictRepository groupDictRepository;
    @Autowired
    private RptCatgLevel1Repository rptCatgRepository;
    @Autowired
    private EventRepCatgRepository eventRepCatgRepository;

    /**
     * Private method to get the Report Categories for a particular event.
     *
     * @return hashTable containing ReportCategoriesData Object.
     * @params EventId, accessLevel.
     */
    public ReportCatgResponse getReportCategories(
            String eventId,String accessLevel) {
        ReportCatgResponse reportCatgResponse = new ReportCatgResponse();
        List<List<?>> elements = new ArrayList<>();
        List<ReportingCategoryValues> reportCategories = new ArrayList<>();
        List<ReportCategoriesActiveKeyValueData> reportCategoriesKeyValues = new ArrayList<>();

        try {
            logger.info("==> before calling the getReportCategories for the eventID " + eventId);
            elements =
                    getReportingCategories(eventId,accessLevel);
            if (elements != null) {
                reportCategories = (List<ReportingCategoryValues>) elements.get(0);
                reportCategoriesKeyValues = (List<ReportCategoriesActiveKeyValueData>) elements.get(1);
            }

            reportCatgResponse.setReportingCategories(reportCategories);
            reportCatgResponse.setReportingCategoryKeys(reportCategoriesKeyValues);

        } catch (Exception e) {
            logger.warn(
                    "ERROR MetsRetrieval getReportCategories()  exception >> "
                            + e.getMessage());
        }
        return reportCatgResponse;

    }

    /**
     * Private method to get the Report Categories for a particular event.
     *
     * @return hashTable containing ReportCategoriesData Object.
     * @params EventTypw, accessLevel.
     */
    public ReportCatgResponse getReportingCategoriesByEventType(
            String eventType,String accessLevel) {
        ReportCatgResponse reportCatgResponse = new ReportCatgResponse();
        List<ReportingCategoryValues> reportCategories = new ArrayList<>();
        try {
            logger.info("==> before calling the getReportCategories for the eventType " + eventType);
            String groupId = groupDictRepository.getGroupId(accessLevel);
            reportCategories =
                    rptCatgRepository.getRepCategories(groupId, eventType);
            reportCatgResponse.setReportingCategories(reportCategories);

        } catch (Exception e) {
            logger.warn(
                    "ERROR MetsRetrieval getReportCategories()  exception >> "
                            + e.getMessage());
        }
        return reportCatgResponse;

    }

    /**
     * The following getReportCategories() is used to retreive the Report Categories for a particular event.
     *
     * @return List of List of (reportCategories,reportCategoriesKeyValues).
     * @params String eventId, groupId.
     */
    public List<List<?>> getReportingCategories(String eventId,String accessLevel) {

        List<List<?>> resultList = new ArrayList<>();
        List<ReportingCategoryValues> reportCategories = new ArrayList<>();
        List<ReportCategoryKeyValues> reportCategoriesKeyValues = new ArrayList<>();
        String eventType = "",
                groupId = "";

        try {
            eventType = eventsRepository.getEventType(Integer.parseInt(eventId));
            groupId = groupDictRepository.getGroupId(accessLevel);
        } catch (Exception e) {
            logger.warn(
                    "ERROR getReportCategories() group>> " + e.getMessage());
            e.printStackTrace();
        }

        try {
            List<ReportingCategoryValues> repCategories = rptCatgRepository.getRepCategories(groupId, eventType);
            logger.info("==> repCategories size: " + repCategories.size());
            reportCategories.addAll(repCategories);
        } catch (Exception e) {
            logger.warn("ERROR getReportCategories() >> " + e.getMessage());
        }

        try {
            reportCategoriesKeyValues =
                    getReportCategoriesKeyValues(eventId, groupId);

        } catch (Exception pairs) {
            logger.warn(
                    "ERROR getReportCategories() pairs>> " + pairs.getMessage());
            pairs.printStackTrace();
        }

        resultList.add(reportCategories);
        resultList.add(reportCategoriesKeyValues);

        return resultList;
    }

    /**
     * The following getReportCategoriesKeyValues() is used to retreive the  Key Value pairs(defult selection) of
     * Report Categories for a particular event.
     *
     * @return List of ReportCategoryKeyValues.
     * @params String eventId.
     */
    public List<ReportCategoryKeyValues> getReportCategoriesKeyValues(String eventId, String groupId) {
        List<ReportCategoryKeyValues> resultList = new ArrayList<>();

        try {
            List<ReportCategoryKeyValues> data = eventRepCatgRepository.getRepCategoryKeyValues(eventId,groupId);
            resultList.addAll(data);

        } catch (Exception e) {
            logger.warn(
                    "ERROR getReportCategoriesKeyValues() >> " + e.getMessage());
            e.printStackTrace();
        }
        return resultList;
    }

}
