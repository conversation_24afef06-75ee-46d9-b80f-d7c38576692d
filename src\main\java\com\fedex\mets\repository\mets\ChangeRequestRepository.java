package com.fedex.mets.repository.mets;

import com.fedex.mets.entity.mets.ChangeRequest;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface ChangeRequestRepository extends JpaRepository<ChangeRequest,String>{

    @Modifying
    @Transactional
    @Query(value = "delete from change_request where acn=:acn", nativeQuery = true)
    public void deleteChangeRequestRecord(@Param("acn") String acn);

    @Query(value="select REQUEST_STATUS, NEW_STATUS from CHANGE_REQUEST where ACN=:acn",nativeQuery = true)
    public String getChangeRequestStatus(@Param("acn") String acn);

    @Query(value="select EVENT_ID from CHANGE_REQUEST where ACN=:acn",nativeQuery = true)
    public Integer getEventIdByAcn(@Param("acn") String acn);


    @Query(value="select count(*) from CHANGE_REQUEST where ACN=:acn",nativeQuery = true)
    int getCountOfChangeRequestRecords(@Param("acn") String acn);

    @Query(value="select count(*) from CHANGE_REQUEST where EVENT_ID=:eventId",nativeQuery = true)
    int getCountByEventId(@Param("eventId") Integer eventId);

    @Query(value="select count(*) from CHANGE_REQUEST where EVENT_ID=:eventId and LAST_UPDATE_DT_TM=TO_DATE(:lastUpdateDtTm ,'mm/dd/yy hh24:mi:ss')",nativeQuery = true)
    int getCountByEventIdAndLastUpdateDtTm(@Param("eventId") Integer eventId, @Param("lastUpdateDtTm") String lastUpdateDtTm);

    @Query(value="select count(*) from CHANGE_REQUEST where EVENT_ID=:eventId  and REQUEST_STATUS <> 'C' and NEW_STATUS <> 'UP'",nativeQuery = true)
    int getCoundByEventIdAndStatus(@Param("eventId") Integer eventId);

    @Query(value="select * from CHANGE_REQUEST where ACN=:acn",nativeQuery = true)
    ChangeRequest getChangeRequestByAcn(@Param("acn") String acn);

    @Query(value="select * from CHANGE_REQUEST where EVENT_ID=:eventId",nativeQuery = true)
    ChangeRequest getChangeRequestByEventId(@Param("eventId") String eventId);

    @Query(value="select count(*) from CHANGE_REQUEST where EVENT_ID=:eventId and REQUEST_STATUS='U' and LAST_UPDATE_DT_TM=TO_DATE(:strLookupDate ,'mm/dd/yy hh24:mi:ss')",nativeQuery = true)
    int getActiveEventCountByEventStatusUp(@Param("eventId") Integer eventId,@Param("strLookupDate") String strLookupDate);
    
    @Query(value="select count(*) from CHANGE_REQUEST where ACN=:acn and REQUEST_STATUS='U' and CHANGE_TYPE='15'",nativeQuery = true)
    int getCountOfChangeRequestRecordsByAcnChangeType(@Param("acn") String acn);
    
    @Query(value="select count(*) from CHANGE_REQUEST where ACN=:acn and REQUEST_STATUS='U'",nativeQuery = true)
    int getCountOfChangeRequestRecordsByAcn(@Param("acn") String acn);
    
    @Query(value="select count(*) from CHANGE_REQUEST where ACN=:acn and NEW_STATUS <> 'UP'",nativeQuery = true)
    int getCountOfChangeRequestRecordsByNewStatus(@Param("acn") String acn);
}
