package com.fedex.mets.entity.mets;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.*;

import java.io.Serializable;

@Embeddable
@Getter
@Setter
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
public class EventFltInfoPk implements Serializable {
    @Column(name = "EVENT_ID")
    public int eventId;
    @Column(name = "FLT_FLAG")
    public Character flightFlag;
}
