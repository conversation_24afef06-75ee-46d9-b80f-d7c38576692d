package com.fedex.mets.util;

public class TextFormatUtil {

	/**
	 * Replaces the provided charSequence with replacement using the specified
	 * regular expression.
	 */
	public static String replaceAll(String value, String charSequence, String regex, String replacement) {
		if (value != null && value.contains(charSequence)) {
			return value.replaceAll(regex, replacement);
		}
		return value;
	}

	/**
	 * Inserts the provided replacement String at the specified index of the
	 * value.
	 */
	public static String insert(String value, int insertIndex, String replacement) {
		if (insertIndex <= value.length()) {
			StringBuffer buff = new StringBuffer(value.substring(0, insertIndex));
			buff.append(replacement);
			if (insertIndex <= value.length()) {
				buff.append(value.substring(insertIndex, value.length()));
			}
			return buff.toString();
		} else {
			return value;
		}
	}

	/**
	 * Prepends the provided replacement up to the specified minimum index
	 * within the value.
	 */
	public static String prepend(String value, int minimum, String replacement) {
		if (value != null && value.length() < minimum) {
			StringBuffer buff = new StringBuffer();
			for (int i = 0; i < minimum - value.length(); i++) {
				buff.append(replacement);
			}
			buff.append(value);
			return buff.toString();
		}
		return value;
	}
}

