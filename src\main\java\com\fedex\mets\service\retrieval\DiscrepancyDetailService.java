package com.fedex.mets.service.retrieval;

import com.fedex.mets.config.MachSoapClientConfig;
import com.fedex.mets.config.OktaTokenGenService;
import com.fedex.mets.dto.DscrpTxt;
import com.fedex.mets.util.DiscrepancyTextProcessor;
import com.fedex.mets.wsdl.discrepancy.*;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@SuppressWarnings("unused")
@Service
@RequiredArgsConstructor
public class DiscrepancyDetailService {

    private static final Logger logger = LoggerFactory.getLogger(DiscrepancyDetailService.class);

    @Autowired
    private MachSoapClientConfig wb;

    @Autowired
    private OktaTokenGenService oktaTokenGenService;

    @SuppressWarnings("unchecked")
    public List<String> getDiscrepancyDetail(
            String acn,
            String ata,
            String discrepancyNumber,
            String user) {

        String strATA = ata.substring(0, 2) + ata.substring(3, 5);
        String tokenId = "";
        logger.info("getDiscrepancyDescription acn >" + acn + " ATA >"
                + strATA + " DISC >" + discrepancyNumber);
        List<String> omdisList = new ArrayList<>();
        try {
            String discrepancyText = getAllDiscrepancyTextStr(
                    user, acn, strATA, discrepancyNumber, tokenId);
            if (discrepancyText == null || discrepancyText.startsWith("Error")) {

                String error = "getDiscrepancyDescription() trans.getError()>>>>"
                        + discrepancyText
                        + " for acn >"
                        + acn
                        + " ATA >"
                        + strATA + " DISC >" + discrepancyNumber;
                logger.info(error);

            } else {

                logger.info("getDiscrepancyDescription() success>>>>"
                        + discrepancyText
                        + " for acn >"
                        + acn
                        + " ATA >"
                        + strATA + " DISC >" + discrepancyNumber);

                String[] discs = discrepancyText.split("\n");
                for (int i = 0; i < discs.length; i++) {
                    omdisList.add(discs[i]);
                }

            }
        } catch (Exception e) {
            logger.warn(
                    "ERROR MetsRetrieval Servlet getDiscrepancyDetail() e >> "
                            + e.getMessage(), e);
        }

        return omdisList;
    }


    // Existing method for getting discrepancy text
    public String getAllDiscrepancyTextStr(String userId, String acn, String ata, String dscrpNbr, String tokenId) throws Exception {
        long time = System.currentTimeMillis();
        tokenId = this.oktaTokenGenService.generateToken();

        try {
            GetDetailAircraftDiscrepancyResponse detailResp = getDiscrepancyResponse(userId, acn, ata, dscrpNbr, tokenId);

            if (detailResp.getAcnDscrp() == null || detailResp.getAcnDscrp().getAcnDiscrepancyOid() == null) {
                return "Discrepancy Not Found";
            }

            GetAllMaintUpdateResponse maintResp = getMaintenanceUpdate(detailResp.getAcnDscrp().getAcnDiscrepancyOid().longValue(), userId, tokenId);

            if (maintResp == null || !maintResp.getStatus().isSuccessful()) {
                return "Error Loading Discrepancy: " + (maintResp != null ? maintResp.getStatus().getMessage() : "null");
            }

            GetDscrpWorkRlseHistoryResponse planResp = getPlanHistoryFrDscrp(acn, ata, dscrpNbr, userId, tokenId);

            DiscrepancyTextProcessor tp = buildDiscrepancyTextProcessor(detailResp, maintResp, planResp, acn, ata, dscrpNbr,false);

            logger.info("Aircraft Discrepancy Text took " + ((System.currentTimeMillis() - time) / 1000) + " seconds");
            return tp.createTextContent();

        } catch (Exception e) {
            logger.error("Error retrieving discrepancy detail.", e);
            return "Error --" + e.getMessage();
        }
    }

    public List<DscrpTxt> getAllDiscrepancyUpdtTxts(String userId, String acn, String ata, String dscrpNbr, String tokenId) throws Exception {
        long time = System.currentTimeMillis();
        tokenId = this.oktaTokenGenService.generateToken();
        String strATA = ata.substring(0, 2) + ata.substring(3, 5);
        try {
            GetDetailAircraftDiscrepancyResponse detailResp = getDiscrepancyResponse(userId, acn, strATA, dscrpNbr, tokenId);

            if (detailResp.getAcnDscrp() == null || detailResp.getAcnDscrp().getAcnDiscrepancyOid() == null) {
                return new ArrayList<>();
            }

            GetAllMaintUpdateResponse maintResp = getMaintenanceUpdate(detailResp.getAcnDscrp().getAcnDiscrepancyOid().longValue(), userId, tokenId);

            if (maintResp == null || !maintResp.getStatus().isSuccessful()) {
                return new ArrayList<>();
            }

            GetDscrpWorkRlseHistoryResponse planResp = getPlanHistoryFrDscrp(acn, strATA, dscrpNbr, userId, tokenId);

            DiscrepancyTextProcessor tp = buildDiscrepancyTextProcessor(detailResp, maintResp, planResp, acn, strATA, dscrpNbr,true);

            logger.info("Aircraft Discrepancy User Extraction took " + ((System.currentTimeMillis() - time) / 1000) + " seconds");
            return tp.fetchAllUpdatedTxts();

        } catch (Exception e) {
            logger.error("Error retrieving user list from discrepancy.", e);
            return new ArrayList<>();
        }
    }

    // Private helper to get discrepancy response
    private GetDetailAircraftDiscrepancyResponse getDiscrepancyResponse(String userId, String acn, String ata, String dscrpNbr, String tokenId) throws Exception {
        return getDiscrepancyDetails(userId, acn, ata, dscrpNbr, tokenId);
    }

    // Private helper to get maintenance update
    private GetAllMaintUpdateResponse getMaintenanceUpdate(Long discrepancyOid, String userId, String tokenId) throws Exception {
        GetAllMaintUpdateRequest request = new GetAllMaintUpdateRequest();
        request.getAcnDiscrepancyOids().add(discrepancyOid);

        SessionType sessionType = new SessionType();
        sessionType.setUserId(userId);
        sessionType.setToken(tokenId);
        sessionType.setAuthSourceSysName(AuthSourceSysType.LDAP);
        request.setSession(sessionType);

        return getAllMaintUpdate(request);
    }

    // Private helper to build DiscrepancyTextProcessor
    private DiscrepancyTextProcessor buildDiscrepancyTextProcessor(
            GetDetailAircraftDiscrepancyResponse detailResp,
            GetAllMaintUpdateResponse maintResp,
            GetDscrpWorkRlseHistoryResponse planResp,
            String acn, String ata, String dscrpNbr,boolean fetchUpdtTxts) {

        List<DscrpAttachedLogpageNbrs> dscrpLogNbrs = new ArrayList<>();
        DscrpAttachedLogpageNbrs logPageNbr = new DscrpAttachedLogpageNbrs();
        logPageNbr.setAircraftNbr(acn);
        logPageNbr.setAtaNbr(new BigDecimal(ata));
        logPageNbr.setDscrpNbr(dscrpNbr);
        logPageNbr.getLogpageNbrs().addAll(detailResp.getAcnDscrp().getLogPageNbrs());
        dscrpLogNbrs.add(logPageNbr);

        if (planResp.getWorkRlseHistory() != null) {
            for (PlannedItemAsgmtType item : planResp.getWorkRlseHistory()) {
                if (item.getDiscrepancyOid() == null) {
                    item.setDiscrepancyOid(detailResp.getAcnDscrp().getAcnDiscrepancyOid());
                }
            }
        }

        List<AcnDiscrepancy> discrepancies = new ArrayList<>();
        discrepancies.add(detailResp.getAcnDscrp());

        return new DiscrepancyTextProcessor(
                discrepancies,
                maintResp.getDscrpMaintUpdates(),
                dscrpLogNbrs,
                planResp.getWorkRlseHistory(),
                false,fetchUpdtTxts
        );
    }

    // Existing supporting methods (unchanged)
    public GetDetailAircraftDiscrepancyResponse getDiscrepancyDetails(
            String userId, String acn, String ata, String dscrpNbr, String tokenId) throws Exception {

        logger.info("______getDiscrepancyDetails for ACN:" + acn);

        GetDetailAircraftDiscrepancyRequest request = new GetDetailAircraftDiscrepancyRequest();

        DetailDiscrepancyQueryFilter filter = new DetailDiscrepancyQueryFilter();
        filter.setAircraftNbr(acn);
        filter.setAtaNbr(new BigDecimal(ata));
        filter.setDiscrepancyNbr(dscrpNbr);
        request.setDetailDiscrepancyFilter(filter);

        SessionType sessionType = new SessionType();
        sessionType.setUserId(userId);
        sessionType.setAuthSourceSysName(AuthSourceSysType.LDAP);
        sessionType.setToken(tokenId);
        request.setSession(sessionType);

        GetDetailAircraftDiscrepancyResponse resp = (GetDetailAircraftDiscrepancyResponse)
                wb.app1webServiceTemplate().marshalSendAndReceive(request);

        logger.info("getDiscrepancyDetails SOAP Response: " + resp.getAcnDscrp().getAcnDiscrepancyOid());
        return resp;
    }

    public GetAllMaintUpdateResponse getAllMaintUpdate(GetAllMaintUpdateRequest request) throws Exception {
        logger.info("____________getAllMaintUpdate");
        GetAllMaintUpdateResponse resp = (GetAllMaintUpdateResponse)
                wb.app1webServiceTemplate().marshalSendAndReceive(request);
        logger.info("getAllMaintUpdate SOAP Response: " + resp.getStatus().isSuccessful());
        return resp;
    }

    public GetDscrpWorkRlseHistoryResponse getPlanHistoryFrDscrp(
            String acn, String ata, String dscrpNbr, String userId, String tokenId) throws Exception {

        logger.info("____________getPlanHistoryFrDscrp");

        GetDscrpWorkRlseHistoryRequest request = new GetDscrpWorkRlseHistoryRequest();
        request.setAircraftNbr(acn);
        request.setAtaNbr(new BigDecimal(ata));
        request.setDscrpNbr(new BigDecimal(dscrpNbr));

        SessionType sessionType = new SessionType();
        sessionType.setUserId(userId);
        sessionType.setToken(tokenId);
        sessionType.setAuthSourceSysName(AuthSourceSysType.LDAP);
        request.setSession(sessionType);

        GetDscrpWorkRlseHistoryResponse resp = (GetDscrpWorkRlseHistoryResponse)
                wb.app1webServiceTemplate().marshalSendAndReceive(request);
        logger.info("getPlanHistoryFrDscrp SOAP Response: " + resp.getStatus().isSuccessful());

        return resp;
    }
}
