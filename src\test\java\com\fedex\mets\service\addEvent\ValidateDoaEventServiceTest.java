package com.fedex.mets.service.addEvent;

import com.fedex.mets.dao.AircraftBean;
import com.fedex.mets.dao.DoaEvents;
import com.fedex.mets.data.ListViewData;
import com.fedex.mets.data.ValidateEventData;
import com.fedex.mets.repository.mets.EventsRepository;
import com.fedex.mets.repository.mss.ForteLegRepository;
import com.fedex.mets.util.RvDBHelper;
import com.fedex.mets.util.ServerDateHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

public class ValidateDoaEventServiceTest {

    @Mock
    private RvDBHelper rvDBHelper;

    @Mock
    private ForteLegRepository forteLegRepository;

    @Mock
    private EventsRepository eventsRepository;

    @InjectMocks
    private ValidateDoaEventService validateDoaEventService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testValidateDOAEvents_Success() throws SQLException {
        String acn = "123";
        List<DoaEvents> doaEvents = new ArrayList<>();
        DoaEvents event = new DoaEvents();
        event.setEventId(1);
        event.setStation("Station1");
        event.setDoaFleetNumber("Flight123");
        event.setDoaFleetDate(Timestamp.valueOf("2025-04-11 10:00:00"));
        event.setDoaFleetLeg("Leg1");
        doaEvents.add(event);


        List<ValidateEventData> doaList = new ArrayList<>();
        ValidateEventData data = new ValidateEventData();
        data.setEventId(1);
        doaList.add(new ValidateEventData());

        List<ListViewData> listViewData = new ArrayList<>();
        listViewData.add(new ListViewData(1));

        when(eventsRepository.getDOAEvents(anyString())).thenReturn(doaEvents);
        when(forteLegRepository.getLegStatusDepartureTime(anyString(), anyString(), anyString())).thenReturn("Status,2025-04-11 10:00:00");

        AircraftBean aircraftBean = new AircraftBean();
        aircraftBean.setInboundFlight("Flight123");
        aircraftBean.setInboundFlightDate("2025-04-11 10:00:00.0");
        aircraftBean.setInboundFlightLeg("Leg1");
        when(rvDBHelper.getAircraftRecordsFromRampview(anyString())).thenReturn(aircraftBean);
        List<ListViewData> result = validateDoaEventService.validateDOAEvents(acn);
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getEventID());
    }

    @Test
    public void testValidateDOAEvents_Exception() {
        String acn = "ACN123";

        doThrow(new RuntimeException("Internal server error")).when(eventsRepository).getDOAEvents(anyString());

        try {
            validateDoaEventService.validateDOAEvents(acn);
        } catch (Exception e) {
            assertEquals("Internal server error", e.getMessage());
        }
    }
}
