package com.fedex.mets.entity.mets;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "rpt_catg_level_2")
public class RptCatgLevel2 {

    @Id
    @Column(name = "LEVEL_2_ID", nullable = false)
    private String level2Id;

    @Column(name = "LEVEL_2_NAME")
    private String level2Name;

    @Column(name = "DESCRIPTION")
    private String desc;

    @Column(name = "LIST_ORDER")
    private Integer listOrderL2;

    @Column(name = "LEVEL_1_ID")
    private String level1Id;

    @Column(name = "SYSTEM_CATG")
    private String systemCatg;

    @Column(name = "ACTIVE_CATG")
    private String activeCatg;

    @Column(name = "LAST_UPDATE_DT_TM")
    private Timestamp lastUpdateDtTm;
}