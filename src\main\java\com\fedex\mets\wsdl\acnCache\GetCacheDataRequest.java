
package com.fedex.mets.wsdl.acnCache;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;

import com.fedex.mets.wsdl.discrepancy.GenericRequest;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;extension base="{http://www.fedex.com/airops/schemas/Common.xsd}GenericRequest">
 *       &lt;sequence>
 *         &lt;element name="requestType" type="{http:///www.fedex.com/airops/schemas/Mach}RequestTypeEnum"/>
 *         &lt;element name="cacheDataType" type="{http:///www.fedex.com/airops/schemas/Mach}CacheDataTypeEnum"/>
 *         &lt;element name="lastUpdtCacheTime" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="key" type="{http://www.w3.org/2001/XMLSchema}anyType"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "requestType",
    "cacheDataType",
    "lastUpdtCacheTime",
    "key"
})
@XmlRootElement(name = "getCacheDataRequest",namespace="http:///www.fedex.com/airops/schemas/Mach")
public class GetCacheDataRequest
    extends GenericRequest
{

    @XmlElement(required = true)
    protected RequestTypeEnum requestType;
    @XmlElement(required = true)
    protected CacheDataTypeEnum cacheDataType;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar lastUpdtCacheTime;
    @XmlElement(required = true)
    protected Object key;

    /**
     * Gets the value of the requestType property.
     * 
     * @return
     *     possible object is
     *     {@link RequestTypeEnum }
     *     
     */
    public RequestTypeEnum getRequestType() {
        return requestType;
    }

    /**
     * Sets the value of the requestType property.
     * 
     * @param value
     *     allowed object is
     *     {@link RequestTypeEnum }
     *     
     */
    public void setRequestType(RequestTypeEnum value) {
        this.requestType = value;
    }

    /**
     * Gets the value of the cacheDataType property.
     * 
     * @return
     *     possible object is
     *     {@link CacheDataTypeEnum }
     *     
     */
    public CacheDataTypeEnum getCacheDataType() {
        return cacheDataType;
    }

    /**
     * Sets the value of the cacheDataType property.
     * 
     * @param value
     *     allowed object is
     *     {@link CacheDataTypeEnum }
     *     
     */
    public void setCacheDataType(CacheDataTypeEnum value) {
        this.cacheDataType = value;
    }

    /**
     * Gets the value of the lastUpdtCacheTime property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLastUpdtCacheTime() {
        return lastUpdtCacheTime;
    }

    /**
     * Sets the value of the lastUpdtCacheTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLastUpdtCacheTime(XMLGregorianCalendar value) {
        this.lastUpdtCacheTime = value;
    }

    /**
     * Gets the value of the key property.
     * 
     * @return
     *     possible object is
     *     {@link Object }
     *     
     */
    public Object getKey() {
        return key;
    }

    /**
     * Sets the value of the key property.
     * 
     * @param value
     *     allowed object is
     *     {@link Object }
     *     
     */
    public void setKey(Object value) {
        this.key = value;
    }

}
