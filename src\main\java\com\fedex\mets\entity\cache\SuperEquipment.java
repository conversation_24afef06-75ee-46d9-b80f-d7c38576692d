package com.fedex.mets.entity.cache;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "SUPER_EQUIPMENT")
public class SuperEquipment {

    @Id
    @Column(name = "ACN", nullable = false)
    private String acn;

    @Column(name = "EQUIPMENT_CODE")
    private Integer equipmentCode;

    @Column(name = "CURRENT_STATION")
    private String currentStation;

    @Column(name = "CURRENT_GATE")
    private String currentGate;

    @Column(name = "CURRENT_ETIC_TEXT")
    private String currentEticText;

    @Column(name = "OPERATIONAL_STATUS", nullable = false)
    private String operationalStatus;

    @Column(name = "COMMENTS")
    private String comments;

    @Column(name = "AC_REGN")
    private String acRegn;

    @Column(name = "TIME_STAMP")
    private BigDecimal timeStamp;

    @Column(name = "FLIGHT_STATUS")
    private String flightStatus;

    @Column(name = "IN_RANGE")
    private String inRange;

    @Column(name = "RELEASE_TO_LOAD")
    private String releaseToLoad;

    @Column(name = "BEACON_STATUS")
    private String beaconStatus;

    @Column(name = "BEACON_STATUS_STAMP")
    private String beaconStatusStamp;

    @Column(name = "OP_STATUS_STAMP")
    private String opStatusStamp;

    @Column(name = "FLIGHT_STATUS_STAMP")
    private String flightStatusStamp;

    @Column(name = "IN_RANGE_STAMP")
    private String inRangeStamp;

    @Column(name = "LAST_RMAS_EVENT_CODE")
    private String lastRmasEventCode;

    @Column(name = "LAST_RMAS_STAMP")
    private String lastRmasStamp;

    @Column(name = "SUPER_UPDATE_STAMP")
    private String superUpdateStamp;

    @Column(name = "LAST_UPDATE_SOURCE")
    private String lastUpdateSource;

    @Column(name = "CURRENT_FLT_KEY")
    private String currentFltKey;

    @Column(name = "OUTB_FLT_KEY")
    private String outbFltKey;

    @Column(name = "DEICE_LOCATION")
    private String deiceLocation;

    @Column(name = "ORIG_STA_CD")
    private String origStaCd;

    @Column(name = "ORIG_GATE_CD")
    private String origGateCd;

    @Column(name = "DEST_STA_CD")
    private String destStaCd;

    @Column(name = "DEST_GATE_CD")
    private String destGateCd;

    @Column(name = "PREV_STA_CD")
    private String prevStaCd;

    @Column(name = "PREV_GATE_CD")
    private String prevGateCd;

    @Column(name = "ACARS_CD")
    private String acarsCd;

    @Column(name = "PREV_FLT_KEY_CD")
    private String prevFltKeyCd;

    @Column(name = "BLOCK_IN_FUEL_QTY")
    private BigDecimal blockInFuelQty;
}
