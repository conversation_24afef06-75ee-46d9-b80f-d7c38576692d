package com.fedex.mets.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class SentenceFormatDef {

	private static final Logger log = LoggerFactory.getLogger(SentenceFormatDef.class);
	static final boolean DEBUG = false;
	
	String name;
	int length;
	int leadingSpace;
	List<FieldFormatDef> fields;
	int trailingSpace;
	
	boolean multiLine    = false;
	boolean repeatHeader = false;
	private String sentence = null;
	
	private boolean isLmpiTsi = false;
	
	private static int lineNum = 1;


	static char[] filler;
	
	public SentenceFormatDef(String name, int length, int leadingSpace, int trailingSpace) {
		this(name, length, leadingSpace, trailingSpace, false) ;
	}

	public SentenceFormatDef(String name, int length, int leadingSpace, int trailingSpace, boolean tsiLmpi) {
		super();
		fields = new ArrayList<FieldFormatDef>();
		
		if( filler == null ){
			filler = new char[500];
			for (int i = 0; i < filler.length; i++) {
				filler[i] = ' ';
			}
		}
		this.name = name;
		this.length = length;
		this.leadingSpace = leadingSpace;
		this.trailingSpace = trailingSpace;
		this.isLmpiTsi = tsiLmpi;
	}

	public String getName() {
		return name;
	}
	
	public void setName(String name) {
		this.name = name;
	}
	
	public int getLength() {
		return length;
	}
	
	public void setLength(int length) {
		this.length = length;
	}

	
	public void setLeadingSpace(int leadingSpace) {
		this.leadingSpace = leadingSpace;
	}
	
	public List<FieldFormatDef> getFields() {
		return fields;
	}

	public void setTrailingSpace(int trailingSpace) {
	
		this.trailingSpace = trailingSpace;
	}

	public void setMultiLine(boolean multiLine) {
		this.multiLine = multiLine;
	}
	
	public void resetLinesNums(boolean reset){
		if(reset){
			lineNum = 1;
		}
	}

	public static String pad(FieldFormatDef ffd) {
		int lenFactor = 1;
		if( ffd.isAllignLeft() ){
			lenFactor = -1;
		}
		return pad( ffd.getValue(), lenFactor * ffd.getLength());
	}


	public static String pad(String s, int n, boolean leftAlign) {
		int lenFactor = 1;
		if( leftAlign ){
			lenFactor = -1;
		}
		return pad( s, lenFactor * n);
	}

	public static String pad(String s, int n) {
	    return String.format("%1$" + n + "s", s);  
	}

	
	private void addTrailingSpace(StringBuilder sb){
		if( trailingSpace != 0){
			sb.append( pad("",  trailingSpace) );
		}

	}
	
	
	public String getSentence(){
		return getSentence(false);
	}
	
	
	public String getSentence(boolean reparse){
		if( !reparse && sentence != null ){
			return sentence;
		}
		StringBuilder sb = new StringBuilder();
		if( leadingSpace != 0){
			sb.append( pad("",  leadingSpace) );
		}
		if( fields != null ){
			
			
			for (FieldFormatDef fieldFormatDef : fields) {
				if( DEBUG)
				log.info(fieldFormatDef + ", padLen:" );
				
				// either the value is not null or
				// valueList is not null
				if( fieldFormatDef.getValue() != null  ){
					if( fieldFormatDef.getLength() >0 ){
						sb.append(  pad(fieldFormatDef) );
					}
				}
				else
				if(fieldFormatDef.getValueList() != null  ){
					StringBuilder values = new StringBuilder();
					for (String value : fieldFormatDef.getValueList()) {
						// pad with spaces, if each field is less than
						// the field length
						int length = 0;
						// Added null check for whole block
						if (value != null) {
							length = value.length();

							int partialLen = length % fieldFormatDef.getLength();
							if (length > fieldFormatDef.getLength() || partialLen == 0) {
								if (!isLmpiTsi && isCheckForSpace(value)) {
									partialLen = (length - value.lastIndexOf(" ")) + 1;
								}
								values.append(value.substring(0, length - partialLen));

							}
							if (partialLen != 0) {
								values.append(pad(value.substring((length - partialLen), length), fieldFormatDef.getLength(), true));
							}
						}
					}
					formatMultiline(fieldFormatDef, sb, values);
				}
			}
			
			
		}
		addTrailingSpace(sb);
		sentence = sb.toString();
		return sentence;
	}
	
	/**
	 * Checks for Spaces in the line along with characters
	 * @param text
	 * @return
	 */
	private boolean isCheckForSpace(String text) {
		if (text != null && !text.isEmpty() && text.lastIndexOf(" ") >= 0) {
			char txt = text.charAt(text.length() - 1);
			if ((txt >= 'a' && txt <= 'z') || (txt >= 'A' && txt <= 'Z')) {
				return true;
			}
			if (txt >= '0' && txt <= '9') {
				return true;
			}
		}
		return false;
	}
	
	private void formatMultiline(FieldFormatDef ffd, StringBuilder sb, StringBuilder value){
	
		int allowedSpaceWithHeader  = length - (leadingSpace + trailingSpace);
		int allowedSpaceNoHeader   = allowedSpaceWithHeader - getFields().get(0).getLength();

		//if currLength can fit into the space of this 
		// just add the string and return
		if( value.length() <= allowedSpaceNoHeader ){
			if(DEBUG)System.out.println(pad(value.toString(), allowedSpaceNoHeader, true) );
			if (isLmpiTsi) {
				addLineNum(sb);
			}
			sb.append(pad(value.toString(), allowedSpaceNoHeader, true) );
			return;
		}
		
		String[] splits = 
				splitWorker(value.toString(), allowedSpaceWithHeader, 
						        repeatHeader, getFields().get(0).getLength());
		
		for (int i = 0; i < splits.length; i++) {

			if( i != 0){
				sb.append("\n");
				if( leadingSpace != 0){
					sb.append( pad("",  leadingSpace) );
				}
				if (isLmpiTsi) {
					addLineNum(sb);
				}
			
				if( repeatHeader){
					// add the header
					// if i == 0, then the header is already added
					sb.append( pad(getFields().get(0)) );
				}
			}else{
				if (isLmpiTsi) {
					addLineNum(sb);
				}
			}

			if( i ==0  || repeatHeader){
				if(DEBUG)System.out.println("allowedSpaceNoHeader:" + allowedSpaceNoHeader);
				sb.append(pad( splits[i], allowedSpaceNoHeader, true) );
			}
			else{
				if(DEBUG)System.out.println("allowedSpaceWithHeader:" + allowedSpaceWithHeader);
				sb.append(pad( splits[i], allowedSpaceWithHeader, true) );
			}
			
			
			if(i < (splits.length - 1))
			addTrailingSpace(sb);

		}
	}
	
	private void addLineNum(StringBuilder sb){
		sb.append(lineNum++ + "  ");
	}
	
	

    /**
     * 
     * @param str
     * @param breakLen
     * @param repeatHeader
     * @param headerLen
     * @return
     */
    private String[] splitWorker(String str, int breakLen, boolean repeatHeader, int headerLen) {
        if (str == null) {
        	return new String[0];
        }
        
        int len = str.length();
        if (len == 0) {
        	return new String[0];
        }

        
        int loopStartPos = 0;
        List<String> list = new ArrayList<String>();

        // first string needs header space
        if( headerLen > 0 ){
	        String firstString = str.substring(0, (breakLen - headerLen ) ) ;
	        list.add( firstString );
	        loopStartPos = firstString.length();
        }

		
        // get rest of the lines
        // rest of them may of mayNOt need it
        int otherLinesLen = repeatHeader ? (breakLen - headerLen ) : breakLen; 


//		while( true ) {  - hate to be in an infinite loop
        int fullLoop = str.length() / otherLinesLen;
        for (int i = 0; i < (fullLoop  + 500); i++) {			
        	if(str.length() == loopStartPos){
        		break;
        	}
			if( (str.length() <= (loopStartPos +otherLinesLen)) ){
				  String temp1 =  str.substring(loopStartPos);
				  if(!temp1.trim().isEmpty()){
					  list.add( str.substring(loopStartPos) );  
				  }
				
				break;
			}
				
			String tempStr = str.substring( loopStartPos, (loopStartPos + otherLinesLen));
			
			//
			// For LMPI / TSI don;t check the for space  
			//
			int lastSpaceChar = 0;
			if( !isLmpiTsi ){
				lastSpaceChar = tempStr.lastIndexOf(" ") + 1; 
			}
			if( lastSpaceChar == 0){
				lastSpaceChar = tempStr.length();
			}
			list.add( tempStr.substring(0, lastSpaceChar));
			loopStartPos += lastSpaceChar; 
		}

        if(DEBUG)System.out.println( list);
        return (String[]) list.toArray(new String[list.size()]);
    }
    
    /**
     * Convenience method to insert text into the current
     * sentence. Used to add tokens for icon placement
     * in discrepancies
     */
    public void addTextToSentence(String textToAdd){
    	
    	if(textToAdd != null){
    		this.sentence = getSentence() + textToAdd;
    	}
    }
	
}

