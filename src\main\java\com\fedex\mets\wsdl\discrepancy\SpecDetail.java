
package com.fedex.mets.wsdl.discrepancy;

import java.math.BigDecimal;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for SpecDetail complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="SpecDetail">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="controlingSpec" type="{http://www.fedex.com/airops/schemas/EnumTypes.xsd}SpecType"/>
 *         &lt;element name="oilSpecDetailType" type="{http://www.fedex.com/airops/schemas/EnumTypes.xsd}OilSpecDetailType" minOccurs="0"/>
 *         &lt;element name="cycleRemaining" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="hoursRemaining" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="daysRemaining" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="dropDeadDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="dueDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SpecDetail", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", propOrder = {
    "controlingSpec",
    "oilSpecDetailType",
    "cycleRemaining",
    "hoursRemaining",
    "daysRemaining",
    "dropDeadDate",
    "dueDate"
})
public class SpecDetail {

    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", required = true)
    protected SpecType controlingSpec;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected OilSpecDetailType oilSpecDetailType;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal cycleRemaining;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal hoursRemaining;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal daysRemaining;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dropDeadDate;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dueDate;

    /**
     * Gets the value of the controlingSpec property.
     * 
     * @return
     *     possible object is
     *     {@link SpecType }
     *     
     */
    public SpecType getControlingSpec() {
        return controlingSpec;
    }

    /**
     * Sets the value of the controlingSpec property.
     * 
     * @param value
     *     allowed object is
     *     {@link SpecType }
     *     
     */
    public void setControlingSpec(SpecType value) {
        this.controlingSpec = value;
    }

    /**
     * Gets the value of the oilSpecDetailType property.
     * 
     * @return
     *     possible object is
     *     {@link OilSpecDetailType }
     *     
     */
    public OilSpecDetailType getOilSpecDetailType() {
        return oilSpecDetailType;
    }

    /**
     * Sets the value of the oilSpecDetailType property.
     * 
     * @param value
     *     allowed object is
     *     {@link OilSpecDetailType }
     *     
     */
    public void setOilSpecDetailType(OilSpecDetailType value) {
        this.oilSpecDetailType = value;
    }

    /**
     * Gets the value of the cycleRemaining property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getCycleRemaining() {
        return cycleRemaining;
    }

    /**
     * Sets the value of the cycleRemaining property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setCycleRemaining(BigDecimal value) {
        this.cycleRemaining = value;
    }

    /**
     * Gets the value of the hoursRemaining property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getHoursRemaining() {
        return hoursRemaining;
    }

    /**
     * Sets the value of the hoursRemaining property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setHoursRemaining(BigDecimal value) {
        this.hoursRemaining = value;
    }

    /**
     * Gets the value of the daysRemaining property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getDaysRemaining() {
        return daysRemaining;
    }

    /**
     * Sets the value of the daysRemaining property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setDaysRemaining(BigDecimal value) {
        this.daysRemaining = value;
    }

    /**
     * Gets the value of the dropDeadDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDropDeadDate() {
        return dropDeadDate;
    }

    /**
     * Sets the value of the dropDeadDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDropDeadDate(XMLGregorianCalendar value) {
        this.dropDeadDate = value;
    }

    /**
     * Gets the value of the dueDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDueDate() {
        return dueDate;
    }

    /**
     * Sets the value of the dueDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDueDate(XMLGregorianCalendar value) {
        this.dueDate = value;
    }

}
