package com.fedex.mets.service.cancelEvent;

import com.fedex.mets.data.WizardEventData;
import com.fedex.mets.entity.mets.*;
import com.fedex.mets.entity.pcs.WorksOrders;
import com.fedex.mets.repository.mets.*;
import com.fedex.mets.repository.pcs.WorksOrdersRepository;
import com.fedex.mets.service.changeEvent.EventVerfiyService;
import com.fedex.mets.service.reviewEvent.SuperUpdateHelper;
import com.fedex.mets.util.IServerConstants;
import com.fedex.mets.util.ServerDateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

@Service
public class EventCancelService {
    private static final Logger logger = LoggerFactory.getLogger(EventCancelService.class);

    @Autowired
    private EventsRepository eventsRepository;

    @Autowired
    private ChangeRequestRepository changeRequestRepository;

    @Autowired
    private ChangeRequestHistoryRepository changeRequestHistoryRepository;

    @Autowired
    private ChangeRequestLogRepository changeRequestLogRepository;

    @Autowired
    private EventTfNotesRepository eventTfNotesRepository;

    @Autowired
    private EventTimersRepository eventTimersRepository;

    @Autowired
    private EventDoaRepository eventDoaRepository;

    @Autowired
    private WorksOrdersRepository worksOrdersRepository;
    
    @Autowired
    private GroupDictRepository groupDictRepository;

    @Autowired
    private SuperUpdateHelper superUpdateHelper;
    
    @Autowired
    private EventVerfiyService eventVerifyService;
    
    public HashMap cancelEventWrapper(WizardEventData wizardEventData) throws Exception {
    	
    	HashMap finalHashMap = new HashMap<>();
		String serverError = "", isTimerPublishRequired = "";
		
		HashMap eventCancelledHashMap = cancelEvent(wizardEventData);
		logger.info("Test4" , wizardEventData.getEventId());
		if (eventCancelledHashMap != null) {
			serverError = (String) eventCancelledHashMap.get(IServerConstants.ERROR);
			isTimerPublishRequired = (String) eventCancelledHashMap.get(IServerConstants.IS_TIMER_PUBLISH_REQUIRED);
//			superUpdateSendBuffer = (SendBuffer) returnHashMap.get(IServerConstants.SUPER_UPDATE_SENDBUFFER);

			if (serverError == null) {

				boolean isTranscationComplete = false;

				try {
					isTranscationComplete = eventVerifyService.deleteEventAction(wizardEventData.getEventId(), null, wizardEventData.getEmployeeName());
				} catch (Exception ce) {
					logger.info("Cancel Event Verify create exception " + ce.getMessage());
				}
				logger.info("/n Event Action record Deleted >> " + isTranscationComplete);

//			TODO: Need to implement below code
				
//			if (eventWizardSessionHome != null) {
//					try {
//						eventWizardSession = eventWizardSessionHome.create();
//
//						eventWizardSession.publishEvent(
//							wizardEventData.getEventId(),
//							IServerConstants.CANCEL_EVENT);
//					} catch (javax.ejb.CreateException ce) {
//						getLogger().info("Publishing Message create exception " + ce.getMessage());
//					} catch (RemoteException re) {
//						getLogger().info("Publishing Message remote exception " + re.getMessage());
//					}
//
//			}

//			boolean isTFNotesPublished = publishEventUpdate(
//						wizardEventData.getEventId(),
//						isTimerPublishRequired);
//			getLogger().info(" isTFNotesPublished >> " + isTFNotesPublished);

				String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
	
				finalHashMap.put(IServerConstants.CURRENT_TIME, currentDateTime);
				finalHashMap.put(IServerConstants.WIZARD_EVENT_DATA, wizardEventData);
				finalHashMap.put("MoreData", eventCancelledHashMap);
				
//				TODO: Need to implement this later on
//				if(superUpdateSendBuffer != null) {
//					boolean isSuperUpdated = SuperUpdateHelper.sendSuperUpdate(superUpdateSendBuffer);
//					getLogger().info("MetsEventCloseServlet sendSuperUpdateBuffer " + (isSuperUpdated ? "succeeded" : "failed"));
//				}		
			} else {
				String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
				finalHashMap.put(IServerConstants.CURRENT_TIME, currentDateTime);
				finalHashMap.put(IServerConstants.ERROR, serverError);
			}
		} else {
			String currentDateTime = ServerDateHelper.getCurrentTimeStamp();
			finalHashMap.put(IServerConstants.CURRENT_TIME, currentDateTime);
			finalHashMap.put(IServerConstants.ERROR, "The Event could not be cancelled.");
		}	
	
		return eventCancelledHashMap;
    	
    }
    
    public HashMap cancelEvent(WizardEventData wizardEventData) throws Exception {
    	
    	HashMap returnHashMap = new HashMap<>();
		int activeEvent = 0;
		int alreadyUP = 0;
		int activeOOSEvent = 0;
		boolean unreviewedEvent = false;

        Events events = null;
        Events eventsData = null;
        ChangeRequest changeRequest = null;
        ChangeRequest changeRequestData = null;
        ChangeRequestHistory changeRequestHistory = null;
        ChangeRequestHistory changeRequestHistoryData = null;
        ChangeRequestHistory newChangeRequestHistory = null;
        ChangeRequestHistory newChangeRequestHistoryData = null;
        ChangeRequestLog changeRequestLog = null;
        ChangeRequestLog newChangeRequestLog = null;
        ChangeRequestLog changeRequestLogData = null;
        ChangeRequestLog newChangeRequestLogData = null;
        
		boolean validEvent = false;
		String accessLevel = wizardEventData.getAccessLevel();
		logger.info("Test2" , wizardEventData.getAccessLevel());
		try {
			if (accessLevel != null) {
				String strLastUpdateTime = "";
				int eventCheckCount = 0;
				if (wizardEventData.getChangeRequestLastUpdated() != null) {
					try {
						strLastUpdateTime = String.valueOf(changeRequestHistoryRepository.getLastUpdateDtTm(wizardEventData.getEventId()));
						logger.info("Test" , strLastUpdateTime);
						logger.info("Test2" , wizardEventData.getEventId());
						if (strLastUpdateTime != null && wizardEventData.getChangeRequestLastUpdated() != null) {
							if (strLastUpdateTime.trim().equals(wizardEventData.getChangeRequestLastUpdated())) {
								eventCheckCount = eventCheckCount + 1;
							}
						}

						if (eventCheckCount == 0) {
							logger.debug("An intervening change has occurred. The Event cannot be closed.");
							returnHashMap.put(IServerConstants.ERROR, "An intervening change has been made.\nThe changes made will not be saved.");
							return returnHashMap;
						}
					} catch (Exception updated) {
						logger.warn("ERROR Close Event closeEvent() Checking Event Updated time >> " + updated.getMessage());
					}
				}

				if (accessLevel.trim().equals("80")) {
					if (wizardEventData.getEventType().trim().equals("OOS")) {
						int numberOfRecords = changeRequestRepository.getCountOfChangeRequestRecordsByAcnChangeType(wizardEventData.getACN());
						if (numberOfRecords > 0) {
							validEvent = true;
							unreviewedEvent = true;
						}
					} else {
						int numberOfRecords = eventsRepository.getCountOfOOSEventsByEventId(wizardEventData.getACN(), wizardEventData.getEventId());
						if (numberOfRecords == 0)
							validEvent = true;
					}
				} else if (accessLevel.trim().equals("90")) {
					if (wizardEventData.getEventType().trim().equals("OOS")) {
						int numberOfRecords = changeRequestRepository.getCountOfChangeRequestRecordsByAcn(wizardEventData.getACN());
						if (numberOfRecords == 0)
							validEvent = true;
					} else {
						int numberOfRecords = eventsRepository.getCountOfOOSEventsByEventId(wizardEventData.getACN(), wizardEventData.getEventId());
						if (numberOfRecords == 0)
							validEvent = true;
					}
				}
			}
			logger.info("validEvent ========================" + validEvent);

			if (!validEvent) {
				returnHashMap.put(IServerConstants.ERROR, "An intervening change has occurred and this event, \n can no longer be cancelled.");
				return returnHashMap;
			}

			Events eventData = eventsRepository.getActiveEventsByEventId(wizardEventData.getEventId());
			
			if (eventData != null) {
				activeEvent = activeEvent + 1;
				logger.info("Existing active event====== " + activeEvent);

				wizardEventData.setEventType(eventData.getType());
				wizardEventData.setStartDateTime(eventData.getStartDateTime().toString());
				wizardEventData.setACN(eventData.getAcn());
				wizardEventData.setAircraftType(eventData.getFleetDesc());
				wizardEventData.setStation(eventData.getStation());
				wizardEventData.setStatus(eventData.getStatus());
				wizardEventData.setEticDateTime(eventData.getEticDateTime().toString());
				wizardEventData.setEticInfo(eventData.getEticText());
				wizardEventData.setEticComment(eventData.getCurComment());
			}

			if (wizardEventData.getEventType().trim().equalsIgnoreCase("OOS")) {

				alreadyUP = changeRequestRepository.getCountOfChangeRequestRecordsByNewStatus(wizardEventData.getACN());
			    logger.info("alreadyUP ========================" + alreadyUP);
			}

			if (wizardEventData.getEventType().trim().equalsIgnoreCase("DOA")) {
				activeOOSEvent = eventsRepository.getCountOfActiveOOSEvents(wizardEventData.getACN());
				logger.info("activeOOSEvent ========================" + activeOOSEvent);
			}

			try {
				wizardEventData.setGroupTitle(groupDictRepository.getGroupTitleByAccessLevel(wizardEventData.getAccessLevel()));
			} catch (Exception group) {
				logger.warn(" ERROR Cancel Event cancelEvent()  group>>" + group.getMessage());
			}

			if (activeEvent == 0) {
				returnHashMap.put(IServerConstants.ERROR, "This event is no longer active and cannot be cancelled.");
				return returnHashMap;
			}

		} catch (Exception e) {
			
			logger.warn(" ERROR Cancel Event cancelEvent()  event no longer active>>"+ e.getMessage());
			returnHashMap.put(IServerConstants.ERROR, "This event is no longer active and cannot be cancelled.");
			return returnHashMap;
			
		}
		
		java.sql.Timestamp cancelledDateTimeStamp = null, eticDateTimeStamp = null, newEticDateTime = null;

		java.sql.Timestamp incrementedCancelledDateTime = null;

		String strEticDateTime = wizardEventData.getEticDateTime();
		try {
			cancelledDateTimeStamp = ServerDateHelper.getTimeStamp();
			logger.info("cancelledDateTimeStamp >>>>>>>>>>>>>> " + cancelledDateTimeStamp);

			java.util.Date currentDate = ServerDateHelper.getDate_UTC();
			incrementedCancelledDateTime = new java.sql.Timestamp(currentDate.getTime() + 1000);
			logger.info("incrementedCancelledDateTime >>>>>>>>>>>>>> " + incrementedCancelledDateTime);

			wizardEventData.setCancelledDateTime(cancelledDateTimeStamp);
			if (strEticDateTime != null) {
				eticDateTimeStamp = ServerDateHelper.getConvertedTimestamp(strEticDateTime);
				logger.info("eticDateTimeStamp  after converting -------" + eticDateTimeStamp);
			}
		} catch (Exception convert) {
			logger.warn(" ERROR Cancel Event cancelEvent()  convert time stamp>>"+ convert.getMessage());
			returnHashMap.put(IServerConstants.ERROR, "The Date Time String could not converted to TimeStamp variable." + convert.getMessage());
			return returnHashMap;
		}

		try {
			if (activeEvent > 0) {
				try {
					events = eventsRepository.findById(wizardEventData.getEventId()).orElse(null);
				} catch (Exception pk) {
					logger.warn(" ERROR Cancel Event cancelEvent()  This event does not exist, and hence cannot be cancelled "+ pk.getMessage());
					returnHashMap.put(IServerConstants.ERROR, "This event does not exist, and hence cannot be cancelled." + pk.getMessage());
					return returnHashMap;
				}
			}
		} catch (Exception rem) {
			logger.warn(" ERROR Cancel Event cancelEvent()  remove " + rem.getMessage());
		}

		try {
			if (events != null && activeEvent > 0) {
				logger.info("before adding the TF_Notes for the event " + wizardEventData.getEventId());
				boolean tfNotesAdded = addTFNotes(wizardEventData);

				if (!tfNotesAdded) {
					events = null;
					returnHashMap.put(IServerConstants.ERROR, "TF Notes could not be added for the Event");
				}
				logger.info(" -- tfNotesAdded " + tfNotesAdded);
			}
		} catch (Exception tfNote) {
			logger.warn(" ERROR Cancel Event cancelEvent()  tfNote " + tfNote.getMessage());
			events = null;
			returnHashMap.put(IServerConstants.ERROR, "TF Notes could not be added for the Event");
		}
		
		try {
			if (wizardEventData.getEventType().trim().equals("OOS")) {
				if (events != null && alreadyUP == 0) {
					logger.info("before updating the Change Request for the event " + wizardEventData.getEventId() + " for type " + wizardEventData.getEventType());
					try {
						changeRequest = changeRequestRepository.findById(wizardEventData.getACN()).orElse(null);
					} catch (Exception pk) {
						logger.warn(pk+"");
					}

					try {
						if (changeRequest != null) {
							changeRequestData = changeRequest;

							if (wizardEventData.getAccessLevel().trim().equals("80")) {
								changeRequestHistoryData = generateChangeRequestHistoryData(changeRequestData, wizardEventData.getCancelledDateTime());

								changeRequestLogData = generateUnreviewedChangeRequestLogData(changeRequestData.getEventId(), wizardEventData.getCancelledDateTime(), changeRequestData.getCreatedDtTm());

								try {
									logger.info(" -- removing the current record from Change Request table.");
									changeRequestData = null;
									changeRequestRepository.delete(changeRequest);
									changeRequest = null;
								} catch (Exception remove) {
									logger.warn(" ERROR Cancel Event cancelEvent()  remove " + remove.getMessage());
								}

							} else if (
								wizardEventData.getAccessLevel().trim().equals("90")) {
								changeRequestHistoryData = generateChangeRequestHistoryData(changeRequestData, wizardEventData.getCancelledDateTime());

								changeRequestLogData = generateCancelledChangeRequestLogData(changeRequestData.getEventId(), wizardEventData.getCancelledDateTime(), changeRequestData.getCreatedDtTm());
								try {
									changeRequestData = null;
									changeRequestRepository.delete(changeRequest);
								} catch (Exception remove) {
									logger.warn(" ERROR Cancel Event cancelEvent()  remove **** "+ remove.getMessage());
								}

								try {
									logger.info(" -- inserting new records in request history && log tables");
									changeRequestData = generateChangeRequestData(wizardEventData, incrementedCancelledDateTime, eticDateTimeStamp);

									newChangeRequestHistoryData = generateNewChangeRequestHistoryData(wizardEventData, incrementedCancelledDateTime, eticDateTimeStamp);

									newChangeRequestLogData = generateSubmittedChangeRequestLogData(changeRequestData.getEventId(), incrementedCancelledDateTime, changeRequestData.getCreatedDtTm());
								} catch (Exception changeReq) {
									
									logger.warn(" ERROR Cancel Event cancelEvent()  changeReq " + changeReq.getMessage());
									events = null;
									returnHashMap.put(IServerConstants.ERROR, "Could not insert the record in to Change Request table for the event " + wizardEventData.getEventId() + "\n" + changeReq.getMessage());
									logger.info("Could not insert the record in to Change Request table for the event " + wizardEventData.getEventId() + " " + changeReq.getMessage());
								
								}
							}
						}
						else { 
							if (wizardEventData.getAccessLevel().trim().equals("90")) {
								try {
									changeRequestData = generateChangeRequestData(wizardEventData, incrementedCancelledDateTime, eticDateTimeStamp);

									newChangeRequestHistoryData = generateNewChangeRequestHistoryData(wizardEventData, incrementedCancelledDateTime, eticDateTimeStamp);

									newChangeRequestLogData = generateSubmittedChangeRequestLogData(changeRequestData.getEventId(), incrementedCancelledDateTime, changeRequestData.getCreatedDtTm());
								} catch (Exception changeReq) {

									events = null;
									returnHashMap.put(IServerConstants.ERROR, "Could not insert the record in to Change Request table for the event " + wizardEventData.getEventId() + "\n" + changeReq.getMessage());
									logger.info("Could not insert the record in to Change Request table for the event " + wizardEventData.getEventId() + " " + changeReq.getMessage());
								}
							}
						}
					} catch (Exception changeReq) {
						logger.warn(" ERROR Cancel Event cancelEvent()  changeReq ******** " + changeReq.getMessage());
						events = null;
						returnHashMap.put(IServerConstants.ERROR, "Could not update Change Request for the event " + wizardEventData.getEventId() + "\n" + changeReq.getMessage());
					}
				}

			} else if (wizardEventData.getEventType().trim().equals("DOA")) {
				if (events != null && alreadyUP == 0 && activeOOSEvent == 0) {
					logger.info("before updating the Change Request for the event " + wizardEventData.getEventId() + " for type " + wizardEventData.getEventType());
					try {
						changeRequest = changeRequestRepository.findById(wizardEventData.getACN()).orElse(null);
					} catch (Exception pk) {
						logger.warn(pk + "");
					}

					try {
						if (changeRequest != null) {
							changeRequestData = changeRequest;

							if (wizardEventData.getAccessLevel().trim().equals("80")) {
								changeRequestHistoryData = generateChangeRequestHistoryData(changeRequestData, wizardEventData.getCancelledDateTime());

								try {
									changeRequestLogData = generateUnreviewedChangeRequestLogData(changeRequestData.getEventId(), wizardEventData.getCancelledDateTime(), changeRequestData.getCreatedDtTm());
								} catch (Exception logData) {
									logger.warn(" ERROR Cancel Event cancelEvent()  logData ******** " + logData.getMessage());
								}

								try {
									changeRequestData = null;
									changeRequestRepository.delete(changeRequest);
									changeRequest = null;
								} catch (Exception remove) {
									logger.warn(" ERROR Cancel Event cancelEvent()  remove  111 " + remove.getMessage());
								}

							} else if (
								wizardEventData.getAccessLevel().trim().equals("90")) {							
								changeRequestHistoryData = generateChangeRequestHistoryData(changeRequestData, wizardEventData.getCancelledDateTime());

								try {
									changeRequestLogData = generateCancelledChangeRequestLogData(changeRequestData.getEventId(), wizardEventData.getCancelledDateTime(), changeRequestData.getCreatedDtTm());
								} catch (Exception logData) {
									logger.warn(" ERROR Cancel Event cancelEvent()  ## logData 1 " + logData.getMessage());
								}

								try {
									changeRequestData = null;
									changeRequestRepository.delete(changeRequest);
									changeRequest = null;
								} catch (Exception remove) {
									logger.warn(" ERROR Cancel Event cancelEvent()  ## remove 1 " + remove.getMessage());
								}

								try {

									changeRequestData = generateChangeRequestData(wizardEventData, incrementedCancelledDateTime, eticDateTimeStamp);

									newChangeRequestHistoryData = generateNewChangeRequestHistoryData(wizardEventData, incrementedCancelledDateTime, eticDateTimeStamp);

									newChangeRequestLogData = generateSubmittedChangeRequestLogData(changeRequestData.getEventId(), incrementedCancelledDateTime, changeRequestData.getCreatedDtTm());
								} catch (Exception changeReq) {
									logger.warn(" ERROR Cancel Event cancelEvent() Could not insert the record in to Change Request table for the event " + wizardEventData.getEventId() + " " + changeReq.getMessage());
									events = null;
									returnHashMap.put(IServerConstants.ERROR, "Could not insert the record in to Change Request table for the event " + wizardEventData.getEventId() + "\n" + changeReq.getMessage());
								}
							}
						}
						else { 
							if (wizardEventData.getAccessLevel().trim().equals("90")) {
								try {
									
									changeRequestData = generateChangeRequestData(wizardEventData, incrementedCancelledDateTime, eticDateTimeStamp);

									newChangeRequestHistoryData = generateNewChangeRequestHistoryData(wizardEventData, incrementedCancelledDateTime, eticDateTimeStamp);

									newChangeRequestLogData = generateSubmittedChangeRequestLogData(changeRequestData.getEventId(), incrementedCancelledDateTime, changeRequestData.getCreatedDtTm());
								} catch (Exception changeReq) {
									events = null;
									returnHashMap.put(IServerConstants.ERROR, "Could not insert the record in to Change Request table for the event " + wizardEventData.getEventId() + "\n" + changeReq.getMessage());
									logger.info("Could not insert the record in to Change Request table for the event " + wizardEventData.getEventId() + " " + changeReq.getMessage());
								}
							}
						}
					} catch (Exception changeReq) {
						logger.warn(" ERROR Cancel Event cancelEvent() Could not update Change Request for the event " + wizardEventData.getEventId() + " " + changeReq.getMessage());
						events = null;
						returnHashMap.put(IServerConstants.ERROR, "Could not update Change Request for the event " + wizardEventData.getEventId() + "\n" + changeReq.getMessage());
					}
				}
			}
		} catch (Exception chRequest) {
			logger.warn(" ERROR Cancel Event cancelEvent() chRequest " + chRequest.getMessage());
		}
		
		try {
			if (events != null && wizardEventData.getActiveTimerId() != null) {

				boolean resultFromBean = false;
                String strTimerId = wizardEventData.getActiveTimerId();
                String timerStopDateTime = wizardEventData.getTimerStopDateTime();
                String timerCreatedDateTime = wizardEventData.getTimerCreatedDateTime();
                String lastUpdated = wizardEventData.getTimerLastUpdated();
				logger.info("before stoping the NIW timer for Event Id " + wizardEventData.getEventId());

				try {
					resultFromBean = stopNIWTimer(wizardEventData.getEventId(), strTimerId, timerStopDateTime, timerCreatedDateTime, lastUpdated, wizardEventData.getClosedDateTime());
				} catch (Exception stop) {
					logger.warn(" ERROR Cancel Event cancelEvent() stop timer " + stop.getMessage());
					events = null;
					returnHashMap.put(IServerConstants.ERROR, "Could not Stop the Timer for the event " + wizardEventData.getEventId() + "\n" + stop.getMessage());
					logger.info("Could not Stop the Timer for the event " + wizardEventData.getEventId() + " " + stop.getMessage());
				}

				if (resultFromBean) {
					returnHashMap.put(IServerConstants.IS_TIMER_PUBLISH_REQUIRED, "TIMER_PUBLISH_REQUIRED");
				}
				logger.info("Timer Id Stopped >>>>>>" + resultFromBean);
			}
		} catch (Exception timers) {
			logger.warn(" ERROR Cancel Event cancelEvent() timers " + timers.getMessage());
		}

		try {
			if (wizardEventData.getEventType().trim().equals("DOA")) {
				logger.info(" ****** doa Event update ");
				EventDoa eventDOA = null;

				try {
					eventDOA = eventDoaRepository.findById(wizardEventData.getEventId()).orElse(null);
					if (eventDOA != null) {
						
                        eventDOA.setClosedBy(wizardEventData.getUserId());
                        eventDOA.setClosedDtTm(wizardEventData.getClosedDateTime());
                        eventDOA.setMaintCW("N");
                        eventDOA.setLastUpdtDtTm(wizardEventData.getClosedDateTime());

                        eventDoaRepository.save(eventDOA);
					}
				} catch (Exception doaUpdate) {
					logger.warn(" ERROR Cancel Event cancelEvent() ****** doa Event update problem " + doaUpdate.getMessage());
				}
			}
		} catch (Exception doa) {
			logger.warn(" ERROR Cancel Event cancelEvent()  ==== doa problem " + doa.getMessage());
		}

		
		// start here
		try {
			if (events != null) {
				try {
					eventsData = eventsRepository.findById(events.getEventId()).orElse(null);

					eventsData.setEndDateTime(wizardEventData.getCancelledDateTime());
					eventsData.setLastUpdateDateTime(wizardEventData.getCancelledDateTime());
					eventsData.setLastUpdatedBy(wizardEventData.getUserId());
					
					if(null!=wizardEventData.getResMgrId() && wizardEventData.getResMgrId().trim().length() >0){
						eventsData.setFedexIdNumber(wizardEventData.getResMgrId());
					}
					
					if(null!=wizardEventData.getMemDeskContact() && wizardEventData.getMemDeskContact().trim().length() >0){
						eventsData.setMemDeskContact(wizardEventData.getMemDeskContact());
					}

					logger.info("wizardEventData.getEventType() " + wizardEventData.getEventType());

					if (wizardEventData.getEventType().trim().equals("TRK") || wizardEventData.getEventType().trim().equals("NOTE")) {
						eventsData.setActiveEvent("N");
					} else if (wizardEventData.getEventType().trim().equals("DOA")) {
						if (activeOOSEvent == 0) {
							eventsData.setActiveEvent("Y");
						} else if (activeOOSEvent > 0) {
							eventsData.setActiveEvent("N");
						}
					} else if (wizardEventData.getEventType().trim().equals("OOS")) {
						if (unreviewedEvent) {
							eventsData.setActiveEvent("N");
						}
					}
					eventsData.setCancelled("Y");
					eventsRepository.save(eventsData);
					
					logger.info("After updating the record in the Events table. for event Id----->" + wizardEventData.getEventId());
				} catch (Exception eventUpdate) {
					logger.warn(" ERROR Cancel Event cancelEvent()  ==== eventUpdate " + eventUpdate.getMessage());
				}

				try {
					if (changeRequestLogData != null) {
						changeRequestLog = changeRequestLogRepository.save(changeRequestLogData);
						logger.info("After creating the ChangeRequest log for event Id----->" + wizardEventData.getEventId());
					}
				} catch (Exception log) {
					logger.warn(" ERROR Cancel Event cancelEvent()  ==== changeRequestLog " + log.getMessage());
				}

				try {
					if (changeRequestHistoryData != null) {
						changeRequestHistory = changeRequestHistoryRepository.save(changeRequestHistoryData);
						logger.info("After creating the ChangeRequestHistory for event Id----->" + wizardEventData.getEventId());
					}
				} catch (Exception log) {
					logger.warn(" ERROR Cancel Event cancelEvent()  ==== changeRequestHistory " + log.getMessage());
				}

				try {
					if (newChangeRequestLogData != null) {
						newChangeRequestLog = changeRequestLogRepository.save(newChangeRequestLogData);
						logger.info("After creating a new record for the ChangeRequest log for event Id----->" + wizardEventData.getEventId());
					}
				} catch (Exception log) {
					logger.warn(" ERROR Cancel Event cancelEvent()  ==== newchangeRequestLog " + log.getMessage());
				}

				try {
					if (newChangeRequestHistoryData != null) {
						newChangeRequestHistory = changeRequestHistoryRepository.save(newChangeRequestHistoryData);
						logger.info("After creating a new record for the ChangeRequestHistory for event Id----->" + wizardEventData.getEventId());
					}
				} catch (Exception log) {
					logger.warn(" ERROR Cancel Event cancelEvent()  ==== newchangeRequestHistory " + log.getMessage());
				}

				try {
					if (changeRequestData != null) {
						changeRequest = changeRequestRepository.save(changeRequestData);
						logger.info("After creating the ChangeRequest for event Id----->" + wizardEventData.getEventId());
					}
				} catch (Exception log) {
					logger.warn(" ERROR Cancel Event cancelEvent()  ==== changeRequest " + log.getMessage());
					changeRequestData = null;
				}
			}
		} catch (Exception close) {
			logger.warn(" ERROR Cancel Event cancelEvent()  close " + close.getMessage());
		}

		if (changeRequestData != null && changeRequest != null) {
			boolean isPCSUpdated = false;
			if (wizardEventData.getAcnRegistrationNumber() != null) {
				isPCSUpdated = updatePCSData(wizardEventData.getAcnRegistrationNumber(), changeRequestData.getCreatedDtTm());
			}
		}

		if (wizardEventData.getAccessLevel().trim().equals("90") && changeRequestData != null && changeRequest != null) {
			boolean isSuperUpdateRequired = false;
			try {
				isSuperUpdateRequired = superUpdateHelper.superUpdateRequired(wizardEventData.getACN(), changeRequestData.getNewStatus(), wizardEventData.getEticDateTime(), changeRequestData.getNewEticText(), changeRequestData.getNewComment());
			} catch (Exception updateRequired) {
				logger.warn(" ERROR Cancel Event cancelEvent()  updateRequired " + updateRequired.getMessage());
			}

			try {
				if (isSuperUpdateRequired) {
					//TODO: need to implement this functionality
					// added to avoid race condition where GDI returns SUPER confirmation before CHANGE_REQUEST table is updated
					// returnHashMap.put(IServerConstants.SUPER_UPDATE_SENDBUFFER, generateSendBufferData(wizardEventData, changeRequestData));
					
				} else {

					boolean isMetsDataUpdated = updateMetsDataRecords(changeRequest, changeRequestHistory, changeRequestLog, events);
					logger.info("is Auto Confirmation DONE >>" + isMetsDataUpdated);
				}
			} catch (Exception superUpdate) {
				logger.warn(" ERROR Cancel Event cancelEvent()  superUpdate " + superUpdate.getMessage());
			}
		}
		
		return returnHashMap;
    }

    private ChangeRequestHistory generateChangeRequestHistoryData(
            ChangeRequest changeRequestData,
            java.sql.Timestamp cancelledDateTime) {
        ChangeRequestHistory changeRequestHistoryData = new ChangeRequestHistory();
        ChangeRequestHistoryPk changeRequestHistoryPk = new ChangeRequestHistoryPk();
        changeRequestHistoryPk.setEventId(changeRequestData.getEventId());
        changeRequestHistoryPk.setAcn(changeRequestData.getAcn());
        changeRequestHistoryPk.setCreatedDtTm(cancelledDateTime);
        changeRequestHistoryData.setChangeRequestHistoryPk(changeRequestHistoryPk);

        if (changeRequestData.getOldEticText() != null && changeRequestData.getOldEticText().startsWith("M")) {
            logger.info("= >>> ERROR Me OldEticText in History table >" + changeRequestData.getOldEticText());
        }
        logger.info("= >>> OldEticText in History table >" + changeRequestData.getOldEticText());
        changeRequestHistoryData.setOldEticText(changeRequestData.getOldEticText());
        changeRequestHistoryData.setOldComment(changeRequestData.getOldComment());
        changeRequestHistoryData.setOldStatus(changeRequestData.getOldStatus());
        logger.info("= >>> NewEticText in History table >" + changeRequestData.getNewEticText());
        changeRequestHistoryData.setNewEticText(changeRequestData.getNewEticText());
        changeRequestHistoryData.setNewComment(changeRequestData.getNewComment());
        changeRequestHistoryData.setNewStatus(changeRequestData.getNewStatus());
        changeRequestHistoryData.setRequestStatus("X");
        changeRequestHistoryData.setLastUpdateDtTm(changeRequestData.getLastUpdateDtTm());
        changeRequestHistoryData.setEnteredInError(changeRequestData.getEnteredInError());
        changeRequestHistoryData.setChangeType(changeRequestData.getChangeType());
        return changeRequestHistoryData;
    }

    private boolean stopNIWTimer(
            int eventId,
            String timerId,
            String timerStopDateTime,
            String createdDateTime,
            String lastUpdated,
            java.sql.Timestamp closedDateTime)
            throws Exception {
        EventTimers eventTimers = null;
        boolean result = false;
        int lastUpdatedRecords = 0;

        String strLookupCreatedDateTime = ServerDateHelper.getLookUpFormat(createdDateTime);
        String strLookupLastUpdatedDateTime = ServerDateHelper.getLookUpFormat(lastUpdated);

        try {
            lastUpdatedRecords = eventTimersRepository.getTimerCountByEventIdAndTimerId(eventId, timerId, strLookupCreatedDateTime, strLookupLastUpdatedDateTime);
            logger.info("Last updated Records ========================" + lastUpdatedRecords);

        } catch (Exception count) {
            logger.warn("ERROR Close Event stopNIWTimer() count exception >> " + count.getMessage());
        }
        if (lastUpdatedRecords == 0) {
            try {
                java.sql.Timestamp stopDateTimeStamp = null, createdDateTimeStamp = null;
                if (timerStopDateTime != null) {
                    stopDateTimeStamp = ServerDateHelper.getConvertedTimestamp(timerStopDateTime);
                    logger.info("stopDateTimeStamp  after converting -------" + stopDateTimeStamp);
                }

                if (createdDateTime != null) {
                    createdDateTimeStamp = ServerDateHelper.getConvertedTimestamp(createdDateTime);
                    logger.info("createdDateTimeStamp  after converting -------" + createdDateTimeStamp);
                }

                if (stopDateTimeStamp != null && createdDateTimeStamp != null) {
                    try {
                        eventTimers = eventTimersRepository.findById(new EventTimersPk(eventTimers.getEventTimersPk().getEventId(), eventTimers.getEventTimersPk().getCreationDtTm())).orElse(null);
                        if (eventTimers != null) {
                            eventTimers.setTimerStopDtTm(stopDateTimeStamp);
                            eventTimers.setLastUpdateDtTm(closedDateTime);
                            eventTimersRepository.save(eventTimers);
                            result = true;
                        }
                    } catch (Exception e) {
                        logger.warn("ERROR Close Event stopNIWTimer() update exception >> " + e.getMessage());
                        result = false;
                    }
                }
            } catch (Exception ee) {
                logger.warn("ERROR Close Event stopNIWTimer() update exception >> " + ee.getMessage());
                result = false;
            }
        }
        if (lastUpdatedRecords > 0) {
            throw new Exception("Not In Work Timer has been updated, could not STOP the Timer");
        }
        return result;
    }

    private boolean updatePCSData(String acnRegistration, java.sql.Timestamp updatedTime) {
        boolean isPCSUpdated = false;
        long tempWorkOrder = 0;
        logger.info("updatePCSData.............. acnRegistration >> " + acnRegistration);
        try {
            List<WorksOrders> pcsRecords = worksOrdersRepository.findWORecords(acnRegistration);
            for (int i = 0; i < pcsRecords.size(); i++) {
                try {
                    WorksOrders pcsWorkOrderData = worksOrdersRepository.findById(pcsRecords.get(i).getWoNo()).orElse(null);
                    tempWorkOrder = pcsWorkOrderData.getWoNo();
                    pcsWorkOrderData.setWoActualRfmDtTm(updatedTime);
                    logger.info("..PCS Work Order >" + tempWorkOrder + "WO_Actual_Rfm_Dt_Tm " + pcsWorkOrderData.getWoActualRfmDtTm());

                    WorksOrders wo = worksOrdersRepository.save(pcsWorkOrderData);
                    logger.info(".....Successfully updated  pcsWorkOrder " + wo.getWoNo());
                    if (wo != null) {
                        isPCSUpdated = true;
                    }
                } catch (Exception update) {
                    logger.warn("ERROR Cancel Event updatePCSData() update exception >> " + update.getMessage());
                    logger.info("Error updating PCS Record for Work Order >> " + tempWorkOrder + " Error Mesg >"  + update.getMessage());
                }
            }
        } catch (Exception finder) {
            logger.warn("ERROR Close Event updatePCSData() finder exception >> " + finder.getMessage());
        }

        try {
            List<WorksOrders> pcsRecords = worksOrdersRepository.findWOWithRTMDateTime(acnRegistration);
            for (int i = 0; i < pcsRecords.size(); i++) {
                try {
                    WorksOrders pcsWorkOrderData = worksOrdersRepository.findById(pcsRecords.get(i).getWoNo()).orElse(null);
                    tempWorkOrder = pcsWorkOrderData.getWoNo();
                    pcsWorkOrderData.setWoActualRtmDtTm(null);
                    logger.info("..pcsWorkOrderWithRTMDateTime >" + tempWorkOrder);
                    WorksOrders wo = worksOrdersRepository.save(pcsWorkOrderData);
                    logger.info(".....Successfully updated  pcsWorkOrderWithRTMDateTime " + wo.getWoNo());
                    if (wo != null) {
                        isPCSUpdated = true;
                    }
                } catch (Exception update) {
                    logger.warn("ERROR Cancel Event updatePCSData() update exception 1 >> " + update.getMessage());
                    logger.info("Error ## 1 updating PCS Record for Work Order >> " + tempWorkOrder + " Error Mesg >" + update.getMessage());
                }
            }
        } catch (Exception finder) {
            logger.warn("ERROR Cancel Event updatePCSData() finder exception 1 >> " + finder.getMessage());
        }

        return isPCSUpdated;
    }


    private boolean updateMetsDataRecords(
            ChangeRequest changeRequest,
            ChangeRequestHistory changeRequestHistory,
            ChangeRequestLog changeRequestLog,
            Events events) {
        boolean isMetsUpdated = false;

        try {
            java.util.Date currentDate = ServerDateHelper.getDate_UTC();
            java.sql.Timestamp incrementedClosedDateTime = new java.sql.Timestamp(currentDate.getTime() + 5000);
            if (changeRequest != null) {
                ChangeRequest superChangeRequestData = null;
                Events superConfirmationData = eventsRepository.findById(events.getEventId()).orElse(null);

                try {
                    logger.info(" Before confirming Change Request Data");
                    superChangeRequestData = changeRequestRepository.findById(changeRequest.getAcn()).orElse(null);
                    superChangeRequestData.setRequestStatus("C");
                    changeRequestRepository.save(superChangeRequestData);
                } catch (Exception changeRequestError) {
                    logger.warn("ERROR Cancel Event updateMetsDataRecords() changeRequestError exception >> " + changeRequestError.getMessage());
                    events = null;
                }

                try {
                    logger.info(" Before confirming Change Request Hisotry Data");
                    ChangeRequestHistory superChangeRequestHistoryData = changeRequestHistoryRepository.findById(changeRequestHistory.getChangeRequestHistoryPk()).orElse(null);
                    superChangeRequestHistoryData.setRequestStatus("C");
                    changeRequestHistoryRepository.save(superChangeRequestHistoryData);
                } catch (Exception changeRequestHistoryError) {
                    logger.warn("ERROR Cancel Event updateMetsDataRecords() changeRequestHistoryError exception >> " + changeRequestHistoryError.getMessage());
                }

                try {
                    logger.info(" Before confirming Change Request Log Data");
                    ChangeRequestLog superChangeRequestLogData = new ChangeRequestLog();
                    ChangeRequestLogPk superChangeRequestLogPk = new ChangeRequestLogPk();
                    superChangeRequestLogPk.setEventId(superChangeRequestData.getEventId());
                    superChangeRequestLogPk.setStatusChangedDtTm(incrementedClosedDateTime);
                    superChangeRequestLogPk.setCreatedDtTm(incrementedClosedDateTime);
                    superChangeRequestLogData.setChangeRequestLogPk(superChangeRequestLogPk);
                    superChangeRequestLogData.setNewRequestStatus("C");
                    changeRequestLog = changeRequestLogRepository.save(superChangeRequestLogData);
                } catch (Exception changeReqLog) {
                    logger.warn("ERROR Cancel Event updateMetsDataRecords() changeReqLog exception >> " + changeReqLog.getMessage());
                }

                if (events != null) {
                    try {
                        logger.info(" Before confirming Events Data");
                        superConfirmationData.setStatus(superChangeRequestData.getNewStatus());
                        superConfirmationData.setEticDateTime(superChangeRequestData.getNewEticDtTm());
                        superConfirmationData.setEticText(superChangeRequestData.getNewEticText());
                        superConfirmationData.setCurComment(superChangeRequestData.getNewComment());
                        superConfirmationData.setLastUpdateDateTime(incrementedClosedDateTime);
                        superConfirmationData.setLastUpdatedBy("SUPER");

                        eventsRepository.save(superConfirmationData);
                    } catch (Exception eventsUpdated) {
                        logger.warn("ERROR Cancel Event updateMetsDataRecords() eventsUpdated exception >> " + eventsUpdated.getMessage());
                        events = null;
                    }
                }

                if (events != null) {
                    try {
                        logger.info(" Before inserting confirmation in TF Notes table");
                        String strTFNotesString = "Confirm New Status: UP";

                        boolean tfNoteAdded = addTFNotes(superConfirmationData.getEventId(), strTFNotesString, incrementedClosedDateTime);
                        isMetsUpdated = true;
                    } catch (Exception tfNoteInserted) {
                        logger.warn("ERROR Cancel Event updateMetsDataRecords() tfNoteInserted exception >> " + tfNoteInserted.getMessage());
                        isMetsUpdated = false;
                    }
                }
            }
        } catch (Exception pk) {
            logger.warn("ERROR Cancel Event updateMetsDataRecords() pk exception >> " + pk.getMessage());
            isMetsUpdated = false;
        }

        return isMetsUpdated;
    }
    
    private boolean addTFNotes(int eventId, String tfNote, java.sql.Timestamp updatedTimeStamp) {
        boolean result = false;
        int noteId = 0;
        try {
            noteId = eventTfNotesRepository.getMaxNoteId(eventId);
            noteId = noteId + 2;
            logger.debug("after incrementing noteId====== " + noteId);
        } catch (Exception e) {
            logger.warn("ERROR Cancel Event addTFNotes() incremente note id 1 >> " + e.getMessage());
        }
        if (noteId > 0) {
            logger.info(" tfNote while inserting the record ======" + tfNote + "<<");

            EventTfNotes eventTFNotesData = new EventTfNotes();
            EventTfNotesPk eventTfNotesPk = new EventTfNotesPk();
            eventTfNotesPk.setEventId(eventId);
            eventTfNotesPk.setTfDtTm(updatedTimeStamp);
            eventTFNotesData.setEventTfNotesPk(eventTfNotesPk);
            eventTFNotesData.setEditedFlag("N");
            eventTFNotesData.setNoteId(noteId);
            eventTFNotesData.setTfNote(tfNote);
            eventTFNotesData.setNoteType(4);
            eventTFNotesData.setLastUpdateDtTm(updatedTimeStamp);

            try {
                eventTfNotesRepository.save(eventTFNotesData);
                result = true;
            } catch (Exception tfNotes) {
                logger.warn("ERROR Cancel Event addTFNotes() tfNotes #1 exception >> " + tfNotes.getMessage());
            }
        }
        return result;
    }
    
	private boolean addTFNotes(WizardEventData wizardEventData) {
		boolean result = false;
		EventTfNotes eventTFNotes = null;
		try {
			int noteId = 0;
			int activeEvents = 0;
			try {
				noteId = eventTfNotesRepository.getMaxNoteId(wizardEventData.getEventId());

				noteId = noteId + 1;
				logger.info("after incrementing noteId====== " + noteId);

				activeEvents = eventsRepository.getCountOfActiveOOSEvents(wizardEventData.getACN());

			} catch (Exception n) {
				logger.warn("ERROR Cancel Event addTFNotes() increment note id >> " + n.getMessage());
			}

			if (noteId > 0) {
				
	            EventTfNotes eventTFNotesData = new EventTfNotes();
	            EventTfNotesPk eventTfNotesPk = new EventTfNotesPk();
	            eventTfNotesPk.setEventId(wizardEventData.getEventId());
	            eventTfNotesPk.setTfDtTm(wizardEventData.getCancelledDateTime());
	            eventTFNotesData.setEventTfNotesPk(eventTfNotesPk);

	            
	            eventTFNotesData.setEmpNum(wizardEventData.getUserId());
				eventTFNotesData.setEmpName(wizardEventData.getEmployeeName());
				eventTFNotesData.setEmpDepartment(wizardEventData.getEmpDepartment());
				eventTFNotesData.setEditedFlag("N");
				eventTFNotesData.setNoteId(noteId);

				if (wizardEventData.getEventType().trim().equals("TRK") || wizardEventData.getEventType().trim().equals("NOTE")) {
					eventTFNotesData.setTfNote("Event Cancelled");
					eventTFNotesData.setChangeType(0);
					eventTFNotesData.setNoteType(5);

				} else if (wizardEventData.getEventType().trim().equals("OOS")) {

					eventTFNotesData.setChangeType(7);
					eventTFNotesData.setNoteType(3);

					if (wizardEventData.getAccessLevel().trim().equals("90")) {
						eventTFNotesData.setTfNote("Event Cancelled Posted New Status: UP");
					} else {
						eventTFNotesData.setTfNote("Event Cancelled");
					}

				} else if (wizardEventData.getEventType().trim().equals("DOA")) {
					if (activeEvents == 0) {
						eventTFNotesData.setTfNote("Event Cancelled Posted New Status: UP");
						eventTFNotesData.setNoteType(3);
						eventTFNotesData.setChangeType(7);

					} else {
						eventTFNotesData.setTfNote("Event Cancelled");
						eventTFNotesData.setNoteType(5);
						eventTFNotesData.setChangeType(0);
					}

				}

				eventTFNotesData.setLastUpdateDtTm(wizardEventData.getCancelledDateTime());

				try {
					eventTfNotesRepository.save(eventTFNotesData);
	                result = true;
	                eventTFNotes = null;
				} catch (Exception tfNotes) {
					logger.warn("ERROR Cancel Event addTFNotes() tfNotes >> " + tfNotes.getMessage());
				}

				if (wizardEventData.getTfNotesList() != null && wizardEventData.getTfNotesList().size() > 0) {
					for (int tf = 0; tf < wizardEventData.getTfNotesList().size(); tf++) {
						java.sql.Timestamp tfNotesDateTime = ServerDateHelper.getTimeStamp();

						if (tfNotesDateTime.equals(wizardEventData.getCancelledDateTime())) {
							java.util.Date currentDate = ServerDateHelper.getDate_UTC();
							tfNotesDateTime = new java.sql.Timestamp(currentDate.getTime() + 1000);
							logger.info("incremented tfNotesDateTime >>>>>>>>>>>>>> " + tfNotesDateTime);
						}

						String clientData = wizardEventData.getTfNotesList().get(tf);

                        EventTfNotes eventTFNotesData1 = new EventTfNotes();
                        EventTfNotesPk eventTfNotesPk1 = new EventTfNotesPk();
                        eventTfNotesPk1.setEventId(wizardEventData.getEventId());
                        eventTfNotesPk1.setTfDtTm(tfNotesDateTime);
                        eventTFNotesData1.setEventTfNotesPk(eventTfNotesPk1);

                        eventTFNotesData1.setEmpNum(wizardEventData.getUserId());
                        eventTFNotesData1.setEmpName(wizardEventData.getEmployeeName());
                        eventTFNotesData1.setEmpDepartment(wizardEventData.getEmpDepartment());
                        eventTFNotesData1.setEditedFlag("N");
                        eventTFNotesData1.setNoteId(noteId);
                        eventTFNotesData1.setTfNote(clientData);
                        eventTFNotesData1.setNoteType(0);

						if (wizardEventData.getEventType().trim().equals("OOS")) {
							eventTFNotesData1.setChangeType(7);
						} else if (
							wizardEventData.getEventType().trim().equals("TRK")
								|| wizardEventData.getEventType().trim().equals("NOTE")) {
							eventTFNotesData1.setChangeType(0);
						} else if (wizardEventData.getEventType().trim().equals("DOA")) {
							if (activeEvents == 0) {
								eventTFNotesData1.setChangeType(7);
							} else {
								eventTFNotesData1.setChangeType(0);
							}
						}

						eventTFNotesData1.setLastUpdateDtTm(wizardEventData.getCancelledDateTime());

						try {
                            if (eventTFNotesData1 != null) {
                                eventTFNotes = eventTfNotesRepository.save(eventTFNotesData1);
                                result = true;
                            }
							eventTFNotes = null;
						} catch (Exception tfNotes) {
							logger.warn("ERROR Cancel Event addTFNotes() tfNotes ---- >> " + tfNotes.getMessage());
						}
					}
				}
			}
		} catch (Exception notes) {
			logger.warn("ERROR Cancel Event addTFNotes() notes >> " + notes.getMessage());
		}
		return result;
	}
	
	private ChangeRequestLog generateUnreviewedChangeRequestLogData(int eventId, java.sql.Timestamp cancelledTimeStamp, java.sql.Timestamp changeRequestCreatedTimeStamp) {
		
		ChangeRequestLog changeRequestLogData = new ChangeRequestLog();
		ChangeRequestLogPk changeRequestLogDataPk = new ChangeRequestLogPk();
		changeRequestLogDataPk.setEventId(eventId);
		changeRequestLogDataPk.setStatusChangedDtTm(cancelledTimeStamp);
		changeRequestLogDataPk.setCreatedDtTm(changeRequestCreatedTimeStamp);
		changeRequestLogData.setChangeRequestLogPk(changeRequestLogDataPk);
		changeRequestLogData.setOldRequestStatus("U");
		changeRequestLogData.setNewRequestStatus("X");
		return changeRequestLogData;
	}
	
	private ChangeRequestLog generateCancelledChangeRequestLogData(int eventId, java.sql.Timestamp cancelledTimeStamp, java.sql.Timestamp changeRequestCreatedTimeStamp) {
		
		ChangeRequestLog changeRequestLogData = new ChangeRequestLog();
		ChangeRequestLogPk changeRequestLogDataPk = new ChangeRequestLogPk();
		changeRequestLogDataPk.setEventId(eventId);
		changeRequestLogDataPk.setStatusChangedDtTm(cancelledTimeStamp);
		changeRequestLogDataPk.setCreatedDtTm(changeRequestCreatedTimeStamp);
		changeRequestLogData.setChangeRequestLogPk(changeRequestLogDataPk);
		changeRequestLogData.setOldRequestStatus("S");
		changeRequestLogData.setNewRequestStatus("X");
		return changeRequestLogData;
	}
	
	private ChangeRequestLog generateSubmittedChangeRequestLogData(
			int eventId,
			java.sql.Timestamp cancelledTimeStamp,
			java.sql.Timestamp changeRequestCreatedTimeStamp) {
		ChangeRequestLog changeRequestLogData = new ChangeRequestLog();
		ChangeRequestLogPk changeRequestLogDataPk = new ChangeRequestLogPk();

		changeRequestLogDataPk.setEventId(eventId);
		changeRequestLogDataPk.setStatusChangedDtTm(cancelledTimeStamp);
		changeRequestLogDataPk.setCreatedDtTm(changeRequestCreatedTimeStamp);
		changeRequestLogData.setChangeRequestLogPk(changeRequestLogDataPk);
		changeRequestLogData.setNewRequestStatus("S");

		return changeRequestLogData;
	}
	
	private ChangeRequest generateChangeRequestData(
			WizardEventData wizardEventData,
			java.sql.Timestamp incrementedCancelledDateTime,
			java.sql.Timestamp eticDateTimeStamp) {
		ChangeRequest changeRequestData = new ChangeRequest();

		changeRequestData.setEventId(wizardEventData.getEventId());
		changeRequestData.setAcn(wizardEventData.getACN());
		changeRequestData.setCreatedDtTm(incrementedCancelledDateTime);

		changeRequestData.setOldEticDtTm(eticDateTimeStamp);

		if (wizardEventData.getEticInfo() != null) {
			if (wizardEventData.getEticInfo().startsWith("M")) {
				logger.info("= >>> ERROR Me OldEticText in table >" + wizardEventData.getEticInfo());
			}
			logger.info("= >>> OldEticText in change Request table >" + wizardEventData.getEticInfo());
			changeRequestData.setOldEticText(wizardEventData.getEticInfo().trim());
		} else
			changeRequestData.setOldEticText("");

		if (wizardEventData.getEticComment() != null)
			changeRequestData.setOldComment(wizardEventData.getEticComment().trim());
		else
			changeRequestData.setOldComment("");

		changeRequestData.setOldStatus(wizardEventData.getStatus());
		changeRequestData.setNewStatus("UP");
		changeRequestData.setRequestStatus("S");
		changeRequestData.setLastUpdateDtTm(wizardEventData.getCancelledDateTime());
		changeRequestData.setEnteredInError("N");
		changeRequestData.setChangeType(7);

		return changeRequestData;
	}
	
	private ChangeRequestHistory generateNewChangeRequestHistoryData(
			WizardEventData wizardEventData,
			java.sql.Timestamp incrementedCancelledDateTime,
			java.sql.Timestamp eticDateTimeStamp) {
		
		ChangeRequestHistory newChangeRequestHistoryData =new ChangeRequestHistory();
		
		ChangeRequestHistoryPk newchangeRequestHistoryPk = new ChangeRequestHistoryPk();

		newchangeRequestHistoryPk.setEventId(wizardEventData.getEventId());
		newchangeRequestHistoryPk.setAcn(wizardEventData.getACN());
		newchangeRequestHistoryPk.setCreatedDtTm(incrementedCancelledDateTime);
		
		newChangeRequestHistoryData.setChangeRequestHistoryPk(newchangeRequestHistoryPk);
		
		if (eticDateTimeStamp != null)
			newChangeRequestHistoryData.setOldEticDtTm(eticDateTimeStamp);

		if (wizardEventData.getEticInfo() != null) {
			if (wizardEventData.getEticInfo().startsWith("M")) {
				logger.info("= >>> ERROR Me OldEticText in History table >" + wizardEventData.getEticInfo());
			}
			logger.info("= >>> OldEticText in History table >" + wizardEventData.getEticInfo());
			newChangeRequestHistoryData.setOldEticText(wizardEventData.getEticInfo().trim());
		}

		if (wizardEventData.getEticComment() != null)
			newChangeRequestHistoryData.setOldComment(
					wizardEventData.getEticComment().trim());

		newChangeRequestHistoryData.setOldStatus(wizardEventData.getStatus());
		newChangeRequestHistoryData.setNewStatus("UP");
		newChangeRequestHistoryData.setRequestStatus("S");
		newChangeRequestHistoryData.setLastUpdateDtTm(wizardEventData.getCancelledDateTime());
		newChangeRequestHistoryData.setEnteredInError("N");
		newChangeRequestHistoryData.setChangeType(7);

		return newChangeRequestHistoryData;
	}
    
}
