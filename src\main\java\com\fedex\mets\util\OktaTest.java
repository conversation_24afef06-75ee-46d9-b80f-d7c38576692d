package com.fedex.mets.util;//package com.fedex.mets.config;


import java.net.MalformedURLException;

import com.fedex.airops.mss.auth.AuthResult;
import com.fedex.airops.mss.auth.SimpleAuthConfigurationManager;
import com.fedex.airops.mss.auth.SimpleOktaAuthentication;

public class OktaTest {

	public static void main(String[] args) throws MalformedURLException {

		SimpleAuthConfigurationManager.setAppToAppTokenCacheEnabled(true);

		SimpleAuthConfigurationManager.setClientId("0oa1i8z5tfhMGNtRm0h8"); // clientID given when you onboard to PurpleID, for
																	// example "0aughbcdefg"
		SimpleAuthConfigurationManager.setIssuer("https://purpleid-test.oktapreview.com/oauth2/aus1gipvjr85gnnGM0h8");
		// issuer given when you onboard to PurpleID, for example https://purpleid-test.oktapreview.com/oauth2/abcdefgh1234abcd"

		SimpleAuthConfigurationManager.setProxy(null);
		SimpleAuthConfigurationManager.setScope("Custom_Scope");
		// for webapps, you probably want "openid profile" and for apptoapp, you probably want "Custom_Scope"
		SimpleAuthConfigurationManager.applyToGlobalAuthenticationFacadeInstance();
		AuthResult r = SimpleOktaAuthentication.globalInstance()
				.authenticateAppToApp("9nNbxiVKh5ox8-gbLu37467YgDBz_V6Yv8SiTraF");


		if (r.hasError()) {
			// authentication/misc error
			Throwable e = r.getError();
			String msg = r.getErrorMessage();
		} else {
			// if you want DSS specific logic
			boolean usedDss = r.usedDss();

			String accessToken = r.getAccessToken(); // this will be the Okta Access Token JWT OR the MSS token from DSS
			// there will never be a client ID token in an App To App scenario
			System.out.println(accessToken);
		}

	}

}