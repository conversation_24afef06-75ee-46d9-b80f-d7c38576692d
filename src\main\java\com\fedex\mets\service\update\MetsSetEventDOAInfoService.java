package com.fedex.mets.service.update;

import com.fedex.mets.data.DOAData;
import com.fedex.mets.data.DOADiscrepancyData;
import com.fedex.mets.data.DOADataEntity;
import com.fedex.mets.data.MetsEventUpdateEntity;
import com.fedex.mets.repository.EventDOAUpdateRepository;
import com.fedex.mets.service.retrieval.EventDoaService;
import com.fedex.mets.util.IServerConstants;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class MetsSetEventDOAInfoService {

    private static final Logger logger = LoggerFactory.getLogger(MetsSetEventDOAInfoService.class);
    //	@Autowired
    private EventDOAUpdateRepository eventdoaupdaterepository;

    @Autowired
    private EventDoaService eventdoaservice;
    @Autowired
    private EventDiscrepanciesUpdateService eventDiscrepanciesUpdateService;

    public Map<String, Object> setEventDOAInfo(MetsEventUpdateEntity request, Map<String, Object> hashMap,
                                               DOADataEntity doaData, String flag,String userId, Boolean eventActive) throws Exception {

        DOAData elements = new DOAData();
        boolean resultFromBean = false, validFlight = false, isEventActive = true;

        if (eventActive != null) {
            isEventActive = eventActive.booleanValue();
        }

        int eventId = doaData.getEventId(), lastUpdatedRecords = 0;

        try {

            lastUpdatedRecords = eventdoaupdaterepository.findLastUpdated(doaData);

        } catch (Exception e) {
            logger.warn("ERROR MetsUpdate Service setEventDOAInfo() e >> " + e.getMessage());
        }

        if (lastUpdatedRecords == 0) {
            if (flag.equalsIgnoreCase(IServerConstants.EDIT)) {

                try {
                    validFlight = eventdoaupdaterepository.validateFlight(doaData);
                } catch (Exception valid) {
                    logger.warn("ERROR MetsUpdate Service setEventDOAInfo() valid >> " + valid.getMessage());
                }

                logger.info("is valid flight for DOA_UPDATE----------" + validFlight);

                if (validFlight) {

                    resultFromBean = eventdoaupdaterepository.addEventDOAData(doaData,  isEventActive);

                } else {

                    logger.warn("ERROR The Flight details(Flight Date, Flight Leg & Flight Number) entered are not valid Error returned to Client");
                    hashMap.put(IServerConstants.ERROR, "The Flight details(Flight Date, Flight Leg & Flight Number) entered are not valid");

                }

            } else if (flag.equalsIgnoreCase(IServerConstants.DELETE)) {

                resultFromBean = eventdoaupdaterepository.deleteEventDOAData(doaData);

            } else if (flag.equalsIgnoreCase(IServerConstants.CANCEL)) {

                resultFromBean = eventdoaupdaterepository.cancelEventDOAData(doaData);

            } else if (flag.equalsIgnoreCase(IServerConstants.CLOSE)) {

                resultFromBean = eventdoaupdaterepository.closeEventDOAData(doaData);
            }

        } else {
            hashMap.put(IServerConstants.ERROR, "Could not Alter the data as the Record has been modified prior to this transaction.");
        }

        if (resultFromBean) {
            try {

                String strEventId = "" + eventId;
                elements = eventdoaservice.getEventDOAInfo(strEventId,userId);

            } catch (Exception exec) {

                logger.warn("ERROR MetsUpdate Service setEventDOAInfo() exec2 >> " + exec.getMessage());
                hashMap.put(IServerConstants.ERROR, exec.getMessage());

            }

            /******************Before publishing the Update******************/
            try {

                logger.info("Before publishing the DOA_UPDATE Update on JMS ");
                boolean isMessagePublished = publishUpdate(eventId, IServerConstants.DOA_UPDATE);
                logger.info("isMessagePublished >> " + isMessagePublished);

            } catch (Exception e) {
                logger.warn("ERROR MetsUpdate Service setEventDOAInfo() publish >> " + e.getMessage());
            }
        }

        DOAData data = elements;

        List<DOADiscrepancyData> vec = data.getDiscVector();

        logger.info("===>" + data.getEventId() + "--" + data.getDoaOriginator()
                + "--" + data.getFlightNumber() + "--" + data.getFlightDate()
                + "--" + data.getFlightLegNumber() + "--" + data.getAdditionalDescription());

        for (int j = 0; j < vec.size(); j++) {
            DOADiscrepancyData discData = (DOADiscrepancyData) vec.get(j);

            logger.info("= discrepany details >" + discData.getAta()
                    + " -- " + discData.getDiscrepancy() + " -- " + discData.getDiscrepancyText());
        }

//		}

        hashMap.put(IServerConstants.DOA_DETAILS, elements);

        return hashMap;
    }

    private boolean publishUpdate(int eventId, String updateType) throws Exception {
        boolean isMessagePublished = false;
        try {

            logger.info("Before publishing the Update on JMS");
            eventDiscrepanciesUpdateService.publishEventUpdate(eventId, updateType);
            isMessagePublished = true;

        } catch (Exception publishException) {
            logger.warn("ERROR MetsUpdate Servlet editNIWTimerDetails() publish >> " + publishException.getMessage());
        }
        return isMessagePublished;
    }


}
