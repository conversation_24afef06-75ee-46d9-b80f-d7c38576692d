
package com.fedex.mets.wsdl.discrepancy;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import jakarta.activation.DataHandler;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttachmentRef;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for AcnDiscrepMaintUpdtRef complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="AcnDiscrepMaintUpdtRef">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="AcnDiscrepancyOid" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="AcnDiscrepMaintUpdtRefOid" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="ActiveFlg" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="AddProgramNm" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="AddSourceSysCd" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="AddTm" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="AddUserId" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="IntentCd" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="MediaTypeCd" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ReferenceTypeCd" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ReferenceUrl" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="fileName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="UpdateProgramNm" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="UpdateSourceSysCd" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="UpdateTm" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/>
 *         &lt;element name="UpdateUserId" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="attachment" type="{http://ws-i.org/profiles/basic/1.1/xsd}swaRef"/>
 *         &lt;element name="dscrpMaintUpdtRefTags" type="{http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd}dscrpMaintUpdtRefTag" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AcnDiscrepMaintUpdtRef", propOrder = {
    "acnDiscrepancyOid",
    "acnDiscrepMaintUpdtRefOid",
    "activeFlg",
    "addProgramNm",
    "addSourceSysCd",
    "addTm",
    "addUserId",
    "intentCd",
    "mediaTypeCd",
    "referenceTypeCd",
    "referenceUrl",
    "fileName",
    "updateProgramNm",
    "updateSourceSysCd",
    "updateTm",
    "updateUserId",
    "attachment",
    "dscrpMaintUpdtRefTags"
})
public class AcnDiscrepMaintUpdtRef {

    @XmlElement(name = "AcnDiscrepancyOid")
    protected BigDecimal acnDiscrepancyOid;
    @XmlElement(name = "AcnDiscrepMaintUpdtRefOid")
    protected BigDecimal acnDiscrepMaintUpdtRefOid;
    @XmlElement(name = "ActiveFlg")
    protected String activeFlg;
    @XmlElement(name = "AddProgramNm")
    protected String addProgramNm;
    @XmlElement(name = "AddSourceSysCd")
    protected String addSourceSysCd;
    @XmlElement(name = "AddTm")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar addTm;
    @XmlElement(name = "AddUserId")
    protected BigDecimal addUserId;
    @XmlElement(name = "IntentCd")
    protected String intentCd;
    @XmlElement(name = "MediaTypeCd")
    protected String mediaTypeCd;
    @XmlElement(name = "ReferenceTypeCd")
    protected String referenceTypeCd;
    @XmlElement(name = "ReferenceUrl")
    protected String referenceUrl;
    protected String fileName;
    @XmlElement(name = "UpdateProgramNm")
    protected String updateProgramNm;
    @XmlElement(name = "UpdateSourceSysCd")
    protected String updateSourceSysCd;
    @XmlElement(name = "UpdateTm")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar updateTm;
    @XmlElement(name = "UpdateUserId")
    protected BigDecimal updateUserId;
    @XmlElement(required = true, type = String.class)
    @XmlAttachmentRef
    protected DataHandler attachment;
    protected List<DscrpMaintUpdtRefTag> dscrpMaintUpdtRefTags;

    /**
     * Gets the value of the acnDiscrepancyOid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAcnDiscrepancyOid() {
        return acnDiscrepancyOid;
    }

    /**
     * Sets the value of the acnDiscrepancyOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAcnDiscrepancyOid(BigDecimal value) {
        this.acnDiscrepancyOid = value;
    }

    /**
     * Gets the value of the acnDiscrepMaintUpdtRefOid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAcnDiscrepMaintUpdtRefOid() {
        return acnDiscrepMaintUpdtRefOid;
    }

    /**
     * Sets the value of the acnDiscrepMaintUpdtRefOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAcnDiscrepMaintUpdtRefOid(BigDecimal value) {
        this.acnDiscrepMaintUpdtRefOid = value;
    }

    /**
     * Gets the value of the activeFlg property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getActiveFlg() {
        return activeFlg;
    }

    /**
     * Sets the value of the activeFlg property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setActiveFlg(String value) {
        this.activeFlg = value;
    }

    /**
     * Gets the value of the addProgramNm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAddProgramNm() {
        return addProgramNm;
    }

    /**
     * Sets the value of the addProgramNm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAddProgramNm(String value) {
        this.addProgramNm = value;
    }

    /**
     * Gets the value of the addSourceSysCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAddSourceSysCd() {
        return addSourceSysCd;
    }

    /**
     * Sets the value of the addSourceSysCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAddSourceSysCd(String value) {
        this.addSourceSysCd = value;
    }

    /**
     * Gets the value of the addTm property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getAddTm() {
        return addTm;
    }

    /**
     * Sets the value of the addTm property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setAddTm(XMLGregorianCalendar value) {
        this.addTm = value;
    }

    /**
     * Gets the value of the addUserId property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAddUserId() {
        return addUserId;
    }

    /**
     * Sets the value of the addUserId property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAddUserId(BigDecimal value) {
        this.addUserId = value;
    }

    /**
     * Gets the value of the intentCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIntentCd() {
        return intentCd;
    }

    /**
     * Sets the value of the intentCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIntentCd(String value) {
        this.intentCd = value;
    }

    /**
     * Gets the value of the mediaTypeCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMediaTypeCd() {
        return mediaTypeCd;
    }

    /**
     * Sets the value of the mediaTypeCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMediaTypeCd(String value) {
        this.mediaTypeCd = value;
    }

    /**
     * Gets the value of the referenceTypeCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReferenceTypeCd() {
        return referenceTypeCd;
    }

    /**
     * Sets the value of the referenceTypeCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReferenceTypeCd(String value) {
        this.referenceTypeCd = value;
    }

    /**
     * Gets the value of the referenceUrl property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReferenceUrl() {
        return referenceUrl;
    }

    /**
     * Sets the value of the referenceUrl property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReferenceUrl(String value) {
        this.referenceUrl = value;
    }

    /**
     * Gets the value of the fileName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFileName() {
        return fileName;
    }

    /**
     * Sets the value of the fileName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFileName(String value) {
        this.fileName = value;
    }

    /**
     * Gets the value of the updateProgramNm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUpdateProgramNm() {
        return updateProgramNm;
    }

    /**
     * Sets the value of the updateProgramNm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUpdateProgramNm(String value) {
        this.updateProgramNm = value;
    }

    /**
     * Gets the value of the updateSourceSysCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUpdateSourceSysCd() {
        return updateSourceSysCd;
    }

    /**
     * Sets the value of the updateSourceSysCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUpdateSourceSysCd(String value) {
        this.updateSourceSysCd = value;
    }

    /**
     * Gets the value of the updateTm property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getUpdateTm() {
        return updateTm;
    }

    /**
     * Sets the value of the updateTm property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setUpdateTm(XMLGregorianCalendar value) {
        this.updateTm = value;
    }

    /**
     * Gets the value of the updateUserId property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getUpdateUserId() {
        return updateUserId;
    }

    /**
     * Sets the value of the updateUserId property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setUpdateUserId(BigDecimal value) {
        this.updateUserId = value;
    }

    /**
     * Gets the value of the attachment property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public DataHandler getAttachment() {
        return attachment;
    }

    /**
     * Sets the value of the attachment property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAttachment(DataHandler value) {
        this.attachment = value;
    }

    /**
     * Gets the value of the dscrpMaintUpdtRefTags property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the dscrpMaintUpdtRefTags property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getDscrpMaintUpdtRefTags().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link DscrpMaintUpdtRefTag }
     * 
     * 
     */
    public List<DscrpMaintUpdtRefTag> getDscrpMaintUpdtRefTags() {
        if (dscrpMaintUpdtRefTags == null) {
            dscrpMaintUpdtRefTags = new ArrayList<DscrpMaintUpdtRefTag>();
        }
        return this.dscrpMaintUpdtRefTags;
    }

}
