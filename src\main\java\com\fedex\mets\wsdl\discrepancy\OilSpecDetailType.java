
package com.fedex.mets.wsdl.discrepancy;

import jakarta.xml.bind.annotation.XmlEnum;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for OilSpecDetailType.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="OilSpecDetailType">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="INTERIM_OIL"/>
 *     &lt;enumeration value="FINAL_OIL"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 * 
 */
@XmlType(name = "OilSpecDetailType", namespace = "http://www.fedex.com/airops/schemas/EnumTypes.xsd")
@XmlEnum
public enum OilSpecDetailType {

    INTERIM_OIL,
    FINAL_OIL;

    public String value() {
        return name();
    }

    public static OilSpecDetailType fromValue(String v) {
        return valueOf(v);
    }

}
