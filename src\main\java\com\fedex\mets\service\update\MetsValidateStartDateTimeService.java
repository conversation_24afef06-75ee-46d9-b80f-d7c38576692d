package com.fedex.mets.service.update;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.fedex.mets.data.DetailViewDataEntity;
import com.fedex.mets.data.MetsEventUpdateEntity;

@Service
public class MetsValidateStartDateTimeService {
	
	public Map<String, Object> validateStartDateTime(MetsEventUpdateEntity request, Map<String, Object> hashMap, 
			DetailViewDataEntity detailViewData, String acn, String strStartDateTime, String userId, String tokenId) throws Exception {
		
		return null;
	}

}
