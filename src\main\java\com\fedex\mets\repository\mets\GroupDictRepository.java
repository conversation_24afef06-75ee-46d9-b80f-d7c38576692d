package com.fedex.mets.repository.mets;

import com.fedex.mets.entity.mets.GroupDict;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface GroupDictRepository extends JpaRepository<GroupDict,Integer> {

    @Query(value="Select Group_Id from Group_Dict where Access_Level=:accessLevel",nativeQuery = true)
    public String getGroupId(@Param("accessLevel") String accessLevel);
    
    @Query(value="Select GROUP_TITLE from Group_Dict where Access_Level=:accessLevel",nativeQuery = true)
    public String getGroupTitleByAccessLevel(@Param("accessLevel") String accessLevel);

    @Query(value="select GROUP_TITLE from Group_dict where group_id=:acOwner",nativeQuery = true)
    public String getGroupTitle(@Param("acOwner") String acOwner);

    @Query(value = "select GROUP_ID from GROUP_DICT where GROUP_TITLE=:GroupTitle",nativeQuery = true)
    String getGroupIdByGroupTitle(@Param("GroupTitle") String GroupTitle);

    @Query(value = "select group_title from group_dict where AC_owner='Y'",nativeQuery = true)
    List<String> getGroupTitleByACOwner();
}
