package com.fedex.mets.repository.rampview;

import com.fedex.mets.entity.rampview.RvRegions;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RvRegionsRepository extends JpaRepository<RvRegions, String> {

    @Query(value="SELECT DISTINCT DESCRIPTION FROM RV_REGIONS ORDER BY DESCRIPTION ASC",nativeQuery = true)
    public List<String> getRegions();


    @Query("SELECT DISTINCT s.stationCd FROM RvRegionStationFull s\n" +
            "Left Join RvRegions r on r.regionId = s.regionId \n" +
            "WHERE r.description =:regionDescription")
    public List<String> getRegionStations(@Param("regionDescription") String regionDescription);
}
