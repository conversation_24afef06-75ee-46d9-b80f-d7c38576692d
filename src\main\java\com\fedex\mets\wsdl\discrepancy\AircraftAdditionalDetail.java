
package com.fedex.mets.wsdl.discrepancy;

import java.math.BigDecimal;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for aircraftAdditionalDetail complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="aircraftAdditionalDetail">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="nbrAutoPilots" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="autoPilot1AStatus" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="autoPilot1ADate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="autoPilot1AAtaNbr" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="autoPilot1ADscrpNbr" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="autoPilot2BStatus" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="autoPilot2BDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="autoPilot2BAtaNbr" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="autoPilot2BDscrpNbr" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="autoPilot3CStatus" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="autoPilot3CDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="autoPilot3CAtaNbr" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="autoPilot3CDscrpNbr" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="svcChk2Date" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="svcChk2Station" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="svcChk2SpecDays" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="svcChk2EntId" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="svcChk2RptId" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="svcChk1Date" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="svcChk1Station" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="svcChk1EntId" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="svcChk1RptId" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="tireChkTime" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="tireChkExpFltDt" type="{http://www.w3.org/2001/XMLSchema}date"/>
 *         &lt;element name="tireChkExpFltLeg" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="tireChkExpFltNbr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="tireChkExpStation" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="tireChkStation" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="tireChkEntId" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="tireChkRptId" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="apuInFlightDueDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="maxPowerDueDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="melBookCode" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="lastPDSCDate" type="{http://www.w3.org/2001/XMLSchema}date"/>
 *         &lt;element name="lastPDSCtime" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="lastPdscStation" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="lastPdscEntById" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="lastPdscRptById" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="pdscExpirationDt" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="etopsRestrictedInd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="etopsMaxMinutes" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="verificationFlightInd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="tspOnBoardInd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="serviceCheckDueDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="tirePressureCheckDueDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="llmMaxStatusCd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="apuStatus" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ardStatus" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="secChkTime" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="secChkStation" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="secChkEntId" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="secChkRptId" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="hostName" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="earliestCheckDueDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="checkDueType" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "aircraftAdditionalDetail", propOrder = {
    "nbrAutoPilots",
    "autoPilot1AStatus",
    "autoPilot1ADate",
    "autoPilot1AAtaNbr",
    "autoPilot1ADscrpNbr",
    "autoPilot2BStatus",
    "autoPilot2BDate",
    "autoPilot2BAtaNbr",
    "autoPilot2BDscrpNbr",
    "autoPilot3CStatus",
    "autoPilot3CDate",
    "autoPilot3CAtaNbr",
    "autoPilot3CDscrpNbr",
    "svcChk2Date",
    "svcChk2Station",
    "svcChk2SpecDays",
    "svcChk2EntId",
    "svcChk2RptId",
    "svcChk1Date",
    "svcChk1Station",
    "svcChk1EntId",
    "svcChk1RptId",
    "tireChkTime",
    "tireChkExpFltDt",
    "tireChkExpFltLeg",
    "tireChkExpFltNbr",
    "tireChkExpStation",
    "tireChkStation",
    "tireChkEntId",
    "tireChkRptId",
    "apuInFlightDueDate",
    "maxPowerDueDate",
    "melBookCode",
    "lastPDSCDate",
    "lastPDSCtime",
    "lastPdscStation",
    "lastPdscEntById",
    "lastPdscRptById",
    "pdscExpirationDt",
    "etopsRestrictedInd",
    "etopsMaxMinutes",
    "verificationFlightInd",
    "tspOnBoardInd",
    "serviceCheckDueDate",
    "tirePressureCheckDueDate",
    "llmMaxStatusCd",
    "apuStatus",
    "ardStatus",
    "secChkTime",
    "secChkStation",
    "secChkEntId",
    "secChkRptId",
    "hostName",
    "earliestCheckDueDate",
    "checkDueType"
})
public class AircraftAdditionalDetail {

    @XmlElement(required = true)
    protected BigDecimal nbrAutoPilots;
    @XmlElement(required = true)
    protected String autoPilot1AStatus;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar autoPilot1ADate;
    @XmlElement(required = true)
    protected BigDecimal autoPilot1AAtaNbr;
    @XmlElement(required = true)
    protected BigDecimal autoPilot1ADscrpNbr;
    @XmlElement(required = true)
    protected String autoPilot2BStatus;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar autoPilot2BDate;
    @XmlElement(required = true)
    protected BigDecimal autoPilot2BAtaNbr;
    @XmlElement(required = true)
    protected BigDecimal autoPilot2BDscrpNbr;
    @XmlElement(required = true)
    protected String autoPilot3CStatus;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar autoPilot3CDate;
    @XmlElement(required = true)
    protected BigDecimal autoPilot3CAtaNbr;
    @XmlElement(required = true)
    protected BigDecimal autoPilot3CDscrpNbr;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar svcChk2Date;
    @XmlElement(required = true)
    protected String svcChk2Station;
    @XmlElement(required = true)
    protected BigDecimal svcChk2SpecDays;
    @XmlElement(required = true)
    protected BigDecimal svcChk2EntId;
    @XmlElement(required = true)
    protected BigDecimal svcChk2RptId;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar svcChk1Date;
    @XmlElement(required = true)
    protected String svcChk1Station;
    @XmlElement(required = true)
    protected BigDecimal svcChk1EntId;
    @XmlElement(required = true)
    protected BigDecimal svcChk1RptId;
    @XmlElement(required = true)
    protected BigDecimal tireChkTime;
    @XmlElement(required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar tireChkExpFltDt;
    @XmlElement(required = true)
    protected BigDecimal tireChkExpFltLeg;
    @XmlElement(required = true)
    protected String tireChkExpFltNbr;
    @XmlElement(required = true)
    protected String tireChkExpStation;
    @XmlElement(required = true)
    protected String tireChkStation;
    @XmlElement(required = true)
    protected BigDecimal tireChkEntId;
    @XmlElement(required = true)
    protected BigDecimal tireChkRptId;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar apuInFlightDueDate;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar maxPowerDueDate;
    @XmlElement(required = true)
    protected String melBookCode;
    @XmlElement(required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar lastPDSCDate;
    @XmlElement(required = true)
    protected BigDecimal lastPDSCtime;
    @XmlElement(required = true)
    protected String lastPdscStation;
    @XmlElement(required = true)
    protected BigDecimal lastPdscEntById;
    @XmlElement(required = true)
    protected BigDecimal lastPdscRptById;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar pdscExpirationDt;
    @XmlElement(required = true)
    protected String etopsRestrictedInd;
    @XmlElement(required = true)
    protected BigDecimal etopsMaxMinutes;
    @XmlElement(required = true)
    protected String verificationFlightInd;
    @XmlElement(required = true)
    protected String tspOnBoardInd;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar serviceCheckDueDate;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar tirePressureCheckDueDate;
    @XmlElement(required = true)
    protected String llmMaxStatusCd;
    @XmlElement(required = true)
    protected String apuStatus;
    @XmlElement(required = true)
    protected String ardStatus;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar secChkTime;
    @XmlElement(required = true)
    protected String secChkStation;
    @XmlElement(required = true)
    protected BigDecimal secChkEntId;
    @XmlElement(required = true)
    protected BigDecimal secChkRptId;
    @XmlElement(required = true)
    protected String hostName;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar earliestCheckDueDate;
    @XmlElement(required = true)
    protected String checkDueType;

    /**
     * Gets the value of the nbrAutoPilots property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getNbrAutoPilots() {
        return nbrAutoPilots;
    }

    /**
     * Sets the value of the nbrAutoPilots property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setNbrAutoPilots(BigDecimal value) {
        this.nbrAutoPilots = value;
    }

    /**
     * Gets the value of the autoPilot1AStatus property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAutoPilot1AStatus() {
        return autoPilot1AStatus;
    }

    /**
     * Sets the value of the autoPilot1AStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAutoPilot1AStatus(String value) {
        this.autoPilot1AStatus = value;
    }

    /**
     * Gets the value of the autoPilot1ADate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getAutoPilot1ADate() {
        return autoPilot1ADate;
    }

    /**
     * Sets the value of the autoPilot1ADate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setAutoPilot1ADate(XMLGregorianCalendar value) {
        this.autoPilot1ADate = value;
    }

    /**
     * Gets the value of the autoPilot1AAtaNbr property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAutoPilot1AAtaNbr() {
        return autoPilot1AAtaNbr;
    }

    /**
     * Sets the value of the autoPilot1AAtaNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAutoPilot1AAtaNbr(BigDecimal value) {
        this.autoPilot1AAtaNbr = value;
    }

    /**
     * Gets the value of the autoPilot1ADscrpNbr property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAutoPilot1ADscrpNbr() {
        return autoPilot1ADscrpNbr;
    }

    /**
     * Sets the value of the autoPilot1ADscrpNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAutoPilot1ADscrpNbr(BigDecimal value) {
        this.autoPilot1ADscrpNbr = value;
    }

    /**
     * Gets the value of the autoPilot2BStatus property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAutoPilot2BStatus() {
        return autoPilot2BStatus;
    }

    /**
     * Sets the value of the autoPilot2BStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAutoPilot2BStatus(String value) {
        this.autoPilot2BStatus = value;
    }

    /**
     * Gets the value of the autoPilot2BDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getAutoPilot2BDate() {
        return autoPilot2BDate;
    }

    /**
     * Sets the value of the autoPilot2BDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setAutoPilot2BDate(XMLGregorianCalendar value) {
        this.autoPilot2BDate = value;
    }

    /**
     * Gets the value of the autoPilot2BAtaNbr property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAutoPilot2BAtaNbr() {
        return autoPilot2BAtaNbr;
    }

    /**
     * Sets the value of the autoPilot2BAtaNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAutoPilot2BAtaNbr(BigDecimal value) {
        this.autoPilot2BAtaNbr = value;
    }

    /**
     * Gets the value of the autoPilot2BDscrpNbr property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAutoPilot2BDscrpNbr() {
        return autoPilot2BDscrpNbr;
    }

    /**
     * Sets the value of the autoPilot2BDscrpNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAutoPilot2BDscrpNbr(BigDecimal value) {
        this.autoPilot2BDscrpNbr = value;
    }

    /**
     * Gets the value of the autoPilot3CStatus property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAutoPilot3CStatus() {
        return autoPilot3CStatus;
    }

    /**
     * Sets the value of the autoPilot3CStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAutoPilot3CStatus(String value) {
        this.autoPilot3CStatus = value;
    }

    /**
     * Gets the value of the autoPilot3CDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getAutoPilot3CDate() {
        return autoPilot3CDate;
    }

    /**
     * Sets the value of the autoPilot3CDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setAutoPilot3CDate(XMLGregorianCalendar value) {
        this.autoPilot3CDate = value;
    }

    /**
     * Gets the value of the autoPilot3CAtaNbr property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAutoPilot3CAtaNbr() {
        return autoPilot3CAtaNbr;
    }

    /**
     * Sets the value of the autoPilot3CAtaNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAutoPilot3CAtaNbr(BigDecimal value) {
        this.autoPilot3CAtaNbr = value;
    }

    /**
     * Gets the value of the autoPilot3CDscrpNbr property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAutoPilot3CDscrpNbr() {
        return autoPilot3CDscrpNbr;
    }

    /**
     * Sets the value of the autoPilot3CDscrpNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAutoPilot3CDscrpNbr(BigDecimal value) {
        this.autoPilot3CDscrpNbr = value;
    }

    /**
     * Gets the value of the svcChk2Date property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getSvcChk2Date() {
        return svcChk2Date;
    }

    /**
     * Sets the value of the svcChk2Date property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setSvcChk2Date(XMLGregorianCalendar value) {
        this.svcChk2Date = value;
    }

    /**
     * Gets the value of the svcChk2Station property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSvcChk2Station() {
        return svcChk2Station;
    }

    /**
     * Sets the value of the svcChk2Station property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSvcChk2Station(String value) {
        this.svcChk2Station = value;
    }

    /**
     * Gets the value of the svcChk2SpecDays property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getSvcChk2SpecDays() {
        return svcChk2SpecDays;
    }

    /**
     * Sets the value of the svcChk2SpecDays property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setSvcChk2SpecDays(BigDecimal value) {
        this.svcChk2SpecDays = value;
    }

    /**
     * Gets the value of the svcChk2EntId property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getSvcChk2EntId() {
        return svcChk2EntId;
    }

    /**
     * Sets the value of the svcChk2EntId property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setSvcChk2EntId(BigDecimal value) {
        this.svcChk2EntId = value;
    }

    /**
     * Gets the value of the svcChk2RptId property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getSvcChk2RptId() {
        return svcChk2RptId;
    }

    /**
     * Sets the value of the svcChk2RptId property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setSvcChk2RptId(BigDecimal value) {
        this.svcChk2RptId = value;
    }

    /**
     * Gets the value of the svcChk1Date property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getSvcChk1Date() {
        return svcChk1Date;
    }

    /**
     * Sets the value of the svcChk1Date property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setSvcChk1Date(XMLGregorianCalendar value) {
        this.svcChk1Date = value;
    }

    /**
     * Gets the value of the svcChk1Station property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSvcChk1Station() {
        return svcChk1Station;
    }

    /**
     * Sets the value of the svcChk1Station property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSvcChk1Station(String value) {
        this.svcChk1Station = value;
    }

    /**
     * Gets the value of the svcChk1EntId property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getSvcChk1EntId() {
        return svcChk1EntId;
    }

    /**
     * Sets the value of the svcChk1EntId property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setSvcChk1EntId(BigDecimal value) {
        this.svcChk1EntId = value;
    }

    /**
     * Gets the value of the svcChk1RptId property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getSvcChk1RptId() {
        return svcChk1RptId;
    }

    /**
     * Sets the value of the svcChk1RptId property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setSvcChk1RptId(BigDecimal value) {
        this.svcChk1RptId = value;
    }

    /**
     * Gets the value of the tireChkTime property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTireChkTime() {
        return tireChkTime;
    }

    /**
     * Sets the value of the tireChkTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTireChkTime(BigDecimal value) {
        this.tireChkTime = value;
    }

    /**
     * Gets the value of the tireChkExpFltDt property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getTireChkExpFltDt() {
        return tireChkExpFltDt;
    }

    /**
     * Sets the value of the tireChkExpFltDt property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setTireChkExpFltDt(XMLGregorianCalendar value) {
        this.tireChkExpFltDt = value;
    }

    /**
     * Gets the value of the tireChkExpFltLeg property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTireChkExpFltLeg() {
        return tireChkExpFltLeg;
    }

    /**
     * Sets the value of the tireChkExpFltLeg property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTireChkExpFltLeg(BigDecimal value) {
        this.tireChkExpFltLeg = value;
    }

    /**
     * Gets the value of the tireChkExpFltNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTireChkExpFltNbr() {
        return tireChkExpFltNbr;
    }

    /**
     * Sets the value of the tireChkExpFltNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTireChkExpFltNbr(String value) {
        this.tireChkExpFltNbr = value;
    }

    /**
     * Gets the value of the tireChkExpStation property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTireChkExpStation() {
        return tireChkExpStation;
    }

    /**
     * Sets the value of the tireChkExpStation property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTireChkExpStation(String value) {
        this.tireChkExpStation = value;
    }

    /**
     * Gets the value of the tireChkStation property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTireChkStation() {
        return tireChkStation;
    }

    /**
     * Sets the value of the tireChkStation property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTireChkStation(String value) {
        this.tireChkStation = value;
    }

    /**
     * Gets the value of the tireChkEntId property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTireChkEntId() {
        return tireChkEntId;
    }

    /**
     * Sets the value of the tireChkEntId property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTireChkEntId(BigDecimal value) {
        this.tireChkEntId = value;
    }

    /**
     * Gets the value of the tireChkRptId property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTireChkRptId() {
        return tireChkRptId;
    }

    /**
     * Sets the value of the tireChkRptId property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTireChkRptId(BigDecimal value) {
        this.tireChkRptId = value;
    }

    /**
     * Gets the value of the apuInFlightDueDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getApuInFlightDueDate() {
        return apuInFlightDueDate;
    }

    /**
     * Sets the value of the apuInFlightDueDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setApuInFlightDueDate(XMLGregorianCalendar value) {
        this.apuInFlightDueDate = value;
    }

    /**
     * Gets the value of the maxPowerDueDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getMaxPowerDueDate() {
        return maxPowerDueDate;
    }

    /**
     * Sets the value of the maxPowerDueDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setMaxPowerDueDate(XMLGregorianCalendar value) {
        this.maxPowerDueDate = value;
    }

    /**
     * Gets the value of the melBookCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMelBookCode() {
        return melBookCode;
    }

    /**
     * Sets the value of the melBookCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMelBookCode(String value) {
        this.melBookCode = value;
    }

    /**
     * Gets the value of the lastPDSCDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLastPDSCDate() {
        return lastPDSCDate;
    }

    /**
     * Sets the value of the lastPDSCDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLastPDSCDate(XMLGregorianCalendar value) {
        this.lastPDSCDate = value;
    }

    /**
     * Gets the value of the lastPDSCtime property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getLastPDSCtime() {
        return lastPDSCtime;
    }

    /**
     * Sets the value of the lastPDSCtime property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setLastPDSCtime(BigDecimal value) {
        this.lastPDSCtime = value;
    }

    /**
     * Gets the value of the lastPdscStation property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLastPdscStation() {
        return lastPdscStation;
    }

    /**
     * Sets the value of the lastPdscStation property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLastPdscStation(String value) {
        this.lastPdscStation = value;
    }

    /**
     * Gets the value of the lastPdscEntById property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getLastPdscEntById() {
        return lastPdscEntById;
    }

    /**
     * Sets the value of the lastPdscEntById property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setLastPdscEntById(BigDecimal value) {
        this.lastPdscEntById = value;
    }

    /**
     * Gets the value of the lastPdscRptById property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getLastPdscRptById() {
        return lastPdscRptById;
    }

    /**
     * Sets the value of the lastPdscRptById property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setLastPdscRptById(BigDecimal value) {
        this.lastPdscRptById = value;
    }

    /**
     * Gets the value of the pdscExpirationDt property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getPdscExpirationDt() {
        return pdscExpirationDt;
    }

    /**
     * Sets the value of the pdscExpirationDt property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setPdscExpirationDt(XMLGregorianCalendar value) {
        this.pdscExpirationDt = value;
    }

    /**
     * Gets the value of the etopsRestrictedInd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEtopsRestrictedInd() {
        return etopsRestrictedInd;
    }

    /**
     * Sets the value of the etopsRestrictedInd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEtopsRestrictedInd(String value) {
        this.etopsRestrictedInd = value;
    }

    /**
     * Gets the value of the etopsMaxMinutes property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getEtopsMaxMinutes() {
        return etopsMaxMinutes;
    }

    /**
     * Sets the value of the etopsMaxMinutes property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setEtopsMaxMinutes(BigDecimal value) {
        this.etopsMaxMinutes = value;
    }

    /**
     * Gets the value of the verificationFlightInd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getVerificationFlightInd() {
        return verificationFlightInd;
    }

    /**
     * Sets the value of the verificationFlightInd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setVerificationFlightInd(String value) {
        this.verificationFlightInd = value;
    }

    /**
     * Gets the value of the tspOnBoardInd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTspOnBoardInd() {
        return tspOnBoardInd;
    }

    /**
     * Sets the value of the tspOnBoardInd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTspOnBoardInd(String value) {
        this.tspOnBoardInd = value;
    }

    /**
     * Gets the value of the serviceCheckDueDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getServiceCheckDueDate() {
        return serviceCheckDueDate;
    }

    /**
     * Sets the value of the serviceCheckDueDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setServiceCheckDueDate(XMLGregorianCalendar value) {
        this.serviceCheckDueDate = value;
    }

    /**
     * Gets the value of the tirePressureCheckDueDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getTirePressureCheckDueDate() {
        return tirePressureCheckDueDate;
    }

    /**
     * Sets the value of the tirePressureCheckDueDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setTirePressureCheckDueDate(XMLGregorianCalendar value) {
        this.tirePressureCheckDueDate = value;
    }

    /**
     * Gets the value of the llmMaxStatusCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLlmMaxStatusCd() {
        return llmMaxStatusCd;
    }

    /**
     * Sets the value of the llmMaxStatusCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLlmMaxStatusCd(String value) {
        this.llmMaxStatusCd = value;
    }

    /**
     * Gets the value of the apuStatus property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getApuStatus() {
        return apuStatus;
    }

    /**
     * Sets the value of the apuStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setApuStatus(String value) {
        this.apuStatus = value;
    }

    /**
     * Gets the value of the ardStatus property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getArdStatus() {
        return ardStatus;
    }

    /**
     * Sets the value of the ardStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setArdStatus(String value) {
        this.ardStatus = value;
    }

    /**
     * Gets the value of the secChkTime property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getSecChkTime() {
        return secChkTime;
    }

    /**
     * Sets the value of the secChkTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setSecChkTime(XMLGregorianCalendar value) {
        this.secChkTime = value;
    }

    /**
     * Gets the value of the secChkStation property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSecChkStation() {
        return secChkStation;
    }

    /**
     * Sets the value of the secChkStation property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSecChkStation(String value) {
        this.secChkStation = value;
    }

    /**
     * Gets the value of the secChkEntId property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getSecChkEntId() {
        return secChkEntId;
    }

    /**
     * Sets the value of the secChkEntId property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setSecChkEntId(BigDecimal value) {
        this.secChkEntId = value;
    }

    /**
     * Gets the value of the secChkRptId property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getSecChkRptId() {
        return secChkRptId;
    }

    /**
     * Sets the value of the secChkRptId property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setSecChkRptId(BigDecimal value) {
        this.secChkRptId = value;
    }

    /**
     * Gets the value of the hostName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHostName() {
        return hostName;
    }

    /**
     * Sets the value of the hostName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHostName(String value) {
        this.hostName = value;
    }

    /**
     * Gets the value of the earliestCheckDueDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getEarliestCheckDueDate() {
        return earliestCheckDueDate;
    }

    /**
     * Sets the value of the earliestCheckDueDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setEarliestCheckDueDate(XMLGregorianCalendar value) {
        this.earliestCheckDueDate = value;
    }

    /**
     * Gets the value of the checkDueType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCheckDueType() {
        return checkDueType;
    }

    /**
     * Sets the value of the checkDueType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCheckDueType(String value) {
        this.checkDueType = value;
    }

}
