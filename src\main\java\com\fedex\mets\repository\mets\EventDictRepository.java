package com.fedex.mets.repository.mets;

import com.fedex.mets.entity.mets.EventDict;
import com.fedex.mets.entity.mets.EventDoa;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EventDictRepository extends JpaRepository<EventDict, Integer> {

    @Query(value = "select SUPER_ETIC from EVENT_DICT where TYPE=:type", nativeQuery = true)
    public String getSuperEtic(@Param("type") String type);
}
