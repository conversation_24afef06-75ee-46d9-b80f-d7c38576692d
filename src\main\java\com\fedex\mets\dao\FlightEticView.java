package com.fedex.mets.dao;

import jakarta.persistence.Column;
import lombok.*;

import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
public class FlightEticView {
    @Column(name = "FLT_NUM")
    public String flightNumber;

    @Column(name = "FLT_DATE")
    public Timestamp flightDate;

    @Column(name = "FLT_LEG")
    public String flightLeg;

    @Column(name = "FLT_ACN")
    public String flightACN;

    @Column(name = "FLT_DEST")
    public String flightDestination;

    @Column(name= "FLT_ORIGIN")
    public String flightOrigin;

    @Column(name = "FLT_STATUS")
    public String flightStatus;

    @Column(name = "FLT_TYPE")
    public String flightType;


    @Column(name = "FLT_SCHED_DEPT_DT_TM")
    public Timestamp flightSchedDeptDateTime;

    @Column(name = "FLT_ACT_DEPT_DT_TM")
    public Timestamp flightActualDeptDateTime;

    @Column(name = "FLT_TOTAL_DELAY")
    public int flightTotalDelay;

    @Column(name="DESCRIPTION")
    public String Description;

}
