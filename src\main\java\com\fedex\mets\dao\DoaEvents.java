package com.fedex.mets.dao;

import jakarta.persistence.Column;
import lombok.*;

import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class DoaEvents {
    @Column(name = "EVENT_ID")
    private Integer eventId;

    @Column(name = "STATION")
    private String station;

    @Column(name = "START_DT_TM")
    private Timestamp startDateTime;

    @Column(name = "STATUS")
    private String status;

    @Column(name = "CUR_COMMENT")
    private String curComment;

    @Column(name = "DOA_FLT_NUM")
    public String doaFleetNumber;

    @Column(name = "DOA_FLT_DATE")
    public Timestamp doaFleetDate;

    @Column(name = "DOA_FLT_LEG")
    public String doaFleetLeg;

}
