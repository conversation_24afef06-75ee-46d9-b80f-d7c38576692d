package com.fedex.mets.entity.mets;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;


@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "Event_Action")
public class EventAction {

    @Id
    @Column(name = "EVENT_ID", nullable = false)
    private Long eventId;

    @Column(name = "ACTION", length = 20)
    private String action;

    @Column(name = "EMP_NUM", length = 10)
    private String empNum;

    @Column(name = "EMP_NAME", length = 30)
    private String empName;

    @Column(name = "EMP_DEPARTMENT", length = 25)
    private String empDepartment;

    @Column(name = "LAST_UPDATE_DT_TM")
    private Timestamp lastUpdateDtTm;
}