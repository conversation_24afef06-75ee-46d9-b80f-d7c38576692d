package com.fedex.mets.dao;

import jakarta.persistence.Column;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ReportCategoriesActiveKeyValueData {

	public Object distinctLevel2Id;

	@Column(name = "EVENT_ID")
	public Integer eventId;

	@Column(name = "LEVEL_1_ID", nullable = false)
	public String level1Id;

	@Column(name = "LEVEL_2_ID", nullable = false)
	public String level2Id;

	@Column(name = "LAST_UPDATED_DT_TM")
	public Timestamp lastUpdatedDtTm;

	@Column(name = "LEVEL_2_NAME")
	public String level2Name;
}