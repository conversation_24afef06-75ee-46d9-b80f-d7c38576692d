package com.fedex.mets.repository.mars;

import com.fedex.mets.dao.EventListView;
import com.fedex.mets.dao.ManagerDetails;
import com.fedex.mets.entity.mars.EmpDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EmpDetailRepository extends JpaRepository<EmpDetail, String> {

    @Query("SELECT new com.fedex.mets.dao.ManagerDetails(s.respEmpnum, e.empFirstName, e.empMiddleInitial, " +
            "e.empLastName, s.comments, s.deptId, e.empJobCode," +
            "e.empJobTitle,e.empMgtLevel) \n " +
            "FROM StationRespEmp s " +
            "LEFT JOIN EmpDetail e on e.empNum = s.respEmpnum \n" +
            "where s.staId=:station AND e.empMgtLevel > 0 " +
            "ORDER BY e.empLastName,e.empFirstName,e.empMiddleInitial")
    public List<ManagerDetails> getManagersList(@Param("station") String station);
}
