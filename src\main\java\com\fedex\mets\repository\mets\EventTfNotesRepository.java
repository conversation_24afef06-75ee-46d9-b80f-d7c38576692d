package com.fedex.mets.repository.mets;

import com.fedex.mets.entity.mets.EventFlightInfo;
import com.fedex.mets.entity.mets.EventTfNotes;
import com.fedex.mets.entity.mets.EventTfNotesPk;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

@Repository
public interface EventTfNotesRepository extends JpaRepository<EventTfNotes, EventTfNotesPk> {

    @Query(value = " Select * from EVENT_TF_NOTES where EVENT_ID=:eventId order by NOTE_ID, NOTE_TYPE DESC",nativeQuery = true)
    public List<EventTfNotes> getAllTFNotes(@Param("eventId") int eventId);
    @Query(value = " Select * from EVENT_TF_NOTES where EVENT_ID=:eventId AND TF_DT_TM=to_timestamp(:creationTimeStamp, 'yyyy-mm-dd hh24:mi:ss.ff')",nativeQuery = true)
    EventTfNotes getTFNotes(@Param("eventId") int eventId,@Param("creationTimeStamp") Timestamp creationTimeStamp);

    @Query(value="select MAX(NOTE_ID) from EVENT_TF_NOTES where EVENT_ID=:eventId",nativeQuery = true)
    public Integer getMaxNoteId(@Param("eventId") int eventId);

    @Query(value="select count(*) from EVENT_TF_NOTES where EVENT_ID=:eventId",nativeQuery = true)
    public int getTfNotesCountdByEventId(@Param("eventId") int eventId);
}
