package com.fedex.mets.entity.mets;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "Event_Maxi_Dwning_Itm")
public class EventMaxiDwningItm{

    @Id
    @Column(name = "EVENT_ID")
    private Long eventId;

    @Column(name = "ATA")
    private String ata;

    @Column(name = "DISC_NUM")
    private String discNum;
}