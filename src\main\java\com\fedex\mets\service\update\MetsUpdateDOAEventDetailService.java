package com.fedex.mets.service.update;

import com.fedex.mets.data.DOAData;
import com.fedex.mets.data.DetailViewData;
import com.fedex.mets.data.DOADataEntity;
import com.fedex.mets.data.DetailViewDataEntity;
import com.fedex.mets.data.MetsEventUpdateEntity;
import com.fedex.mets.entity.cache.SuperEquipment;
import com.fedex.mets.entity.mets.*;
import com.fedex.mets.repository.cache.SuperEquipmentRepository;
import com.fedex.mets.repository.mets.*;
import com.fedex.mets.service.retrieval.EventDoaService;
import com.fedex.mets.service.retrieval.EventListDetailViewService;
import com.fedex.mets.util.ETICHelper;
import com.fedex.mets.util.IServerConstants;
import com.fedex.mets.util.ServerDateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.rmi.RemoteException;
import java.sql.Clob;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;

@Service
public class MetsUpdateDOAEventDetailService {
    private static final Logger log = LoggerFactory.getLogger(MetsUpdateDOAEventDetailService.class);
    @Autowired
    EventsRepository eventsRepository;

    @Autowired
    ChangeRequestRepository changeRequestRepository;

    @Autowired
    ChangeRequestHistoryRepository changeRequestHistoryRepository;

    @Autowired
    ChangeRequestLogRepository changeRequestLogRepository;

    @Autowired
    GroupDictRepository groupDictRepository;

    @Autowired
    EventDoaRepository eventDoaRepository;

    @Autowired
    EventTfNotesRepository eventTfNotesRepository;

    @Autowired
    SuperEquipmentRepository superEquipmentRepository;
    @Autowired
    EventDoaService eventDoaService;

    @Autowired
    EventListDetailViewService eventListDetailViewService;

    public Map<String, Object> updateDOAEventDetail(Map<String, Object> hashMap,
                                                    DetailViewDataEntity detailViewData, DOADataEntity eventDOAData, boolean commentUpdated, String accessLevel, String userId, String tokenId) throws Exception {
        //TODO token validation
        List<DetailViewData> detailElements = new ArrayList<>();
                DetailViewData elements = new DetailViewData();

        boolean resultFromBean = false,
                isCommentUpdated = commentUpdated;
        DOAData doaDataObject = new DOAData();
        int eventId = detailViewData.getEventID(), lastUpdatedRecords = 0;
        String strLastUpdatedTime = detailViewData.getEventLastUpdateDateTime();
        try {
            lastUpdatedRecords = findLastUpdated(eventId, strLastUpdatedTime);
        } catch (Exception e) {
            log.warn(
                    "ERROR MetsUpdate Servlet updateDOAEventDetail() e >> "
                            + e.getMessage());
        }

        if (lastUpdatedRecords == 0) {
            updateDOAEventData(
                    detailViewData,
                    eventDOAData,
                    accessLevel,
                    isCommentUpdated,
                    userId,
                    tokenId);

            resultFromBean = hashMap.get(IServerConstants.ERROR) == null;

//            if (resultFromBean) {
//                SendBuffer superUpSendBuffer = (SendBuffer) htResult.get(IServerConstants.SUPER_UPDATE_SENDBUFFER);
//                if (superUpSendBuffer != null) {
//                    boolean buffSent = SuperUpdateHelper.sendSuperUpdate(superUpSendBuffer);
//                }
//            }
        }
        if (resultFromBean) {
            if (detailViewData.isEventActive()) {
                detailElements =
                        eventListDetailViewService.getEventDetail(
                                detailViewData.getEventACN(),
                                userId);
            } else {
                detailElements =
                        eventListDetailViewService.getEventDetail(
                                detailViewData.getEventACN(),
                                userId);
            }
            //TODO a method needs to be added
//            if(detailElements.getEventType().equalsIgnoreCase("DOA")) {
//                String strEventId = String.valueOf(detailElements.getEventID());
//                doaDataObject = eventDoaService.getEventDOAInfo(strEventId, userId);
//                if (doaDataObject != null) {
//                    detailElements.setDoaData(doaDataObject);
//                }
//            }
            hashMap.put(IServerConstants.DETAIL_VIEW_OBJECT, detailElements);
        }else if (lastUpdatedRecords > 0) {
            log.info("Could not Alter the data as the Record has been modified prior to this transaction.");
            hashMap.put(
                    IServerConstants.ERROR,
                    "Could not Alter the data as the Record has been modified prior to this transaction.");
        }
        return hashMap;
    }

    public int findLastUpdated(int eventId, String lastUpdatedTime) {
        int existingRecords = 0;
        String lookupUpdatedTime =
                ServerDateHelper.getLookUpFormat(lastUpdatedTime);
        existingRecords = eventsRepository.getCountOfUpdatedRecords(eventId, lookupUpdatedTime);
        return existingRecords;

    }

    /**
     * The following updateDOAEventData() is used to update the detail data for a particular event.
     *
     * @return Hashtable result.
     * @params DetailViewData detailViewData, DOAData doaData, String accessLevel, boolean commentUpdated, String userId, String tokenId.
     */
    public HashMap updateDOAEventData(
            DetailViewDataEntity detailViewData,
            DOADataEntity doaData,
            String accessLevel,
            boolean commentUpdated,
            String userId,
            String tokenId)
            throws RemoteException {
        //TODO Token validation and Access level validation
        String strGroupId = "",
                strCheckFlight = "N",
                strMaintCW = "N";
        boolean isSuperUpdateRequired = false;
        Events events = null;
//        EventDoa eventDoa = null;
        HashMap result = new HashMap();
        EventDoa eventDoaNew = null;
        ChangeRequest changeRequest = null;
        ChangeRequestHistory changeRequestHistoryData = null;
        ChangeRequestLog changeRequestLogData = null;
        int eventId = detailViewData.getEventID(),
                activeOOSEvent = 0,
                existingChangeRequestRecords = 0;
        String strStartDateTime = detailViewData.getStartDateTime();
        if (doaData.isCheckFlightRequrired()) {
            strCheckFlight = "Y";
        }

        if (doaData.isMaintenanceCrew()) {
            strMaintCW = "Y";
        }

        /**the following is to check if there is an active OOS event for the same aircraft*/
        if (detailViewData.getEventType() != null
                && detailViewData.getEventType().trim().equalsIgnoreCase("DOA")) {
            activeOOSEvent = eventsRepository.getCountOfActiveOOSEvents(detailViewData.getEventACN());
        }
        /**The following is to check if there is any existing record in the Change Request table for the ACN*/

        existingChangeRequestRecords = changeRequestRepository.getCountOfChangeRequestRecords(detailViewData.getEventACN());

        strGroupId = groupDictRepository.getGroupIdByGroupTitle(detailViewData.getAcOwnerGroupId());
        try {
            try {
                events = eventsRepository.getEventsByEventId(eventId);
                if (events == null) {
                    result.put(
                            IServerConstants.ERROR,
                            "Event Id could not be generated to Change the Event.");
                    throw new RemoteException(
                            "Record for Event Id " + eventId + " could not be found to update");
                }
            } catch (RemoteException re) {
                log.warn(
                        " ERROR updateDOAEventData() Record for Event Id "
                                + eventId
                                + " could not be found to update >>"
                                + re.getMessage());
                throw new RemoteException(
                        "Record for Event Id " + eventId + " could not be found to update");
            } catch (Exception e) {
                log.warn(" ERROR updateDOAEventData() e >>" + e.getMessage());
            }
            try {
                eventDoaNew = eventDoaRepository.getEventDOAInfo(eventId);
                if (eventDoaNew == null) {
                    result.put(
                            IServerConstants.ERROR,
                            "EventDOA Id could not be generated to Change the Event.");
                    throw new RemoteException(
                            "DOA Record for Event Id "
                                    + eventId
                                    + " could not be found to update");

                }
            } catch (RemoteException re) {
                log.warn(
                        " ERROR updateDOAEventData() DOA Record for Event Id "
                                + eventId
                                + " could not be found to update >>"
                                + re.getMessage());
                throw new RemoteException(
                        "DOA Record for Event Id "
                                + eventId
                                + " could not be found to update");
            } catch (Exception e) {
                log.warn(
                        " ERROR updateDOAEventData() ee --  >>" + e.getMessage());
            }
            if (events != null && eventDoaNew != null) {
                Timestamp startTimeStamp = null,
                        updatedTimeStamp = null,
                        endDateTimeStamp = null;

                if (strStartDateTime != null) {
                    startTimeStamp =
                            ServerDateHelper.getConvertedTimestamp(strStartDateTime);
                }

                if (detailViewData.getEndDateTime() != null
                        && detailViewData.getEndDateTime().trim().length() > 0) {
                    endDateTimeStamp =
                            ServerDateHelper.getConvertedTimestamp(
                                    detailViewData.getEndDateTime());
                }

                updatedTimeStamp = ServerDateHelper.getTimeStamp();


                events.setStartDateTime(startTimeStamp);
                events.setStation(detailViewData.getEventStation());
                events.setAcOwnerGroupId(strGroupId);
                events.setCurComment(detailViewData.getEventCurrentComment());
                events.setPrimaryContact(detailViewData.getContact());
                events.setLastUpdateDateTime(updatedTimeStamp);
                events.setLastUpdatedBy(userId);

                if (detailViewData.isEventCancelled()) {
                    events.setCancelled("Y");
                } else {
                    events.setCancelled("N");
                }

                if (endDateTimeStamp != null) {
                    events.setEndDateTime(endDateTimeStamp);
                }

                /*
                 * Changes made in Database to store responsible manager Id
                 * */
                //TODO : Need to check if ResMgrId is necessary
//            events.setResMgrId(detailViewData.getResMgrId());

                events.setMemDeskContact(detailViewData.getMemDeskContact());

                //eventsData.setMgrNotes(detailViewData.getManagerNote());
                if (detailViewData.getEventType() != null
                        && detailViewData.getEventType().trim().equals("DOA")
                        && commentUpdated
                        && activeOOSEvent == 0) {
                    if (existingChangeRequestRecords == 0) {
                        int noteId = 0;
                        changeRequest = changeRequestRepository.getChangeRequestByAcn(detailViewData.getEventACN());
                        if (changeRequest == null) {
                            changeRequest = new ChangeRequest();
                            changeRequest.setEventId(detailViewData.getEventID());
                            changeRequest.setAcn(detailViewData.getEventACN());
                            changeRequest.setOldStatus(events.getStatus());
                            if (events.getEticText() != null
                                    && events.getEticText().startsWith("M")) {
                                log.info(
                                        "= >>> ERROR Me OldEticText table >"
                                                + events.getEticText());
                            }
                            changeRequest.setOldEticText(events.getEticText());
                            //TODO check if we need to get the value from detailViewData
                            changeRequest.setNewStatus(events.getStatus());
                            changeRequest.setNewEticDtTm(
                                    events.getEticDateTime());
                            changeRequest.setNewEticText(events.getEticText());
                            changeRequest.setNewComment(
                                    detailViewData.getEventCurrentComment());
                            changeRequest.setRequestStatus("S");
                            changeRequest.setLastUpdateDtTm(updatedTimeStamp);
                            changeRequest.setEnteredInError("N");
                            changeRequest.setChangeType(1);
                            changeRequest.setCreatedDtTm(updatedTimeStamp);
                            changeRequestRepository.save(changeRequest);
                        } else {

                            changeRequest.setOldStatus(events.getStatus());
                            if (events.getEticText() != null
                                    && events.getEticText().startsWith("M")) {
                            }
                            changeRequest.setOldEticText(events.getEticText());
                            //TODO check if we need to get the value from detailViewData
                            changeRequest.setNewStatus(events.getStatus());
                            changeRequest.setNewEticDtTm(
                                    events.getEticDateTime());
                            changeRequest.setNewEticText(events.getEticText());
                            changeRequest.setNewComment(
                                    detailViewData.getEventCurrentComment());
                            changeRequest.setRequestStatus("S");
                            changeRequest.setLastUpdateDtTm(updatedTimeStamp);
                            changeRequest.setEnteredInError("N");
                            changeRequest.setChangeType(1);
                            changeRequestRepository.save(changeRequest);

                        }

                        if (changeRequest != null) {
                            try {
                                String strNewEticDateTime = null;

                                if (changeRequest.getNewEticDtTm() != null)
                                    strNewEticDateTime =
                                            String.valueOf(changeRequest.getNewEticDtTm());
                                //TODO need to uncomment the below
                                isSuperUpdateRequired =
                                        superUpdateRequired(
                                                changeRequest.getAcn(),
                                                changeRequest.getNewStatus(),
                                                strNewEticDateTime,
                                                changeRequest.getNewEticText(),
                                                changeRequest.getNewComment());
                                log.info("*************isSuperUpdateRequired required >>************" + isSuperUpdateRequired);
                            } catch (Exception updateRequired) {
                                log.error("ERROR updateDOAEventData() isSuperUpdateRequired>> >>: " + updateRequired.getMessage());
                                throw new RemoteException(
                                        "ERROR updateDOAEventData() isSuperUpdateRequired>> >>: " + updateRequired.getMessage());
                            }

                            try {
                                noteId = eventTfNotesRepository.getMaxNoteId(detailViewData.getEventID()) != null ? eventTfNotesRepository.getMaxNoteId(detailViewData.getEventID()) : 0;
                                noteId = noteId + 1;
                            } catch (Exception e) {
                                log.error("ERROR updateDOAEventData() getMaxNoteId()>> >>: " + e.getMessage());
                            }
                            try {
                                if (noteId > 0) {
                                    java.util.Date currentDate =
                                            ServerDateHelper.getDate_UTC();
                                    java.sql.Timestamp tfNotesDateTime =
                                            new java.sql.Timestamp(currentDate.getTime() + 1000);

                                    String tfNote =
                                            "Posted New Comment: "
                                                    + detailViewData.getEventCurrentComment();

                                    EventTfNotes eventTFNotesData =
                                            new EventTfNotes();

                                    eventTFNotesData.setEventTfNotesPk(new EventTfNotesPk(detailViewData.getEventID(), tfNotesDateTime));
                                    eventTFNotesData.setEditedFlag("N");
                                    eventTFNotesData.setNoteId(noteId);
                                    eventTFNotesData.setTfNote(tfNote);
                                    eventTFNotesData.setNoteType(1);
                                    eventTFNotesData.setLastUpdateDtTm(tfNotesDateTime);
                                    eventTfNotesRepository.save(eventTFNotesData);
                                }
                            } catch (Exception tubNotes) {
                                log.error("ERROR updateDOAEventData() EventTfNotes()>> >>: " + tubNotes.getMessage());
                            }
                        }
                        try {
                            changeRequestHistoryData =
                                    new ChangeRequestHistory();

                            changeRequestHistoryData.setChangeRequestHistoryPk(new ChangeRequestHistoryPk(detailViewData.getEventID(), detailViewData.getEventACN(), updatedTimeStamp));
                            changeRequestHistoryData.setOldStatus(events.getStatus());
                            if (events.getEticText() != null
                                    && events.getEticText().startsWith("M")) {
                            }
                            changeRequestHistoryData.setOldEticText(
                                    events.getEticText());
                            //TODO check if the below needs to be update with detailViewData.getNewStatus
                            changeRequestHistoryData.setNewStatus(events.getStatus());
                            changeRequestHistoryData.setNewEticDtTm(
                                    events.getEticDateTime());
                            changeRequestHistoryData.setNewEticText(
                                    events.getEticText());
                            changeRequestHistoryData.setNewComment(
                                    detailViewData.getEventCurrentComment());
                            changeRequestHistoryData.setRequestStatus("S");
                            changeRequestHistoryData.setLastUpdateDtTm(
                                    updatedTimeStamp);
                            changeRequestHistoryData.setEnteredInError("N");
                            changeRequestHistoryData.setChangeType(1);
                            changeRequestHistoryRepository.save(changeRequestHistoryData);
                        } catch (Exception changeReqHist) {
                            log.error("ERROR updateDOAEventData() changeReqHist()>> >>: " + changeReqHist.getMessage());
                        }

                        try {
                            changeRequestLogData =
                                    new ChangeRequestLog();
                            changeRequestLogData.setChangeRequestLogPk(new ChangeRequestLogPk(detailViewData.getEventID(), updatedTimeStamp, updatedTimeStamp));
                            changeRequestLogData.setNewRequestStatus("S");
                            changeRequestLogRepository.save(changeRequestLogData);
                        } catch (Exception changeReqLog) {
                            log.error("ERROR updateDOAEventData() ChangeRequestLog()>> >>: " + changeReqLog.getMessage());
                        }
                    } else {

                        Timestamp createdDateTime = null;
                        try {
                            changeRequest = changeRequestRepository.getChangeRequestByAcn(detailViewData.getEventACN());

                            if (changeRequest != null) {

                                createdDateTime = changeRequest.getCreatedDtTm();
                                changeRequest.setNewComment(
                                        detailViewData.getEventCurrentComment());
                                changeRequest.setCreatedDtTm(updatedTimeStamp);

                                changeRequestRepository.save(changeRequest);

                                try {
                                    String strNewEticDateTime = null;

                                    if (changeRequest.getNewEticDtTm() != null)
                                        strNewEticDateTime =
                                                String.valueOf(changeRequest.getNewEticDtTm());
                                    //TODO need to uncomment the below
                                    isSuperUpdateRequired =
                                            superUpdateRequired(
                                                    changeRequest.getAcn(),
                                                    changeRequest.getNewStatus(),
                                                    strNewEticDateTime,
                                                    changeRequest.getNewEticText(),
                                                    changeRequest.getNewComment());
                                    log.info("*************isSuperUpdateRequired required >>************" + isSuperUpdateRequired);
                                } catch (Exception updateRequired) {
                                    log.warn("Exception Occurred in updateDOAEventData() isSuperUpdateRequired>> >>: " + updateRequired.getMessage());
                                }
                            }
                            if (changeRequest != null) {
                                int noteId = 0;
                                try {
                                    noteId = eventTfNotesRepository.getMaxNoteId(detailViewData.getEventID()) != null ? eventTfNotesRepository.getMaxNoteId(detailViewData.getEventID()) : 0;
                                    noteId = noteId + 1;
                                } catch (Exception e) {
                                    log.error("ERROR updateDOAEventData() getMaxNoteId()>> >>: " + e.getMessage());
                                }
                                if (noteId > 0) {
                                    Date currentDate =
                                            ServerDateHelper.getDate_UTC();
                                    Timestamp tfNotesDateTime =
                                            new java.sql.Timestamp(currentDate.getTime() + 1000);
                                    String tfNote =
                                            "Posted New Comment: "
                                                    + detailViewData.getEventCurrentComment();
                                    EventTfNotes eventTFNotesData =
                                            new EventTfNotes();

                                    eventTFNotesData.setEventTfNotesPk(new EventTfNotesPk(detailViewData.getEventID(), tfNotesDateTime));
                                    eventTFNotesData.setEditedFlag("N");
                                    eventTFNotesData.setNoteId(noteId);
                                    eventTFNotesData.setTfNote(tfNote);
                                    eventTFNotesData.setNoteType(1);
                                    eventTFNotesData.setLastUpdateDtTm(tfNotesDateTime);
                                    eventTfNotesRepository.save(eventTFNotesData);

                                }
                            }

                            try {
                                if (createdDateTime != null) {
                                    changeRequestHistoryData = changeRequestHistoryRepository.getChangeRequestHistory(detailViewData.getEventID(), detailViewData.getEventACN(), createdDateTime);

                                    changeRequestHistoryData.setNewComment(
                                            detailViewData.getEventCurrentComment());
                                    changeRequestHistoryData.getChangeRequestHistoryPk().setCreatedDtTm(updatedTimeStamp);

                                    changeRequestHistoryRepository.save(changeRequestHistoryData);
                                }
                            } catch (Exception history) {
                                log.error("ERROR updateDOAEventData() changeRequestHistoryData()>> >>: " + history.getMessage());
                            }
                            try {
                                changeRequestLogData =
                                        new ChangeRequestLog();
                                changeRequestLogData.setChangeRequestLogPk(new ChangeRequestLogPk(detailViewData.getEventID(), updatedTimeStamp, updatedTimeStamp));
                                changeRequestLogData.setNewRequestStatus("S");
                                changeRequestLogRepository.save(changeRequestLogData);
                            } catch (Exception changeReqHist) {
                                log.error("ERROR updateDOAEventData() changeReqHist()>> >>: " + changeReqHist.getMessage());
                            }
                        } catch (Exception update) {
                            log.error("ERROR updateDOAEventData() update()>> >>: " + update.getMessage());
                        }
                    }//end of else existingChangeRequestRecords > 0
                }//end of if(detailViewData.getEventType().trim().equals("DOA") && commentUpdated)


                //setting the EVENT_DOA record values
                try {
                    java.sql.Timestamp sqlFlightDate = null;
                    if (doaData.getFlightDate() != null
                            && doaData.getFlightDate().trim().length() > 0) {
                        sqlFlightDate = Timestamp.valueOf(doaData.getFlightDate());
                    }

                    Timestamp etaDateTime = null;

                    if (doaData.getEstimatedTimeOfArrival() != null) {
                        etaDateTime =
                                ServerDateHelper.getConvertedTimestamp(
                                        doaData.getEstimatedTimeOfArrival());
                    }

                    eventDoaNew.setCheckFleet(strCheckFlight);
                    eventDoaNew.setMaintCW(strMaintCW);
                    eventDoaNew.setDoaFleetNumber(doaData.getFlightNumber());
                    eventDoaNew.setDoaFleetDate(sqlFlightDate);
                    eventDoaNew.setDoaFleetLeg(doaData.getFlightLegNumber());
                    eventDoaNew.setDoaFleetComments(
                            doaData.getAdditionalDescription());
                    eventDoaNew.setLastUpdtDtTm(updatedTimeStamp);

                    eventDoaNew.setDoaFltDest(doaData.getDestination());

                    if (etaDateTime != null) {
                        eventDoaNew.setDoaFltEta(etaDateTime);
                    }

                } catch (Exception ee) {
                    log.error("ERROR updateDOAEventData() eventDoaNew()>> >>: " + ee.getMessage());
                }

                Clob clobComment = null;
                if (detailViewData.getManagerNote() != null && detailViewData.getManagerNote().trim().length() > 0) {
                    clobComment = eventsRepository.findMgrNotesByEventId(eventId);
                    if (clobComment != null) {
                        try {
                            clobComment.setString(1, detailViewData.getManagerNote());
                        } catch (SQLException e) {
                            throw new RuntimeException(e);
                        }
                    }
                }
                events.setMgrNotes(clobComment);
                //updating the Event table with the new Data modified on the Detail view screen.
                eventsRepository.save(events);
                //updating the Event DOA table with the new Data modified on the Detail view screen.
                eventDoaRepository.save(eventDoaNew);
                try {
                    if (events != null
                            && changeRequest != null) {
                        if (isSuperUpdateRequired) {

                            // added to avoid race condition where GDI returns SUPER confirmation before CHANGE_REQUEST table is updated
                            //TODO need to check what the below code does
//                        result.put(
//                                IServerConstants.SUPER_UPDATE_SENDBUFFER,
//                                generateSendBufferData(
//                                        changeRequestData,
//                                        userId,
//                                        detailViewData.getEventACN()));
                            // NO LONGER USED
/*							String strSuperOmniHost =
								ConfigHelper.getProperty("SUPER_OMNI_HOST_NAME");
							getLogger().info("strSuperOmniHost >>" + strSuperOmniHost);

							HttpMessage httpMessage =
								new HttpMessage(new java.net.URL(strSuperOmniHost));

							SendBuffer sendBuffer =
								generateSendBufferData(
									changeRequestData,
									userId,
									detailViewData.getEventACN());

							Object obj = httpMessage.execute(sendBuffer);

							ReceiveBuffer receiveBuffer = (ReceiveBuffer) obj;

							Boolean _superUpdated =
								(Boolean) receiveBuffer.get(MetsGDIConstants.RET_RESULTS);
							getLogger().info("Requested for Super Update >>>> " + _superUpdated);*/
                        } else {
                            /**
                             As SUPER_EQUIPMENT data and Change Request Data matches, Auto Confirmation should take place.
                             Update Change Request Table, Events table and insert a record in TF_Notes table.
                             **/
                            log.info("***********superUpdateRequired() is false*******");
                            boolean isMetsDataUpdated =
                                    updateMetsDataRecords(
                                            changeRequest,
                                            changeRequestHistoryData,
                                            events);
                        }
                    }
                } catch (Exception superUpdate) {
                    log.error("ERROR updateDOAEventData() superUpdate()>> >>: " + superUpdate.getMessage());
                }

            }
        } catch (Exception superUpdate) {
            log.error("ERROR updateDOAEventData() superUpdate()>> >>: " + superUpdate.getMessage());
        }
        return result;
    }

    /**
     * private method to update METS Data as SUPER_Equipment Data matches with Change Request created by the Client.
     *
     * @ params	ChangeRequest, ChangeRequestHistory,
     * ChangeRequestLog, Events events, WizardEventData
     * @ return	result
     */
    private boolean updateMetsDataRecords(
            ChangeRequest changeRequest,
            ChangeRequestHistory changeRequestHistory,
            Events events) {
        boolean isMetsUpdated = false;

        try {
            java.util.Date currentDate = ServerDateHelper.getDate_UTC();
            java.sql.Timestamp incrementedUpdatedDateTime =
                    new java.sql.Timestamp(currentDate.getTime() + 5000);
            if (changeRequest != null) {
                try {
                    changeRequest.setRequestStatus("C");
                    changeRequestRepository.save(changeRequest);
                } catch (Exception changeRequestError) {
                    log.warn(" ERROR Event Update changeRequestError  ** >>" + changeRequestError.getMessage());
                    events = null;
                }

                try {
                    changeRequestHistory.setRequestStatus("C");
                    changeRequestHistoryRepository.save(changeRequestHistory);
                } catch (Exception changeRequestHistoryError) {
                    log.warn(" ERROR Event Update changeRequestHistoryError  ** >>" + changeRequestHistoryError.getMessage());
                }

                try {
                    ChangeRequestLog superChangeRequestLogData =
                            new ChangeRequestLog();

                    superChangeRequestLogData.setChangeRequestLogPk(new ChangeRequestLogPk(changeRequest.getEventId(), incrementedUpdatedDateTime, incrementedUpdatedDateTime));
                    superChangeRequestLogData.setNewRequestStatus("C");
                    changeRequestLogRepository.save(superChangeRequestLogData);
                } catch (Exception changeReqLog) {
                    log.warn(" ERROR Event Update changeReqLog  ** >>" + changeReqLog.getMessage());
                }

                if (events != null) {
                    try {
                        events.setStatus(
                                changeRequest.getNewStatus());
                        events.setEticDateTime(
                                changeRequest.getNewEticDtTm());
                        events.setEticText(
                                changeRequest.getNewEticText());
                        events.setCurComment(
                                changeRequest.getNewComment());
                        events.setLastUpdateDateTime(
                                incrementedUpdatedDateTime);
                        events.setLastUpdatedBy("SUPER");
                        eventsRepository.save(events);
                    } catch (Exception eventsUpdated) {
                        log.warn(" ****cERROR Event Update eventsUpdated  ***** >>" + eventsUpdated.getMessage());
                    }
                }

                if (events != null) {
                    try {
                        String strEticComment = "",
                                strETICForm = "",
                                strStatus = "",
                                strTFNotesString = "";

                        String strNewEticDateTime = null;
                        if (changeRequest.getNewEticDtTm() != null)
                            strNewEticDateTime =
                                    "" + changeRequest.getNewEticDtTm();

                        if (strNewEticDateTime == null
                                && changeRequest.getNewEticText() != null) {
                            strETICForm = changeRequest.getNewEticText();
                        }
                        if (strNewEticDateTime != null
                                && changeRequest.getNewEticText() != null) {
                            strETICForm =
                                    ETICHelper.getETICFormat(
                                            strNewEticDateTime,
                                            changeRequest.getNewEticText());
                        }
                        if (strNewEticDateTime != null
                                && changeRequest.getNewEticText() == null) {
                            strETICForm = ETICHelper.getETICFormat(strNewEticDateTime, null);
                        }

                        if (changeRequest.getNewComment() != null
                                && changeRequest.getNewComment().trim().length() > 0)
                            strEticComment = changeRequest.getNewComment();

                        if (changeRequest.getNewStatus() != null
                                && changeRequest.getNewStatus().length() > 0)
                            strStatus = changeRequest.getNewStatus();

                        if (changeRequest.getChangeType() == 1)
                            strTFNotesString = "Confirm New Comment: " + strEticComment;
                        else if (changeRequest.getChangeType() == 2)
                            strTFNotesString = "Confirm New ETIC: " + strETICForm;
                        else if (changeRequest.getChangeType() == 3)
                            strTFNotesString =
                                    "Confirm New ETIC: " + strETICForm + ", " + strEticComment;
                        else if (changeRequest.getChangeType() == 4)
                            strTFNotesString = "Confirm New Status: " + strStatus;
                        else if (changeRequest.getChangeType() == 5)
                            strTFNotesString =
                                    "Confirm New Status: " + strStatus + ", " + strEticComment;
                        else if (changeRequest.getChangeType() == 6)
                            strTFNotesString =
                                    "Confirm New Status: " + strStatus + ", " + strETICForm;
                        else if (changeRequest.getChangeType() >= 7) {
                            strTFNotesString =
                                    "Confirm New Status: "
                                            + strStatus
                                            + ", "
                                            + strETICForm
                                            + ", "
                                            + strEticComment;
                        }

                        try {
                            boolean tfNoteAdded =
                                    addTFNotes(
                                            changeRequest.getEventId(),
                                            strTFNotesString,
                                            incrementedUpdatedDateTime);
                            log.info("***********tfNoteAdded method inside updateMetsDataRecords>>************" + tfNoteAdded);
                        } catch (Exception tfNote) {
                            log.warn(" ERROR Event Update tfNote  ** >>" + tfNote.getMessage());
                        }
                    } catch (Exception tfNoteInserted) {
                        log.warn(" ERROR Event Update tfNoteInserted  ** >>" + tfNoteInserted.getMessage());
                        events = null;
                    }

                    if (events != null) {
                        isMetsUpdated = true;
                    }
                }
            }
        } catch (Exception e) {
            log.warn(" ERROR Event Update  ** >>" + e.getMessage());
        }

        return isMetsUpdated;
    }

    /**
     * The following method is a private/sub method to support the addEvent method, to add a Tub File Note to the database
     *
     * @ params int eventId, String tfNote, Timestamp UpdatedTime.
     * @ return boolean result
     */
    private boolean addTFNotes(
            int eventId,
            String tfNote,
            java.sql.Timestamp updatedTimeStamp) {
        boolean result = false;
        int noteId = 0;
        noteId = eventTfNotesRepository.getMaxNoteId(eventId);

        noteId = noteId + 2;

        if (noteId > 0) {
            EventTfNotes eventTFNotesData = new EventTfNotes();
            eventTFNotesData.setEventTfNotesPk(new EventTfNotesPk(eventId, updatedTimeStamp));
            eventTFNotesData.setEditedFlag("N");
            eventTFNotesData.setNoteId(noteId);
            eventTFNotesData.setTfNote(tfNote);
            eventTFNotesData.setNoteType(1);
            eventTFNotesData.setLastUpdateDtTm(updatedTimeStamp);
            eventTfNotesRepository.save(eventTFNotesData);
            result = true;
        }
        return result;
    }

    private boolean superUpdateRequired(
            String strAcn,
            String strNewStatus,
            String strNewEticDateTime,
            String strNewEticText,
            String strNewComment)
            throws RemoteException {

        boolean updateRequired = false;
        Timestamp eticDateTime = null, eticDateTimeStamp = null;
        String strEticText = "", strOperationalStatus = "", strComment = "";
        String strEticTextToSuper = "";
        if (strNewEticDateTime != null) {
            eticDateTimeStamp =
                    ServerDateHelper.getConvertedTimestamp(strNewEticDateTime);
        }
        if (strNewEticDateTime == null && strNewEticText != null) {
            strEticTextToSuper = strNewEticText;
        }
        if (strNewEticDateTime != null && strNewEticText != null) {
            strEticTextToSuper =
                    ETICHelper.getETICFormat(strNewEticDateTime, strNewEticText);
        } else if (strNewEticDateTime != null && strNewEticText == null) {
            strEticTextToSuper =
                    ETICHelper.getETICFormat(strNewEticDateTime, "");
        }
        SuperEquipment superEquipment = superEquipmentRepository.getSuperEquipmentByAcn(strAcn);
        if (superEquipment == null) {
            throw new RemoteException("Record for ACN " + strAcn + " could not be found to update");
        }
        strEticText = superEquipment.getCurrentEticText();
        strOperationalStatus = superEquipment.getOperationalStatus();
        strComment = superEquipment.getComments();
        if (strOperationalStatus != null
                && strOperationalStatus.trim().length() > 0) {
            if (!strOperationalStatus.equalsIgnoreCase(strNewStatus)) {
                log.warn("Operational Status is different");
                updateRequired = true;
            }

            if (strComment != null
                    && !(strComment.trim().equalsIgnoreCase(strNewComment))) {
                log.info("Comment is different");
                updateRequired = true;
            }

            if (strEticText != null
                    && !(strEticText.trim().equalsIgnoreCase(strEticTextToSuper))) {
                log.info(" As strEticTextToSuper does not match SUPER_EQUIPMENT update is required");
                updateRequired = true;
            } else if (
                    strEticText == null
                            && (strEticTextToSuper != null
                            && strEticTextToSuper.trim().length() > 0)) {
                log.info(" As strEticText is null && strEticTextToSuper does not match SUPER_EQUIPMENT update is required");
                updateRequired = true;
            }
        } else if (strOperationalStatus == null
                || strOperationalStatus.trim().length() == 0) {
            updateRequired = true;
        }

        return updateRequired;
    }

}
