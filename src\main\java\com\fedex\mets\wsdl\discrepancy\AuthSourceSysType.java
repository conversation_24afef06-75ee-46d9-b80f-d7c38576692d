
package com.fedex.mets.wsdl.discrepancy;

import jakarta.xml.bind.annotation.XmlEnum;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for authSourceSysType.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="authSourceSysType">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="LDAP"/>
 *     &lt;enumeration value="RACF"/>
 *     &lt;enumeration value="MSS"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 * 
 */
@XmlType(name = "authSourceSysTypeDis")
@XmlEnum
public enum AuthSourceSysType {

    LDAP,
    RACF,
    MSS;

    public String value() {
        return name();
    }

    public static AuthSourceSysType fromValue(String v) {
        return valueOf(v);
    }

}
