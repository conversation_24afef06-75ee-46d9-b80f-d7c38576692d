package com.fedex.mets.repository.mets;

import com.fedex.mets.entity.mets.Question;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface QuestionRepository extends JpaRepository<Question,Integer> {

    @Query(value = "SELECT * FROM Question WHERE question_id IN (:ids)", nativeQuery = true)
    List<Question> getQuestionsByListOfId(List<Integer> ids);
}
