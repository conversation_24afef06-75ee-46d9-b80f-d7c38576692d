package com.fedex.mets.dao;

import lombok.*;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class DiscrepancyList implements Serializable {
    public String ata;
    public String number;
    public String discType;
    public String eventType;
    public String openDate;
    public String openStation;
    public boolean inWork=false;
    public String closed;
    public String[] text;
    public String[] message;
    public String status;
}