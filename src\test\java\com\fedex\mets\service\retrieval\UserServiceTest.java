package com.fedex.mets.service.retrieval;

import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

import com.fedex.mets.entity.ldap.User;
import com.fedex.mets.repository.ldap.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Optional;

public class UserServiceTest {

    @Mock
    private UserRepository userRepository;

    @InjectMocks
    private UserService userService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testGetUserByUid_Success() {
        // Arrange
        String uid = "testUid";
        User mockUser = new User();
        mockUser.setUid(uid);
        Optional<User> mockOptionalUser = Optional.of(mockUser);

        when(userRepository.findByUid(uid)).thenReturn(mockOptionalUser);

        // Act
        Optional<User> result = userService.getUserByUid(uid);

        // Assert
        assertTrue(result.isPresent());
        assertEquals(uid, result.get().getUid());
    }

    @Test
    public void testGetUserByUid_NotFound() {
        // Arrange
        String uid = "testUid";
        Optional<User> mockOptionalUser = Optional.empty();

        when(userRepository.findByUid(uid)).thenReturn(mockOptionalUser);

        // Act
        Optional<User> result = userService.getUserByUid(uid);

        // Assert
        assertFalse(result.isPresent());
    }
}
