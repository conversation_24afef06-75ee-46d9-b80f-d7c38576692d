package com.fedex.mets.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class MetsRequest {
	  	private String mode;
	    private String userId;
	    private String tokenId;
	    private String accessLevel;
	    private String eventId;
        private String eventType;
	    private String requestType;
	    private String groupId;
	    private String station;
	    private String acn;
	    private String region;
	    private String sequenceNumber;
	    private String timerId;
	    private String ata;
		private String discrepancyNumber;
		private String equipmentCode;
		private String flightSearchFromDate;
		private String flightSearchToDate;
		private String discrepancyFilter;
		private String discrepancyFromDate;
		private	String	discrepancyToDate;
		private String discrepancySpan;
		private String department;
		private String msn;

}
