package com.fedex.mets.data;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.TimeZone;

public class FlightDetails extends GregorianCalendar {

    private static final Logger logger = LoggerFactory.getLogger(FlightDetails.class);
    private String m_fltNbr;
    private Calendar m_fltTime;
    public FlightDetails( String fltNumber, Calendar flightTime){
        super(TimeZone.getTimeZone("UTC"));
        if( flightTime != null ){
              setTime(flightTime.getTime());
        }
        m_fltNbr = fltNumber;
        m_fltTime = flightTime;
    }

    public void setFlightNumber( String fltNbr){
        m_fltNbr = fltNbr;
    }
    public void setFlightDate(Calendar fltTime ){
        m_fltTime = fltTime;
    }
    public String getFlightNumber(){
        return m_fltNbr;
    }
    public Calendar getFlightDate(){
        return m_fltTime;
    }
    public String getRenderingString(){
        StringBuffer buffer = new StringBuffer();
        if( m_fltNbr != null ){
            buffer.append(m_fltNbr);
        }
        if( m_fltTime != null ){
            buffer.append("/");
            SimpleDateFormat formatter = new SimpleDateFormat("dd");
            formatter.setTimeZone(TimeZone.getTimeZone("UTC"));
            buffer.append(formatter.format(m_fltTime.getTime()));
        }
        return buffer.toString();
    }
}