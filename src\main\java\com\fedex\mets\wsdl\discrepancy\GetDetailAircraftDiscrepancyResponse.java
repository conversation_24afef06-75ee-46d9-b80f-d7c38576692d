
package com.fedex.mets.wsdl.discrepancy;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;extension base="{http://www.fedex.com/airops/schemas/Common.xsd}GenericResponse">
 *       &lt;sequence>
 *         &lt;element name="acnDscrp" type="{http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd}AcnDiscrepancy" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "acnDscrp"
})
@XmlRootElement(name = "getDetailAircraftDiscrepancyResponse",namespace="http:///www.fedex.com/airops/schemas/Mach")
public class GetDetailAircraftDiscrepancyResponse
    extends GenericResponse
{

    protected AcnDiscrepancy acnDscrp;

    /**
     * Gets the value of the acnDscrp property.
     * 
     * @return
     *     possible object is
     *     {@link AcnDiscrepancy }
     *     
     */
    public AcnDiscrepancy getAcnDscrp() {
        return acnDscrp;
    }

    /**
     * Sets the value of the acnDscrp property.
     * 
     * @param value
     *     allowed object is
     *     {@link AcnDiscrepancy }
     *     
     */
    public void setAcnDscrp(AcnDiscrepancy value) {
        this.acnDscrp = value;
    }

}
