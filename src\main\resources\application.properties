# OracleDB connection settings
server.port=8999


#METS DataSource
spring.datasource.mets.jdbc-url=******************************************************************************,cn=OracleContext
spring.datasource.mets.username=METS_APP
spring.datasource.mets.password=BYnWc1nRwiPOvpNJ9pOtmxKpR+YfMzORTYqwA6WsmXk=
spring.datasource.mets.driver-class-name=oracle.jdbc.OracleDriver
spring.jpa.properties.hibernate.jdbc.fetch_size=1000


#CACHE DataSource
spring.datasource.cache.jdbc-url=********************************************************************************,cn=OracleContext
spring.datasource.cache.username=OCACHE_METS_APP
spring.datasource.cache.password=BPiFCb2rytcQHwr0a/7uTrKJj0gk09+x3/7Zt01zo+8=
spring.datasource.cache.driver-class-name=oracle.jdbc.OracleDriver


# MSS DataSource
spring.datasource.mss.jdbc-url=******************************************************************************,cn=OracleContext
spring.datasource.mss.username=MSS_METS_APP
spring.datasource.mss.password=NuleJLO9a8KZSid67WmRbNWNCHY0pbx3J8Pp/S7PACI=
spring.datasource.mss.driver-class-name=oracle.jdbc.OracleDriver


#RAMPVIEW DataSource
spring.datasource.rampview.jdbc-url=***********************************************************,cn=OracleContext,dc=prod,dc=fedex,dc=com
spring.datasource.rampview.username=RVIEW_USER_APP
spring.datasource.rampview.password=orbJlDstdM6R0mau3jpumvqEb4Cjwe2aMHHpFesBMtk=
spring.datasource.rampview.driver-class-name=oracle.jdbc.OracleDriver

#MARS DataSource
spring.datasource.mars.jdbc-url=******************************************************************************,cn=OracleContext
spring.datasource.mars.username=MARS_APP
spring.datasource.mars.password=7dlvACpo4P1f/hvvb8akQg==
spring.datasource.mars.driver-class-name=oracle.jdbc.OracleDriver

#PCS DataSource
spring.datasource.pcs.jdbc-url=***************************************************************************,cn=OracleContext
spring.datasource.pcs.username=PCS_METS_APP
spring.datasource.pcs.password=U26FYtNVoHXxK5cY/hXtA30F/aHxhBtidZidEbR7U0I=
spring.datasource.pcs.driver-class-name=oracle.jdbc.OracleDriver

##################################################################
# Spring Boot Logging Configuration
logging.level.org.springframework.ws=DEBUG
logging.level.org.glassfish.jaxb=DEBUG
logging.level.org.springframework.ws.client.MessageTracing=DEBUG
logging.level.org.springframework.ws.transport.http.MessageTracing=TRACE
logging.level.org.springframework.ws.transport=DEBUG




spring.mvc.content-negotiation.favor-parameter=true
spring.mvc.content-negotiation.favor-path-extension=false
spring.mvc.content-negotiation.media-types.json=application/json
spring.mvc.content-negotiation.media-types.xml=application/xml

#
## HikariCP settings
#
#spring.datasource.hikari.minimumIdle=5
#
#spring.datasource.hikari.maximumPoolSize=20
#
#spring.datasource.hikari.idleTimeout=30000
#
#spring.datasource.hikari.maxLifetime=2000000
#
#spring.datasource.hikari.connectionTimeout=30000
#
#spring.datasource.hikari.poolName=HikariPoolBooks

#Mach Service Properties
mach-services-url=https://workbench-mach-l4.test.cloud.fedex.com:8443/MachServices/MachWS
maxi-service-url=https://workbench-l4.test.cloud.fedex.com:8443/MaxiWeb/ShortageNoticeServices
emr-services-url=https://workbench-mach-l4.test.cloud.fedex.com:8443/EMRWeb/EMRService


#Mach Services Okta Properties
okta.client.id=0oa1i8z5tfhMGNtRm0h8
okta.client.secret=9nNbxiVKh5ox8-gbLu37467YgDBz_V6Yv8SiTraF
okta.issuer=https://purpleid-test.oktapreview.com/oauth2/aus1gipvjr85gnnGM0h8

#JWT Properties
app.id=APP1841
app.okta.uri=https://purpleid-stage.oktapreview.com/oauth2/aus1kmc1lccSd9aSX0h8

#app.id=APP5231
#app.okta.uri=https://purpleid-test.oktapreview.com/oauth2/aus1gipvjr85gnnGM0h8

#Swagger Properties
springdoc.api-docs.path=/v3/api-docs
springdoc.api-docs.enabled=true
springdoc.swagger-ui.enabled=true
springdoc.swagger-ui.path=/swagger-ui.html

#LDAP Properties
spring.ldap.urls=ldap://directory.fedex.com:389
spring.ldap.base=o=fedex,c=US

spring.mail.host=mapper.gslb.fedex.com
spring.mail.port=25
spring.mail.properties.mail.smtp.auth=false
spring.mail.properties.mail.smtp.starttls.enable=false

#dss properties
dss.url=https://dss-l4.test.cloud.fedex.com:443/DSS/AAServlet

