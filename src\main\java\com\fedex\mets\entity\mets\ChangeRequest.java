package com.fedex.mets.entity.mets;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;

import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Entity
@Table(name = "CHANGE_REQUEST")
public class ChangeRequest {
    @Id
    @Column(name = "ACN", nullable = false)
    private String acn;

    @Column(name = "EVENT_ID")
    private Integer eventId;

    @Column(name = "OLD_ETIC_DT_TM")
    private Timestamp oldEticDtTm;

    @Column(name = "NEW_ETIC_DT_TM")
    private Timestamp newEticDtTm;

    @Column(name = "OLD_ETIC_TEXT")
    private String oldEticText;

    @Column(name = "NEW_ETIC_TEXT")
    private String newEticText;

    @Column(name = "OLD_COMMENT")
    private String oldComment;

    @Column(name = "NEW_COMMENT")
    private String newComment;

    @Column(name = "OLD_STATUS")
    private String oldStatus;

    @Column(name = "NEW_STATUS")
    private String newStatus;

    @Column(name = "REQUEST_STATUS", nullable = false)
    private String requestStatus;

    @Column(name = "LAST_UPDATE_DT_TM", nullable = false)
    private Timestamp lastUpdateDtTm;

    @Column(name = "ENTERED_IN_ERROR")
    private String enteredInError;

    @Column(name = "CHANGE_TYPE", nullable = false)
    private Integer changeType;

    @Column(name = "CREATED_DT_TM")
    private Timestamp createdDtTm;

    @Column(name = "OLD_OST")
    private String oldOst;

    @Column(name = "NEW_OST")
    private String newOst;

    @Column(name = "OLD_ETIC_RSN_CD")
    private String oldEticRsnCd;

    @Column(name = "NEW_ETIC_RSN_CD")
    private String newEticRsnCd;

    @Column(name = "OLD_ETIC_RSN_COMMENT")
    private String oldEticRsnComment;

    @Column(name = "NEW_ETIC_RSN_COMMENT")
    private String newEticRsnComment;
}