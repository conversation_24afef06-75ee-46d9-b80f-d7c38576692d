package com.fedex.mets.service.addEvent;

import com.fedex.mets.dao.AircraftBean;
import com.fedex.mets.dao.DoaEvents;
import com.fedex.mets.data.ListViewData;
import com.fedex.mets.data.ValidateEventData;
import com.fedex.mets.repository.mets.EventsRepository;
import com.fedex.mets.repository.mss.ForteLegRepository;
import com.fedex.mets.util.RvDBHelper;
import com.fedex.mets.util.ServerDateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

@Service
public class ValidateDoaEventService {
    private static final Logger logger = LoggerFactory.getLogger(ValidateDoaEventService.class);
    @Autowired
    RvDBHelper rvDBHelper;

    @Autowired
    private ForteLegRepository forteLegRepository;

    @Autowired
    private EventsRepository eventsRepository;

    /**
     * The following validateDOAEvents() is used to find the Events that are not closed/ Current active DOA Event for a particular ACN.
     * @params String acn.
     * @return List of Current & Unclosed DOA Events Data Object.
     */
    public List<ListViewData> validateDOAEvents(String acn){
        List<ListViewData> currentDOAList = new ArrayList<>();
        //to hold the current active DOA events

        List<ListViewData> eventsList = new ArrayList<>();
        //to hold the listView Object for client representation

        List<ValidateEventData> doaList = new ArrayList<>(); //to hold the active DOA events.

        String  strInboundFlightNumber = "",
                strInboundFlightDate = "",
                strInboundFlightLegNumber = "";
        try {

            List<DoaEvents> doaEvents = eventsRepository.getDOAEvents(acn);
            for(DoaEvents doaEvent: doaEvents)
            {
                ValidateEventData data = new ValidateEventData();
                int eventId= doaEvent.getEventId();
                String strStation = doaEvent.getStation();
                String strStartDateTime = String.valueOf(doaEvent.getStartDateTime());
                String strStatus = doaEvent.getStatus();
                String strCurrComment = doaEvent.getCurComment();
                String strFlightNumber = doaEvent.getDoaFleetNumber();
                String strFlightDate = String.valueOf(doaEvent.getDoaFleetDate());
                String strFlightLegNumber = doaEvent.getDoaFleetLeg();
                if(eventId>0)
                {
                    data.setEventId(eventId);
                    data.setDoaStation(strStation);
                    data.setDoaFlightNumber(strFlightNumber);
                    data.setDoaFlightDate(strFlightDate);
                    data.setDoaFlightLegNumber(strFlightLegNumber);

                    doaList.add(data);

                    //the following is the list view data object used to notify the client if there are any current active DOA events.
                    ListViewData listViewData = new ListViewData();

                    listViewData.setEventID(eventId);
                    listViewData.setStation(strStation);
                    listViewData.setACN(acn);
                    listViewData.setType("DOA");
                    listViewData.setStartDateTime(strStartDateTime);
                    listViewData.setStatus(strStatus);
                    listViewData.setCurComment(strCurrComment);

                    eventsList.add(listViewData);
                }

            }
        } catch (Exception doa) {
            logger.warn(
                    "ERROR ADD Event validateDOAEvents() doa " + doa.getMessage());
        }

        try {
            for (int i = 0; i < doaList.size(); i++) {
                ValidateEventData doaData = null;

                if (doaList.get(i) != null)
                    doaData = (ValidateEventData) doaList.get(i);

                if (doaData != null
                        && doaData.getDoaFlightNumber() != null
                        && doaData.getDoaFlightDate() != null) {
                    try {
                        String strFlightDate =
                                ServerDateHelper.getLookUpFormat(doaData.getDoaFlightDate());

                        String flightNumber= doaData.getDoaFlightNumber();
                        String flightLegNumber= doaData.getDoaFlightLegNumber();

                        String forteResultList = forteLegRepository.getLegStatusDepartureTime(flightNumber, strFlightDate, flightLegNumber);
                        if(forteResultList!=null)
                        {
                            String[] legStatusDepartureTime = forteResultList.split(",");
                            if (forteResultList!=null)
                            {
                                doaData.setDoaFlightStatus(legStatusDepartureTime[0]);
                                if(legStatusDepartureTime[1]!=null)
                                {
                                    doaData.setDoaDepartureTime(Timestamp.valueOf(legStatusDepartureTime[1]));
                                }
                            }
                        }
                    } catch (Exception e) {
                        logger.warn(
                                "ERROR ADD Event validateDOAEvents() e " + e.getMessage());
                    }
                }
            }
        } catch (Exception forte) {
            logger.warn(
                    "ERROR ADD Event validateDOAEvents() forte " + forte.getMessage());
        }

        try {
            logger.info("@@@@ Calling RvDBHelper for ACN: " + acn);
            AircraftBean acb = rvDBHelper.getAircraftRecordsFromRampview(acn);

            strInboundFlightNumber = acb.getInboundFlight();
            strInboundFlightDate = acb.getInboundFlightDate();
            strInboundFlightLegNumber = acb.getInboundFlightLeg();

        } catch (Exception e) {
            logger.warn(
                    "ERROR ADD Event validateDOAEvents() flight details " + e.getMessage());
        }
		/*
		if the values not found for the Flight Details (aircraft) or cancelled then it requires action.
		get the current flight from fris cache and if this matches with DOA flight details then it is current which needs conversion
		if both the above conditions does not match and the flight leg is open in Forte cache then this is Future event.
			or if the leg status in Forte is closed then	it requires action.
		*/
        for (int j = 0; j < doaList.size(); j++) {
            ValidateEventData doaData = (ValidateEventData) doaList.get(j);
            String doaFlightDateString = doaData.getDoaFlightDate();
            if (strInboundFlightDate != null && doaFlightDateString != null &&
                    strInboundFlightNumber.trim().equals(
                            doaData.getDoaFlightNumber().trim())
                            && strInboundFlightDate.trim().equals(doaFlightDateString.trim())
                            && strInboundFlightLegNumber.trim().equals(
                            doaData.getDoaFlightLegNumber().trim())) {
                currentDOAList.add(eventsList.get(j));
            }
        }
        return currentDOAList;
    }

}
