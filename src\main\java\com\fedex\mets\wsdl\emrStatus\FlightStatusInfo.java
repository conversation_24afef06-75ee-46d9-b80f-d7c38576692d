
package com.fedex.mets.wsdl.emrStatus;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for flightStatusInfo complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="flightStatusInfo">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="acn" type="{http://fedex.com/airops/emr/jaxws/services}acnType"/>
 *         &lt;element name="isValidAcnAircraft" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="flightReadiness" type="{http://fedex.com/airops/emr/jaxws/services}flightReadinessType" minOccurs="0"/>
 *         &lt;element name="flightStatus" type="{http://fedex.com/airops/emr/jaxws/services}statusInfo" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "flightStatusInfo", propOrder = {
    "acn",
    "isValidAcnAircraft",
    "flightReadiness",
    "flightStatus"
})
public class FlightStatusInfo {

    @XmlElement(required = true,namespace = "http://fedex.com/airops/emr/jaxws/services")
    protected String acn;
    @XmlElement(namespace = "http://fedex.com/airops/emr/jaxws/services")
    protected boolean isValidAcnAircraft;
    @XmlElement(namespace = "http://fedex.com/airops/emr/jaxws/services")
    protected FlightReadinessType flightReadiness;
    @XmlElement(namespace = "http://fedex.com/airops/emr/jaxws/services")
    protected StatusInfo flightStatus;

    /**
     * Gets the value of the acn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAcn() {
        return acn;
    }

    /**
     * Sets the value of the acn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAcn(String value) {
        this.acn = value;
    }

    /**
     * Gets the value of the isValidAcnAircraft property.
     * 
     */
    public boolean isIsValidAcnAircraft() {
        return isValidAcnAircraft;
    }

    /**
     * Sets the value of the isValidAcnAircraft property.
     * 
     */
    public void setIsValidAcnAircraft(boolean value) {
        this.isValidAcnAircraft = value;
    }

    /**
     * Gets the value of the flightReadiness property.
     * 
     * @return
     *     possible object is
     *     {@link FlightReadinessType }
     *     
     */
    public FlightReadinessType getFlightReadiness() {
        return flightReadiness;
    }

    /**
     * Sets the value of the flightReadiness property.
     * 
     * @param value
     *     allowed object is
     *     {@link FlightReadinessType }
     *     
     */
    public void setFlightReadiness(FlightReadinessType value) {
        this.flightReadiness = value;
    }

    /**
     * Gets the value of the flightStatus property.
     * 
     * @return
     *     possible object is
     *     {@link StatusInfo }
     *     
     */
    public StatusInfo getFlightStatus() {
        return flightStatus;
    }

    /**
     * Sets the value of the flightStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link StatusInfo }
     *     
     */
    public void setFlightStatus(StatusInfo value) {
        this.flightStatus = value;
    }

}
