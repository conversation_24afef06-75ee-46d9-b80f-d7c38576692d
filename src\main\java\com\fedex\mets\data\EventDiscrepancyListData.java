package com.fedex.mets.data;

import lombok.*;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
public class EventDiscrepancyListData {

	public int eventId;
	public boolean link;
	public String ata;
	public String number;
	public String discType;
	public String eventType;
	public String openDate;
	public String openStation;
	public boolean inWork;
	public String closed;
	public String status;
	public boolean isModified;
	public boolean isLinkModified;
	public boolean isDowningModified;
	public boolean isDowningItem;
	public String[] text;
	public String[] message;
	public String priority;
	public String timeRemaining;
	public BigDecimal discrepancyOid;
}
