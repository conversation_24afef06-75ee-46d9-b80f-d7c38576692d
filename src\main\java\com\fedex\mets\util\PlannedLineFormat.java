package com.fedex.mets.util;

public class PlannedLineFormat extends BaseLineFormat
{

    static int line = 80;

    @Override
    protected void init() {
	sfd = new SentenceFormatDef("Update Line", line, 5, 49);

	addPlannedType();
	addDate();
	addDateValue();
	addAT();
	addStation();
	addEntBy();
	addEntByValue();
	addOn();
	addOnDtTmValue();
    }

    private void addPlannedType() {
	FieldFormatDef ffd = new FieldFormatDef("Planned Type", 6);
	ffd.setValue("PLND:");
	sfd.getFields().add(ffd);
    }
    
    private void addDate() {
   	FieldFormatDef ffd = new FieldFormatDef("Date ", 5, true);
   	ffd.setValue("DATE:");
   	sfd.getFields().add(ffd);
       }

    private void addDateValue() {
	FieldFormatDef ffd = new FieldFormatDef("Date Value", 8, true);
	sfd.getFields().add(ffd);
    }
    private void addAT() {
   	FieldFormatDef ffd = new FieldFormatDef("AT", 4);
   	ffd.setValue("AT: ");
   	sfd.getFields().add(ffd);
       }

    private void addStation() {
	FieldFormatDef ffd = new FieldFormatDef("Station", 9);
	sfd.getFields().add(ffd);
    }

    private void addEntBy() {
	FieldFormatDef ffd = new FieldFormatDef("EntBy", 7);
	ffd.setValue("ENT BY:");
	sfd.getFields().add(ffd);
    }
    
    private void addEntByValue() {
   	FieldFormatDef ffd = new FieldFormatDef("EntByVal", 8);
   	sfd.getFields().add(ffd);
       }
    
    
    private void addOn() {
   	FieldFormatDef ffd = new FieldFormatDef("on", 4);
   	ffd.setValue("ON: ");
   	sfd.getFields().add(ffd);
       }

    
    
    private void addOnDtTmValue() {
   	FieldFormatDef ffd = new FieldFormatDef("OnDtTmVal", 18);
   	sfd.getFields().add(ffd);
       }
}
