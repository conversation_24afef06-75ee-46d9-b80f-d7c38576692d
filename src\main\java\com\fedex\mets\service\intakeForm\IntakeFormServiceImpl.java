package com.fedex.mets.service.intakeForm;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fedex.mets.dao.*;
import com.fedex.mets.entity.ldap.User;
import com.fedex.mets.entity.mets.*;
import com.fedex.mets.repository.mets.*;
import com.fedex.mets.service.retrieval.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class IntakeFormServiceImpl implements IntakeFormService {
    @Autowired
    QuestionRepository questionRepository;
    @Autowired
    AnswerRepository answerRepository;
    @Autowired
    IntakeFormRepository intakeFormRepository;
    @Autowired
    IntakeFormResponseRepository intakeFormResponseRepository;
    @Autowired
    UserIntakeFormRepository userIntakeFormRepository;

    @Autowired
    UserService userService;

    @Override
    @Transactional
    public Boolean createUserIntakeForm(UserIntakeFormDao userIntakeFormDao) {
        try {
            System.out.println(userIntakeFormDao.getIntakeForm().getQuestions().get(0).getQuestionTxt());
            Timestamp currentTimestamp = getCurrentTimestamp();
            UserIntakeForm userIntakeFormEntity = new UserIntakeForm();
            userIntakeFormEntity.setRoleId(userIntakeFormDao.getRoleId());
            userIntakeFormEntity.setEventId(userIntakeFormDao.getEventId());
            userIntakeFormEntity.setDssAuthCode(userIntakeFormDao.getDssAuthCode()!= null ? userIntakeFormDao.getDssAuthCode() : "");
            userIntakeFormEntity.setCreatedTMSTP(currentTimestamp);
            userIntakeFormEntity.setUpdatedTMSTP(currentTimestamp);
            userIntakeFormEntity.setIntakeFormNm(userIntakeFormDao.getIntakeFormNm());
            IntakeForm intakeForm = new IntakeForm();
            intakeForm.setCreatedTMSTP(currentTimestamp);
            intakeForm.setUpdatedTMSTP(currentTimestamp);
            intakeForm.setCreatedBy(userIntakeFormDao.getIntakeForm().getCreatedBy());
            List<Question> questionList = addNewQuestions(userIntakeFormDao.getIntakeForm().getQuestions());
            intakeForm.setQuestions(questionList);
            userIntakeFormEntity.setIntakeForm(intakeForm);
            userIntakeFormRepository.save(userIntakeFormEntity);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public List<Question> addNewQuestions(List<QuestionDao> questions) {
        Timestamp currentTimestamp = getCurrentTimestamp();
        return questions.stream().map(questionDao -> {
            Question question = new Question();
            question.setQuestionTxt(questionDao.getQuestionTxt());
            question.setQuestionGrp(questionDao.getQuestionGrp());
            question.setCreatedTMSTP(currentTimestamp);
            question.setUpdatedTMSTP(currentTimestamp);
            question.setRequired(questionDao.getRequired() != null && questionDao.getRequired() ? 'Y' : 'N');
            List<Answer> answerList = questionDao.getAnswers().stream().map(answerDao -> {
                Answer answer = new Answer();
                answer.setAnswerText(answerDao.getAnswerTxt());
                answer.setCreatedTMSTP(currentTimestamp);
                answer.setUpdatedTMSTP(currentTimestamp);
                return answer;
            }).collect(Collectors.toList());
            question.setAnswers(answerList);
            return question;
        }).collect(Collectors.toList());
    }

    @Override
    public UserRoleEventTypeDao getRoleAndEventType() {
        System.out.println("Inside the UserRoleEventTypeDao");
        List<UserRoleDao> userRoleList = new ArrayList<>();
        userRoleList.add(new UserRoleDao(1, "MOC"));
        userRoleList.add(new UserRoleDao(2, "LINE"));
        userRoleList.add(new UserRoleDao(3, "TLD"));
        List<EventTypeDao> eventTypeList = new ArrayList<>();
        eventTypeList.add(new EventTypeDao(1001, "TRK"));
        eventTypeList.add(new EventTypeDao(1111, "NOTE"));
        eventTypeList.add(new EventTypeDao(1100, "DOA"));

        UserRoleEventTypeDao userRoleEventTypeDao = new UserRoleEventTypeDao(userRoleList, eventTypeList);
        return userRoleEventTypeDao;
    }


    @Override
    public List<UserIntakeFormDao> getAllUserIntakeForms() {
        List<UserIntakeForm> userIntakeFormList = userIntakeFormRepository.findAll();
        return userIntakeFormList.stream().map(userIntakeForm -> {
//            UserIntakeFormPk userIntakeFormPk = userIntakeForm.getUserIntakeFormPk();
            UserIntakeFormDao userIntakeFormDao = new UserIntakeFormDao();
            userIntakeFormDao.setUserIntakeFormId(userIntakeForm.getUserIntakeFormId());
            userIntakeFormDao.setRoleId(userIntakeForm.getRoleId());
            userIntakeFormDao.setEventId(userIntakeForm.getEventId());
            userIntakeFormDao.setDssAuthCode(userIntakeForm.getDssAuthCode() != null ? userIntakeForm.getDssAuthCode() : "");
            userIntakeFormDao.setIntakeFormNm(userIntakeForm.getIntakeFormNm());
            IntakeFormDao intakeFormDao = new IntakeFormDao();
            intakeFormDao.setIntakeFormId(userIntakeForm.getIntakeForm().getIntakeFormId());
            intakeFormDao.setCreatedBy(userIntakeForm.getIntakeForm().getCreatedBy());
            List<QuestionDao> questionDaos = userIntakeForm.getIntakeForm().getQuestions().stream().map(question -> {
                QuestionDao questionDao = new QuestionDao();
                questionDao.setQuestionId(question.getQuestion_id());
                questionDao.setQuestionTxt(question.getQuestionTxt());
                questionDao.setQuestionGrp(question.getQuestionGrp());
                questionDao.setRequired(question.getRequired() != null && question.getRequired() == 'Y');
                List<AnswerDao> answerDaos = question.getAnswers().stream().map(answer -> {
                    AnswerDao answerDao = new AnswerDao();
                    answerDao.setAnswerId(answer.getAnswerId());
                    answerDao.setAnswerTxt(answer.getAnswerText());
                    return answerDao;
                }).collect(Collectors.toList());
                questionDao.setAnswers(answerDaos);
                return questionDao;
            }).collect(Collectors.toList());
            intakeFormDao.setQuestions(questionDaos);
            userIntakeFormDao.setIntakeForm(intakeFormDao);
            return userIntakeFormDao;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void deleteUserIntakeForm(int userIntakeFormId) {
        UserIntakeForm userIntakeForm = userIntakeFormRepository.findById(userIntakeFormId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "User intake form not found"));

        IntakeForm intakeForm = userIntakeForm.getIntakeForm();

        // Detach intakeForm from questions
        List<Question> questions = intakeForm.getQuestions();
        if (questions != null) {
            for (Question question : questions) {
                question.getIntakeForms().remove(intakeForm);
                questionRepository.save(question);
            }
        }

        userIntakeFormRepository.delete(userIntakeForm);
    }


    @Override
    public void editIntakeForm(ModifiedIntakeFormDao modifiedIntakeFormDao) {
        System.out.println("Inside the editIntakeForm:" + modifiedIntakeFormDao);
        IntakeForm intakeForm = intakeFormRepository.findById(modifiedIntakeFormDao.getIntakeFormId()).orElse(null);
        if (intakeForm == null) {
            throw new RuntimeException();
        }
        if (!modifiedIntakeFormDao.getNewlyAddedQuestions().isEmpty()) {
            List<Question> newlyAddedQuestionList = addNewQuestions(modifiedIntakeFormDao.getNewlyAddedQuestions());
            System.out.println("Newly Added Questions: " + newlyAddedQuestionList);
            intakeForm.getQuestions().addAll(newlyAddedQuestionList);
            intakeFormRepository.save(intakeForm);
        }
        if (!modifiedIntakeFormDao.getAddedQuestions().isEmpty()) {
            List<Question> addedQuestionList = questionRepository.getQuestionsByListOfId(modifiedIntakeFormDao.getAddedQuestions().stream().map(QuestionDao::getQuestionId).collect(Collectors.toList()));
            intakeForm.getQuestions().addAll(addedQuestionList);
            intakeFormRepository.save(intakeForm);
        }

        if (!modifiedIntakeFormDao.getDeletedQuestions().isEmpty()) {
            List<Question> deletedQuestionList = questionRepository.getQuestionsByListOfId(modifiedIntakeFormDao.getDeletedQuestions().stream().map(QuestionDao::getQuestionId).collect(Collectors.toList()));
            List<Question> currentQuestionList = intakeForm.getQuestions();
            List<Integer> ids = currentQuestionList.stream().map(Question::getQuestion_id).collect(Collectors.toList());
            for (Question question : deletedQuestionList) {
                if (ids.contains(question.getQuestion_id())) {
                    System.out.println("Question IS PRESENT: " + question.getQuestionTxt());
                    currentQuestionList.remove(question);
                }
            }
            intakeForm.setQuestions(currentQuestionList);
            intakeFormRepository.save(intakeForm);
        }
    }

    @Override
    public void deleteQuestion(int questionId) {
        Question question = questionRepository.findById(questionId).orElse(null);
        List<IntakeForm> associatedForms = question.getIntakeForms();
        System.out.println("Associated Forms: " + associatedForms);
        if (associatedForms != null) {
            for (IntakeForm form : associatedForms) {
                form.getQuestions().remove(question);
                intakeFormRepository.save(form);
            }
        }
        questionRepository.delete(question);
    }

    @Override
    public void updateQuestion(QuestionDao questionDao) {
        Question question = questionRepository.findById(questionDao.getQuestionId()).orElse(null);
        if (question == null) {
            throw new RuntimeException();
        }
        question.setQuestionTxt(questionDao.getQuestionTxt());
        question.setQuestionGrp(questionDao.getQuestionGrp());
        question.setUpdatedTMSTP(getCurrentTimestamp());
        question.setRequired(questionDao.getRequired() != null && questionDao.getRequired() ? 'Y' : 'N');
        updateAnswers(question, questionDao.getAnswers(), getCurrentTimestamp());
        questionRepository.save(question);
    }

    private void updateAnswers(Question question, List<AnswerDao> editedAnswers, Timestamp currentTimestamp) {
        List<Answer> existingAnswers = question.getAnswers();

        for (Answer existingAnswer : existingAnswers) {
            editedAnswers.stream()
                    .filter(editedAnswer -> editedAnswer.getAnswerId() == existingAnswer.getAnswerId())
                    .findFirst()
                    .ifPresent(editedAnswer -> {
                        existingAnswer.setAnswerText(editedAnswer.getAnswerTxt());
                        existingAnswer.setUpdatedTMSTP(currentTimestamp);
//                        answerRepository.save(existingAnswer);
                        question.getAnswers().add(existingAnswer);
                    });
        }

        List<Integer> existingAnswerIds = existingAnswers.stream()
                .map(Answer::getAnswerId)
                .collect(Collectors.toList());

        editedAnswers.stream()
                .filter(editedAnswer -> !existingAnswerIds.contains(editedAnswer.getAnswerId()))
                .forEach(editedAnswer -> {
                    Answer newAnswer = new Answer();
                    newAnswer.setAnswerText(editedAnswer.getAnswerTxt());
                    newAnswer.setCreatedTMSTP(currentTimestamp);
                    newAnswer.setUpdatedTMSTP(currentTimestamp);
//                    answerRepository.save(newAnswer);
                    question.getAnswers().add(newAnswer);
                });
    }

    @Override
    public List<IntakeFormDao> getIntakeForms() {
        List<IntakeForm> intakeFormList = intakeFormRepository.findAll();
        return intakeFormList.stream().map(intakeForm -> {
            IntakeFormDao intakeFormDao = new IntakeFormDao();
            intakeFormDao.setIntakeFormId(intakeForm.getIntakeFormId());
            intakeFormDao.setCreatedBy(intakeForm.getCreatedBy());
            List<QuestionDao> questionDaos = intakeForm.getQuestions().stream().map(question -> {
                QuestionDao questionDao = new QuestionDao();
                questionDao.setQuestionId(question.getQuestion_id());
                questionDao.setQuestionTxt(question.getQuestionTxt());
                questionDao.setQuestionGrp(question.getQuestionGrp());
                questionDao.setRequired(question.getRequired() != null && question.getRequired() == 'Y');
                List<AnswerDao> answerDaos = question.getAnswers().stream().map(answer -> {
                    AnswerDao answerDao = new AnswerDao();
                    answerDao.setAnswerId(answer.getAnswerId());
                    answerDao.setAnswerTxt(answer.getAnswerText());
                    return answerDao;
                }).collect(Collectors.toList());
                questionDao.setAnswers(answerDaos);
                return questionDao;
            }).collect(Collectors.toList());
            intakeFormDao.setQuestions(questionDaos);
            return intakeFormDao;
        }).collect(Collectors.toList());
    }

    @Override
    public List<QuestionDao> getAllQuestions() {
        List<Question> questions = questionRepository.findAll();
        return questions.stream().map(question -> {
            QuestionDao questionDao = new QuestionDao();
            questionDao.setQuestionId(question.getQuestion_id());
            questionDao.setQuestionTxt(question.getQuestionTxt());
            questionDao.setQuestionGrp(question.getQuestionGrp());
            questionDao.setRequired(question.getRequired() != null && question.getRequired() == 'Y');
            List<AnswerDao> answerDaos = question.getAnswers().stream().map(answer -> {
                AnswerDao answerDao = new AnswerDao();
                answerDao.setAnswerId(answer.getAnswerId());
                answerDao.setAnswerTxt(answer.getAnswerText());
                return answerDao;
            }).collect(Collectors.toList());
            questionDao.setAnswers(answerDaos);
            return questionDao;
        }).collect(Collectors.toList());
    }

    @Override
    public IntakeFormResponseDao getIntakeFormByEventId(int eventId) {
        String responseJson = intakeFormResponseRepository.getResponseJsonByEventId(eventId);
        ObjectMapper objectMapper = new ObjectMapper();
        if (!responseJson.isEmpty() && responseJson != null) {
            try {
                IntakeFormResponseDao intakeFormResponseDao = objectMapper.readValue(responseJson, IntakeFormResponseDao.class);
                return intakeFormResponseDao;
            } catch (Exception e) {
                throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error parsing response JSON", e);
            }
        } else {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Intake form not found for event ID: " + eventId);
        }
    }

    @Override
    public void updateIntakeFormResponse(IntakeFormResponseDao intakeFormResponseDao) {
        int eventId = intakeFormResponseDao.getEventId();
        if (eventId == 0) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Event ID cannot be null");
        }
        Long userId = getCurrentUserId();
        User user = userService.getUserByUid(userId.toString()).orElse(new User());
        String responseJson;
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            responseJson = objectMapper.writeValueAsString(intakeFormResponseDao);
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error converting IntakeFormDao to JSON", e);
        }

        Optional<IntakeFormResponse> existingResponse = intakeFormResponseRepository.findById(eventId);
        if (existingResponse.isPresent()) {
            IntakeFormResponse intakeFormResponse = existingResponse.get();
            intakeFormResponse.setResponseJson(responseJson);
            intakeFormResponse.setUpdatedTMSTP(getCurrentTimestamp());
            intakeFormResponseRepository.save(intakeFormResponse);
        } else {
            IntakeFormResponse newIntakeFormResponse = new IntakeFormResponse();
            newIntakeFormResponse.setResponseJson(responseJson);
            newIntakeFormResponse.setEmpNm(user.getName());
            newIntakeFormResponse.setEmpNbr(Math.toIntExact(userId));
            newIntakeFormResponse.setCreatedTMSTP(getCurrentTimestamp());
            newIntakeFormResponse.setUpdatedTMSTP(getCurrentTimestamp());
            newIntakeFormResponse.setEventId(eventId);
            intakeFormResponseRepository.save(newIntakeFormResponse);
        }
    }

    @Override
    public UserIntakeFormDao getUserIntakeForm(int roleId, int eventId) {
        UserIntakeForm userIntakeForm = userIntakeFormRepository.getUserIntakeForm(roleId, eventId);
        if (userIntakeForm == null) {
            throw new RuntimeException();
        }
        UserIntakeFormDao userIntakeFormDao = new UserIntakeFormDao();
        userIntakeFormDao.setUserIntakeFormId(userIntakeForm.getUserIntakeFormId());
        userIntakeFormDao.setRoleId(userIntakeForm.getRoleId());
        userIntakeFormDao.setEventId(userIntakeForm.getEventId());
        userIntakeFormDao.setDssAuthCode(userIntakeForm.getDssAuthCode());
        userIntakeFormDao.setIntakeFormNm(userIntakeForm.getIntakeFormNm());
        IntakeFormDao intakeFormDao = new IntakeFormDao();
        intakeFormDao.setIntakeFormId(userIntakeForm.getIntakeForm().getIntakeFormId());
        intakeFormDao.setCreatedBy(userIntakeForm.getIntakeForm().getCreatedBy());
        List<QuestionDao> questionDaos = userIntakeForm.getIntakeForm().getQuestions().stream().map(question -> {
            QuestionDao questionDao = new QuestionDao();
            questionDao.setQuestionId(question.getQuestion_id());
            questionDao.setQuestionTxt(question.getQuestionTxt());
            questionDao.setQuestionGrp(question.getQuestionGrp());
            questionDao.setRequired(question.getRequired() != null && question.getRequired() == 'Y');
            List<AnswerDao> answerDaos = question.getAnswers().stream().map(answer -> {
                AnswerDao answerDao = new AnswerDao();
                answerDao.setAnswerId(answer.getAnswerId());
                answerDao.setAnswerTxt(answer.getAnswerText());
                return answerDao;
            }).collect(Collectors.toList());
            questionDao.setAnswers(answerDaos);
            return questionDao;
        }).collect(Collectors.toList());
        intakeFormDao.setQuestions(questionDaos);
        userIntakeFormDao.setIntakeForm(intakeFormDao);
        return userIntakeFormDao;
    }


    public Timestamp getCurrentTimestamp() {
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formattedNow = now.format(formatter);
        return Timestamp.valueOf(formattedNow);
    }

    private Long getCurrentUserId() {
        String username = SecurityContextHolder.getContext().getAuthentication() != null ?
                SecurityContextHolder.getContext().getAuthentication().getName() : null;

        if (username == null || username.trim().isEmpty()) {
            return 0L; // Default to 0 if no user is found
        }

        try {
            return Long.parseLong(username);
        } catch (NumberFormatException e) {
            return 0L; // Default to 0 if username is not a valid Long
        }
    }
}
