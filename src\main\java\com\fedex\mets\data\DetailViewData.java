package com.fedex.mets.data;

import com.fedex.mets.entity.mets.EventMsns;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;


@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class DetailViewData implements Serializable{
	
	public int			eventID;                
	public String 		eventType;   
	public String		startDateTime;
	public String		startDateTimeUTC;
	public String		endDateTime;//added on 07/26/2002 for NIW Timers Stop time should not be more than Events End Dt Time
	public String 		eventACN;                      
	public String 		eventFleetDesc;             
	public String 		eventStation;                  
	public String 		eventStatus; 
	public String 		eventEticDateTime;            
	public String 		eventEticText;               
	public String 		eventCurrentComment;
	public String		eventOST;//added on 09-07-2012 for reducing OOS times initiative by MOCC
	public String 		eventLastUpdateDateTime; 
	public String 		eventLastUpdatedBy;      
	public String 		eventCreatedDateTime;       
	public String 		eventCreatedBy;		
	public String		eventOnwerGroupId;
	public String		acOwnerGroupId;
	public String		errorText;
	public String		gate;
	public String		mxSpeedDial;
	public String		crew;
	public String		contact;
	public String		eventEticReasonCd;
	public String		eventEticReasonComment;

/*
*	Variables declared to hold the flight leg values for an aircraft
*/
	public String       inboundFlightNumber;
	public String       inboundFlightDate;
	public String       inboundLegNumber;
	public String       inboundLegDate;
	public String       inboundOrigination;
	public String       inboundFlightDepartureTime;
	public String       inboundDestination;
	public String       inboundArrivalDate;
	public String       inboundArrivalTime;

	public String       outboundFlightNumber;
	public String       outboundFlightDate;
	public String       outboundLegNumber;
	public String       outboundLegDate;
	public String       outboundOrigination;
	public String       outboundFlightDepartureTime;
	public String       outboundDestination;
	public String       outboundArrivalDate;

	public String		equipmentType;


	//added for client side presentation to indicate number of forms for a particular event. 08/21/02
	public boolean		activeTimer=false;

	//added for client side presentation to indicate number of forms for a particular event. 08/27/02
	public boolean		doaAlert=false;

	//added for client side presentation to show the # of linked discrepancies for the Event 09/16/02
	public String		numberOfDiscrepancies;

	//added for client side presentation 09/20/02
	public String		requestStatus;

	//added for client side presentation 10/07/02
	public int			changeType;

	//added for the Server to retreive the linked Discrepancies and then match the results with ATA, DISC returned by the OSMSN server
	//then count the matching #MSN values to return back to the Client. 10/08/02
	public List<DOADiscrepancyData>		linkedDiscList;

	public List<EventMsns> linkedMsns;

	//variable used to display the # of MSN's on ACN
	public String		numberOfMSN;

	//variable set on the Server As the client will only have the access level and not the Group id.
	public String		groupId;

	//vector declared to hold the Owner values added on 01/08/2003
	public List<String>		contactInfoOwnerList;

	//data object declared on 01/13/03 as Raghu Goda requested for Client Side reading would make it easier.
	private DOAData		doaData=null;
	
	//data variable declared on 01/14/03 to display if the Event had been Cancelled on Client Side.
	public boolean		isEventCancelled=false;

	//data variable declared on 01/14/03 to be added to the doaDataObject.
	public String		eventOriginalComment;

	//data variable declared on 01/27/03 to declare if the Event is Active/In Active.(Required while updating an Inactive Event to check the Security)
	public boolean		isEventActive=false;

	//data variable declared on 01/31/03 to hold the MGR_Notes for an Event.
	public String		managerNote;
	
	//data variable declared on 02/05/03 for Client side representation of Alert
	public String 		eventNewStatus; 
	
	//added for removing resource bottle necks used on Server side 12-31-2003
	public String		doaFlightNumber;
	public String		doaFlightDate;
	public String		doaFlightLegNumber;
	
	public String	resMgrId;//added on 06-29-2012, Server would retrieve the EMP DEPT ID based on USER_ID from MARS database while inserting the tub file note.

	public String	memDeskContact;//added 01/26/2016, MEM desk lead for MOCC Orange Man project, requested by Ebben Raves

	public String duration;
	
	
}