package com.fedex.mets.entity.mets;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "MOCC_CONTACT_INFO")
public class MoccContactInfo {

    @Id
    @Column(name = "AC_MFG", nullable = false)
    private String acMfg;

    @Column(name = "HOURS_PHONE")
    private String hoursPhone;
}