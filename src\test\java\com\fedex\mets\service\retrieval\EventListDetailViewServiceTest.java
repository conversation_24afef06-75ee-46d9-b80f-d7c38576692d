package com.fedex.mets.service.retrieval;

import com.fedex.mets.dao.EventDetailView;
import com.fedex.mets.data.DetailViewData;
import com.fedex.mets.entity.mets.EventTfNotes;
import com.fedex.mets.entity.mets.EventTfNotesPk;
import com.fedex.mets.repository.cache.SuperEquipmentRepository;
import com.fedex.mets.repository.mars.StadeptDetailRepository;
import com.fedex.mets.repository.mets.*;
import com.fedex.mets.util.RvDBHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import javax.sql.rowset.serial.SerialClob;
import java.sql.Clob;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

public class EventListDetailViewServiceTest {

    @Mock
    private EventTfNotesRepository tfNotesRepo;

    @Mock
    private EventDiscrepanciesService eventDiscrepanciesService;

    @Mock
    private GroupDictRepository groupDictRepository;

    @Mock
    private EventsRepository eventRepository;

    @Mock
    private EventDoaRepository eventDoaRepository;

    @Mock
    private EventMaxiDiscRepository eventMaxiDiscRepository;

    @Mock
    private EventTimersRepository eventTimersRepository;

    @Mock
    private StadeptDetailRepository stadeptDetailRepository;

    @Mock
    private SuperEquipmentRepository superEquipmentRepository;

    @Mock
    private MsnDetailService msnDetailService;

    @Mock
    private EventMsnsRepository eventMsnsRepository;

    @Mock
    private RvDBHelper rvDBHelper;

    @InjectMocks
    private EventListDetailViewService eventListDetailViewService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testGetEventDetailView_Success() throws Exception {
        String acn = "ACN123";
        String userId = "User1";

        EventDetailView eventDetailView = new EventDetailView();
        eventDetailView.setEventId(1);
        eventDetailView.setType("DOA");
        eventDetailView.setStartDateTime(Timestamp.valueOf("2025-04-11 10:00:00"));
        eventDetailView.setEndDateTime(Timestamp.valueOf("2025-04-12 10:00:00"));
        eventDetailView.setAcn("ACN123");
        eventDetailView.setFleetDesc("FleetDesc");
        eventDetailView.setStation("Station");
        eventDetailView.setStatus("Status");
        eventDetailView.setEticDateTime(Timestamp.valueOf("2025-04-11 12:00:00"));
        eventDetailView.setEticText("EticText");
        eventDetailView.setCurrentOrigComment("CurrentOrigComment");
        eventDetailView.setLastUpdateDateTime(Timestamp.valueOf("2025-04-12 10:00:00"));
        eventDetailView.setLastUpdatedBy("User1");
        eventDetailView.setCreatedDateTime(Timestamp.valueOf("2025-04-10 10:00:00"));
        eventDetailView.setCreatedBy("User1");
        eventDetailView.setPrimaryContact("PrimaryContact");
        eventDetailView.setAcOwnerGroupId("GroupId");
        eventDetailView.setEventOwnerGroupId("EventOwnerGroupId");
        eventDetailView.setRequestStatus("S");
        eventDetailView.setChangeType(1);
        eventDetailView.setOrigComment("OrigComment");
        eventDetailView.setCancelled("Y");
        eventDetailView.setNewStatus("NewStatus");
        eventDetailView.setNewEticText("NewEticText");
        eventDetailView.setNewOldComment("NewOldComment");
        eventDetailView.setNewEticDtTm(Timestamp.valueOf("2025-04-11 14:00:00"));
        eventDetailView.setMemDeskContact("MemDeskContact");

        String clobData = "This is a sample Clob data.";
        Clob clob = new SerialClob(clobData.toCharArray());
        eventDetailView.setMgrNotes(clob);
        eventDetailView.setOst("OST");
        eventDetailView.setEticRsnCd("EticRsnCd");
        eventDetailView.setEticRsnComment("EticRsnComment");

        List<EventDetailView> eventDetailViews = new ArrayList<>();
        eventDetailViews.add(eventDetailView);

        when(eventRepository.getDetailListView(anyString())).thenReturn(eventDetailViews);

        List<DetailViewData> result = eventListDetailViewService.getEventDetailView(acn, userId);

        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getEventID());
        assertEquals("DOA", result.get(0).getEventType());
        assertEquals("04/11 10:00", result.get(0).getStartDateTime());
        assertEquals("2025-04-12 10:00:00.0", result.get(0).getEndDateTime());
        assertEquals("ACN123", result.get(0).getEventACN());
        assertEquals("FleetDesc", result.get(0).getEventFleetDesc());
        assertEquals("Station", result.get(0).getEventStation());
        assertEquals("NewStatus", result.get(0).getEventStatus());
        assertEquals("1400/041125", result.get(0).getEventEticDateTime());
        assertEquals("NewEticText", result.get(0).getEventEticText());
        assertEquals("NewOldComment", result.get(0).getEventCurrentComment());
        assertEquals("2025-04-12 10:00:00.0", result.get(0).getEventLastUpdateDateTime());
        assertEquals("User1", result.get(0).getEventLastUpdatedBy());
        assertEquals("2025-04-10 10:00:00.0", result.get(0).getEventCreatedDateTime());
        assertEquals("User1", result.get(0).getEventCreatedBy());
        assertEquals("PrimaryContact", result.get(0).getContact());
        assertEquals(null, result.get(0).getAcOwnerGroupId());
        assertEquals("EventOwnerGroupId", result.get(0).getEventOnwerGroupId());
        assertEquals("S", result.get(0).getRequestStatus());
        assertEquals(1, result.get(0).getChangeType());
        assertEquals("OrigComment", result.get(0).getEventOriginalComment());
        assertEquals(true, result.get(0).isEventCancelled());
        assertEquals("NewStatus", result.get(0).getEventNewStatus());
        assertEquals("NewEticText", result.get(0).getEventEticText());
        assertEquals("NewOldComment", result.get(0).getEventCurrentComment());
        assertEquals("1400/041125", result.get(0).getEventEticDateTime());
        assertEquals("MemDeskContact", result.get(0).getResMgrId());
        assertEquals("OST", result.get(0).getEventOST());
        assertEquals("EticRsnCd", result.get(0).getEventEticReasonCd());
        assertEquals("EticRsnComment", result.get(0).getEventEticReasonComment());
    }

    @Test
    public void testGetEventDetailView_Exception() throws Exception {
        String acn = "ACN123";
        String userId = "User1";

        doThrow(new RuntimeException("Internal server error")).when(eventRepository).getDetailListView(anyString());

        List<DetailViewData> result = eventListDetailViewService.getEventDetailView(acn, userId);

        assertEquals(0, result.size());
    }

    @Test
    public void testGetTFNotes_Success() {
        String eventId = "1";

        EventTfNotes eventTfNotes = new EventTfNotes();
        EventTfNotesPk eventTfNotesPk = new EventTfNotesPk();
        eventTfNotesPk.setEventId(1);
        eventTfNotes.setEventTfNotesPk(eventTfNotesPk);
        eventTfNotes.setTfNote("TFNotes");

        List<EventTfNotes> eventTfNotesList = new ArrayList<>();
        eventTfNotesList.add(eventTfNotes);

        when(tfNotesRepo.getAllTFNotes(anyInt())).thenReturn(eventTfNotesList);

        List<EventTfNotes> result = eventListDetailViewService.getTFNotes(eventId);

        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getEventTfNotesPk().getEventId());
        assertEquals("TFNotes", result.get(0).getTfNote());
    }

    @Test
    public void testGetTFNotes_Exception() {
        String eventId = "1";

        doThrow(new RuntimeException("Internal server error")).when(tfNotesRepo).getAllTFNotes(anyInt());

        List<EventTfNotes> result = eventListDetailViewService.getTFNotes(eventId);

        assertEquals(0, result.size());
    }
}
