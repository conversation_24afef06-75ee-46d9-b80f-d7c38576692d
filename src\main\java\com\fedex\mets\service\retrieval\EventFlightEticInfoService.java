package com.fedex.mets.service.retrieval;

import com.fedex.mets.dao.FlightEticView;
import com.fedex.mets.data.EventFlightEticData;
import com.fedex.mets.entity.mets.EventFlightDelays;
import com.fedex.mets.entity.mets.EventFlightInfo;
import com.fedex.mets.repository.mets.EventFlightDelaysRepository;
import com.fedex.mets.repository.mets.EventFlightInfoRepository;
import com.fedex.mets.repository.mets.EventsRepository;
import com.fedex.mets.util.ServerDateHelper;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class EventFlightEticInfoService {
	private static final Logger logger = LoggerFactory.getLogger(EventFlightEticInfoService.class);

	@Autowired
	private EventsRepository eventsRepository;

	@Autowired
	private EventFlightInfoRepository eventFlightInfoRepository;

	@Autowired
	private EventFlightDelaysRepository eventFlightDelaysRepository;


	/**
	 * The following getFlightEticDetail() is used to retreive the Flight number and dates for an aircraft.
	 *
	 * @return List of flightEticDetails.
	 * @params int eventId
	 */
	public EventFlightEticData getFlightEticDetail(int eventId) {
		logger.info("in the getFlightEticDetail Service....."+eventId);
		String flightNumber = "",
				flightDate = "";
		EventFlightEticData flightEticData = new EventFlightEticData();
		try {
			String flightEtic = eventsRepository.getFlightEtic(eventId);
			if(flightEtic!=null || flightEtic.trim().length() > 0){
				String[] resultData = flightEtic.split(",");
				flightEticData.setEventId(Integer.parseInt(resultData[0]));
				flightEticData.setInitialEtic(resultData[1]);
				flightEticData.setEticNumber(Integer.parseInt(resultData[2]));

				int tempEventId = flightEticData.getEventId();

				FlightEticView flightEticList = eventFlightInfoRepository.getFligthEticData(tempEventId);
				if(flightEticList!=null ) {
					flightEticData.setFlightNumber(flightEticList.getFlightNumber());
					flightEticData.setFlightDate(String.valueOf(flightEticList.getFlightDate()));
					flightEticData.setFlightLegNumber(flightEticList.getFlightLeg());
					flightEticData.setAcn(flightEticList.getFlightACN());

					flightEticData.setDestination(flightEticList.getFlightDestination());
					flightEticData.setOrigin(flightEticList.getFlightOrigin());
					flightEticData.setFlightStatus(flightEticList.getFlightStatus());
					flightEticData.setScheduledDeparture(String.valueOf(flightEticList.getFlightSchedDeptDateTime()));
					flightEticData.setActualDeparture(String.valueOf(flightEticList.getFlightActualDeptDateTime()));
					flightEticData.setTotalDelay(String.valueOf(flightEticList.getFlightTotalDelay()));
					flightEticData.setFlightType(flightEticList.getDescription());

					EventFlightInfo result = eventFlightInfoRepository.findByEventIdAndFltFlagI(tempEventId);
					if(result != null) {
						flightNumber = result.getFlightNumber();
						flightDate = String.valueOf(result.getFlightDate());
						if (flightDate.trim().length() > 0) {
							flightDate = flightDate.substring(8, 10);
						}
						flightEticData.setFltIn(flightNumber + "/" + flightDate);
						flightEticData.setArrival(String.valueOf(result.getFlightActualArrivalDateTime()));
					}
					else{
						logger.info("No FlightEticData found for eventId: " + tempEventId + " with FltFlagI");
					}
					EventFlightInfo result1 = eventFlightInfoRepository.findByEventIdAndFltFlagOAndFltFlagB(tempEventId);
					if(result1 != null){
						flightNumber = result1.getFlightNumber();
						flightDate = String.valueOf(result1.getFlightDate());
						if (flightDate.trim().length() > 0) {
							flightDate = flightDate.substring(8, 10);
						}
						flightEticData.setFltOut(flightNumber + "/" + flightDate);
						flightEticData.setDeparture(String.valueOf(result1.getFlightActualDeptDateTime()));
					}
					else{
						logger.info("No FlightEticData found for eventId: " + tempEventId + " with FltFlagO and FltFlagB");
					}
					List<EventFlightDelays> eventFlightDelays = eventFlightDelaysRepository.getAllByEventId(tempEventId);
					if(eventFlightDelays != null && eventFlightDelays.size() > 0) {
						String delayCode = "";
						for (EventFlightDelays eventFlightDelay : eventFlightDelays) {
							String code = eventFlightDelay.getDelayCode();
							int delayTime = eventFlightDelay.getDelayTime();

							String strDealyTime = ServerDateHelper.getFormatedTime(delayTime);

							delayCode += strDealyTime + " (" + code + ")" + ", ";
						}
						flightEticData.setDelayCodes(delayCode);
						logger.info("Retrieved FlightEticData: " + flightEticData);
					}
					else{
						logger.info("No FlightEticData found for eventId: " + tempEventId + " in EventFlightDelays");
					}
				}
				else{
					logger.info("No FlightEticData found for eventId: " + eventId);
				}
			}
			else{
				logger.info("No FlightEticData found for eventId: " + eventId);
			}
		} catch (Exception ee) {
			logger.warn("ERROR getFlightEticDetail() >> " + ee.getMessage());
			ee.printStackTrace();
		}
		return flightEticData;
	}
}
