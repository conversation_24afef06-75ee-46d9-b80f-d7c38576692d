
package com.fedex.mets.wsdl.discrepancy;

import java.math.BigDecimal;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for acnAircraftDetail complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="acnAircraftDetail">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="acn" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="fleet" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="series" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="fleetModelCd" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="model" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="registration" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="canadianAircraft" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/>
 *         &lt;element name="equipType" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="efvsFlag" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="etopsFlag" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="basisHours" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="basisCycles" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="lastTireChk" type="{http://www.w3.org/2001/XMLSchema}date" minOccurs="0"/>
 *         &lt;element name="lastServiceChk" type="{http://www.w3.org/2001/XMLSchema}date" minOccurs="0"/>
 *         &lt;element name="lastReliabilityChk" type="{http://www.w3.org/2001/XMLSchema}date" minOccurs="0"/>
 *         &lt;element name="rvsm" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="fans" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="rnpar" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="catStatus" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="tsn" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="csn" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="mgfDate" type="{http://www.w3.org/2001/XMLSchema}date" minOccurs="0"/>
 *         &lt;element name="serviceDate" type="{http://www.w3.org/2001/XMLSchema}date" minOccurs="0"/>
 *         &lt;element name="status" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="currentLocation" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="purchaseDate" type="{http://www.w3.org/2001/XMLSchema}date" minOccurs="0"/>
 *         &lt;element name="engineDesc" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="lastUpdtTime" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="llmExpDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="fltNbr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="fltDate" type="{http://www.w3.org/2001/XMLSchema}date" minOccurs="0"/>
 *         &lt;element name="legNbr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="legDest" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="legArrivalTm" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="showDccArd" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="etopsRestMin" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="acnAdditionalDetail" type="{http:///www.fedex.com/airops/schemas/Mach}aircraftAdditionalDetail"/>
 *         &lt;element name="legActualArrivalTime" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="tspCapableInd" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "acnAircraftDetail", propOrder = {
    "acn",
    "fleet",
    "series",
    "fleetModelCd",
    "model",
    "registration",
    "canadianAircraft",
    "equipType",
    "efvsFlag",
    "etopsFlag",
    "basisHours",
    "basisCycles",
    "lastTireChk",
    "lastServiceChk",
    "lastReliabilityChk",
    "rvsm",
    "fans",
    "rnpar",
    "catStatus",
    "tsn",
    "csn",
    "mgfDate",
    "serviceDate",
    "status",
    "currentLocation",
    "purchaseDate",
    "engineDesc",
    "lastUpdtTime",
    "llmExpDate",
    "fltNbr",
    "fltDate",
    "legNbr",
    "legDest",
    "legArrivalTm",
    "showDccArd",
    "etopsRestMin",
    "acnAdditionalDetail",
    "legActualArrivalTime",
    "tspCapableInd"
})
public class AcnAircraftDetail {

    protected String acn;
    protected String fleet;
    protected String series;
    protected String fleetModelCd;
    protected String model;
    protected String registration;
    protected Boolean canadianAircraft;
    protected String equipType;
    protected String efvsFlag;
    protected String etopsFlag;
    protected BigDecimal basisHours;
    protected BigDecimal basisCycles;
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar lastTireChk;
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar lastServiceChk;
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar lastReliabilityChk;
    protected String rvsm;
    protected String fans;
    protected String rnpar;
    protected String catStatus;
    protected BigDecimal tsn;
    protected BigDecimal csn;
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar mgfDate;
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar serviceDate;
    protected String status;
    protected String currentLocation;
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar purchaseDate;
    protected String engineDesc;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar lastUpdtTime;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar llmExpDate;
    @XmlElement(required = true)
    protected String fltNbr;
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar fltDate;
    @XmlElement(required = true)
    protected String legNbr;
    @XmlElement(required = true)
    protected String legDest;
    @XmlElement(required = true)
    protected BigDecimal legArrivalTm;
    protected boolean showDccArd;
    @XmlElement(required = true)
    protected BigDecimal etopsRestMin;
    @XmlElement(required = true)
    protected AircraftAdditionalDetail acnAdditionalDetail;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar legActualArrivalTime;
    protected String tspCapableInd;

    /**
     * Gets the value of the acn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAcn() {
        return acn;
    }

    /**
     * Sets the value of the acn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAcn(String value) {
        this.acn = value;
    }

    /**
     * Gets the value of the fleet property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFleet() {
        return fleet;
    }

    /**
     * Sets the value of the fleet property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFleet(String value) {
        this.fleet = value;
    }

    /**
     * Gets the value of the series property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSeries() {
        return series;
    }

    /**
     * Sets the value of the series property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSeries(String value) {
        this.series = value;
    }

    /**
     * Gets the value of the fleetModelCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFleetModelCd() {
        return fleetModelCd;
    }

    /**
     * Sets the value of the fleetModelCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFleetModelCd(String value) {
        this.fleetModelCd = value;
    }

    /**
     * Gets the value of the model property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getModel() {
        return model;
    }

    /**
     * Sets the value of the model property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setModel(String value) {
        this.model = value;
    }

    /**
     * Gets the value of the registration property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRegistration() {
        return registration;
    }

    /**
     * Sets the value of the registration property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRegistration(String value) {
        this.registration = value;
    }

    /**
     * Gets the value of the canadianAircraft property.
     * 
     * @return
     *     possible object is
     *     {@link Boolean }
     *     
     */
    public Boolean isCanadianAircraft() {
        return canadianAircraft;
    }

    /**
     * Sets the value of the canadianAircraft property.
     * 
     * @param value
     *     allowed object is
     *     {@link Boolean }
     *     
     */
    public void setCanadianAircraft(Boolean value) {
        this.canadianAircraft = value;
    }

    /**
     * Gets the value of the equipType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEquipType() {
        return equipType;
    }

    /**
     * Sets the value of the equipType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEquipType(String value) {
        this.equipType = value;
    }

    /**
     * Gets the value of the efvsFlag property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEfvsFlag() {
        return efvsFlag;
    }

    /**
     * Sets the value of the efvsFlag property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEfvsFlag(String value) {
        this.efvsFlag = value;
    }

    /**
     * Gets the value of the etopsFlag property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEtopsFlag() {
        return etopsFlag;
    }

    /**
     * Sets the value of the etopsFlag property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEtopsFlag(String value) {
        this.etopsFlag = value;
    }

    /**
     * Gets the value of the basisHours property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getBasisHours() {
        return basisHours;
    }

    /**
     * Sets the value of the basisHours property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setBasisHours(BigDecimal value) {
        this.basisHours = value;
    }

    /**
     * Gets the value of the basisCycles property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getBasisCycles() {
        return basisCycles;
    }

    /**
     * Sets the value of the basisCycles property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setBasisCycles(BigDecimal value) {
        this.basisCycles = value;
    }

    /**
     * Gets the value of the lastTireChk property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLastTireChk() {
        return lastTireChk;
    }

    /**
     * Sets the value of the lastTireChk property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLastTireChk(XMLGregorianCalendar value) {
        this.lastTireChk = value;
    }

    /**
     * Gets the value of the lastServiceChk property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLastServiceChk() {
        return lastServiceChk;
    }

    /**
     * Sets the value of the lastServiceChk property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLastServiceChk(XMLGregorianCalendar value) {
        this.lastServiceChk = value;
    }

    /**
     * Gets the value of the lastReliabilityChk property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLastReliabilityChk() {
        return lastReliabilityChk;
    }

    /**
     * Sets the value of the lastReliabilityChk property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLastReliabilityChk(XMLGregorianCalendar value) {
        this.lastReliabilityChk = value;
    }

    /**
     * Gets the value of the rvsm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRvsm() {
        return rvsm;
    }

    /**
     * Sets the value of the rvsm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRvsm(String value) {
        this.rvsm = value;
    }

    /**
     * Gets the value of the fans property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFans() {
        return fans;
    }

    /**
     * Sets the value of the fans property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFans(String value) {
        this.fans = value;
    }

    /**
     * Gets the value of the rnpar property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRnpar() {
        return rnpar;
    }

    /**
     * Sets the value of the rnpar property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRnpar(String value) {
        this.rnpar = value;
    }

    /**
     * Gets the value of the catStatus property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCatStatus() {
        return catStatus;
    }

    /**
     * Sets the value of the catStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCatStatus(String value) {
        this.catStatus = value;
    }

    /**
     * Gets the value of the tsn property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getTsn() {
        return tsn;
    }

    /**
     * Sets the value of the tsn property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setTsn(BigDecimal value) {
        this.tsn = value;
    }

    /**
     * Gets the value of the csn property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getCsn() {
        return csn;
    }

    /**
     * Sets the value of the csn property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setCsn(BigDecimal value) {
        this.csn = value;
    }

    /**
     * Gets the value of the mgfDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getMgfDate() {
        return mgfDate;
    }

    /**
     * Sets the value of the mgfDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setMgfDate(XMLGregorianCalendar value) {
        this.mgfDate = value;
    }

    /**
     * Gets the value of the serviceDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getServiceDate() {
        return serviceDate;
    }

    /**
     * Sets the value of the serviceDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setServiceDate(XMLGregorianCalendar value) {
        this.serviceDate = value;
    }

    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStatus(String value) {
        this.status = value;
    }

    /**
     * Gets the value of the currentLocation property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCurrentLocation() {
        return currentLocation;
    }

    /**
     * Sets the value of the currentLocation property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCurrentLocation(String value) {
        this.currentLocation = value;
    }

    /**
     * Gets the value of the purchaseDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getPurchaseDate() {
        return purchaseDate;
    }

    /**
     * Sets the value of the purchaseDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setPurchaseDate(XMLGregorianCalendar value) {
        this.purchaseDate = value;
    }

    /**
     * Gets the value of the engineDesc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEngineDesc() {
        return engineDesc;
    }

    /**
     * Sets the value of the engineDesc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEngineDesc(String value) {
        this.engineDesc = value;
    }

    /**
     * Gets the value of the lastUpdtTime property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLastUpdtTime() {
        return lastUpdtTime;
    }

    /**
     * Sets the value of the lastUpdtTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLastUpdtTime(XMLGregorianCalendar value) {
        this.lastUpdtTime = value;
    }

    /**
     * Gets the value of the llmExpDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLlmExpDate() {
        return llmExpDate;
    }

    /**
     * Sets the value of the llmExpDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLlmExpDate(XMLGregorianCalendar value) {
        this.llmExpDate = value;
    }

    /**
     * Gets the value of the fltNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFltNbr() {
        return fltNbr;
    }

    /**
     * Sets the value of the fltNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFltNbr(String value) {
        this.fltNbr = value;
    }

    /**
     * Gets the value of the fltDate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getFltDate() {
        return fltDate;
    }

    /**
     * Sets the value of the fltDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setFltDate(XMLGregorianCalendar value) {
        this.fltDate = value;
    }

    /**
     * Gets the value of the legNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLegNbr() {
        return legNbr;
    }

    /**
     * Sets the value of the legNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLegNbr(String value) {
        this.legNbr = value;
    }

    /**
     * Gets the value of the legDest property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLegDest() {
        return legDest;
    }

    /**
     * Sets the value of the legDest property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLegDest(String value) {
        this.legDest = value;
    }

    /**
     * Gets the value of the legArrivalTm property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getLegArrivalTm() {
        return legArrivalTm;
    }

    /**
     * Sets the value of the legArrivalTm property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setLegArrivalTm(BigDecimal value) {
        this.legArrivalTm = value;
    }

    /**
     * Gets the value of the showDccArd property.
     * 
     */
    public boolean isShowDccArd() {
        return showDccArd;
    }

    /**
     * Sets the value of the showDccArd property.
     * 
     */
    public void setShowDccArd(boolean value) {
        this.showDccArd = value;
    }

    /**
     * Gets the value of the etopsRestMin property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getEtopsRestMin() {
        return etopsRestMin;
    }

    /**
     * Sets the value of the etopsRestMin property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setEtopsRestMin(BigDecimal value) {
        this.etopsRestMin = value;
    }

    /**
     * Gets the value of the acnAdditionalDetail property.
     * 
     * @return
     *     possible object is
     *     {@link AircraftAdditionalDetail }
     *     
     */
    public AircraftAdditionalDetail getAcnAdditionalDetail() {
        return acnAdditionalDetail;
    }

    /**
     * Sets the value of the acnAdditionalDetail property.
     * 
     * @param value
     *     allowed object is
     *     {@link AircraftAdditionalDetail }
     *     
     */
    public void setAcnAdditionalDetail(AircraftAdditionalDetail value) {
        this.acnAdditionalDetail = value;
    }

    /**
     * Gets the value of the legActualArrivalTime property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLegActualArrivalTime() {
        return legActualArrivalTime;
    }

    /**
     * Sets the value of the legActualArrivalTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLegActualArrivalTime(XMLGregorianCalendar value) {
        this.legActualArrivalTime = value;
    }

    /**
     * Gets the value of the tspCapableInd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTspCapableInd() {
        return tspCapableInd;
    }

    /**
     * Sets the value of the tspCapableInd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTspCapableInd(String value) {
        this.tspCapableInd = value;
    }

}
