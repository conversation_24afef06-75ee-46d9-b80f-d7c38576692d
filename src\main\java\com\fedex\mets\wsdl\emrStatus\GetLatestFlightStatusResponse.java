package com.fedex.mets.wsdl.emrStatus;

import jakarta.xml.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;


/**
 * <p>Java class for anonymous complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;extension base="{http://www.fedex.com/airops/schemas/Common.xsd}GenericResponse">
 *       &lt;sequence>
 *         &lt;element name="statusInfo" type="{http://fedex.com/airops/emr/jaxws/services}flightStatusInfo" maxOccurs="unbounded"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "statusInfo"
})
@XmlRootElement(name = "getLatestFlightStatusResponse", namespace = "http://fedex.com/airops/emr/jaxws/services")
public class GetLatestFlightStatusResponse
        extends GenericResponse {

    @XmlElement(required = true,namespace = "http://fedex.com/airops/emr/jaxws/services")
    protected List<FlightStatusInfo> statusInfo;

    /**
     * Gets the value of the statusInfo property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the statusInfo property.
     *
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getStatusInfo().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link FlightStatusInfo }
     */
    public List<FlightStatusInfo> getStatusInfo() {
        if (statusInfo == null) {
            statusInfo = new ArrayList<FlightStatusInfo>();
        }
        return this.statusInfo;
    }

}
