package com.fedex.mets.repository;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import com.fedex.mets.config.MetsDataSourceConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.fedex.mets.data.EventNIWTimersDataEntity;
import com.fedex.mets.data.NIWTimersDataEntity;
import com.fedex.mets.entity.mets.EventTimers;
import com.fedex.mets.util.ServerDateHelper;

@Repository
public class NIWTimersUpdateRepository {

	@Autowired
	MetsDataSourceConfig metsDataSourceConfig;

//    private final JdbcTemplate metsJdbcTemplate;
//
//    public NIWTimersUpdateRepository(
//            @Qualifier("metsJdbcTemplate") JdbcTemplate metsJdbcTemplate) {
//
//        this.metsJdbcTemplate = metsJdbcTemplate;
//    }

	private static final Logger logger = LoggerFactory.getLogger(NIWTimersUpdateRepository.class);


	public List<Object> findNIWTimerDetails(int eventId, String timerId, String creationDateTime, String startDateTime, String stopDateTime) throws Exception {
		List<Object> result = new ArrayList<Object>();

		Statement stat = null;
		Connection conn = null;
		ResultSet rs = null;
		String queryString = "";

		try {
			conn = metsDataSourceConfig.metsDataSource().getConnection();
			stat = conn.createStatement();

			if (creationDateTime != null) {
				queryString =
						"select a.EVENT_ID, a.TIMER_ID, a.TIMER_START_DT_TM , a.TIMER_STOP_DT_TM, b.TIMER_NAME from EVENT_TIMERS a, TIMERS b where ((to_date('"
								+ startDateTime
								+ "','mm/dd/yy hh24:mi:ss') between a.timer_start_dt_tm and a.timer_stop_dt_tm) or (to_date('"
								+ stopDateTime
								+ "','mm/dd/yy hh24:mi:ss') between a.timer_start_dt_tm and a.timer_stop_dt_tm) or (to_date('"
								+ startDateTime
								+ "','mm/dd/yy hh24:mi:ss') <= a.timer_start_dt_tm and to_date('"
								+ stopDateTime
								+ "','mm/dd/yy hh24:mi:ss')>= a.timer_stop_dt_tm)) and a.creation_dt_tm <> to_date('"
								+ creationDateTime
								+ "','mm/dd/yy hh24:mi:ss') and a.event_id='"
								+ eventId
								+ "' and a.TIMER_ID=b.TIMER_ID";
			} else {
				queryString =
						"select a.EVENT_ID, a.TIMER_ID, a.TIMER_START_DT_TM , a.TIMER_STOP_DT_TM, b.TIMER_NAME from EVENT_TIMERS a, TIMERS b where ((to_date('"
								+ startDateTime
								+ "','mm/dd/yy hh24:mi:ss') between a.timer_start_dt_tm and a.timer_stop_dt_tm) or (to_date('"
								+ stopDateTime
								+ "','mm/dd/yy hh24:mi:ss') between a.timer_start_dt_tm and a.timer_stop_dt_tm) or (to_date('"
								+ startDateTime
								+ "','mm/dd/yy hh24:mi:ss') <= a.timer_start_dt_tm and to_date('"
								+ stopDateTime
								+ "','mm/dd/yy hh24:mi:ss')>= a.timer_stop_dt_tm)) and a.event_id='"
								+ eventId
								+ "' and a.TIMER_ID=b.TIMER_ID";
			}

			logger.info("queryString ========================" + queryString);

			rs = stat.executeQuery(queryString);

			while (rs.next()) {
				EventTimers existingTimer = new EventTimers();
				
				existingTimer.getEventTimersPk().setEventId(rs.getInt(1));
				existingTimer.setTimerId(rs.getString(2));
				existingTimer.setTimerStartDtTm(Timestamp.valueOf(rs.getString(3)));
				existingTimer.setTimerStopDtTm(Timestamp.valueOf(rs.getString(4)));

				result.add(existingTimer);
			}
		} catch (Exception e) {

			logger.warn(" ERROR NIW Timers findNIWTimerDetails()  >> " + e.getMessage());
			throw new Exception("findNIWTimerDetails() Problem finding the existing NIW Timers Data");

		} finally {
			try {
				if (rs != null)
					rs.close();

				if (stat != null)
					stat.close();

				if (conn != null)
					conn.close();

			} catch (Exception e) {
				logger.warn(" ERROR NIW Timers findNIWTimerDetails() closing connection >> " + e.getMessage());
			}
		}

		return result;
	}

	public int findLastUpdated(int eventId, String timerId, String creationDateTime, String lookupLastUpdatedDateTime) {

		Statement stat = null;
		Connection conn = null;
		ResultSet rs = null;
		String queryString = "";

		int lastUpdatedRecords = 0;
		try {
			conn = metsDataSourceConfig.metsDataSource().getConnection();
			stat = conn.createStatement();

			queryString =
					"Select count(*) from EVENT_TIMERS where EVENT_ID='"
							+ eventId
							+ "' and TIMER_ID='"
							+ timerId
							+ "' and CREATION_DT_TM = to_date('"
							+ creationDateTime
							+ "','mm/dd/yy hh24:mi:ss') and LAST_UPDATE_DT_TM > to_date('"
							+ lookupLastUpdatedDateTime
							+ "','mm/dd/yy hh24:mi:ss')";

			logger.info("Look up query ==========" + queryString);

			rs = stat.executeQuery(queryString);

			while (rs.next()) {

				lastUpdatedRecords = rs.getInt(1);
				logger.info("Last updated Records ========================" + lastUpdatedRecords);
			}

		} catch (Exception count) {

			logger.warn(" ERROR NIW Timers findLastUpdated() count >> " + count.getMessage());

		} finally {
			try {
				if (rs != null)
					rs.close();
				if (stat != null)
					stat.close();
				if (conn != null)
					conn.close();
			} catch (Exception e) {
				logger.warn(" ERROR NIW Timers findLastUpdated() closing connection >> " + e.getMessage());
			}
		}

		return lastUpdatedRecords;
	}

	public boolean updateNIWTimerDetails(int eventId, String timerId, Timestamp creationDateTime, Timestamp startDateTime, Timestamp stopDateTime) throws Exception {
		boolean result = false;

		logger.info("in the repository  == updateNIWTimerDetails()");
		logger.info("eventId ==" + eventId);
		logger.info("timerId ==" + timerId);
		logger.info("creationDateTime ==" + creationDateTime);
		logger.info("startDateTime ==" + startDateTime);
		logger.info("stopDateTime ==" + stopDateTime);

		Statement stat = null;
		Connection conn = null;
		String queryString = "";

		try {
			conn = metsDataSourceConfig.metsDataSource().getConnection();
			stat = conn.createStatement();

			Timestamp updateTime = ServerDateHelper.getTimeStamp();

			queryString = "UPDATE EVENT_TIMERS SET TIMER_START_DT_TM=" +
					"to_timestamp('" + startDateTime + "', 'yyyy-mm-dd hh24:mi:ss.ff')" + ", TIMER_STOP_DT_TM=" +
					"to_timestamp('" + stopDateTime + "', 'yyyy-mm-dd hh24:mi:ss.ff')" +  ", LAST_UPDATE_DT_TM=" +
					"to_timestamp('" + updateTime + "', 'yyyy-mm-dd hh24:mi:ss.ff')" + " WHERE EVENT_ID=" + eventId + " AND CREATION_DT_TM=" +
					"to_timestamp('" + creationDateTime + "', 'yyyy-mm-dd hh24:mi:ss.ff')" + " AND TIMER_ID=" + timerId;

			logger.info("updateQueryString ========================" + queryString);
			int recordsUpdated = stat.executeUpdate(queryString);

			if(recordsUpdated > 0)
				result = true;
			else
				result = false;

		} catch (Exception e) {

			result = false;
			logger.warn(" ERROR NIW Timers updateNIWTimerDetails() update >> " + e.getMessage());
			throw new Exception(e.getMessage());

		} finally {
			try {

				if (stat != null)
					stat.close();

				if (conn != null)
					conn.close();

			} catch (Exception e) {
				logger.warn(" ERROR NIW Timers deleteNIWTimerDetails() connection closing >> " + e.getMessage());
			}
		}

		return result;
	}

	public boolean deleteNIWTimerDetails(int eventId, String timerId, Timestamp creationDateTime, String userId, String tokenId, boolean isEventActive) throws Exception {
		boolean result = false;
		logger.info("in the bean  == deleteNIWTimerDetails()");
		logger.info("eventId ==" + eventId);
		logger.info("timerId ==" + timerId);
		logger.info("creationDateTime ==" + creationDateTime);

		securityCheck(userId, tokenId, isEventActive);

		Statement stat = null;
		Connection conn = null;
		String queryString = "";

		try {
			conn = metsDataSourceConfig.metsDataSource().getConnection();
			stat = conn.createStatement();

			queryString = "DELETE FROM EVENT_TIMERS WHERE EVENT_ID=" + eventId + " AND TIMER_ID=" + timerId + " AND CREATION_DT_TM = to_timestamp('" + creationDateTime + "', 'yyyy-mm-dd hh24:mi:ss.ff')";
			logger.info("updateQueryString ========================" + queryString);
			int recordsUpdated = stat.executeUpdate(queryString);

			if(recordsUpdated > 0)
				result = true;
			else
				result = false;

		} catch (Exception e) {
			result = false;
			logger.warn(" ERROR NIW Timers deleteNIWTimerDetails() delete >> " + e.getMessage());
			throw new Exception(e.getMessage());

		} finally {
			try {

				if (stat != null)
					stat.close();

				if (conn != null)
					conn.close();

			} catch (Exception e) {
				logger.warn(" ERROR NIW Timers deleteNIWTimerDetails() connection closing >> " + e.getMessage());
			}
		}

		return result;
	}

	public boolean addNIWTimerDetails(int eventId, String timerId, Timestamp creationDateTime,
									  Timestamp startDateTime, Timestamp stopDateTime) throws Exception {

		boolean result = false;

		logger.info("inside the method  == addNIWTimerDetails()");
		logger.info("eventId ==" + eventId);
		logger.info("timerId ==" + timerId);
		logger.info("creationDateTime ==" + creationDateTime);
		logger.info("startDateTime ==" + startDateTime);
		logger.info("stopDateTime ==" + stopDateTime);


		Timestamp updateTime = ServerDateHelper.getTimeStamp();

		Statement stat = null;
		Connection conn = null;
		String queryString = "";

		try {
			conn = metsDataSourceConfig.metsDataSource().getConnection();
			stat = conn.createStatement();

			queryString = "INSERT INTO EVENT_TIMERS VALUES(" + eventId +
					", to_timestamp('" + startDateTime + "', 'yyyy-mm-dd hh24:mi:ss.ff')" + ", to_timestamp('" + stopDateTime + "', 'yyyy-mm-dd hh24:mi:ss.ff')" + ", " + timerId +
					", to_timestamp('" + updateTime + "', 'yyyy-mm-dd hh24:mi:ss.ff')" + ", to_timestamp('" + updateTime + "', 'yyyy-mm-dd hh24:mi:ss.ff')" + ")";
			logger.info("updateQueryString ========================" + queryString);

			int recordsUpdated = stat.executeUpdate(queryString);

			if(recordsUpdated > 0)
				result = true;
			else
				result = false;

		} catch (Exception e) {
			result = false;
			logger.warn(" ERROR NIW Timers addNIWTimerDetails() create >> " + e.getMessage());
			throw new Exception(e.getMessage());

		} finally {
			try {

				if (stat != null)
					stat.close();

				if (conn != null)
					conn.close();

			} catch (Exception e) {
				logger.warn(" ERROR NIW Timers addNIWTimerDetails() connection closing >> " + e.getMessage());
			}
		}

		return result;
	}

	public boolean setNIWTimerDetails(String eventId, String timerId, String flag) throws Exception {
		boolean result = true;

		if (flag.equalsIgnoreCase("STOP") || flag.equalsIgnoreCase("START")) {

			Statement stat = null;
			Connection conn = null;
			ResultSet rs = null;
			String queryString = "";

			try {
				conn = metsDataSourceConfig.metsDataSource().getConnection();
				stat = conn.createStatement();
				Statement stat2 = conn.createStatement();

				queryString = "SELECT * FROM EVENT_TIMERS WHERE EVENT_ID=" + eventId + " AND TIMER_STOP_DT_TM IS NULL";
				logger.info("QueryString ========================" + queryString);

				rs = stat.executeQuery(queryString);

				while(rs.next()) {
					Timestamp stopDateTime = ServerDateHelper.getTimeStamp();
					logger.info("stopDateTime  after converting -------" + stopDateTime);
					logger.info("stopDateTime inserted in to the database  =  " + stopDateTime);

					queryString = "UPDATE EVENT_TIMERS SET TIMER_STOP_DT_TM=to_timestamp('" + stopDateTime + "', 'yyyy-mm-dd hh24:mi:ss.ff'), LAST_UPDATE_DT_TM=to_timestamp('"
							+ stopDateTime + "', 'yyyy-mm-dd hh24:mi:ss.ff') WHERE EVENT_ID=" + rs.getInt(1) + " AND TIMER_STOP_DT_TM IS NULL";

					int recordsUpdated = 0;
					try {
						recordsUpdated = stat2.executeUpdate(queryString);
					} catch(Exception e) {
						result = false;
						logger.info("inner try block setNIWTimers Excveption ", e.getMessage());
					} finally {
						if (stat2 != null)
							stat2.close();
					}

					if(recordsUpdated > 0) {
						logger.info("Existing NIW Timers update having null stop timer value");
					} else {
						logger.info("No Existing timers got updated");
					}
				}

			} catch (Exception e) {
				result = false;
				logger.warn(" ERROR NIW Timers setNIWTimerDetails() STOP update >> " + e.getMessage());
				throw new Exception(e.getMessage());

			} finally {
				try {

					if (stat != null)
						stat.close();

					if (conn != null)
						conn.close();

					if (rs != null)
						rs.close();

				} catch (Exception e) {
					logger.warn(" ERROR NIW Timers setNIWTimerDetails() connection closing >> " + e.getMessage());
				}
			}

		}

		if (flag.equalsIgnoreCase("START")) {
			Statement stat = null;
			Connection conn = null;
			String queryString = "";

			try {
				conn = metsDataSourceConfig.metsDataSource().getConnection();
				stat = conn.createStatement();

				Timestamp startDateTime = ServerDateHelper.getTimeStamp();
				logger.info("startDateTime  after converting -------" + startDateTime);

				queryString = "INSERT INTO EVENT_TIMERS(\"EVENT_ID\", \"TIMER_START_DT_TM\", \"TIMER_ID\", \"CREATION_DT_TM\", \"LAST_UPDATE_DT_TM\") VALUES(" + eventId +
						", to_timestamp('" + startDateTime + "', 'yyyy-mm-dd hh24:mi:ss.ff')" + ", " + timerId +
						", to_timestamp('" + startDateTime + "', 'yyyy-mm-dd hh24:mi:ss.ff')" + ", to_timestamp('" + startDateTime + "', 'yyyy-mm-dd hh24:mi:ss.ff')" + ")";
				logger.info("updateQueryString ========================" + queryString);

				int recordsUpdated = stat.executeUpdate(queryString);

				if(recordsUpdated > 0)
					result = true;
				else
					result = false;

			} catch (Exception e) {

				result = false;
				logger.warn(" ERROR NIW Timers setNIWTimerDetails() create >> " + e.getMessage());
				throw new Exception(e.getMessage());

			} finally {
				try {

					if (stat != null)
						stat.close();

					if (conn != null)
						conn.close();

				} catch (Exception e) {
					logger.warn(" ERROR NIW Timers setNIWTimerDetails() connection closing >> " + e.getMessage());
				}
			}
		}

		return result;
	}

	public void securityCheck(String userId, String tokenId, boolean isEventActive) throws Exception {

		try {

			List<Object> acessFlagList = null;

			if (userId != null && tokenId != null) {
				try{

//					acessFlagVector = SecurityHelper.getAccessFlags(userId, tokenId, strTransactionId);
					acessFlagList = new ArrayList<Object>();
					acessFlagList.add("SUCCESS");
					acessFlagList.add("80");

				} catch(Exception e){
					logger.warn("ERROR NIWTimersUpdateRepo setNIWTimers() SecurityHelper.getAccessFlags exception " + e.getMessage());
					throw new Exception("512");
				}
			}

			//for Start/Stop Timer check access flag 1 is 80 or 90 else throw exception
			if (acessFlagList != null) {
				String firstElement = (String) acessFlagList.get(0);

				if (firstElement.trim().equals("SUCCESS")) {
					if (isEventActive) {
						String strSecurityAccess = (String) acessFlagList.get(1);

						if (strSecurityAccess.trim().equals("80") || strSecurityAccess.trim().equals("90")) {

							logger.info(" ");
							logger.info("User " + userId + " has access to START/STOP Timer of an Event.");

						} else {
							throw new Exception("User does not have permission/access to START/STOP Timer of to an Event.");
						}

					} else {
						String strSecurityAccess = (String) acessFlagList.get(2);

						if (strSecurityAccess.trim().equals("99")) {

							logger.info(" ");
							logger.info("User " + userId + " has access to START/STOP Timer of an Event.");

						} else {
							throw new Exception("User does not have permission/access to to START/STOP Timer of an Inactive Event.");
						}
					}
				} else {

					if (firstElement.trim().equals("512")) {
						throw new Exception(firstElement);
					} else {
						String strSecurityAccessError = (String) acessFlagList.get(1);
						throw new Exception(strSecurityAccessError);
					}

				}
			} else if (acessFlagList == null) {
				throw new Exception("User does not have permission/access to START/STOP Timer of an Event.");
			}

		} catch (Exception securityRemote) {

			logger.warn(" ERROR NIW Timers setNIWTimerDetails() securityRemote >> " + securityRemote.getMessage());
			throw new Exception(securityRemote.getMessage());

		}

	}



}
