package com.fedex.mets.repository.mets;

import com.fedex.mets.dao.ReportCategoryKeyValues;
import com.fedex.mets.dao.ReportCategoriesActiveKeyValueData;
import com.fedex.mets.entity.mets.EventRepCatg;
import com.fedex.mets.entity.mets.EventRepCatgPk;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EventRepCatgRepository extends JpaRepository<EventRepCatg, EventRepCatgPk> {

    @Query("Select new com.fedex.mets.dao.ReportCategoryKeyValues(a.eventRepCatgPk.eventId, a.eventRepCatgPk.level1Id," +
            " a.eventRepCatgPk.level2Id,a.lastUpdatedDtTm, c.level2Name) from EventRepCatg a \n" +
            " LEFT JOIN RptCatgLevel1 b on a.eventRepCatgPk.level1Id=b.level1Id \n" +
            " LEFT JOIN RptCatgLevel2 c on a.eventRepCatgPk.level2Id=c.level2Id \n" +
            " where a.eventRepCatgPk.eventId=:eventId and b.groupId=:groupId\n" +
            " and b.activeCatg='Y'")
    public List<ReportCategoryKeyValues> getRepCategoryKeyValues(@Param("eventId") String eventId,@Param("groupId") String groupId);


    @Query(value = "Select * from EVENT_REP_CATG " +
            "where LEVEL_1_ID='5' and LEVEL_2_ID='99' \n" +
            "AND EVENT_ID in (:eventIDs)", nativeQuery = true)
    public List<EventRepCatg> getPowerPlantCount(@Param("eventIDs") List<Integer> eventIDs);


    @Query(value = "Select count(*) from EVENT_REP_CATG where EVENT_ID=:eventId \n" +
            "and LEVEL_1_ID=:levelOneId \n" +
            "and LEVEL_2_ID=:levelTwoId \n" +
            "and LAST_UPDATED_DT_TM > to_date(:lookupUpdatedTime,'mm/dd/yy hh24:mi:ss')", nativeQuery = true)
    public int findLastUpdatedRptCatg(@Param("eventId") String eventId,
                                      @Param("levelOneId") String levelOneId,
                                      @Param("levelTwoId") String levelTwoId,
                                      @Param("lookupUpdatedTime") String lookupUpdatedTime);

    @Query(value = "SELECT * FROM EVENT_REP_CATG WHERE EVENT_ID=:eventId \n" +
            "AND LEVEL_1_ID=:levelOneId AND LEVEL_2_ID=:levelTwoId \n", nativeQuery = true)
    public EventRepCatg findEventRepCatgByL1AndL2(@Param("eventId") String eventId,
                                                  @Param("levelOneId") String levelOneId,
                                                  @Param("levelTwoId") String levelTwoId);

}

