package com.fedex.mets.service.update;
import com.fedex.mets.data.MetsEventUpdateEntity;
import com.fedex.mets.entity.mets.EventTimers;
import com.fedex.mets.entity.mets.Timers;
import com.fedex.mets.repository.NIWTimersUpdateRepository;
import com.fedex.mets.service.retrieval.NIWTimersService;
import com.fedex.mets.util.IServerConstants;
import com.fedex.mets.util.ServerDateHelper;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@RequiredArgsConstructor
public class MetsNIWTimerDetailsService {

    private static final Logger logger = LoggerFactory.getLogger(MetsNIWTimerDetailsService.class);
    
    @Autowired
    private NIWTimersUpdateRepository niwtimersupdaterepository;

    @Autowired
    private NIWTimersService nIWTimersService;
    @Autowired
    private EventDiscrepanciesUpdateService eventDiscrepanciesUpdateService;

    public Map<String, Object> editNIWTimerDetails(MetsEventUpdateEntity request, Map<String, Object> hashMap,
                                                   EventTimers eventNIWTimerData,Boolean eventActive) throws Exception {

        List<EventTimers> elements = new ArrayList<>();
        boolean resultFromBean = false, isEventActive = true;

        if (eventActive != null) {
            isEventActive = eventActive.booleanValue();
        }

        List<Object> existingTimerList = new ArrayList<Object>();
        List<List<?>> detailElements = new ArrayList<>();
        List<Timers> timerDataList = new ArrayList<>();
        List<EventTimers> eventTimerDataList = new ArrayList<>();
        List<EventTimers> eventActiveTimerDataList = new ArrayList<>();

        int eventId = 0, lastUpdatedRecords = 0;
        String timerId = "",
                creationDateTime = "",
                startDateTime = "",
                stopDateTime = "",
                lastUpdatedDateTime = "",
                lookupCreationDateTime,
                lookupStartDateTime,
                lookupStopDateTime,
                lookupLastUpdatedTime;

        eventId = eventNIWTimerData.getEventTimersPk().getEventId();
        timerId = eventNIWTimerData.getTimerId();
        startDateTime = eventNIWTimerData.getTimerStartDtTm().toString();
        stopDateTime = eventNIWTimerData.getTimerStopDtTm().toString();
        creationDateTime = eventNIWTimerData.getEventTimersPk().getCreationDtTm().toString();
        lastUpdatedDateTime = eventNIWTimerData.getLastUpdateDtTm().toString();

        lookupCreationDateTime = ServerDateHelper.getLookUpFormat(creationDateTime);
        lookupStartDateTime = ServerDateHelper.getLookUpFormat(startDateTime);
        lookupLastUpdatedTime = ServerDateHelper.getLookUpFormat(lastUpdatedDateTime);

        String substituteStopDateTime = "";

        if ((stopDateTime == null) || (stopDateTime.trim().length() == 0)) {
            Date currentDate = new Date();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            dateFormat.setTimeZone(new SimpleTimeZone(0, "UTC"));
            substituteStopDateTime = dateFormat.format(currentDate);
        }


        try {

            if ((stopDateTime == null) || (stopDateTime.trim().length() == 0)) {
                lookupStopDateTime = ServerDateHelper.getLookUpFormat(substituteStopDateTime);
            } else {
                lookupStopDateTime = ServerDateHelper.getLookUpFormat(stopDateTime);
            }

            existingTimerList = niwtimersupdaterepository.findNIWTimerDetails(eventId, timerId, lookupCreationDateTime, lookupStartDateTime, lookupStopDateTime);

        } catch (Exception exec) {

            logger.warn("ERROR MetsUpdate Servlet editNIWTimerDetails() exec >> " + exec.getMessage());
            hashMap.put(IServerConstants.ERROR, "Could not find the Timers in the Database to UPDATE" + exec.getMessage());

        }

        if (existingTimerList.size() == 0) {
            try {
                try {

                    lastUpdatedRecords = niwtimersupdaterepository.findLastUpdated(eventId, timerId, lookupCreationDateTime, lookupLastUpdatedTime);

                } catch (Exception e) {
                    logger.warn("ERROR MetsUpdate Service editNIWTimerDetails() ee >> " + e.getMessage());
                }

                logger.info("in the service lastUpdatedRecords *************** " + lastUpdatedRecords);

                if (lastUpdatedRecords <= 0) {

                    Date creationDate = null, startDate = null, stopDate = null;
                    Timestamp creationTimeStamp = null, stopTimeStamp = null, startTimeStamp = null;
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

                    creationDate = dateFormat.parse(creationDateTime, new ParsePosition(0));
                    creationTimeStamp = new Timestamp(creationDate.getTime());

                    logger.info("creationDate ====" + creationDate);
                    logger.info("creationTimeStamp ====" + creationTimeStamp);

                    if (!(stopDateTime == null) && (stopDateTime.trim().length() > 0)) {

                        logger.info("stopDateTime ====" + stopDateTime);
                        stopDate = dateFormat.parse(stopDateTime, new ParsePosition(0));
                        stopTimeStamp = new Timestamp(stopDate.getTime());

                    }

                    startDate = dateFormat.parse(startDateTime, new ParsePosition(0));
                    startTimeStamp = new Timestamp(startDate.getTime());
                    logger.info("before calling the updateNIWTimerDetails method in the bean..................");
                    resultFromBean = niwtimersupdaterepository.updateNIWTimerDetails(eventId, timerId, creationTimeStamp, startTimeStamp, stopTimeStamp);
                }

            } catch (Exception exec) {

                logger.warn("ERROR MetsUpdate Service editNIWTimerDetails() exec >> " + exec.getMessage());
                String msg = exec.getMessage();
                if (msg.trim().length() >= 3) {

                    if (msg.substring(msg.length() - 3, msg.length()).equals("512")) {
                        hashMap.put(IServerConstants.ERROR, "512");
                    } else {
                        hashMap.put(IServerConstants.ERROR, "Could not find the Timers in the Database to UPDATE " + exec.getMessage());
                    }

                } else {
                    hashMap.put(IServerConstants.ERROR, "Could not find the Timers in the Database to UPDATE " + exec.getMessage());
                }
            }
        }

        if (resultFromBean) {

            try {

                String strEventId = "" + eventId;

                //this will get back only EventNIWTimerData Object

                elements = nIWTimersService.getNIWTimerDetails(strEventId, timerId);

                //this will get back EventNIWTimerData Object and the NIWTimerData Object

                detailElements = nIWTimersService.getNIWTimers(strEventId);

                //this is used to get the updated timer details on the parent detail window of the client.

                /******************Before publishing the Update******************/
                try {

                    logger.info("Before Publishing the NIW Timer Update results");
                    boolean isMessagePublished = publishUpdate(eventId, IServerConstants.NIW_TIMER_UPDATE);
                    logger.info("isMessagePublished >> " + isMessagePublished);

                } catch (Exception e) {
                    logger.warn("ERROR MetsUpdate Servlet editNIWTimerDetails() publish >> " + e.getMessage());
                }

            } catch (Exception exec) {

                logger.warn("ERROR MetsUpdate Servlet editNIWTimerDetails() exec 1 >> " + exec.getMessage());
                hashMap.put(IServerConstants.ERROR, exec.getMessage());

            }

        } else if (lastUpdatedRecords > 0) {
            hashMap.put(IServerConstants.ERROR, "Record has been updated prior to this transaction UPDATE");
        }

        if (detailElements != null && detailElements.size() == 3) {

            timerDataList = (List<Timers>) detailElements.get(0);
            eventActiveTimerDataList = (List<EventTimers>) detailElements.get(1);
            eventTimerDataList = (List<EventTimers>) detailElements.get(2);

        }

        logger.info("Existing results");
//		if (DEBUG_OUTPUT) {

        for (int j = 0; j < existingTimerList.size(); j++) {
            EventTimers data = (EventTimers) existingTimerList.get(j);
            logger.info("==>" + data.getEventTimersPk().getEventId()
                    + " -->> " + data.getTimerId() + " -->> " + data.getTimerStartDtTm()
                    + " -->> " + data.getTimerStopDtTm() + " -->> "
                    + " -->> " + data.getEventTimersPk().getCreationDtTm());
        }

        logger.info(" Updated results");

        List<EventTimers> data = elements;


        hashMap.put(IServerConstants.OVERLAP_TIMER_DETAILS, existingTimerList);
        hashMap.put(IServerConstants.UPDATED_TIMER_DETAILS, elements);
        hashMap.put(IServerConstants.NIW_TIMERS, timerDataList);
        hashMap.put(IServerConstants.EVENT_ACTIVE_NIW_TIMERS, eventActiveTimerDataList);
        hashMap.put(IServerConstants.EVENT_NIW_TIMERS, eventTimerDataList);

        return hashMap;
    }

    public Map<String, Object> deleteNIWTimerDetails(MetsEventUpdateEntity request, Map<String, Object> hashMap,
                                                     EventTimers eventNIWTimerData, String userId, String tokenId,Boolean eventActive) throws Exception {

        List<EventTimers> elements = new ArrayList<>();
        boolean resultFromBean = false, isEventActive = true;

        if (eventActive != null) {
            isEventActive = eventActive.booleanValue();
        }

        //the following four vectors added as the client requires to update the parent NIW detail window when a timer is deleted.
        List<List<?>> detailElements = new ArrayList<>();
        List<Timers> timerDataList = new ArrayList<>();
        List<EventTimers> eventTimerDataList = new ArrayList<>();
        List<EventTimers> eventActiveTimerDataList = new ArrayList<>();

        int eventId = 0, lastUpdatedRecords = 0;
        String timerId = "",
                creationDateTime = "",
                lastUpdatedDateTime = "",
                lookupLastUpdatedTime,
                lookupCreationTime = "";

        eventId = eventNIWTimerData.getEventTimersPk().getEventId();
        timerId = eventNIWTimerData.getTimerId();
        creationDateTime = eventNIWTimerData.getEventTimersPk().getCreationDtTm().toString();
        lastUpdatedDateTime = eventNIWTimerData.getLastUpdateDtTm().toString();

        lookupLastUpdatedTime = ServerDateHelper.getLookUpFormat(lastUpdatedDateTime);
        lookupCreationTime = ServerDateHelper.getLookUpFormat(creationDateTime);

        logger.info("creationDateTime=====" + creationDateTime);
        logger.info("lookupLastUpdatedTime=====" + lookupLastUpdatedTime);

        try {

            try {
                //to check if the record is updated prior to this transaction.
                lastUpdatedRecords = niwtimersupdaterepository.findLastUpdated(eventId, timerId, lookupCreationTime, lookupLastUpdatedTime);

            } catch (Exception e) {
                logger.warn("ERROR MetsUpdate Servlet deleteNIWTimerDetails() e >> " + e.getMessage());
            }

            if (lastUpdatedRecords == 0) {

                Date creationDate = null;
                Timestamp creationTimeStamp = null;
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

                creationDate = dateFormat.parse(creationDateTime, new ParsePosition(0));
                creationTimeStamp = new Timestamp(creationDate.getTime());

                logger.info("converted creationDate =====" + creationTimeStamp);
                logger.info("eventId =====" + eventId);
                logger.info("timerId =====" + timerId);

                logger.info("before calling the deleteNIWTimerDetails method in the bean..................");
                resultFromBean = niwtimersupdaterepository.deleteNIWTimerDetails(eventId, timerId, creationTimeStamp, userId, tokenId, isEventActive);
            }

        } catch (Exception exec) {

            logger.warn("ERROR MetsUpdate Servlet deleteNIWTimerDetails() exec >> " + exec.getMessage());
            String msg = exec.getMessage();
            if (msg.trim().length() >= 3) {

                if (msg.substring(msg.length() - 3, msg.length()).equals("512")) {
                    hashMap.put(IServerConstants.ERROR, "512");
                } else {
                    hashMap.put(IServerConstants.ERROR, "Could not find the Timers in the Database to DELETE " + exec.getMessage());
                }

            } else {
                hashMap.put(IServerConstants.ERROR, "Could not find the Timers in the Database to DELETE " + exec.getMessage());
            }
        }

        if (resultFromBean) {

            try {

                String strEventId = "" + eventId;

                //this will get back only EventNIWTimerData Object
                elements = nIWTimersService.getNIWTimerDetails(strEventId, timerId);
                //this will get back EventNIWTimerData Object and the NIWTimerData Object

                detailElements = nIWTimersService.getNIWTimers(strEventId);
                //this is used to get the updated timer details on the parent detail window of the client.

                /******************Before publishing the Update******************/
                try {

                    logger.info("Before Publishing the NIW Timer Update (3)");
                    boolean isMessagePublished = publishUpdate(eventId, IServerConstants.NIW_TIMER_UPDATE);
                    logger.info("isMessagePublished >> " + isMessagePublished);

                } catch (Exception e) {
                    logger.warn("ERROR MetsUpdate Servlet deleteNIWTimerDetails() publish >> " + e.getMessage());
                }
            } catch (Exception exec) {

                logger.warn("ERROR MetsUpdate Servlet deleteNIWTimerDetails() exec 1 >> " + exec.getMessage());
                hashMap.put(IServerConstants.ERROR, exec.getMessage());

            }

        } else if (lastUpdatedRecords > 0) {
            hashMap.put(IServerConstants.ERROR, "Record has been updated prior to this transaction DELETE");
        }

        if (detailElements != null && detailElements.size() == 3) {

            timerDataList = (List<Timers>) detailElements.get(0);
            eventActiveTimerDataList = (List<EventTimers>) detailElements.get(1);
            eventTimerDataList = (List<EventTimers>) detailElements.get(2);

        }

//		if (DEBUG_OUTPUT) {
        logger.info(" Updated results");

        List<EventTimers> data = new ArrayList<>();

        hashMap.put(IServerConstants.UPDATED_TIMER_DETAILS, elements);
        hashMap.put(IServerConstants.NIW_TIMERS, timerDataList);
        hashMap.put(IServerConstants.EVENT_ACTIVE_NIW_TIMERS, eventActiveTimerDataList);
        hashMap.put(IServerConstants.EVENT_NIW_TIMERS, eventTimerDataList);

        return hashMap;
    }

    public Map<String, Object> addNIWTimerDetails(MetsEventUpdateEntity request, Map<String, Object> hashMap,
                                                  EventTimers eventNIWTimerData,Boolean eventActive) throws Exception {

        List<EventTimers> elements = new ArrayList<>();
        boolean resultFromBean = false, isEventActive = true;

        if (eventActive != null) {
            isEventActive = eventActive.booleanValue();
        }

        List<Object> existingTimerList = new ArrayList<Object>();
        //the following four lists added as the client requires to update the parent NIW detail window when a timer is added.
        List<List<?>> detailElements = new ArrayList<>();
        List<Timers> timerDataList = new ArrayList<>();
        List<EventTimers> eventTimerDataList = new ArrayList<>();
        List<EventTimers> eventActiveTimerDataList = new ArrayList<>();

        int eventId = 0;
        String timerId = "",
                startDateTime = "",
                stopDateTime = "",
                lookupStartDateTime,
                lookupStopDateTime;

        eventId = eventNIWTimerData.getEventTimersPk().getEventId();
        timerId = eventNIWTimerData.getTimerId();
        startDateTime = eventNIWTimerData.getTimerStartDtTm().toString();
        stopDateTime = eventNIWTimerData.getTimerStopDtTm().toString();

        try {

            lookupStartDateTime = ServerDateHelper.getLookUpFormat(startDateTime);
            lookupStopDateTime = ServerDateHelper.getLookUpFormat(stopDateTime);

            //to check if there are any records overlapping with the new Start and Stop time.
            existingTimerList = niwtimersupdaterepository.findNIWTimerDetails(eventId, timerId, null, lookupStartDateTime, lookupStopDateTime);
        } catch (Exception exec) {

            logger.warn("ERROR MetsUpdate Service addNIWTimerDetails() exec >> " + exec.getMessage());
            hashMap.put(IServerConstants.ERROR, "Record already exists in the Database" + exec.getMessage());

        }

        if (existingTimerList.size() == 0) {

            try {

                Date startDate = null,
                        stopDate = null;
                Timestamp creationTimeStamp = null,
                        stopTimeStamp = null,
                        startTimeStamp = null;

                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

                startDate = dateFormat.parse(startDateTime, new ParsePosition(0));
                stopDate = dateFormat.parse(stopDateTime, new ParsePosition(0));

                stopTimeStamp = new Timestamp(stopDate.getTime());
                startTimeStamp = new Timestamp(startDate.getTime());

                resultFromBean = niwtimersupdaterepository.addNIWTimerDetails(eventId, timerId, creationTimeStamp, startTimeStamp, stopTimeStamp);

            } catch (Exception exec) {

                logger.warn("ERROR MetsUpdate Service addNIWTimerDetails() exec >> " + exec.getMessage());
                String msg = exec.getMessage();
                if (msg.trim().length() >= 3) {

                    if (msg.substring(msg.length() - 3, msg.length()).equals("512")) {
                        hashMap.put(IServerConstants.ERROR, "512");
                    } else {
                        hashMap.put(IServerConstants.ERROR, "Could not add the Timers in the Database " + exec.getMessage());
                    }
                } else {
                    hashMap.put(IServerConstants.ERROR, "Could not add the Timers in the Database " + exec.getMessage());
                }
            }
        }

        if (resultFromBean) {

            try {

                String strEventId = "" + eventId;

                //elements=niwTimersUpdateSession.getUpdatedTimers(eventId);	//this will get back only EventNIWTimerData Object
                elements = nIWTimersService.getNIWTimerDetails(strEventId, timerId);
                //this will get back EventNIWTimerData Object and the NIWTimerData Object

                detailElements = nIWTimersService.getNIWTimers(strEventId);
                //this is used to get the updated timer details on the parent detail window of the client.

                /******************Before publishing the Update******************/
                try {

                    logger.info("Before Publishing the NIW Timer Update (2)");
                    boolean isMessagePublished = publishUpdate(eventId, IServerConstants.NIW_TIMER_UPDATE);
                    logger.info("isMessagePublished >> " + isMessagePublished);

                } catch (Exception e) {
                    logger.warn("ERROR MetsUpdate Service addNIWTimerDetails() publish >> " + e.getMessage());
                }

            } catch (Exception exec) {

                logger.warn("ERROR MetsUpdate Service addNIWTimerDetails() exec 2 >> " + exec.getMessage());
                hashMap.put(IServerConstants.ERROR, exec.getMessage());

            }
        }

        if (detailElements != null && detailElements.size() == 3) {

            timerDataList = (List<Timers>) detailElements.get(0);
            eventActiveTimerDataList = (List<EventTimers>) detailElements.get(1);
            eventTimerDataList = (List<EventTimers>) detailElements.get(2);

        }

        logger.info(" Existing results");
//		if (DEBUG_OUTPUT) {
        for (int j = 0; j < existingTimerList.size(); j++) {
            EventTimers data = (EventTimers) existingTimerList.get(j);

            logger.info("==>" + data.getEventTimersPk().getEventId()
                    + " -->> " + data.getTimerId() + " -->> " + data.getTimerStartDtTm()
                    + " -->> " + data.getTimerStopDtTm() + " -->> "
                    + " -->> " + data.getEventTimersPk().getCreationDtTm());
        }

        logger.info(" Updated results");

        List<EventTimers> data = elements;

        hashMap.put(IServerConstants.OVERLAP_TIMER_DETAILS, existingTimerList);
        hashMap.put(IServerConstants.UPDATED_TIMER_DETAILS, elements);
        hashMap.put(IServerConstants.NIW_TIMERS, timerDataList);
        hashMap.put(IServerConstants.EVENT_ACTIVE_NIW_TIMERS, eventActiveTimerDataList);
        hashMap.put(IServerConstants.EVENT_NIW_TIMERS, eventTimerDataList);

        return hashMap;
    }

    public Map<String, Object> setNIWTimerDetails(MetsEventUpdateEntity request, Map<String, Object> hashMap,
                                                  String eventId, String timerId, String flag,Boolean eventActive) throws Exception {

        List<Timers> timerDataList = new ArrayList<>();
        List<EventTimers> eventActiveTimerDataList = new ArrayList<>();
        List<EventTimers> eventTimerDataList = new ArrayList<>();

        List<List<?>> elements = null;
        boolean resultFromBean = false, isEventActive = true;

        if (eventActive != null) {
            isEventActive = eventActive.booleanValue();
        }

        try {
            resultFromBean = niwtimersupdaterepository.setNIWTimerDetails(eventId, timerId, flag);
        } catch (Exception exec) {

            logger.warn("ERROR MetsUpdate Servlet setNIWTimerDetails() exec >> " + exec.getMessage());
            String msg = exec.getMessage();
            if (msg.trim().length() >= 3) {

                if (msg.substring(msg.length() - 3, msg.length()).equals("512")) {
                    hashMap.put(IServerConstants.ERROR, "512");
                } else {
                    hashMap.put(IServerConstants.ERROR, "Could not find the Timers in the Database to UPDATE " + exec.getMessage());
                }

            } else {
                hashMap.put(IServerConstants.ERROR, "Could not find the Timers in the Database to UPDATE " + exec.getMessage());
            }
        }

        if (resultFromBean) {
            try {
                //elements=niwTimersUpdateSession.getUpdatedTimers(eventId);	//this will get back only EventNIWTimerData Object
                elements = nIWTimersService.getNIWTimers(eventId);
                //this will get back EventNIWTimerData Object and the NIWTimerData Object
            } catch (Exception exec) {

                logger.warn("ERROR MetsUpdate Service setNIWTimerDetails() exec 1 >> " + exec.getMessage());
                hashMap.put(IServerConstants.ERROR, exec.getMessage());
            }

            /******************Before publishing the Update******************/
            try {

                logger.info("Before Publishing the NIW Timer Update (4)");
                boolean isMessagePublished = publishUpdate(Integer.parseInt(eventId), IServerConstants.NIW_TIMER_UPDATE);
                logger.info("isMessagePublished >> " + isMessagePublished);

            } catch (Exception e) {
                logger.warn("ERROR MetsUpdate Service setNIWTimerDetails() publish >> " + e.getMessage());
            }

        }

        if (elements != null) {

            timerDataList = (List<Timers>) elements.get(0);
            eventActiveTimerDataList = (List<EventTimers>) elements.get(1);
            eventTimerDataList = (List<EventTimers>) elements.get(2);
        }

//		if (DEBUG_OUTPUT) {
        for (int i = 0; i < eventActiveTimerDataList.size(); i++) {
            EventTimers data = (EventTimers) eventActiveTimerDataList.get(i);

            logger.info("==>" + data.getEventTimersPk().getEventId()
                    + " -->> " + data.getTimerId() + " -->> " + data.getTimerStartDtTm()
                    + " -->> " + data.getTimerStopDtTm());
        }

        for (int j = 0; j < eventTimerDataList.size(); j++) {
            EventTimers data = (EventTimers) eventTimerDataList.get(j);

            logger.info("=====>" + data.getEventTimersPk().getEventId()
                    + " -->> " + data.getTimerId() + " -->> " + data.getTimerStartDtTm()
                    + " -->> " + data.getTimerStopDtTm());
        }

        for (int k = 0; k < timerDataList.size(); k++) {
            Timers data = (Timers) timerDataList.get(k);

            logger.info("==>" + data.getTimerName() + " -->> " + data.getTimerDesc() + " -->> " + data.getTimerId());
        }
//		}

        hashMap.put(IServerConstants.NIW_TIMERS, timerDataList);
        hashMap.put(IServerConstants.EVENT_ACTIVE_NIW_TIMERS, eventActiveTimerDataList);
        hashMap.put(IServerConstants.EVENT_NIW_TIMERS, eventTimerDataList);

        return hashMap;
    }


    private boolean publishUpdate(int eventId, String updateType) throws Exception {
        boolean isMessagePublished = false;
        try {

            logger.info("Before publishing the Update on JMS");
            eventDiscrepanciesUpdateService.publishEventUpdate(eventId, updateType);
            isMessagePublished = true;

        } catch (Exception publishException) {
            logger.warn("ERROR MetsUpdate Servlet editNIWTimerDetails() publish >> " + publishException.getMessage());
        }
        return isMessagePublished;
    }


}
