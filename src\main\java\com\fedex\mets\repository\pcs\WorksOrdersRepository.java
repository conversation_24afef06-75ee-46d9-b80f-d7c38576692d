package com.fedex.mets.repository.pcs;

import com.fedex.mets.entity.pcs.WorksOrders;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface WorksOrdersRepository  extends JpaRepository<WorksOrders, Long> {

    @Query(value="SELECT * FROM WORKS_ORDERS WHERE MAXI_FIRST_BOOKING_DT_TM  is not NULL and WO_ACTUAL_RFM_DT_TM is NULL and AC_REGN =:acRegn",nativeQuery = true)
    public List<WorksOrders> findWORecords(@Param("acRegn") String acRegn);

    @Query(value="SELECT * FROM WORKS_ORDERS WHERE WO_FIRST_BOOKING_DT_TM  is NULL and WO_ACTUAL_RTM_DT_TM is not NULL and AC_REGN=:acRegn",nativeQuery = true)
    public List<WorksOrders> findWOWithRTMDateTime(@Param("acRegn") String acRegn);
}
