package com.fedex.mets.util;

public class DetailLineFormatTsiLmpi extends DetailLineFormat {
	
	public DetailLineFormatTsiLmpi(){
		super();
	}
	

	@Override
	protected void init() {
		sfd = new SentenceFormatDef("Update Line", 80, 6, 2, true);
		sfd.setMultiLine(true);

		addHeader();
		addDetailValues();
	}

	protected void addDetailValues() {
		FieldFormatDef ffd = new FieldFormatDef("Detail Lines", 72, true);
		sfd.getFields().add(ffd);
	}

	public void resetLines(boolean reset) {
		if (sfd != null) {
			sfd.resetLinesNums(reset);
		}
	}
}

