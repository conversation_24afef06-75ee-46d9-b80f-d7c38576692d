
package com.fedex.mets.wsdl.discrepancy;

import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;extension base="{http://www.fedex.com/airops/schemas/Common.xsd}GenericResponse">
 *       &lt;sequence>
 *         &lt;element name="workReleases" type="{http://www.fedex.com/airops/schemas/Planning.xsd}workRelseItem" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "workReleases"
})
@XmlRootElement(name = "getWorkRlseStandardResponse",namespace = "http:///www.fedex.com/airops/schemas/Mach")
public class GetWorkRlseStandardResponse
    extends GenericResponse
{

    protected List<WorkRelseItem> workReleases;

    /**
     * Gets the value of the workReleases property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the workReleases property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getWorkReleases().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link WorkRelseItem }
     * 
     * 
     */
    public List<WorkRelseItem> getWorkReleases() {
        if (workReleases == null) {
            workReleases = new ArrayList<WorkRelseItem>();
        }
        return this.workReleases;
    }

}
