
package com.fedex.mets.wsdl.discrepancy;

import java.math.BigDecimal;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for PidPredictionType complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="PidPredictionType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="pid" type="{http://www.fedex.com/airops/schemas/pid.xsd}PidType"/>
 *         &lt;element name="confidence" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="selected" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="selectedUserId" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="selectedSourceCd" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PidPredictionType", propOrder = {
    "pid",
    "confidence",
    "selected",
    "selectedUserId",
    "selectedSourceCd"
})
public class PidPredictionType {

    @XmlElement(required = true)
    protected PidType pid;
    @XmlElement(required = true)
    protected BigDecimal confidence;
    @XmlElement(required = true)
    protected String selected;
    protected String selectedUserId;
    protected String selectedSourceCd;

    /**
     * Gets the value of the pid property.
     * 
     * @return
     *     possible object is
     *     {@link PidType }
     *     
     */
    public PidType getPid() {
        return pid;
    }

    /**
     * Sets the value of the pid property.
     * 
     * @param value
     *     allowed object is
     *     {@link PidType }
     *     
     */
    public void setPid(PidType value) {
        this.pid = value;
    }

    /**
     * Gets the value of the confidence property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getConfidence() {
        return confidence;
    }

    /**
     * Sets the value of the confidence property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setConfidence(BigDecimal value) {
        this.confidence = value;
    }

    /**
     * Gets the value of the selected property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSelected() {
        return selected;
    }

    /**
     * Sets the value of the selected property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSelected(String value) {
        this.selected = value;
    }

    /**
     * Gets the value of the selectedUserId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSelectedUserId() {
        return selectedUserId;
    }

    /**
     * Sets the value of the selectedUserId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSelectedUserId(String value) {
        this.selectedUserId = value;
    }

    /**
     * Gets the value of the selectedSourceCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSelectedSourceCd() {
        return selectedSourceCd;
    }

    /**
     * Sets the value of the selectedSourceCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSelectedSourceCd(String value) {
        this.selectedSourceCd = value;
    }

}
