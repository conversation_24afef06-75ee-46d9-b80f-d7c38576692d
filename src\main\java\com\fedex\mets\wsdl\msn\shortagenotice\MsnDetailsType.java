
package com.fedex.mets.wsdl.msn.shortagenotice;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for msnDetailsType complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="msnDetailsType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="shortageNoticeNumber" type="{http://www.w3.org/2001/XMLSchema}long"/>
 *         &lt;element name="aircraft" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="cpn" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="quantity" type="{http://www.w3.org/2001/XMLSchema}long"/>
 *         &lt;element name="mpn" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="enteredMpn" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="followUpCode" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="shipToStation" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="shipToDept" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="status" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="category" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="resFailureCd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="discrepancyNumber" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="sequenceOrNonRoutine" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="workorderNumber" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="timeRemaining" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="requestedById" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="requestedByName" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="requestedByPhone" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="datePartNeedBy" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="timePartNeedBy" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="taskNbr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="transTm" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="station" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ataChap" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ataSubChap" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="shortageNoticeIsn" type="{http://www.w3.org/2001/XMLSchema}long"/>
 *         &lt;element name="refIpc" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="refTypeNbr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="mpnHasMultipleCpns" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="bohDiscrpCd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="interchangeCpn" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="previousFollowUpCode" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="printerId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="discrepancyOid" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="prevResolutionDate" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="prevResolutionTime" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="maintType" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="fanBlade" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="requiredByDateTime" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "msnDetailsType", namespace = "http://fedex.com/airops/maxi/services/jaxws/shortagenotice", propOrder = {
    "shortageNoticeNumber",
    "aircraft",
    "cpn",
    "quantity",
    "mpn",
    "enteredMpn",
    "followUpCode",
    "shipToStation",
    "shipToDept",
    "status",
    "category",
    "resFailureCd",
    "discrepancyNumber",
    "sequenceOrNonRoutine",
    "workorderNumber",
    "timeRemaining",
    "requestedById",
    "requestedByName",
    "requestedByPhone",
    "datePartNeedBy",
    "timePartNeedBy",
    "taskNbr",
    "transTm",
    "station",
    "ataChap",
    "ataSubChap",
    "shortageNoticeIsn",
    "refIpc",
    "refTypeNbr",
    "mpnHasMultipleCpns",
    "bohDiscrpCd",
    "interchangeCpn",
    "previousFollowUpCode",
    "printerId",
    "discrepancyOid",
    "prevResolutionDate",
    "prevResolutionTime",
    "maintType",
    "fanBlade",
    "requiredByDateTime"
})
public class MsnDetailsType {

    protected long shortageNoticeNumber;
    @XmlElement(required = true)
    protected String aircraft;
    @XmlElement(required = true)
    protected String cpn;
    protected long quantity;
    @XmlElement(required = true)
    protected String mpn;
    @XmlElement(required = true)
    protected String enteredMpn;
    @XmlElement(required = true)
    protected String followUpCode;
    @XmlElement(required = true)
    protected String shipToStation;
    @XmlElement(required = true)
    protected String shipToDept;
    @XmlElement(required = true)
    protected String status;
    @XmlElement(required = true)
    protected String category;
    @XmlElement(required = true)
    protected String resFailureCd;
    @XmlElement(required = true)
    protected String discrepancyNumber;
    @XmlElement(required = true)
    protected String sequenceOrNonRoutine;
    @XmlElement(required = true)
    protected String workorderNumber;
    @XmlElement(required = true)
    protected String timeRemaining;
    @XmlElement(required = true)
    protected String requestedById;
    @XmlElement(required = true)
    protected String requestedByName;
    @XmlElement(required = true)
    protected String requestedByPhone;
    @XmlElement(required = true)
    protected String datePartNeedBy;
    @XmlElement(required = true)
    protected String timePartNeedBy;
    @XmlElement(required = true)
    protected String taskNbr;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar transTm;
    @XmlElement(required = true)
    protected String station;
    @XmlElement(required = true)
    protected String ataChap;
    @XmlElement(required = true)
    protected String ataSubChap;
    protected long shortageNoticeIsn;
    @XmlElement(required = true)
    protected String refIpc;
    @XmlElement(required = true)
    protected String refTypeNbr;
    protected boolean mpnHasMultipleCpns;
    @XmlElement(required = true)
    protected String bohDiscrpCd;
    @XmlElement(required = true)
    protected String interchangeCpn;
    @XmlElement(required = true)
    protected String previousFollowUpCode;
    @XmlElement(required = true)
    protected String printerId;
    @XmlElement(required = true)
    protected String discrepancyOid;
    @XmlElement(required = true)
    protected String prevResolutionDate;
    @XmlElement(required = true)
    protected String prevResolutionTime;
    @XmlElement(required = true)
    protected String maintType;
    protected boolean fanBlade;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar requiredByDateTime;

    /**
     * Gets the value of the shortageNoticeNumber property.
     * 
     */
    public long getShortageNoticeNumber() {
        return shortageNoticeNumber;
    }

    /**
     * Sets the value of the shortageNoticeNumber property.
     * 
     */
    public void setShortageNoticeNumber(long value) {
        this.shortageNoticeNumber = value;
    }

    /**
     * Gets the value of the aircraft property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAircraft() {
        return aircraft;
    }

    /**
     * Sets the value of the aircraft property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAircraft(String value) {
        this.aircraft = value;
    }

    /**
     * Gets the value of the cpn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCpn() {
        return cpn;
    }

    /**
     * Sets the value of the cpn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCpn(String value) {
        this.cpn = value;
    }

    /**
     * Gets the value of the quantity property.
     * 
     */
    public long getQuantity() {
        return quantity;
    }

    /**
     * Sets the value of the quantity property.
     * 
     */
    public void setQuantity(long value) {
        this.quantity = value;
    }

    /**
     * Gets the value of the mpn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMpn() {
        return mpn;
    }

    /**
     * Sets the value of the mpn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMpn(String value) {
        this.mpn = value;
    }

    /**
     * Gets the value of the enteredMpn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEnteredMpn() {
        return enteredMpn;
    }

    /**
     * Sets the value of the enteredMpn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEnteredMpn(String value) {
        this.enteredMpn = value;
    }

    /**
     * Gets the value of the followUpCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFollowUpCode() {
        return followUpCode;
    }

    /**
     * Sets the value of the followUpCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFollowUpCode(String value) {
        this.followUpCode = value;
    }

    /**
     * Gets the value of the shipToStation property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getShipToStation() {
        return shipToStation;
    }

    /**
     * Sets the value of the shipToStation property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setShipToStation(String value) {
        this.shipToStation = value;
    }

    /**
     * Gets the value of the shipToDept property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getShipToDept() {
        return shipToDept;
    }

    /**
     * Sets the value of the shipToDept property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setShipToDept(String value) {
        this.shipToDept = value;
    }

    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStatus(String value) {
        this.status = value;
    }

    /**
     * Gets the value of the category property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCategory() {
        return category;
    }

    /**
     * Sets the value of the category property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCategory(String value) {
        this.category = value;
    }

    /**
     * Gets the value of the resFailureCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getResFailureCd() {
        return resFailureCd;
    }

    /**
     * Sets the value of the resFailureCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setResFailureCd(String value) {
        this.resFailureCd = value;
    }

    /**
     * Gets the value of the discrepancyNumber property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDiscrepancyNumber() {
        return discrepancyNumber;
    }

    /**
     * Sets the value of the discrepancyNumber property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDiscrepancyNumber(String value) {
        this.discrepancyNumber = value;
    }

    /**
     * Gets the value of the sequenceOrNonRoutine property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSequenceOrNonRoutine() {
        return sequenceOrNonRoutine;
    }

    /**
     * Sets the value of the sequenceOrNonRoutine property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSequenceOrNonRoutine(String value) {
        this.sequenceOrNonRoutine = value;
    }

    /**
     * Gets the value of the workorderNumber property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getWorkorderNumber() {
        return workorderNumber;
    }

    /**
     * Sets the value of the workorderNumber property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setWorkorderNumber(String value) {
        this.workorderNumber = value;
    }

    /**
     * Gets the value of the timeRemaining property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTimeRemaining() {
        return timeRemaining;
    }

    /**
     * Sets the value of the timeRemaining property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTimeRemaining(String value) {
        this.timeRemaining = value;
    }

    /**
     * Gets the value of the requestedById property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRequestedById() {
        return requestedById;
    }

    /**
     * Sets the value of the requestedById property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRequestedById(String value) {
        this.requestedById = value;
    }

    /**
     * Gets the value of the requestedByName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRequestedByName() {
        return requestedByName;
    }

    /**
     * Sets the value of the requestedByName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRequestedByName(String value) {
        this.requestedByName = value;
    }

    /**
     * Gets the value of the requestedByPhone property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRequestedByPhone() {
        return requestedByPhone;
    }

    /**
     * Sets the value of the requestedByPhone property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRequestedByPhone(String value) {
        this.requestedByPhone = value;
    }

    /**
     * Gets the value of the datePartNeedBy property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDatePartNeedBy() {
        return datePartNeedBy;
    }

    /**
     * Sets the value of the datePartNeedBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDatePartNeedBy(String value) {
        this.datePartNeedBy = value;
    }

    /**
     * Gets the value of the timePartNeedBy property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTimePartNeedBy() {
        return timePartNeedBy;
    }

    /**
     * Sets the value of the timePartNeedBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTimePartNeedBy(String value) {
        this.timePartNeedBy = value;
    }

    /**
     * Gets the value of the taskNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTaskNbr() {
        return taskNbr;
    }

    /**
     * Sets the value of the taskNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTaskNbr(String value) {
        this.taskNbr = value;
    }

    /**
     * Gets the value of the transTm property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getTransTm() {
        return transTm;
    }

    /**
     * Sets the value of the transTm property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setTransTm(XMLGregorianCalendar value) {
        this.transTm = value;
    }

    /**
     * Gets the value of the station property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStation() {
        return station;
    }

    /**
     * Sets the value of the station property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStation(String value) {
        this.station = value;
    }

    /**
     * Gets the value of the ataChap property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAtaChap() {
        return ataChap;
    }

    /**
     * Sets the value of the ataChap property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAtaChap(String value) {
        this.ataChap = value;
    }

    /**
     * Gets the value of the ataSubChap property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAtaSubChap() {
        return ataSubChap;
    }

    /**
     * Sets the value of the ataSubChap property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAtaSubChap(String value) {
        this.ataSubChap = value;
    }

    /**
     * Gets the value of the shortageNoticeIsn property.
     * 
     */
    public long getShortageNoticeIsn() {
        return shortageNoticeIsn;
    }

    /**
     * Sets the value of the shortageNoticeIsn property.
     * 
     */
    public void setShortageNoticeIsn(long value) {
        this.shortageNoticeIsn = value;
    }

    /**
     * Gets the value of the refIpc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRefIpc() {
        return refIpc;
    }

    /**
     * Sets the value of the refIpc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRefIpc(String value) {
        this.refIpc = value;
    }

    /**
     * Gets the value of the refTypeNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRefTypeNbr() {
        return refTypeNbr;
    }

    /**
     * Sets the value of the refTypeNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRefTypeNbr(String value) {
        this.refTypeNbr = value;
    }

    /**
     * Gets the value of the mpnHasMultipleCpns property.
     * 
     */
    public boolean isMpnHasMultipleCpns() {
        return mpnHasMultipleCpns;
    }

    /**
     * Sets the value of the mpnHasMultipleCpns property.
     * 
     */
    public void setMpnHasMultipleCpns(boolean value) {
        this.mpnHasMultipleCpns = value;
    }

    /**
     * Gets the value of the bohDiscrpCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBohDiscrpCd() {
        return bohDiscrpCd;
    }

    /**
     * Sets the value of the bohDiscrpCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBohDiscrpCd(String value) {
        this.bohDiscrpCd = value;
    }

    /**
     * Gets the value of the interchangeCpn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getInterchangeCpn() {
        return interchangeCpn;
    }

    /**
     * Sets the value of the interchangeCpn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setInterchangeCpn(String value) {
        this.interchangeCpn = value;
    }

    /**
     * Gets the value of the previousFollowUpCode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPreviousFollowUpCode() {
        return previousFollowUpCode;
    }

    /**
     * Sets the value of the previousFollowUpCode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPreviousFollowUpCode(String value) {
        this.previousFollowUpCode = value;
    }

    /**
     * Gets the value of the printerId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPrinterId() {
        return printerId;
    }

    /**
     * Sets the value of the printerId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPrinterId(String value) {
        this.printerId = value;
    }

    /**
     * Gets the value of the discrepancyOid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDiscrepancyOid() {
        return discrepancyOid;
    }

    /**
     * Sets the value of the discrepancyOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDiscrepancyOid(String value) {
        this.discrepancyOid = value;
    }

    /**
     * Gets the value of the prevResolutionDate property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPrevResolutionDate() {
        return prevResolutionDate;
    }

    /**
     * Sets the value of the prevResolutionDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPrevResolutionDate(String value) {
        this.prevResolutionDate = value;
    }

    /**
     * Gets the value of the prevResolutionTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPrevResolutionTime() {
        return prevResolutionTime;
    }

    /**
     * Sets the value of the prevResolutionTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPrevResolutionTime(String value) {
        this.prevResolutionTime = value;
    }

    /**
     * Gets the value of the maintType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMaintType() {
        return maintType;
    }

    /**
     * Sets the value of the maintType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMaintType(String value) {
        this.maintType = value;
    }

    /**
     * Gets the value of the fanBlade property.
     * 
     */
    public boolean isFanBlade() {
        return fanBlade;
    }

    /**
     * Sets the value of the fanBlade property.
     * 
     */
    public void setFanBlade(boolean value) {
        this.fanBlade = value;
    }

    /**
     * Gets the value of the requiredByDateTime property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getRequiredByDateTime() {
        return requiredByDateTime;
    }

    /**
     * Sets the value of the requiredByDateTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setRequiredByDateTime(XMLGregorianCalendar value) {
        this.requiredByDateTime = value;
    }

}
