package com.fedex.mets.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class JsonFileUtilTest {

    @Mock
    private ObjectMapper mockObjectMapper;

    private JsonFileUtil jsonFileUtil;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        jsonFileUtil = new JsonFileUtil(mockObjectMapper);
    }

    @Test
    void testSaveToJsonFile_Success() throws IOException {
        Map<String, String> testData = new HashMap<>();
        testData.put("key1", "value1");
        testData.put("key2", "value2");

        ObjectWriter writer = mock(ObjectWriter.class);
        when(mockObjectMapper.writerWithDefaultPrettyPrinter()).thenReturn(writer);

        String filePath = tempDir.resolve("test.json").toString();
        boolean result = jsonFileUtil.saveToJsonFile(filePath, testData);

        assertTrue(result);
        verify(mockObjectMapper).writerWithDefaultPrettyPrinter();
    }

    @Test
    void testSaveToJsonFile_Exception() throws IOException {
        Map<String, String> testData = new HashMap<>();
        testData.put("key1", "value1");

        ObjectWriter writer = mock(ObjectWriter.class);
        when(mockObjectMapper.writerWithDefaultPrettyPrinter()).thenReturn(writer);
        doThrow(new IOException("Test exception")).when(writer).writeValue(any(File.class), any());

        String filePath = tempDir.resolve("test.json").toString();
        boolean result = jsonFileUtil.saveToJsonFile(filePath, testData);

        assertFalse(result);
        verify(mockObjectMapper).writerWithDefaultPrettyPrinter();
        verify(writer).writeValue(any(File.class), any());
    }

    @Test
    void testReadFromJsonFile_Success() throws IOException {
        Map<String, String> expectedData = new HashMap<>();
        expectedData.put("key1", "value1");

        when(mockObjectMapper.readValue(any(File.class), eq(Map.class))).thenReturn(expectedData);

        File tempFile = tempDir.resolve("test.json").toFile();
        tempFile.createNewFile();

        Map<?, ?> result = jsonFileUtil.readFromJsonFile(tempFile.getAbsolutePath(), Map.class);

        assertNotNull(result);
        assertEquals(expectedData, result);
        verify(mockObjectMapper, times(1)).readValue(any(File.class), eq(Map.class));
    }

    @Test
    void testReadFromJsonFile_FileNotExists() throws IOException {
        String filePath = tempDir.resolve("nonexistent.json").toString();
        Map<?, ?> result = jsonFileUtil.readFromJsonFile(filePath, Map.class);

        assertNull(result);
        verify(mockObjectMapper, never()).readValue(any(File.class), eq(Map.class));
    }

    @Test
    void testReadFromJsonFile_Exception() throws IOException {
        when(mockObjectMapper.readValue(any(File.class), eq(Map.class))).thenThrow(new IOException("Test exception"));

        File tempFile = tempDir.resolve("test.json").toFile();
        tempFile.createNewFile();

        Map<?, ?> result = jsonFileUtil.readFromJsonFile(tempFile.getAbsolutePath(), Map.class);

        assertNull(result);
        verify(mockObjectMapper, times(1)).readValue(any(File.class), eq(Map.class));
    }
}
