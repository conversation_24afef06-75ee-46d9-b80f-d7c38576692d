
package com.fedex.mets.wsdl.msn.shortagenotice;

import java.util.ArrayList;
import java.util.List;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;



/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="status" type="{http://fedex.com/airops/maxi/services/jaxws}resultType"/>
 *         &lt;element name="header" type="{http://fedex.com/airops/maxi/services/jaxws}HeaderType"/>
 *         &lt;element name="shippingInfo" type="{http://fedex.com/airops/maxi/services/jaxws/material}shortageNoticeShipperType" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="shippingInfoTreeData" type="{http://fedex.com/airops/maxi/services/jaxws/material}shortageNoticeShipperType" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "status",
    "header",
    "shippingInfo",
    "shippingInfoTreeData"
})
@XmlRootElement(name = "getMsnShippingInfoResponse", namespace = "http://fedex.com/airops/maxi/services/jaxws/shortagenotice")
public class GetMsnShippingInfoResponse {

    @XmlElement(namespace = "http://fedex.com/airops/maxi/services/jaxws/shortagenotice", required = true)
    protected ResultType status;
    @XmlElement(namespace = "http://fedex.com/airops/maxi/services/jaxws/shortagenotice", required = true)
    protected HeaderType header;
    @XmlElement(namespace = "http://fedex.com/airops/maxi/services/jaxws/shortagenotice")
    protected List<ShortageNoticeShipperType> shippingInfo;
    @XmlElement(namespace = "http://fedex.com/airops/maxi/services/jaxws/shortagenotice")
    protected List<ShortageNoticeShipperType> shippingInfoTreeData;

    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link ResultType }
     *     
     */
    public ResultType getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link ResultType }
     *     
     */
    public void setStatus(ResultType value) {
        this.status = value;
    }

    /**
     * Gets the value of the header property.
     * 
     * @return
     *     possible object is
     *     {@link HeaderType }
     *     
     */
    public HeaderType getHeader() {
        return header;
    }

    /**
     * Sets the value of the header property.
     * 
     * @param value
     *     allowed object is
     *     {@link HeaderType }
     *     
     */
    public void setHeader(HeaderType value) {
        this.header = value;
    }

    /**
     * Gets the value of the shippingInfo property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the shippingInfo property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getShippingInfo().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link ShortageNoticeShipperType }
     * 
     * 
     */
    public List<ShortageNoticeShipperType> getShippingInfo() {
        if (shippingInfo == null) {
            shippingInfo = new ArrayList<ShortageNoticeShipperType>();
        }
        return this.shippingInfo;
    }

    /**
     * Gets the value of the shippingInfoTreeData property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the shippingInfoTreeData property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getShippingInfoTreeData().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link ShortageNoticeShipperType }
     * 
     * 
     */
    public List<ShortageNoticeShipperType> getShippingInfoTreeData() {
        if (shippingInfoTreeData == null) {
            shippingInfoTreeData = new ArrayList<ShortageNoticeShipperType>();
        }
        return this.shippingInfoTreeData;
    }

}
