package com.fedex.mets.service.retrieval;

import com.fedex.mets.config.MaxiSoapClientConfig;
import com.fedex.mets.data.ShortageNoticeData;
import com.fedex.mets.dto.MetsRequest;
import com.fedex.mets.util.IServerConstants;
import com.fedex.mets.wsdl.msn.AuthSourceSysType;
import com.fedex.mets.wsdl.msn.shortagenotice.*;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Service
@RequiredArgsConstructor
public class MsnDetailService {

    private static final Logger logger = LoggerFactory.getLogger(MsnDetailService.class);


    @Autowired
    private MaxiSoapClientConfig wb;

    /**
     * Private method to get the Shortage Notice Details for a ACN.
     *
     * @return  List of Object.
     * @params String acn,String userId.
     */
    @SuppressWarnings("unchecked")
    public List<ShortageNoticeData> getShortageNoticeDetails(
            String acn,
            String userId) {

        logger.info("Getting Shortage Notice details for Aircraft >" + acn);
        List<ShortageNoticeData> shortageNoticeList = new ArrayList<>();
        try {
            GetShortageNoticeListResponse response= getShortageNoticeListResponse(
                    acn,userId);
            if(response!=null && response.getShortageNoticeList()!=null && response.getShortageNoticeList().size()>0){
                for(int i=0;i<response.getShortageNoticeList().size();i++)
                {
                    ShortageNoticeData shortageNotice = new ShortageNoticeData();
                    shortageNotice.setMsn(response.getShortageNoticeList().get(i).getMsn());
                    shortageNotice.setAcn(response.getShortageNoticeList().get(i).getAircraft());
                    shortageNotice.setStatusCode(response.getShortageNoticeList().get(i).getStatusCd());
                    if (response.getShortageNoticeList().get(i).getFollowUpCd().equals("0")) {
                        shortageNotice.setFollowUpCode("NEW ENTRY");
                    } else if (response.getShortageNoticeList().get(i).getFollowUpCd().equals("1")) {
                        shortageNotice.setFollowUpCode("PURCH/EXP");
                    } else if (response.getShortageNoticeList().get(i).getFollowUpCd().equals("2")) {
                        shortageNotice.setFollowUpCode("HOLD/INFO");
                    } else if (response.getShortageNoticeList().get(i).getFollowUpCd().equals("3")) {
                        shortageNotice.setFollowUpCode("ON ORDER");
                    } else if (response.getShortageNoticeList().get(i).getFollowUpCd().equals("4")) {
                        shortageNotice.setFollowUpCode("AVAL FOR");
                    } else if (response.getShortageNoticeList().get(i).getFollowUpCd().equals("5")) {
                        shortageNotice.setFollowUpCode("SHPPD/ACN");
                    } else if (response.getShortageNoticeList().get(i).getFollowUpCd().equals("8")) {
                        shortageNotice.setFollowUpCode("BORROWED");
                    } else {
                        shortageNotice.setFollowUpCode(response.getShortageNoticeList().get(i).getFollowUpCd());
                    }
                    shortageNotice.setManufPartNbr(response.getShortageNoticeList().get(i).getManufPartNbr());
                    shortageNotice.setCoPartNbr(response.getShortageNoticeList().get(i).getCoPartNbr());
                    shortageNotice.setCpnQty(response.getShortageNoticeList().get(i).getCpnQty());
                    shortageNotice.setCpnDescription(response.getShortageNoticeList().get(i).getCpnDescription());
                    shortageNotice.setShiptoSta(response.getShortageNoticeList().get(i).getShiptoSta());
                    shortageNotice.setShiptoDept(response.getShortageNoticeList().get(i).getShiptoDept());
                    shortageNotice.setPlanMetInd(response.getShortageNoticeList().get(i).getPlanMetInd());
                    String date = String.valueOf(response.getShortageNoticeList().get(i).getDatePartNeedBy());
                    String time = String.valueOf(response.getShortageNoticeList().get(i).getTimePartNeedBy());
                    shortageNotice.setDatetimePartNeedBy(date + '/' + time);
                    String requestdById = response.getShortageNoticeList().get(i).getRequestedById();
                    String requestedByName = response.getShortageNoticeList().get(i).getRequestedByName();
                    String requestedByPhone = response.getShortageNoticeList().get(i).getRequestedByPhone();
                    shortageNotice.setRequestedBy(requestdById + '-' + requestedByName + '-' + requestedByPhone);
                    shortageNotice.setAta(response.getShortageNoticeList().get(i).getAtaChap()+response.getShortageNoticeList().get(i).getAtaSubChap());
                    shortageNotice.setDiscNum(response.getShortageNoticeList().get(i).getDscrpNbr());
                    shortageNoticeList.add(shortageNotice);
                }
            }
            else{
                logger.info(IServerConstants.ERROR, "No data found for the given ACN");
            }
        } catch (Exception e) {
            logger.warn(
                    "ERROR MetsRetrieval Servlet getShortageNoticeListDetail() e >> "
                            + e.getMessage(), e);
        }
        return shortageNoticeList;
    }

    public GetMSNDetailsResponse getMSNDetails(
            String msn,
            String userId) {

        logger.info("getMsnDetails for msn >" + msn);
        GetMSNDetailsResponse msnDetails=null;
        try {
             msnDetails = getMsnDetailResponse(
                   msn,userId);
        } catch (Exception e) {
            logger.warn(
                    "ERROR MetsRetrieval Servlet getShortageNoticeListDetail() e >> "
                            + e.getMessage(), e);
        }

        return msnDetails;
    }

    public GetMsnShippingInfoResponse getMsnShippingDetails(
            String msn,String userId) {

        logger.info("getMsnShippingDetails for msn >" + msn);
        GetMsnShippingInfoResponse msnShippingInfo=null;
        try {
            msnShippingInfo = getMsnShippingInfo(
                    msn, userId);

        } catch (Exception e) {
            logger.warn(
                    "ERROR MetsRetrieval Servlet getShortageNoticeListDetail() e >> "
                            + e.getMessage(), e);
        }
        return msnShippingInfo;
    }

    public GetShortageNoticeListResponse getShortageNoticeListResponse(
            String acn, String userId)
            throws Exception {

        logger.info("______getShortageNoticeList for acn:" + acn);

        GetShortageNoticeListRequest getShortageNoticeListRequest = new GetShortageNoticeListRequest();

        SessionType sessionType = new SessionType();
        sessionType.setUserId(userId);
        sessionType.setToken("sample_token");
        sessionType.setAuthSourceSysName(AuthSourceSysType.LDAP);
        sessionType.setUserStation("");
        sessionType.setUserDepartment("");

        ShortageNoticeSearchCriteriaType shortageNoticeSearchCriteriaType = new ShortageNoticeSearchCriteriaType();
        List<ColumnInfoType> columnInfoType = new ArrayList<>();
        ColumnInfoType columnInfoType1 = new ColumnInfoType();
        columnInfoType1.setColumnName("ACN");
        columnInfoType1.getColumnValues().add(acn);
        columnInfoType1.setColumnType("STRING");
        columnInfoType1.setSortOrder(0);
        columnInfoType1.setOperator("SINGLE");
        columnInfoType.add(columnInfoType1);

        ColumnInfoType columnInfoType2 = new ColumnInfoType();
        columnInfoType2.setColumnName("AOGFOLLOWUP");
        columnInfoType2.getColumnValues().add("0");
        columnInfoType2.getColumnValues().add("1");
        columnInfoType2.getColumnValues().add("2");
        columnInfoType2.getColumnValues().add("3");
        columnInfoType2.getColumnValues().add("4");
        columnInfoType2.getColumnValues().add("5");
        columnInfoType2.getColumnValues().add("8");
        columnInfoType2.setSortOrder(0);
        columnInfoType2.setColumnType("STRING");
        columnInfoType2.setOperator("MULTIPLE");
        columnInfoType.add(columnInfoType2);

        shortageNoticeSearchCriteriaType.getColumnInfoTypeList().addAll(columnInfoType);

        getShortageNoticeListRequest.setSession(sessionType);
        getShortageNoticeListRequest.setCriteria(shortageNoticeSearchCriteriaType);
        getShortageNoticeListRequest.setDisplayHotItems(false);
        getShortageNoticeListRequest.setDisplayMyQueue(false);
        getShortageNoticeListRequest.setMsnSearch(false);

        GetShortageNoticeListResponse resp = (GetShortageNoticeListResponse) wb.app2webServiceTemplate().marshalSendAndReceive(getShortageNoticeListRequest);
        return resp;

    }

    public GetMSNDetailsResponse getMsnDetailResponse(
            String msn,String userId)
            throws Exception {

        logger.info("______getMsnDetail for msn:" + msn);

        GetMSNDetailsRequest getMsnDetailRequest = new GetMSNDetailsRequest();

        SessionType sessionType = new SessionType();
        sessionType.setUserId(userId);
        sessionType.setToken("sample_token");
        sessionType.setAuthSourceSysName(AuthSourceSysType.LDAP);
        sessionType.setUserStation("");
        sessionType.setUserDepartment("");
        getMsnDetailRequest.setSession(sessionType);
        getMsnDetailRequest.setMsn(msn);

        GetMSNDetailsResponse resp = (GetMSNDetailsResponse) wb.app2webServiceTemplate().marshalSendAndReceive(getMsnDetailRequest);
        return resp;

    }

    public GetMsnShippingInfoResponse getMsnShippingInfo(
            String msn,String userId)
            throws Exception {

        logger.info("getMsnShippingInfo for msn:" + msn);

        GetMsnShippingInfoRequest getMsnShippingInfoRequest = new GetMsnShippingInfoRequest();

        SessionType sessionType = new SessionType();
        sessionType.setUserId(userId);
        sessionType.setToken("sample_token");
        sessionType.setAuthSourceSysName(AuthSourceSysType.LDAP);
        sessionType.setUserStation("");
        sessionType.setUserDepartment("");
        getMsnShippingInfoRequest.setSession(sessionType);
        getMsnShippingInfoRequest.setMsn(msn);

        GetMsnShippingInfoResponse resp = (GetMsnShippingInfoResponse) wb.app2webServiceTemplate().marshalSendAndReceive(getMsnShippingInfoRequest);
        return resp;

    }


}
