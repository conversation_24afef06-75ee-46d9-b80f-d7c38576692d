
package com.fedex.mets.wsdl.discrepancy;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ActionIdDetail complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ActionIdDetail">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="DscrpActionOid" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="userUpdateType" type="{http://www.fedex.com/airops/schemas/EnumTypes.xsd}UserUpdateTypeEnum"/>
 *         &lt;element name="activeFlg" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="review" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="actionTimeInSecs" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="actionStatus" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="actionDetail" type="{http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd}Action" minOccurs="0"/>
 *         &lt;element name="actionParts" type="{http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd}ActTypeDetail" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="actionTools" type="{http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd}ActTypeDetail" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;element name="actionDocRefs" type="{http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd}ActionDocRef" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ActionIdDetail", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", propOrder = {
    "dscrpActionOid",
    "userUpdateType",
    "activeFlg",
    "review",
    "actionTimeInSecs",
    "actionStatus",
    "actionDetail",
    "actionParts",
    "actionTools",
    "actionDocRefs"
})
public class ActionIdDetail {

    @XmlElement(name = "DscrpActionOid", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", required = true)
    protected BigDecimal dscrpActionOid;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", required = true)
    protected UserUpdateTypeEnum userUpdateType;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", defaultValue = "false")
    protected boolean activeFlg;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", defaultValue = "false")
    protected boolean review;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal actionTimeInSecs;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", defaultValue = "false")
    protected String actionStatus;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected Action actionDetail;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected List<ActTypeDetail> actionParts;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected List<ActTypeDetail> actionTools;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected List<ActionDocRef> actionDocRefs;

    /**
     * Gets the value of the dscrpActionOid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getDscrpActionOid() {
        return dscrpActionOid;
    }

    /**
     * Sets the value of the dscrpActionOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setDscrpActionOid(BigDecimal value) {
        this.dscrpActionOid = value;
    }

    /**
     * Gets the value of the userUpdateType property.
     * 
     * @return
     *     possible object is
     *     {@link UserUpdateTypeEnum }
     *     
     */
    public UserUpdateTypeEnum getUserUpdateType() {
        return userUpdateType;
    }

    /**
     * Sets the value of the userUpdateType property.
     * 
     * @param value
     *     allowed object is
     *     {@link UserUpdateTypeEnum }
     *     
     */
    public void setUserUpdateType(UserUpdateTypeEnum value) {
        this.userUpdateType = value;
    }

    /**
     * Gets the value of the activeFlg property.
     * 
     */
    public boolean isActiveFlg() {
        return activeFlg;
    }

    /**
     * Sets the value of the activeFlg property.
     * 
     */
    public void setActiveFlg(boolean value) {
        this.activeFlg = value;
    }

    /**
     * Gets the value of the review property.
     * 
     */
    public boolean isReview() {
        return review;
    }

    /**
     * Sets the value of the review property.
     * 
     */
    public void setReview(boolean value) {
        this.review = value;
    }

    /**
     * Gets the value of the actionTimeInSecs property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getActionTimeInSecs() {
        return actionTimeInSecs;
    }

    /**
     * Sets the value of the actionTimeInSecs property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setActionTimeInSecs(BigDecimal value) {
        this.actionTimeInSecs = value;
    }

    /**
     * Gets the value of the actionStatus property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getActionStatus() {
        return actionStatus;
    }

    /**
     * Sets the value of the actionStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setActionStatus(String value) {
        this.actionStatus = value;
    }

    /**
     * Gets the value of the actionDetail property.
     * 
     * @return
     *     possible object is
     *     {@link Action }
     *     
     */
    public Action getActionDetail() {
        return actionDetail;
    }

    /**
     * Sets the value of the actionDetail property.
     * 
     * @param value
     *     allowed object is
     *     {@link Action }
     *     
     */
    public void setActionDetail(Action value) {
        this.actionDetail = value;
    }

    /**
     * Gets the value of the actionParts property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the actionParts property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getActionParts().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link ActTypeDetail }
     * 
     * 
     */
    public List<ActTypeDetail> getActionParts() {
        if (actionParts == null) {
            actionParts = new ArrayList<ActTypeDetail>();
        }
        return this.actionParts;
    }

    /**
     * Gets the value of the actionTools property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the actionTools property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getActionTools().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link ActTypeDetail }
     * 
     * 
     */
    public List<ActTypeDetail> getActionTools() {
        if (actionTools == null) {
            actionTools = new ArrayList<ActTypeDetail>();
        }
        return this.actionTools;
    }

    /**
     * Gets the value of the actionDocRefs property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the actionDocRefs property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getActionDocRefs().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link ActionDocRef }
     * 
     * 
     */
    public List<ActionDocRef> getActionDocRefs() {
        if (actionDocRefs == null) {
            actionDocRefs = new ArrayList<ActionDocRef>();
        }
        return this.actionDocRefs;
    }

}
