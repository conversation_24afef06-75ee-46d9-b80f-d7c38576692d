package com.fedex.mets.repository.mets;

import com.fedex.mets.entity.mets.EventFlightDelays;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EventFlightDelaysRepository extends JpaRepository<EventFlightDelays,Integer> {

    @Query(value = "select * from event_flt_delays where EVENT_ID=:id",nativeQuery = true)
    List<EventFlightDelays> getAllByEventId(@Param("id") int id);

    @Query(value = "update event_flt_delays set DELAY_TIME=:delayTime where EVENT_ID=:id and delay_code=:delayCode",nativeQuery = true)
    public void updateDelayTime(@Param("id") int id,@Param("delayTime") String delayTime,@Param("delayCode") int delayCode);
}
