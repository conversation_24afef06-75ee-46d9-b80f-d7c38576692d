
package com.fedex.mets.wsdl.discrepancy;

import java.math.BigDecimal;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for DscrpProcessControl complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="DscrpProcessControl">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="processCntlOid" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="ProcessControlDataDesc" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ProcessControlTypeCd" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="StatusReasonDesc" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="updateDetail" type="{http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd}UpdateDetail" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "DscrpProcessControl", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", propOrder = {
    "processCntlOid",
    "processControlDataDesc",
    "processControlTypeCd",
    "statusReasonDesc",
    "updateDetail"
})
public class DscrpProcessControl {

    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal processCntlOid;
    @XmlElement(name = "ProcessControlDataDesc", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String processControlDataDesc;
    @XmlElement(name = "ProcessControlTypeCd", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String processControlTypeCd;
    @XmlElement(name = "StatusReasonDesc", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected String statusReasonDesc;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected UpdateDetail updateDetail;

    /**
     * Gets the value of the processCntlOid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getProcessCntlOid() {
        return processCntlOid;
    }

    /**
     * Sets the value of the processCntlOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setProcessCntlOid(BigDecimal value) {
        this.processCntlOid = value;
    }

    /**
     * Gets the value of the processControlDataDesc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getProcessControlDataDesc() {
        return processControlDataDesc;
    }

    /**
     * Sets the value of the processControlDataDesc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setProcessControlDataDesc(String value) {
        this.processControlDataDesc = value;
    }

    /**
     * Gets the value of the processControlTypeCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getProcessControlTypeCd() {
        return processControlTypeCd;
    }

    /**
     * Sets the value of the processControlTypeCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setProcessControlTypeCd(String value) {
        this.processControlTypeCd = value;
    }

    /**
     * Gets the value of the statusReasonDesc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStatusReasonDesc() {
        return statusReasonDesc;
    }

    /**
     * Sets the value of the statusReasonDesc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStatusReasonDesc(String value) {
        this.statusReasonDesc = value;
    }

    /**
     * Gets the value of the updateDetail property.
     * 
     * @return
     *     possible object is
     *     {@link UpdateDetail }
     *     
     */
    public UpdateDetail getUpdateDetail() {
        return updateDetail;
    }

    /**
     * Sets the value of the updateDetail property.
     * 
     * @param value
     *     allowed object is
     *     {@link UpdateDetail }
     *     
     */
    public void setUpdateDetail(UpdateDetail value) {
        this.updateDetail = value;
    }

}
