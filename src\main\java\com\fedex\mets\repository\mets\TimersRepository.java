package com.fedex.mets.repository.mets;

import com.fedex.mets.entity.mets.Timers;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TimersRepository extends JpaRepository<Timers,String> {

    @Query(value = "select * from TIMERS order by LIST_ORDER", nativeQuery = true)
    public List<Timers> getTimers();
}
