
package com.fedex.mets.wsdl.acnCache;

import jakarta.xml.bind.annotation.XmlEnum;
import jakarta.xml.bind.annotation.XmlEnumValue;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for CacheDataTypeEnum.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="CacheDataTypeEnum">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="ACN"/>
 *     &lt;enumeration value="ATA"/>
 *     &lt;enumeration value="FLEETSERIES"/>
 *     &lt;enumeration value="AMTLookup"/>
 *     &lt;enumeration value="STATION"/>
 *     &lt;enumeration value="TROUBLESHOOTING"/>
 *     &lt;enumeration value="ENGINEINFO"/>
 *     &lt;enumeration value="LOAN_BOR_AIRLINES"/>
 *     &lt;enumeration value="MEL_REF_FLEET"/>
 *     &lt;enumeration value="TFOA_CATEGORY"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 * 
 */
@XmlType(name = "CacheDataTypeEnum")
@XmlEnum
public enum CacheDataTypeEnum {

    ACN("ACN"),
    ATA("ATA"),
    FLEETSERIES("FLEETSERIES"),
    @XmlEnumValue("AMTLookup")
    AMT_LOOKUP("AMTLookup"),
    STATION("STATION"),
    TROUBLESHOOTING("TROUBLESHOOTING"),
    ENGINEINFO("ENGINEINFO"),
    LOAN_BOR_AIRLINES("LOAN_BOR_AIRLINES"),
    MEL_REF_FLEET("MEL_REF_FLEET"),
    TFOA_CATEGORY("TFOA_CATEGORY");
    private final String value;

    CacheDataTypeEnum(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static CacheDataTypeEnum fromValue(String v) {
        for (CacheDataTypeEnum c: CacheDataTypeEnum.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
