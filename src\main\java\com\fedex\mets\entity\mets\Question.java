package com.fedex.mets.entity.mets;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import java.sql.Timestamp;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name="question")
public class Question {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "question_seq")
    @SequenceGenerator(name = "question_seq", sequenceName = "QUESTION_SEQ", allocationSize = 1)
    @Column(name="question_id")
    private int question_id;

    @Column(name="question_txt")
    private String questionTxt;

    @OneToMany(targetEntity = Answer.class, cascade = CascadeType.ALL)
    @JoinColumn(name = "Question_id")
    public List<Answer> answers;

    @Column(name="question_grp")
    public String questionGrp;

    @ManyToMany(mappedBy = "questions",fetch = FetchType.LAZY)
    @JsonIgnore
    private List<IntakeForm> intakeForms;

    @Column(name="required_flg")
    private Character required;

    @JsonFormat(
            shape = JsonFormat.Shape.STRING,
            pattern = "yyyy-MM-dd HH:mm:ss.SSS",
            timezone = "UTC")
    @CreatedDate
    @Column(name="created_tmstp")
    private Timestamp createdTMSTP;

    @JsonFormat(
            shape = JsonFormat.Shape.STRING,
            pattern = "yyyy-MM-dd HH:mm:ss.SSS",
            timezone = "UTC")
    @LastModifiedDate
    @Column(name="updated_tmstp")
    private Timestamp updatedTMSTP;
}
