
package com.fedex.mets.wsdl.msn.shortagenotice;

import jakarta.xml.bind.annotation.*;

import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for HeaderType complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="HeaderType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="UserId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ClientIp" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ClientBoxDnsName" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ClientReqNbr" type="{http://www.w3.org/2001/XMLSchema}long"/>
 *         &lt;element name="ClientMethodNm" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ClientSubmittedTmstp" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="ServerIp" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ServerBoxDnsName" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ServerReqStartTmstp" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="ServerReqEndTmstp" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="ServerDbTimeTaken" type="{http://www.w3.org/2001/XMLSchema}long"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "HeaderTypeMsn", propOrder = {
    "userId",
    "clientIp",
    "clientBoxDnsName",
    "clientReqNbr",
    "clientMethodNm",
    "clientSubmittedTmstp",
    "serverIp",
    "serverBoxDnsName",
    "serverReqStartTmstp",
    "serverReqEndTmstp",
    "serverDbTimeTaken"
})
public class HeaderType {

    @XmlElement(name = "UserId", required = true)
    protected String userId;
    @XmlElement(name = "ClientIp", required = true)
    protected String clientIp;
    @XmlElement(name = "ClientBoxDnsName", required = true)
    protected String clientBoxDnsName;
    @XmlElement(name = "ClientReqNbr")
    protected long clientReqNbr;
    @XmlElement(name = "ClientMethodNm", required = true)
    protected String clientMethodNm;
    @XmlElement(name = "ClientSubmittedTmstp", required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar clientSubmittedTmstp;
    @XmlElement(name = "ServerIp", required = true)
    protected String serverIp;
    @XmlElement(name = "ServerBoxDnsName", required = true)
    protected String serverBoxDnsName;
    @XmlElement(name = "ServerReqStartTmstp", required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar serverReqStartTmstp;
    @XmlElement(name = "ServerReqEndTmstp", required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar serverReqEndTmstp;
    @XmlElement(name = "ServerDbTimeTaken")
    protected long serverDbTimeTaken;

    /**
     * Gets the value of the userId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUserId() {
        return userId;
    }

    /**
     * Sets the value of the userId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUserId(String value) {
        this.userId = value;
    }

    /**
     * Gets the value of the clientIp property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getClientIp() {
        return clientIp;
    }

    /**
     * Sets the value of the clientIp property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setClientIp(String value) {
        this.clientIp = value;
    }

    /**
     * Gets the value of the clientBoxDnsName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getClientBoxDnsName() {
        return clientBoxDnsName;
    }

    /**
     * Sets the value of the clientBoxDnsName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setClientBoxDnsName(String value) {
        this.clientBoxDnsName = value;
    }

    /**
     * Gets the value of the clientReqNbr property.
     * 
     */
    public long getClientReqNbr() {
        return clientReqNbr;
    }

    /**
     * Sets the value of the clientReqNbr property.
     * 
     */
    public void setClientReqNbr(long value) {
        this.clientReqNbr = value;
    }

    /**
     * Gets the value of the clientMethodNm property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getClientMethodNm() {
        return clientMethodNm;
    }

    /**
     * Sets the value of the clientMethodNm property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setClientMethodNm(String value) {
        this.clientMethodNm = value;
    }

    /**
     * Gets the value of the clientSubmittedTmstp property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getClientSubmittedTmstp() {
        return clientSubmittedTmstp;
    }

    /**
     * Sets the value of the clientSubmittedTmstp property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setClientSubmittedTmstp(XMLGregorianCalendar value) {
        this.clientSubmittedTmstp = value;
    }

    /**
     * Gets the value of the serverIp property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getServerIp() {
        return serverIp;
    }

    /**
     * Sets the value of the serverIp property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setServerIp(String value) {
        this.serverIp = value;
    }

    /**
     * Gets the value of the serverBoxDnsName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getServerBoxDnsName() {
        return serverBoxDnsName;
    }

    /**
     * Sets the value of the serverBoxDnsName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setServerBoxDnsName(String value) {
        this.serverBoxDnsName = value;
    }

    /**
     * Gets the value of the serverReqStartTmstp property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getServerReqStartTmstp() {
        return serverReqStartTmstp;
    }

    /**
     * Sets the value of the serverReqStartTmstp property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setServerReqStartTmstp(XMLGregorianCalendar value) {
        this.serverReqStartTmstp = value;
    }

    /**
     * Gets the value of the serverReqEndTmstp property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getServerReqEndTmstp() {
        return serverReqEndTmstp;
    }

    /**
     * Sets the value of the serverReqEndTmstp property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setServerReqEndTmstp(XMLGregorianCalendar value) {
        this.serverReqEndTmstp = value;
    }

    /**
     * Gets the value of the serverDbTimeTaken property.
     * 
     */
    public long getServerDbTimeTaken() {
        return serverDbTimeTaken;
    }

    /**
     * Sets the value of the serverDbTimeTaken property.
     * 
     */
    public void setServerDbTimeTaken(long value) {
        this.serverDbTimeTaken = value;
    }

}
