package com.fedex.mets.config;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ws.WebServiceMessage;
import org.springframework.ws.client.WebServiceClientException;
import org.springframework.ws.client.support.interceptor.ClientInterceptor;
import org.springframework.ws.context.MessageContext;
import org.springframework.ws.soap.SoapMessage;

import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;

public class SoapLoggingInterceptor implements ClientInterceptor {
	
	private static final Logger logger = LoggerFactory.getLogger(SoapLoggingInterceptor.class);

    @Override
    public boolean handleRequest(MessageContext messageContext) {
    	 if (messageContext.getRequest() instanceof SoapMessage) {
    	        SoapMessage soapMessage = (SoapMessage) messageContext.getRequest();
    	        logSoapMessage("Request", soapMessage);
    	    }
    	    return true;
    }

    @Override
    public boolean handleResponse(MessageContext messageContext) {
        logSoapMessage("Response", messageContext.getResponse());
        return true;
    }

    @Override
    public boolean handleFault(MessageContext messageContext) {
        logSoapMessage("Fault", messageContext.getResponse());
        return true;
    }

    private void logSoapMessage(String messageType, WebServiceMessage webServiceMessage) {
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            webServiceMessage.writeTo(out);
            String message = new String(out.toByteArray(), StandardCharsets.UTF_8);
            logger.info(messageType + ": " + message);
        } catch (Exception e) {
            logger.warn("Error logging SOAP " + messageType + ": " + e.getMessage());
        }
    }

	@Override
	public void afterCompletion(MessageContext messageContext, Exception ex) throws WebServiceClientException {
		// TODO Auto-generated method stub
		
	}
}
