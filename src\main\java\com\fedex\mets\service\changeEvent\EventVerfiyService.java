package com.fedex.mets.service.changeEvent;

import com.fedex.mets.entity.mets.EventAction;
import com.fedex.mets.repository.mets.AircraftActionRepository;
import com.fedex.mets.repository.mets.EventActionRepository;
import com.fedex.mets.util.ServerDateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class EventVerfiyService {

    private static final Logger logger = LoggerFactory.getLogger(EventVerfiyService.class);

    @Autowired
    private EventActionRepository eventActionRepository;

    @Autowired
    private AircraftActionRepository aircraftActionRepository;

    /**
     * The following deleteEventAction() is used to delete the action from ACTIONS table.
     * @params int eventId, String acn
     * @return boolean isRecordDeleted.
     */
    public boolean deleteEventAction(int eventId, String acn, String strCompare)
            throws Exception {
        logger.info("in the deleteEventAction of the bean.....");
        logger.info(
                "\n eventId...."
                        + eventId
                        + "\n acn...."
                        + acn
                        + "\n strCompare...."
                        + strCompare);
        boolean isRecordDeleted = false;

        EventAction eventAction = null;

        try {
            java.sql.Timestamp currentTimestamp = ServerDateHelper.getTimeStamp();
        } catch (Exception time) {
            logger.warn(
                    "ERROR EventVerifySession deleteEventAction() server time stamp >> "
                            + time.getMessage());
        }

        if (eventId > 0) {

            logger.info("for existing events checking the EVENT_ACTION TABLE.....");
                try {
                    eventAction = eventActionRepository.findByPrimaryKey(eventId);
                    if(eventAction!=null) {
                        try {
                            if (strCompare != null
                                    && strCompare.trim().equalsIgnoreCase(
                                    eventAction.getEmpName())) {
                                logger.info("deleting the record as strCompare matched.....");
                                eventActionRepository.deleteByPrimaryKey(eventId);
                                logger.info("Event Action Removed for " + eventId);
                            }

                            isRecordDeleted = true;
                        } catch (Exception create) {
                            logger.warn(
                                    "Could not delete the record in to Actions in Table for event id ."
                                            + eventId
                                            + " ERROR Msg "
                                            + create.getMessage());
                        }
                    }
                } catch (Exception primary) {
                    primary.printStackTrace();
                    logger.warn("Could not find active Actions in Table for event id ."+eventId+" ERROR Msg "+ primary.getMessage());
            }
        }

        if (eventAction == null && acn != null && acn.trim().length() > 0) {
            logger.info("for New events checking the AIRCRAFT_ACTION TABLE.....");
             try{
                 aircraftActionRepository.deleteRecordByAcn(acn);
                 isRecordDeleted = true;
             }
             catch (Exception e) {
                 logger.warn("Could not delete the record in to Aircraft Actions Table for ACN ." + acn + " ERROR Msg " + e.getMessage());
             }
        }
        return isRecordDeleted;
    }

}
