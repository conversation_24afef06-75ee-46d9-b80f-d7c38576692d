package com.fedex.mets.data;

import com.fedex.mets.entity.mets.EventMsns;
import lombok.*;

import java.sql.Timestamp;
import java.util.List;

import com.fedex.mets.entity.mets.EventTimers;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
public class MetsEventUpdateEntity {
	private String mode;
	private String acn;
	private String group_id;
	private String event_id;
	private String access_level;
	private String timer_id;
	private String flag;
	private String request_type;
	private String user_id;
	private String token_id;
	private String discrepancy_filter;
	private String discrepancy_from_date;
	private String discrepancy_to_date;
	private String discrepancy_SPAN;
	private boolean comment_updated;
	private boolean event_active;
	private EventTimers niw_timer_data;
	private EventTfNotesDto tf_notes_data;
	private EventFlightEticDataEntity flight_etic_data;
	private DOADataEntity event_doa_data;
	private DetailViewDataEntity detail_view_data;
	private String start_date_time;
	private EventEmailDataEntity email_data;
	private List<EventDiscrepancyListData> event_discrepancy_data;
	private List<ReportCategoriesKeyValueData> report_categories_data;
	private List<EventMsns> event_msns_data;
	private boolean event_msns_updated;
	private Timestamp wlm_niw_timer_start_time;
	private Timestamp wlm_niw_timer_stop_time;
}
