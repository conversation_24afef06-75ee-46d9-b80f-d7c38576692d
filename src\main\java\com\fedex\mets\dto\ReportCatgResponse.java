package com.fedex.mets.dto;

import com.fedex.mets.dao.ReportCategoriesActiveKeyValueData;
import com.fedex.mets.dao.ReportingCategoryValues;
import com.fedex.mets.data.EventDiscrepancyList;
import com.fedex.mets.data.ReportCategoriesData;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@Schema(description = "Response containing reporting category details")
public class ReportCatgResponse {

    @Schema(description = "List of Reporting Categories")
    private List<ReportingCategoryValues> reportingCategories;

    @Schema(description = "List of Reporting Category Keys")
    private List<ReportCategoriesActiveKeyValueData> reportingCategoryKeys;
    public ReportCatgResponse() {
        this.reportingCategories = new ArrayList<>();
        this.reportingCategoryKeys = new ArrayList<>();
    }
}