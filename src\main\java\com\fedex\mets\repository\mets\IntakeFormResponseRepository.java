package com.fedex.mets.repository.mets;

import com.fedex.mets.entity.mets.IntakeFormResponse;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface IntakeFormResponseRepository extends JpaRepository<IntakeFormResponse,Integer> {

    @Query(value = "select response_json from intake_form_response where event_id=:eventId",nativeQuery = true)
    String getResponseJsonByEventId(@Param("eventId") int eventId);
}
