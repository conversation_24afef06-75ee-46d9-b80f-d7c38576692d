package com.fedex.mets.service.retrieval;

import com.fedex.mets.config.MachSoapClientConfig;
import com.fedex.mets.config.OktaTokenGenService;
import com.fedex.mets.data.EventDiscrepancyList;
import com.fedex.mets.dto.DiscrepancyResponse;
import com.fedex.mets.entity.mets.EventMaxiDisc;
import com.fedex.mets.entity.mets.EventMaxiDwningItm;
import com.fedex.mets.entity.mets.Events;
import com.fedex.mets.repository.mets.EventMaxiDiscRepository;
import com.fedex.mets.repository.mets.EventMaxiDwningItmRepository;
import com.fedex.mets.repository.mets.EventsRepository;
import com.fedex.mets.util.DetailLineFormat;
import com.fedex.mets.util.DiscrepancyTextProcessor;
import com.fedex.mets.wsdl.discrepancy.*;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class EventDiscrepanciesService {

    private static final Logger logger = LoggerFactory.getLogger(EventDiscrepanciesService.class);
    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
    @Autowired
    private MachSoapClientConfig wb;
    @Autowired
    private EventMaxiDiscRepository eventMaxiDiscRepository;

    @Autowired
    private EventMaxiDwningItmRepository eventMaxiDwningItmRepository;
    @Autowired
    private EventsRepository eventsRepository;

    @Autowired
    private OktaTokenGenService oktaTokenGenService;

    /**
     * Private method to get the EVENT DISCREPANCY DETAILS for a particular event falling in a particular date range.
     *
     * @return hashMap containing Discrepancy Detail Object.
     * @params EventId,userId.
     */
    public DiscrepancyResponse getOpenDiscrepancies(
            String eventId,
            String userId) {
        DiscrepancyResponse discResponse = new DiscrepancyResponse();
        List<List<?>> elements = new ArrayList<>();
        List<EventDiscrepancyList> linkedDiscrepancyList = new ArrayList<>();
        List<EventDiscrepancyList> discrepancyList = new ArrayList<>();
        try {
            logger.info("==> before calling the getDiscrepancies()");
            String tokenId=this.oktaTokenGenService.generateToken();
            elements =
                    getDiscrepancies(
                            eventId,
                            userId,
                            tokenId);
            if (elements != null) {
                linkedDiscrepancyList = (List<EventDiscrepancyList>) elements.get(0);
                discrepancyList = (List<EventDiscrepancyList>) elements.get(1);
            }
            for (int i = 0; i < linkedDiscrepancyList.size(); i++) {
                EventDiscrepancyList data =
                        (EventDiscrepancyList) linkedDiscrepancyList.get(i);

                logger.info(
                        "==>"
                                + data.getEventId()
                                + " -->> "
                                + data.getAta()
                                + " --> "
                                + data.getNumber()
                                + " --> "
                                + data.getEventType()
                                + " --> "
                                + data.getDiscType()
                                + " --> "
                                + data.getOpenDate()
                                + " --> "
                                + data.getOpenStation()
                                + " --> "
                                + data.getText()
                                + " && Link--> "
                                + data.isLink()
                                + " && Message--> "
                                + data.getMessage());


                logger.info("Discrepancy Details------------------ ");

                for (int j = 0; j < discrepancyList.size(); j++) {
                    EventDiscrepancyList listdata =
                            (EventDiscrepancyList) discrepancyList.get(j);

                    logger.info(
                            "==>"
                                    + listdata.getEventId()
                                    + " -->> "
                                    + listdata.getAta()
                                    + " --> "
                                    + listdata.getNumber()
                                    + " --> "
                                    + listdata.getEventType()
                                    + " --> "
                                    + listdata.getDiscType()
                                    + " --> "
                                    + listdata.getOpenDate()
                                    + " --> "
                                    + listdata.getOpenStation()
                                    + " --> "
                                    + listdata.getText()
                                    + " && Link--> "
                                    + listdata.isLink()
                                    + " && Message--> "
                                    + listdata.getMessage());
                }
            }
            discResponse.setLinkedDiscrepancyList(linkedDiscrepancyList);
            discResponse.setDiscrepancyList(discrepancyList);
        } catch (Exception re) {
            logger.warn(
                    "ERROR MetsRetrieval Servlet getDiscrepancies() remote exception >> "
                            + re.getMessage());
        }
        return discResponse;
    }


    /**
     * The following getDiscrepancies() is used to retreive all the open discrepancies.
     *
     * @return List of Discrepancy data.
     * @params String eventId, strFromDate, strToDate.
     */
    public List<List<?>> getDiscrepancies(
            String eventId,
            String userId,
            String tokenId) {
        List<List<?>> responseList = new ArrayList<>();
        List<EventDiscrepancyList> eventLinkedDiscrepancy = new ArrayList<>();
        List<EventDiscrepancyList> discrepancyList = new ArrayList<>();
        List<EventDiscrepancyList> omdisList = new ArrayList<>();

        String queryString = "";
        String acn = "";
        String ata = "";
        String disc = "";
        String errorMessage = "";

        char errorCode;

        try {
            List<EventMaxiDisc> maxiElement = eventMaxiDiscRepository.getEventMaxiDiscInfo(Integer.parseInt(eventId));
            EventMaxiDwningItm maxiDwningElement = eventMaxiDwningItmRepository.getEventMaxiDwningItemByEventId(Integer.parseInt(eventId));
            for (EventMaxiDisc maxi:maxiElement){
                EventDiscrepancyList data = new EventDiscrepancyList();

                data.setEventId(Math.toIntExact(maxi.getEventMaxiDisckPk().getEventId()));
                String tempATA = maxi.getEventMaxiDisckPk().getAta();

                tempATA = tempATA.substring(0, 2) + "-" + tempATA.substring(2, 4);
                data.setAta(tempATA);
                data.setNumber(maxi.getEventMaxiDisckPk().getDiscNum());
                data.setEventType(maxi.getType());
                data.setLink(true);
                if(maxiDwningElement.getDiscNum().equals(maxi.getEventMaxiDisckPk().getDiscNum()) && maxiDwningElement.getAta().equals(maxi.getEventMaxiDisckPk().getAta()))
                {
                    data.setDowningItem(true);
                }
                eventLinkedDiscrepancy.add(data);
            }
            Events eventElement = eventsRepository.getEventsByEventId(Integer.parseInt(eventId));

            acn = eventElement.getAcn();

        } catch (Exception ee) {
            logger.warn(
                    "ERROR getDiscrepancies() >> " + ee.getMessage());
        }

        try {
            try {

                omdisList = getAircraftDscrps(acn,userId, eventId, tokenId, null, null);

            } catch (Exception e) {
                logger.warn("ERROR Exception .>> " + e.getMessage());
                throw new Exception(e.getMessage());
            }


            for (int j = 0; j < eventLinkedDiscrepancy.size(); j++) {
                EventDiscrepancyList data =
                        (EventDiscrepancyList) eventLinkedDiscrepancy.get(
                                j);

                Iterator<EventDiscrepancyList> iterator = omdisList.iterator();

                while (iterator.hasNext()) {
                    EventDiscrepancyList rslt = iterator.next();

                    String strATA = rslt.getAta();


                    String strDisc = rslt.getNumber();

                    if (data.getAta().trim().equals(strATA)
                            && data.getNumber().trim().equals(strDisc)) {
                        data.setOpenDate(rslt.getOpenDate());
                        data.setOpenStation(rslt.getOpenStation());
                        data.setDiscType(rslt.getDiscType());
                        data.setText(rslt.getText());
                        data.setStatus(rslt.getStatus());
                        data.setInWork(rslt.isInWork());

                        iterator.remove();
                    }
                }


            }

            if (omdisList.size() > 0) {
                discrepancyList.addAll(omdisList);
            }


        } catch (Exception e) {
            logger.warn(
                    "ERROR getDiscrepancies() >> " + e.getMessage());
        }
        responseList.add(eventLinkedDiscrepancy);
        responseList.add(discrepancyList);

        return responseList;
    }

    /**
     * Retrieve discrepancy given acn, start and end dates. supplying eventID,
     * downingAta and downingDisc will ensure those attributes are set for the
     * matching EventDiscrepancyListData being returned
     *
     * @param acn
     * @param userid
     * @param eventId
     * @param dwningAta
     * @param dwningDisc
     * @return
     */
    public List<EventDiscrepancyList> getAircraftDscrps(String acn,
                                                       String userid, String eventId, String tokenId,
                                                        String dwningAta, String dwningDisc) {

        try {

                // retrieve open discrepancies
                DiscrepancyQueryFilter dscroFilter = new DiscrepancyQueryFilter();
                dscroFilter.getAcn().add(acn);
                dscroFilter.setGetRstText(true);

                dscroFilter.setStatus("O");
                List<EventDiscrepancyList> discs = getAircraftDscrps(acn,
                        dscroFilter, userid, eventId, tokenId, dwningAta, dwningDisc);

                logger.info("Number of discs found: " + discs.size());

                return discs;
//            }


        } catch (Exception e) {
            logger.error(
                    "ERROR Mets DiscrepancyUtil getAircraftDscrps() e >> "
                            + e.getMessage(), e);
        }

        return new ArrayList<EventDiscrepancyList>(1);

    }

    /*
     * Main method for retrieving discrepancy detail
     */
    private List<EventDiscrepancyList> getAircraftDscrps(String acn,
                                                         DiscrepancyQueryFilter dscroFilter, String userid, String eventId, String tokenId,
                                                         String dwningAta, String dwningDisc) {

        long time = System.currentTimeMillis();
        ArrayList<EventDiscrepancyList> discrepancies = new ArrayList<EventDiscrepancyList>();
        GetWorkRlseStandardRequest getWorkRlseStandardRequest = new GetWorkRlseStandardRequest();
        try {
            GetAircraftDiscrepanciesRequest getAircraftDiscrepanciesRequest = new GetAircraftDiscrepanciesRequest();
            getAircraftDiscrepanciesRequest
                    .setDiscrepancyQueryFilter(dscroFilter);
            getAircraftDiscrepanciesRequest.setGetLogpage(false);

            GetAircraftDiscrepanciesResponse detailResp = getAircraftDiscrepancies(
                    getAircraftDiscrepanciesRequest, userid, tokenId);

            if (detailResp.getStatus().isSuccessful()) {

                List<AcnDiscrepancy> aDiscs = detailResp.getDiscrepancies();
                if (aDiscs != null) {
                    for (AcnDiscrepancy rslt : aDiscs) {
                        String strATA = "" + rslt.getAtaNbr();

                        for (int i = strATA.length(); i < 4; i++) {
                            strATA = "0" + strATA;
                        }

                        if (strATA.length() == 4) {
                            strATA = strATA.substring(0, 2) + "-"
                                    + strATA.substring(2, 4);
                        }

                        String strDisc = rslt.getDscrpNbr();
                        if (strDisc.length() > 4) {
                            strDisc = strDisc.substring(
                                    strDisc.length() - 4, strDisc.length());
                        }

                        String discType = DiscrepancyTextProcessor
                                .getDiscrepancyTypeMainframeMatch(rslt
                                        .getDiscrepancyType());
                        String openDate = "";
                        if (rslt.getOpenDt() != null) {
                            Calendar cl = rslt.getOpenDt()
                                    .toGregorianCalendar();
                            openDate = formatter.format(cl.getTime());
                        }
                        String openStation = rslt.getOpenStationCd();

                        String status = "O".equals(rslt.getOpenFlg()
                                .toUpperCase()) || rslt.getFactTm() != null ? "OPEN" : "CLOSE";

                        String strInWork = rslt.getDscrpInWorkFlg();

                        EventDiscrepancyList data = new EventDiscrepancyList();

                        if (eventId != null) {
                            data.setEventId(Integer.parseInt(eventId));
                        }
                        data.setAta(strATA);
                        data.setNumber(strDisc);
                        data.setOpenDate(openDate);
                        data.setOpenStation(openStation);
                        data.setDiscType(discType);
                        data.setStatus(status);
                        data.setDiscrepancyOid(rslt.getAcnDiscrepancyOid() != null ? rslt
                                .getAcnDiscrepancyOid() : null);

                        if (dwningAta != null && dwningDisc != null
                                && dwningAta.equals(strATA)
                                && dwningDisc.equals(strDisc)) {
                            data.setDowningItem(true);
                        }

                        if (strInWork != null
                                && strInWork.equalsIgnoreCase("Y")) {
                            data.setInWork(true);
                        }
                        String[] text = new String[]{"Data not available"};
                        if (rslt.getDscrpMaintUpdt() != null
                                && rslt.getDscrpMaintUpdt().size() > 0) {

                            ArrayList<AcnDiscrepancy> dscs = new ArrayList<AcnDiscrepancy>(
                                    1);
                            dscs.add(rslt);

                            DetailLineFormat dlf = new DetailLineFormat();
                            List<String> details = new ArrayList<String>();

                            if (rslt.getDscrpMaintUpdt().get(0)
                                    .getMaintUpdateText() != null) {
                                String[] maintText = rslt
                                        .getDscrpMaintUpdt().get(0)
                                        .getMaintUpdateText().split("\n");
                                for (String t : maintText) {
                                    details.add(t);
                                }
                            }
                            dlf.getSfd().getFields().get(1)
                                    .setValueList(details);

                            text = dlf.getSfd().getSentence().split("\n");

                            for (int i = 0; i < text.length; i++) {
                                text[i] = text[i].trim();
                            }

                        }
                        data.setText(text);
                        getWorkRlseStandardRequest.getDiscrepancyOid().add(
                                rslt.getAcnDiscrepancyOid() != null ? rslt
                                        .getAcnDiscrepancyOid() : BigDecimal
                                        .valueOf(0));
                        discrepancies.add(data);
                    }
                }
                logger.info("Aircraft Discrepancy Response took "
                        + ((System.currentTimeMillis() - time) / 1000)
                        + " seconds");
            }
            GetWorkRlseStandardResponse workRlseStandardResponse= getWorkRlseStandard(getWorkRlseStandardRequest,userid,tokenId);
            List<WorkRelseItem> workRelseItems=workRlseStandardResponse.getWorkReleases();
            discrepancies.forEach(disc -> {
                workRelseItems.stream()
                        .filter(worklse -> worklse.getDiscrepancyOid().equals(disc.getDiscrepancyOid()))
                        .findFirst()
                        .ifPresent(worklse -> disc.setPriority(String.valueOf(worklse.getPriority())));
            });

            GetOpenDscrpSpecDetailRequest getOpenDscrpSpecDetailRequest = new GetOpenDscrpSpecDetailRequest();
            GetOpenDscrpSpecDetailResponse specDetailResponse= getOpenDscrpSpecDetail(getOpenDscrpSpecDetailRequest,userid, tokenId,acn);
            List<DiscrepancySpecDetail> discrepanciespecDetails=specDetailResponse.getDiscrepancySpecDetail();
            discrepancies.forEach(disc -> {
                discrepanciespecDetails.stream()
                        .filter(specDetail -> specDetail.getAcnDiscrepancyOid().equals(disc.getDiscrepancyOid()))
                        .findFirst()
                        .ifPresent(specDetail ->{
                                List<SpecDetail> specDetails = specDetail.getSpecDetails();
                                if (specDetails != null && !specDetails.isEmpty()) {
                                    disc.setTimeRemaining(specDetails.get(0).getDaysRemaining() + " D");
                                } else {
                                    disc.setTimeRemaining(specDetail.getDeferralType());
                                }
                    });
            });


        } catch (Exception e) {

            logger.error("Exception", e);
        }
        return discrepancies;

    }

    /*
     * Used for DOA/Inactive/ViewOnly events
     */
    public List<EventDiscrepancyList> getDOAAircraftDscrps(
            String acn, String ata, String discNum, String userid) {

        try {

            DiscrepancyQueryFilter dscroFilter = new DiscrepancyQueryFilter();
            dscroFilter.getAcn().add(acn);
            dscroFilter.getDscrpNbrs().add(discNum);
            dscroFilter.setAtaSeleted(ata.replaceAll("-", ""));
            dscroFilter.setGetRstText(true);

            dscroFilter.setSpanDays(99);

            return getAircraftDscrps(acn, userid, null, null, null, null);


        } catch (Exception e) {
            logger.error("ERROR Mets DiscrepancyUtil getDOAAircraftDscrps() e >> "
                    + e.getMessage());
        }

        return new ArrayList<EventDiscrepancyList>(1);
    }

    public DiscrepancyResponse getAllOpenDsicrepancy(String acn, String userId) throws Exception {
        DiscrepancyResponse discResponse = new DiscrepancyResponse();
        String tokenId=this.oktaTokenGenService.generateToken();
        long time = System.currentTimeMillis();
        ArrayList<EventDiscrepancyList> discrepancies = new ArrayList<EventDiscrepancyList>();

        // retrieve open discrepancies
        DiscrepancyQueryFilter dscroFilter = new DiscrepancyQueryFilter();
        dscroFilter.getAcn().add(acn);
        dscroFilter.setGetRstText(true);

        dscroFilter.setStatus("O");

        try {
            GetAircraftDiscrepanciesRequest getAircraftDiscrepanciesRequest = new GetAircraftDiscrepanciesRequest();
            getAircraftDiscrepanciesRequest
                    .setDiscrepancyQueryFilter(dscroFilter);
            getAircraftDiscrepanciesRequest.setGetLogpage(false);

            GetAircraftDiscrepanciesResponse detailResp = getAircraftDiscrepancies(
                    getAircraftDiscrepanciesRequest, userId, tokenId);

            if (detailResp.getStatus().isSuccessful()) {

                List<AcnDiscrepancy> aDiscs = detailResp.getDiscrepancies();
                if (aDiscs != null) {
                    for (AcnDiscrepancy rslt : aDiscs) {
                        String strATA = "" + rslt.getAtaNbr();

                        for (int i = strATA.length(); i < 4; i++) {
                            strATA = "0" + strATA;
                        }

                        if (strATA.length() == 4) {
                            strATA = strATA.substring(0, 2) + "-"
                                    + strATA.substring(2, 4);
                        }

                        String strDisc = rslt.getDscrpNbr();
                        if (strDisc.length() > 4) {
                            strDisc = strDisc.substring(
                                    strDisc.length() - 4, strDisc.length());
                        }

                        String discType = DiscrepancyTextProcessor
                                .getDiscrepancyTypeMainframeMatch(rslt
                                        .getDiscrepancyType());
                        String openDate = "";
                        if (rslt.getOpenDt() != null) {
                            Calendar cl = rslt.getOpenDt()
                                    .toGregorianCalendar();
                            openDate = formatter.format(cl.getTime());
                        }
                        String openStation = rslt.getOpenStationCd();

                        String status = "O".equals(rslt.getOpenFlg()
                                .toUpperCase()) || rslt.getFactTm() != null ? "OPEN" : "CLOSE";

                        String strInWork = rslt.getDscrpInWorkFlg();

                        EventDiscrepancyList data = new EventDiscrepancyList();
                        data.setAta(strATA);
                        data.setNumber(strDisc);
                        data.setOpenDate(openDate);
                        data.setOpenStation(openStation);
                        data.setDiscType(discType);
                        data.setStatus(status);

                        if (strInWork != null
                                && strInWork.equalsIgnoreCase("Y")) {
                            data.setInWork(true);
                        }
                        String[] text = new String[]{"Data not available"};
                        if (rslt.getDscrpMaintUpdt() != null
                                && rslt.getDscrpMaintUpdt().size() > 0) {

                            ArrayList<AcnDiscrepancy> dscs = new ArrayList<AcnDiscrepancy>(
                                    1);
                            dscs.add(rslt);

                            DetailLineFormat dlf = new DetailLineFormat();
                            List<String> details = new ArrayList<String>();

                            if (rslt.getDscrpMaintUpdt().get(0)
                                    .getMaintUpdateText() != null) {
                                String[] maintText = rslt
                                        .getDscrpMaintUpdt().get(0)
                                        .getMaintUpdateText().split("\n");
                                for (String t : maintText) {
                                    details.add(t);
                                }
                            }
                            dlf.getSfd().getFields().get(1)
                                    .setValueList(details);

                            text = dlf.getSfd().getSentence().split("\n");

                            for (int i = 0; i < text.length; i++) {
                                text[i] = text[i].trim();
                            }

                        }
                        data.setText(text);
                        discrepancies.add(data);
                    }
                    discResponse.setDiscrepancyList(discrepancies);
                }
                logger.info("Aircraft Discrepancy Response took "
                        + ((System.currentTimeMillis() - time) / 1000)
                        + " seconds");
            }

        } catch (Exception e) {

            logger.error("Exception", e);
        }
        return discResponse;
    }


    private GetAircraftDiscrepanciesResponse getAircraftDiscrepancies(GetAircraftDiscrepanciesRequest getAircraftDiscrepanciesRequest, String userId, String tokenId)
            throws Exception {
        logger.info("___________getAircraftDiscrepancies");

        SessionType session = new SessionType();
        session.setUserId(userId);
        session.setToken(tokenId);
        session.setAuthSourceSysName(AuthSourceSysType.LDAP);
        getAircraftDiscrepanciesRequest.setSession(session);

        GetAircraftDiscrepanciesResponse resp = (GetAircraftDiscrepanciesResponse) wb.app1webServiceTemplate().marshalSendAndReceive(getAircraftDiscrepanciesRequest);
        logger.info("getAircraftDiscrepancies SOAP Response" + resp.getStatus().isSuccessful());
        return resp;

    }

    private GetOpenDscrpSpecDetailResponse getOpenDscrpSpecDetail(GetOpenDscrpSpecDetailRequest getOpenDscrpSpecDetailRequest, String userId, String tokenId,String acn)
            throws Exception {
        logger.info("___________getOpenDscrpSpecDetail");

        SessionType session = new SessionType();
        session.setUserId(userId);
        session.setToken(tokenId);
        session.setAuthSourceSysName(AuthSourceSysType.LDAP);
        getOpenDscrpSpecDetailRequest.setSession(session);
        getOpenDscrpSpecDetailRequest.setAircraftNbr(acn);

        GetOpenDscrpSpecDetailResponse resp = (GetOpenDscrpSpecDetailResponse) wb.app1webServiceTemplate().marshalSendAndReceive(getOpenDscrpSpecDetailRequest);
        logger.info("getOpenDscrpSpecDetail SOAP Response" + resp.getStatus().isSuccessful());
        return resp;

    }

    private GetWorkRlseStandardResponse getWorkRlseStandard(GetWorkRlseStandardRequest getWorkRlseStandardRequest, String userId, String tokenId)
            throws Exception {
        logger.info("___________getWorkRlseStandardRequest");

        SessionType session = new SessionType();
        session.setUserId(userId);
        session.setToken(tokenId);
        session.setAuthSourceSysName(AuthSourceSysType.LDAP);
        getWorkRlseStandardRequest.setSession(session);

        GetWorkRlseStandardResponse resp = (GetWorkRlseStandardResponse) wb.app1webServiceTemplate().marshalSendAndReceive(getWorkRlseStandardRequest);
        logger.info("getWorkRlseStandardRequest SOAP Response" + resp.getStatus().isSuccessful());
        return resp;

    }


}
