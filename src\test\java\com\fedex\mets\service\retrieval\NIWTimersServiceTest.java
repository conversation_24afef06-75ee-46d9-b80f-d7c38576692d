package com.fedex.mets.service.retrieval;

import com.fedex.mets.dao.EventNIWTimers;
import com.fedex.mets.dto.NiwTimersResponse;
import com.fedex.mets.entity.mets.EventTimers;
import com.fedex.mets.entity.mets.EventTimersPk;
import com.fedex.mets.entity.mets.Timers;
import com.fedex.mets.repository.mets.EventTimersRepository;
import com.fedex.mets.repository.mets.TimersRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

public class NIWTimersServiceTest {

    @Mock
    private TimersRepository timersRepository;

    @Mock
    private EventTimersRepository eventTimersRepository;

    @InjectMocks
    private NIWTimersService niwTimersService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testGetEventNIWTimers_Success() throws Exception {
        String eventId = "1";

        List<Timers> timerDataList = new ArrayList<>();
        Timers timer = new Timers();
        timer.setTimerId("1");
        timer.setTimerName("Timer1");
        timerDataList.add(timer);

        List<EventTimers> eventActiveTimerDataList = new ArrayList<>();
        EventTimers eventTimer = new EventTimers();
        EventTimersPk eventTimersPk = new EventTimersPk();
        eventTimersPk.setEventId(1);
        eventTimer.setEventTimersPk(eventTimersPk);
        eventTimer.setTimerId("1");
        eventActiveTimerDataList.add(eventTimer);

        List<EventNIWTimers> eventTimerDataList = new ArrayList<>();
        EventNIWTimers eventNIWTimer = new EventNIWTimers();
        eventNIWTimer.setEventId(1);
        eventNIWTimer.setTimerId("1");
        eventTimerDataList.add(eventNIWTimer);

        List<List<?>> elements = new ArrayList<>();
        elements.add(timerDataList);
        elements.add(eventActiveTimerDataList);
        elements.add(eventTimerDataList);


        List<Timers> mockTimers = new ArrayList<>();
        List<EventTimers> mockEventTimers = new ArrayList<>();
        when(timersRepository.getTimers()).thenReturn(mockTimers);
        when(eventTimersRepository.getEventActiveTimers(anyInt())).thenReturn(mockEventTimers);


        when(timersRepository.getTimers()).thenReturn(timerDataList);
        when(eventTimersRepository.getEventActiveTimers(anyInt())).thenReturn(eventActiveTimerDataList);
        when(niwTimersService.getNIWTimers(eventId)).thenReturn(elements);
        NiwTimersResponse result = niwTimersService.getEventNIWTimers(eventId);

        assertEquals(1, result.getTimerDataList().size());
        assertEquals(3, result.getEventActiveTimerDataList().size());
    }

    @Test
    public void testGetNIWTimerDetail_Success() throws Exception {
        String eventId = "1";
        String timerId = "1";

        List<EventTimers> timerDetailList = new ArrayList<>();
        EventTimers eventTimer = new EventTimers();
        EventTimersPk eventTimersPk = new EventTimersPk();
        eventTimersPk.setEventId(1);
        eventTimer.setEventTimersPk(eventTimersPk);
        eventTimer.setTimerId("1");
        timerDetailList.add(eventTimer);

        when(niwTimersService.getNIWTimerDetails(anyString(), anyString())).thenReturn(timerDetailList);

        NiwTimersResponse result = niwTimersService.getNIWTimerDetail(eventId, timerId);

        assertEquals(1, result.getTimerDataList().size());
        assertEquals(1, result.getTimerDataList().get(0).getEventTimersPk().getEventId());
        assertEquals("1", result.getTimerDataList().get(0).getTimerId());
    }
}
