
package com.fedex.mets.wsdl.discrepancy;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlType;



/**
 * <p>Java class for autoSchedAcnDiscrepancy complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="autoSchedAcnDiscrepancy">
 *   &lt;complexContent>
 *     &lt;extension base="{http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd}AcnDiscrepancy">
 *       &lt;sequence>
 *         &lt;element name="countMatched" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="deferralType" type="{http://www.w3.org/2001/XMLSchema}int"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "autoSchedAcnDiscrepancy", propOrder = {
    "countMatched",
    "deferralType"
})
public class AutoSchedAcnDiscrepancy
    extends AcnDiscrepancy
{

    protected boolean countMatched;
    protected int deferralType;

    /**
     * Gets the value of the countMatched property.
     * 
     */
    public boolean isCountMatched() {
        return countMatched;
    }

    /**
     * Sets the value of the countMatched property.
     * 
     */
    public void setCountMatched(boolean value) {
        this.countMatched = value;
    }

    /**
     * Gets the value of the deferralType property.
     * 
     */
    public int getDeferralType() {
        return deferralType;
    }

    /**
     * Sets the value of the deferralType property.
     * 
     */
    public void setDeferralType(int value) {
        this.deferralType = value;
    }

}
