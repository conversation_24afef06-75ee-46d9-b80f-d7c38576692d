
package com.fedex.mets.wsdl.discrepancy;

import java.math.BigDecimal;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for Action complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="Action">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="actionOid" type="{http://www.w3.org/2001/XMLSchema}long"/>
 *         &lt;element name="AtaNbr" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/>
 *         &lt;element name="desc" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="actionType" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="actionCd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="manHours" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="fleetModel" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="amtReq" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="deviationCnt" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="devCntLastAdminUpdt" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="reviewNotes" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="duration" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="riiFlg" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Action", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", propOrder = {
    "actionOid",
    "ataNbr",
    "desc",
    "actionType",
    "actionCd",
    "manHours",
    "fleetModel",
    "amtReq",
    "deviationCnt",
    "devCntLastAdminUpdt",
    "reviewNotes",
    "duration",
    "riiFlg"
})
public class Action {

    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected long actionOid;
    @XmlElement(name = "AtaNbr", namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected BigDecimal ataNbr;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", required = true)
    protected String desc;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", required = true)
    protected String actionType;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", required = true)
    protected String actionCd;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", required = true)
    protected BigDecimal manHours;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", required = true)
    protected String fleetModel;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", required = true)
    protected BigDecimal amtReq;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", required = true)
    protected BigDecimal deviationCnt;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", required = true)
    protected BigDecimal devCntLastAdminUpdt;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", required = true)
    protected String reviewNotes;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd", required = true)
    protected BigDecimal duration;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd")
    protected boolean riiFlg;

    /**
     * Gets the value of the actionOid property.
     * 
     */
    public long getActionOid() {
        return actionOid;
    }

    /**
     * Sets the value of the actionOid property.
     * 
     */
    public void setActionOid(long value) {
        this.actionOid = value;
    }

    /**
     * Gets the value of the ataNbr property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAtaNbr() {
        return ataNbr;
    }

    /**
     * Sets the value of the ataNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAtaNbr(BigDecimal value) {
        this.ataNbr = value;
    }

    /**
     * Gets the value of the desc property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDesc() {
        return desc;
    }

    /**
     * Sets the value of the desc property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDesc(String value) {
        this.desc = value;
    }

    /**
     * Gets the value of the actionType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getActionType() {
        return actionType;
    }

    /**
     * Sets the value of the actionType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setActionType(String value) {
        this.actionType = value;
    }

    /**
     * Gets the value of the actionCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getActionCd() {
        return actionCd;
    }

    /**
     * Sets the value of the actionCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setActionCd(String value) {
        this.actionCd = value;
    }

    /**
     * Gets the value of the manHours property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getManHours() {
        return manHours;
    }

    /**
     * Sets the value of the manHours property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setManHours(BigDecimal value) {
        this.manHours = value;
    }

    /**
     * Gets the value of the fleetModel property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFleetModel() {
        return fleetModel;
    }

    /**
     * Sets the value of the fleetModel property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFleetModel(String value) {
        this.fleetModel = value;
    }

    /**
     * Gets the value of the amtReq property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAmtReq() {
        return amtReq;
    }

    /**
     * Sets the value of the amtReq property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAmtReq(BigDecimal value) {
        this.amtReq = value;
    }

    /**
     * Gets the value of the deviationCnt property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getDeviationCnt() {
        return deviationCnt;
    }

    /**
     * Sets the value of the deviationCnt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setDeviationCnt(BigDecimal value) {
        this.deviationCnt = value;
    }

    /**
     * Gets the value of the devCntLastAdminUpdt property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getDevCntLastAdminUpdt() {
        return devCntLastAdminUpdt;
    }

    /**
     * Sets the value of the devCntLastAdminUpdt property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setDevCntLastAdminUpdt(BigDecimal value) {
        this.devCntLastAdminUpdt = value;
    }

    /**
     * Gets the value of the reviewNotes property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReviewNotes() {
        return reviewNotes;
    }

    /**
     * Sets the value of the reviewNotes property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReviewNotes(String value) {
        this.reviewNotes = value;
    }

    /**
     * Gets the value of the duration property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getDuration() {
        return duration;
    }

    /**
     * Sets the value of the duration property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setDuration(BigDecimal value) {
        this.duration = value;
    }

    /**
     * Gets the value of the riiFlg property.
     * 
     */
    public boolean isRiiFlg() {
        return riiFlg;
    }

    /**
     * Sets the value of the riiFlg property.
     * 
     */
    public void setRiiFlg(boolean value) {
        this.riiFlg = value;
    }

}
