package com.fedex.mets.controller;

import com.fedex.mets.data.WizardEventData;
import com.fedex.mets.dto.MetsResponse;
import com.fedex.mets.entity.ldap.User;
import com.fedex.mets.service.cancelEvent.EventCancelService;
import com.fedex.mets.service.retrieval.UserService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Optional;

@RestController
@RequestMapping("/api/mets")
@Tag(name = "METS CANCEL EVENT", description = "Endpoint for cancelling an event.")
public class MetsCancelEventController {
    private static final Logger logger = LoggerFactory.getLogger(MetsCancelEventController.class);

    @Autowired
    private EventCancelService eventCancelService;
    
    @Autowired
    private UserService userService;

    @Operation(summary = "Cancelling an event.", description = "Cancelling an event in METS system.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully cancelled an event."),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @PostMapping(path = "/cancelEvent")
    public ResponseEntity<MetsResponse> cancelEvent(@RequestBody WizardEventData request) throws Exception {
        MetsResponse cancelEventResponse = new MetsResponse();
        HashMap hashmap = new HashMap();
        String userId = request.getUserId();
        String tokenId = request.getTokenId();
        try {
            if (request != null) {
                if (!userId.isEmpty() && !tokenId.isEmpty() && request.getAccessLevel() != null && request.getACN() != null && request.getEventId() > 0) {
                    logger.info(".....calling the cancelEvent");
                    logger.info("Test4" , request.getEventId());
                    Optional<User> user = userService.getUserByUid(userId);
                    request.setEmpDepartment(user.get().getDepartmentname());
                    hashmap = eventCancelService.cancelEventWrapper(request);
                    if (hashmap != null) {
                        cancelEventResponse.setData(hashmap);
                    } else {
                        return ResponseEntity.status(500).build();
                    }
                } else {
                    logger.info("One or more essential parameters are missing from the request.");
                }
            }
            return ResponseEntity.ok(cancelEventResponse);
        } catch (IllegalArgumentException e) {
            logger.error("Invalid input for cancel Event: Wizard Event: {}. Error: {}", request, e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error cancelling the event", e);
            return ResponseEntity.status(500).build();
        }
    }
}
