package com.fedex.mets.util;

public class ReportedEnteredLLineFormat extends BaseLineFormat {

	static int line = 80;
	
	@Override
	protected void init(){
		sfd = new SentenceFormatDef("Update Line", line, 17, 16);
		addControlNbrConstant();
		addControlNbr();
		addRIIConstant();
		addRIINbr();
		addEnteredByConstant();
		addEnteredNbr();
		addReportedByConstant();
		addReportedNbr();
		addEtopsAtaConstant();
		addEtopsAtaNbr();
	}

	private void addControlNbrConstant() {
		FieldFormatDef ffd = new FieldFormatDef("Control Constant",  2);
		ffd.setValue("C/");
		sfd.getFields().add(ffd);
	}

	private void addControlNbr() {
		FieldFormatDef ffd = new FieldFormatDef("Control Nbr",  10);
		sfd.getFields().add(ffd);
	}

	private void addEnteredByConstant() {
		FieldFormatDef ffd = new FieldFormatDef("EnteredByConstant",  7);
		ffd.setValue("ENT BY:");
		sfd.getFields().add(ffd);
	}

	private void addEnteredNbr() {
		FieldFormatDef ffd = new FieldFormatDef("Entered Nbr",  7, false);
		sfd.getFields().add(ffd);
	}

	private void addReportedByConstant() {
		FieldFormatDef ffd = new FieldFormatDef("ReportedByConstant",  8, false);
		ffd.setValue("RPT BY:");
		sfd.getFields().add(ffd);
	}

	private void addReportedNbr() {
		FieldFormatDef ffd = new FieldFormatDef("Reported Nbr",  7, false);
		sfd.getFields().add(ffd);
	}

	private void addRIIConstant() {
		FieldFormatDef ffd = new FieldFormatDef("RIIConstant", 7, true);
		ffd.setValue("RII:");
		sfd.getFields().add(ffd);
	}

	private void addRIINbr() {
		FieldFormatDef ffd = new FieldFormatDef("RII Nbr", 7, true);
		sfd.getFields().add(ffd);
	}

	private void addEtopsAtaConstant() {
		FieldFormatDef ffd = new FieldFormatDef("EtopsAtaConstant", 26, true);
		ffd.setValue("\t\t\tDual Maint. Signoff: ATA:  ");
		sfd.getFields().add(ffd);
	}

	private void addEtopsAtaNbr() {
		FieldFormatDef ffd = new FieldFormatDef("Etops Nbr", 7, true);
		sfd.getFields().add(ffd);
	}

}

