package com.fedex.mets.service.update;

import com.fedex.mets.data.EventTfNotesDto;
import com.fedex.mets.data.MetsEventUpdateEntity;
import com.fedex.mets.entity.mets.EventTfNotes;
import com.fedex.mets.entity.mets.EventTfNotesPk;
import com.fedex.mets.repository.mets.EventTfNotesRepository;
import com.fedex.mets.util.IServerConstants;
import com.fedex.mets.util.ServerDateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.rmi.RemoteException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;


@Service
public class TFNotesUpdateService {

    private static final Logger logger = LoggerFactory.getLogger(TFNotesUpdateService.class);
    @Autowired
    private EventTfNotesRepository tfNotesRepo;


    public Map<String, Object> TFNotes(String flag, MetsEventUpdateEntity request, Map<String, Object> hashMap,
                                       EventTfNotesDto tfNotesData,Boolean eventActive) throws Exception {
        List<EventTfNotes> elements = new ArrayList<>();
        boolean resultFromBean = false, isEventActive = true;

        if (eventActive != null) {
            isEventActive = eventActive.booleanValue();
        }
        try {
            if (flag.equalsIgnoreCase(IServerConstants.ADD)) {
                resultFromBean =
                        addTFNotes(
                                tfNotesData);
            } else {
                resultFromBean =
                        editTFNotes(
                                tfNotesData);
            }
        } catch (Exception exec) {
            logger.warn(
                    "ERROR MetsUpdate Servlet addTFNotes() exec >> " + exec.getMessage());
            String msg = exec.getMessage();
            if (msg.trim().length() >= 3) {
                if (msg.substring(msg.length() - 3, msg.length()).equals("512")) {
                    hashMap.put(IServerConstants.ERROR, "512");
                } else {
                    hashMap.put(
                            IServerConstants.ERROR,
                            "Could not ADD TF_NOTE to the Database " + exec.getMessage());
                }
            } else {
                hashMap.put(
                        IServerConstants.ERROR,
                        "Could not ADD TF_NOTE to the Database " + exec.getMessage());
            }
        }
        if (resultFromBean) {
            int eventId = Math.toIntExact(tfNotesData.getEventId());
            elements = tfNotesRepo.getAllTFNotes(eventId);
//            publishEventUpdate(eventId, "TFNotes");
            hashMap.put(IServerConstants.TF_NOTES_OBJECT, elements);
        } else {
            hashMap.put(IServerConstants.ERROR, "Could not ADD TF_NOTE to the Database");
        }
        return hashMap;
    }

    public boolean addTFNotes(EventTfNotesDto tfNotesData) throws Exception {
        logger.info("in the addTFNotes of the TFNoteUpdateRepository..........");
        boolean result = false;

			/*
			First Step before doing any thing would be to Check the Security Level of the User trying to ADD/Update TF Notes for an Event.
			**/
        //TODO token validation

        int eventId = 0, noteType = 0, noteId = 0, changeType = 0;
        String tfDateTime = "",
                empNumber = "",
                empName = "",
                empDepartment = "",
                editedFlag = "",
                tfNote = "";
        int recordNumber = 0;

        eventId = Math.toIntExact(tfNotesData.getEventId());
        logger.info("eventId" + eventId);
        tfDateTime = String.valueOf(tfNotesData.getTfDateTime());
        logger.info("tfDateTime" + tfDateTime);
        empNumber = tfNotesData.getEmpNumber();
        logger.info("empNumber" + empNumber);
        empName = tfNotesData.getEmpName();
        logger.info("empName" + empName);
        empDepartment = tfNotesData.getEmpDepartment();
        logger.info("empDepartment" + empDepartment);
        editedFlag = tfNotesData.getEditedFlag();
        logger.info("editedFlag" + editedFlag);
        tfNote = tfNotesData.getTfNote();
        logger.info("tfNote" + tfNote);
        noteType = tfNotesData.getNoteType();
        logger.info("noteType" + noteType);
        noteId = tfNotesData.getNoteId();
        logger.info("noteId" + noteId);
        changeType = tfNotesData.getChangeType();
        logger.info("changeType" + changeType);

        try {
            recordNumber = tfNotesRepo.getMaxNoteId(eventId) != null ? tfNotesRepo.getMaxNoteId(eventId) : 0;

            recordNumber += 1;
            logger.info("recordNumber after incrementing ========================" + recordNumber);

        } catch (Exception ee) {

            logger.warn(" ERROR addTFNotes() >>" + ee.getMessage());
            throw new RemoteException("getTFNotes() Problem finding the Event NIW Timers Data");

        }
        try {

            Timestamp tfNotesDateTime = ServerDateHelper.getTimeStamp();
            logger.info("tfNotesDateTime  after converting -------" + tfNotesDateTime);

            Timestamp lastUpdated = ServerDateHelper.getTimeStamp();
            logger.info("lastUpdated  after converting -------" + lastUpdated);
            EventTfNotes eventTfNotes = new EventTfNotes();
            eventTfNotes.setEventTfNotesPk(new EventTfNotesPk(eventId, tfNotesDateTime));
            eventTfNotes.setEmpNum(empNumber);
            eventTfNotes.setEmpName(empName);
            eventTfNotes.setEmpDepartment(empDepartment);
            eventTfNotes.setEditedFlag(editedFlag);
            eventTfNotes.setTfNote(tfNote);
            eventTfNotes.setNoteType(noteType);
            eventTfNotes.setNoteId(recordNumber);
            eventTfNotes.setChangeType(changeType);
            eventTfNotes.setLastUpdateDtTm(lastUpdated);
            tfNotesRepo.save(eventTfNotes);
            result = true;

        } catch (DuplicateKeyException duplicateKey) {

            Date currentDate = ServerDateHelper.getDate_UTC();
            Timestamp tfNotesDateTime = new Timestamp(currentDate.getTime() + 1000);
            logger.info("incremented tfNotesDateTime >>>>>>>>>>>>>> " + tfNotesDateTime);

            Timestamp lastUpdated = ServerDateHelper.getTimeStamp();
            logger.info("lastUpdated  after converting -------" + lastUpdated);
            try {
                EventTfNotes eventTfNotes = new EventTfNotes();
                eventTfNotes.setEventTfNotesPk(new EventTfNotesPk(eventId, tfNotesDateTime));
                eventTfNotes.setEmpNum(empNumber);
                eventTfNotes.setEmpName(empName);
                eventTfNotes.setEmpDepartment(empDepartment);
                eventTfNotes.setEditedFlag(editedFlag);
                eventTfNotes.setTfNote(tfNote);
                eventTfNotes.setNoteType(noteType);
                eventTfNotes.setNoteId(recordNumber);
                eventTfNotes.setChangeType(changeType);
                eventTfNotes.setLastUpdateDtTm(lastUpdated);
                tfNotesRepo.save(eventTfNotes);
            } catch (Exception e) {

                logger.warn(" ERROR Problem with Key even after incrementing the time");
                logger.warn(" ERROR addTFNotes() update>>" + e.getMessage());
                throw new RemoteException(e.getMessage());

            }

        }
        return result;
    }

    //	public Map<String, Object> editTFNotesDup(MetsEventUpdateEntity request, Map<String, Object> hashMap,
//										   EventTfNotesDto tfNotesData, String userId, String tokenId, Boolean eventActive) throws Exception {
//
//	}
    public boolean editTFNotes(EventTfNotesDto tfNotesData) throws Exception {
        boolean result = false;

			/*
			First Step before doing any thing would be to Check the Security Level of the User trying to ADD/Update the Event.
			**/
        //TODO Validaion of token

        int eventId = 0;
        String tfDateTime = "",
                empNumber = "",
                empName = "",
                empDepartment = "",
                tfNote = "";

        eventId = Math.toIntExact(tfNotesData.getEventId());
        tfDateTime = String.valueOf(tfNotesData.getTfDateTime());
        tfNote = tfNotesData.getTfNote();
        empNumber = tfNotesData.getEmpNumber();
        logger.info("empNumber" + empNumber);
        empName = tfNotesData.getEmpName();
        logger.info("empName" + empName);
        empDepartment = tfNotesData.getEmpDepartment();
        logger.info("empDepartment" + empDepartment);

        logger.info("eventId    ==>" + eventId);
        logger.info("tfDateTime ==>" + tfDateTime);
        logger.info("tfNote     ==>" + tfNote);

        Timestamp creationTimeStamp = null, lastUpdated = null;

        lastUpdated = ServerDateHelper.getTimeStamp();
        logger.info("lastUpdated  after converting -------" + lastUpdated);

        if (tfDateTime != null) {
            creationTimeStamp = ServerDateHelper.getConvertedTimestamp(tfDateTime);
            logger.info("creationTimeStamp  after converting -------" + creationTimeStamp);
        }
        try {
            EventTfNotes eventTfNotes = tfNotesRepo.getTFNotes(eventId, creationTimeStamp);
            eventTfNotes.setEmpNum(empNumber);
            eventTfNotes.setEmpName(empName);
            eventTfNotes.setEmpDepartment(empDepartment);
            eventTfNotes.setLastUpdateDtTm(lastUpdated);
            eventTfNotes.setTfNote(tfNote);
            eventTfNotes.setEditedFlag("Y");
            tfNotesRepo.save(eventTfNotes);
            result = true;
        } catch (Exception c) {
            logger.warn(" ERROR editTFNotes() >>" + c.getMessage());
        }
        return result;
    }

//    public void publishEventUpdate(int eventId, String strType) {
//
//        Connection conn1 = null;
//        Statement stat1 = null;
//        ResultSet rs1 = null;
//        String queryString = "";
//        try {
//            conn1 = metsJdbcTemplate.getDataSource().getConnection();
//            stat1 = conn1.createStatement();
//
//            queryString =
//                    "select * from EVENT_TF_NOTES where LAST_UPDATE_DT_TM = (select max(LAST_UPDATE_DT_TM) from EVENT_TF_NOTES where EVENT_ID='"
//                            + eventId
//                            + "')";
//
//            rs1 = stat1.executeQuery(queryString);
//            while (rs1.next()) {
//
//                tfNotes.setEventId(rs1.getInt("EVENT_ID"));
//                tfNotes.setTfDateTime(rs1.getString("TF_DT_TM"));
//                tfNotes.setEmpNumber(rs1.getString("EMP_NUM"));
//                tfNotes.setEmpName(rs1.getString("EMP_NAME"));
//                tfNotes.setEmpDepartment(rs1.getString("EMP_DEPARTMENT"));
//                tfNotes.setEditedFlag(rs1.getString("EDITED_FLAG"));
//                tfNotes.setTfNote(rs1.getString("TF_NOTE"));
//                tfNotes.setNoteType(rs1.getInt("NOTE_TYPE"));
//                tfNotes.setNoteId(rs1.getInt("NOTE_ID"));
//                tfNotes.setChangeType(rs1.getInt("CHANGE_TYPE"));
//            }
//        } catch (Exception c) {
//            logger.warn(" ERROR publishEventUpdate() >>" + c.getMessage());
//        } finally {
//
//            try {
//
//                if (rs1 != null)
//                    rs1.close();
//                if (stat1 != null)
//                    stat1.close();
//                if (conn1 != null)
//                    conn1.close();
//
//            } catch (Exception close) {
//                logger.warn(" ERROR publishEventUpdate() closing connection >>" + close.getMessage());
//            }
//
//        }

//			try {
//				topicConnection = reEstablishConnection();
//				
//				publishMessage(topicConnection, strType, tfNotes);
//			} catch (RemoteException jmsException) {
//				getLogger().info("trying to reEstablish Connection.......... count ");
//				try {
//					topicConnection = reEstablishConnection();
//
//					publishMessage(topicConnection, strType, tfNotes);
//				} catch (Exception reconnect) {
//					getLogger().warn(
//						" ERROR publishEventUpdate() reconnect >>" + reconnect.getMessage());
//					//				reconnect.printStackTrace();
//				}
//			} catch (Throwable t) {
//				getLogger().warn(
//					" ERROR publishEventUpdate() throw excption>>" + t.getMessage());
//				context.setRollbackOnly();
//			}
//    }

    public void securityCheck(String userId, String tokenId, boolean isEventActive) throws Exception {

        try {

            List<Object> acessFlagList = null;

            if (userId != null && tokenId != null) {
                try {

//					acessFlagVector = SecurityHelper.getAccessFlags(userId, tokenId, strTransactionId);
                    acessFlagList = new ArrayList<Object>();
                    acessFlagList.add("SUCCESS");
                    acessFlagList.add("80");

                } catch (Exception e) {
                    logger.warn("ERROR NIWTimersUpdateRepo setNIWTimers() SecurityHelper.getAccessFlags exception " + e.getMessage());
                    throw new Exception("512");
                }
            }

            //for Start/Stop Timer check access flag 1 is 80 or 90 else throw exception
            if (acessFlagList != null) {
                String firstElement = (String) acessFlagList.get(0);

                if (firstElement.trim().equals("SUCCESS")) {
                    if (isEventActive) {
                        String strSecurityAccess = (String) acessFlagList.get(1);

                        if (strSecurityAccess.trim().equals("80") || strSecurityAccess.trim().equals("90")) {

                            logger.info(" ");
                            logger.info("User " + userId + " has access to START/STOP Timer of an Event.");

                        } else {
                            throw new Exception("User does not have permission/access to START/STOP Timer of to an Event.");
                        }

                    } else {
                        String strSecurityAccess = (String) acessFlagList.get(2);

                        if (strSecurityAccess.trim().equals("99")) {

                            logger.info(" ");
                            logger.info("User " + userId + " has access to START/STOP Timer of an Event.");

                        } else {
                            throw new Exception("User does not have permission/access to to START/STOP Timer of an Inactive Event.");
                        }
                    }
                } else {

                    if (firstElement.trim().equals("512")) {
                        throw new Exception(firstElement);
                    } else {
                        String strSecurityAccessError = (String) acessFlagList.get(1);
                        throw new Exception(strSecurityAccessError);
                    }

                }
            } else if (acessFlagList == null) {
                throw new Exception("User does not have permission/access to START/STOP Timer of an Event.");
            }

        } catch (Exception securityRemote) {

            logger.warn(" ERROR NIW Timers setNIWTimerDetails() securityRemote >> " + securityRemote.getMessage());
            throw new Exception(securityRemote.getMessage());

        }

    }


}
