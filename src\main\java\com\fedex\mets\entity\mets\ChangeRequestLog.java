package com.fedex.mets.entity.mets;

import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Entity
@Table(name = "CHANGE_REQUEST_LOG")
public class ChangeRequestLog {
    @EmbeddedId
    private ChangeRequestLogPk changeRequestLogPk;

    @Column(name = "OLD_REQUEST_STATUS")
    private String oldRequestStatus;

    @Column(name = "NEW_REQUEST_STATUS")
    private String newRequestStatus;

}
