package com.fedex.mets.config;

import java.security.Principal;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

import com.fedex.airops.mss.auth.jwt.JwtUtil;

@Component
public class JwtHandshakeInterceptor implements HandshakeInterceptor {


    @Autowired
    private Environment env;

    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response,
                                   WebSocketHandler wsHandler, Map<String, Object> attributes) {

        if (request instanceof ServletServerHttpRequest servletRequest) {
            String token = servletRequest.getServletRequest().getParameter("access_token");
            if (token != null && tokenIsValid(token)) {
                String username = JwtUtil.getEmployeeNumber(token);

                Principal principal = () -> username;
                attributes.put("principal", principal);
                return true;
            }
        }
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        return false;
    }

    @Override
    public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response,
                               WebSocketHandler wsHandler, Exception exception) {
        // no-op
    }

    public boolean tokenIsValid(String token) {
        if (token == null || token.trim().isEmpty()) {
            return false;
        }
        if (JwtUtil.isJwt(token)) {
            return JwtUtil.validateJwtLocallyWithoutSignature(token, env.getProperty("app.id"), env.getProperty("app.okta.uri"));
        } else {
            return false;
        }
    }
}