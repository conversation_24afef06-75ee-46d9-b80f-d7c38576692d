package com.fedex.mets.service.retrieval;

import com.fedex.mets.dao.EventListView;
import com.fedex.mets.dao.ManagerDetails;
import com.fedex.mets.data.ListViewData;
import com.fedex.mets.repository.cache.SuperEquipmentRepository;
import com.fedex.mets.repository.mars.EmpDetailRepository;
import com.fedex.mets.repository.mets.EventDoaRepository;
import com.fedex.mets.repository.mets.EventRepCatgRepository;
import com.fedex.mets.repository.mets.EventsRepository;
import com.fedex.mets.repository.mss.ForteLegRepository;
import com.fedex.mets.util.RvDBHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.rmi.RemoteException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

public class EventListViewServiceTest {

    @Mock
    private EventDoaRepository eventDoaRepository;

    @Mock
    private EventsRepository eventsRepository;

    @Mock
    private SuperEquipmentRepository superEquipmentRepository;

    @Mock
    private EmpDetailRepository empDetailRepository;

    @Mock
    private EventRepCatgRepository eventRepCatgRepository;

    @Mock
    private ForteLegRepository forteLegRepository;

    @Mock
    private RvDBHelper rvDBHelper;

    @InjectMocks
    EventListViewService eventListViewService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testGetManagerDetail_Success() {
        String station = "Station1";

        ManagerDetails managerDetails = new ManagerDetails();
        managerDetails.setDeptId("Manager1");
        managerDetails.setEmpFirstName("John Doe");

        List<ManagerDetails> managerDetailsList = new ArrayList<>();
        managerDetailsList.add(managerDetails);

        when(empDetailRepository.getManagersList(anyString())).thenReturn(managerDetailsList);

        List<ManagerDetails> result = eventListViewService.getManagerDetail(station);

        assertEquals(1, result.size());
        assertEquals("Manager1", result.get(0).getDeptId());
        assertEquals("John Doe", result.get(0).getEmpFirstName());
    }

    @Test
    public void testGetManagerDetail_Exception() {
        String station = "Station1";

        doThrow(new RuntimeException("Internal server error")).when(empDetailRepository).getManagersList(anyString());

        List<ManagerDetails> result = eventListViewService.getManagerDetail(station);

        assertEquals(0, result.size());
    }

    @Test
    public void testGetEventList_Success() throws RemoteException {
        EventListView eventListView = new EventListView();
        eventListView.setEventId(1);
        eventListView.setType("DOA");
        eventListView.setStartDateTime(Timestamp.valueOf("2025-04-11 10:00:00"));
        eventListView.setEndDateTime(Timestamp.valueOf("2025-04-12 10:00:00"));
        eventListView.setAcn("ACN123");
        eventListView.setFleetDesc("FleetDesc");
        eventListView.setStation("Station");
        eventListView.setStatus("Status");
        eventListView.setEticDateTime(Timestamp.valueOf("2025-04-11 12:00:00"));
        eventListView.setEticText("EticText");
        eventListView.setCurrentComment("CurrentComment");
        eventListView.setLastUpdateDtTm(Timestamp.valueOf("2025-04-12 10:00:00"));
        eventListView.setLastUpdatedBy("User1");
        eventListView.setCreatedDateTime(Timestamp.valueOf("2025-04-10 10:00:00"));
        eventListView.setCreatedBy("User1");
        eventListView.setRequestStatus("S");
        eventListView.setChangeType(1);
        eventListView.setNewStatus("NewStatus");
        eventListView.setNewEticText("NewEticText");
        eventListView.setNewEticRsnComment("NewEticRsnComment");
        eventListView.setNewEticDtTm(Timestamp.valueOf("2025-04-11 14:00:00"));
        eventListView.setAcOwnerGroupId("GroupId");
        eventListView.setGroupTitle("GroupTitle");
        eventListView.setActiveEvent("Y");
        eventListView.setOst("OST");
        eventListView.setNewOst("NewOST");
        eventListView.setEticRsnCd("EticRsnCd");
        eventListView.setNewEticRsnCd("NewEticRsnCd");
        eventListView.setEticRsnComment("EticRsnComment");
        eventListView.setNewEticRsnComment("NewEticRsnComment");

        List<EventListView> eventListViews = new ArrayList<>();
        eventListViews.add(eventListView);

        when(eventsRepository.getListViewList()).thenReturn(eventListViews);

        List<ListViewData> result = eventListViewService.getEventList();

        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getEventID());
        assertEquals("DOA", result.get(0).getType());
        assertEquals("04/11 10:00", result.get(0).getStartDateTime());
        assertEquals("2025-04-12 10:00:00.0", result.get(0).getEndDateTime());
        assertEquals("ACN123", result.get(0).getACN());
        assertEquals("FleetDesc", result.get(0).getFleetDesc());
        assertEquals("Station", result.get(0).getStation());
        assertEquals("NewStatus", result.get(0).getStatus());
        assertEquals("1400/041125", result.get(0).getEticDateTime());
        assertEquals("NewEticText", result.get(0).getEticText());
        assertEquals("NewEticRsnComment", result.get(0).getCurComment());
        assertEquals("2025-04-12 10:00:00.0", result.get(0).getLastUpdateDateTime());
        assertEquals("User1", result.get(0).getLastUpdatedBy());
        assertEquals("2025-04-10 10:00:00.0", result.get(0).getCreatedDateTime());
        assertEquals("User1", result.get(0).getCreatedBy());
        assertEquals("S", result.get(0).getRequestStatus());
        assertEquals("1", result.get(0).getChangeType());
        assertEquals("NewStatus", result.get(0).getNewStatus());
        assertEquals("NewEticText", result.get(0).getNewEticText());
        assertEquals("NewEticRsnComment", result.get(0).getNewEticComment());
        assertEquals("1400/041125", result.get(0).getNewEticDateTime());
        assertEquals("GroupId", result.get(0).getAcOwnerGroupId());
        assertEquals("GroupTitle", result.get(0).getOwner());
        assertEquals(true, result.get(0).isEventActive());
        assertEquals("OST", result.get(0).getOst());
        assertEquals("NewOST", result.get(0).getNewOST());
        assertEquals("EticRsnCd", result.get(0).getEticReasonCd());
        assertEquals("NewEticRsnCd", result.get(0).getNewEticReasonCd());
        assertEquals("EticRsnComment", result.get(0).getEticRsnComments());
        assertEquals("NewEticRsnComment", result.get(0).getNewEticRsnComments());
    }

}
