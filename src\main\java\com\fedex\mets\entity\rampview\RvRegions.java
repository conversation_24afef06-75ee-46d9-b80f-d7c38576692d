package com.fedex.mets.entity.rampview;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "rv_regions")
public class RvRegions {

    @Id
    @Column(name = "REGION_ID", nullable = false)
    private Long regionId;

    @Column(name = "DESCRIPTION", nullable = false)
    private String description;

    @Column(name = "ORGANIZATION_CD", nullable = false)
    private String organizationCd;

    @Column(name = "PARENT_REGION_ID")
    private Long parentRegionId;

    @Column(name = "DERIVED_IS_ROOT_NODE")
    private Character derivedIsRootNode;

    @Column(name = "DERIVED_IS_LEAF_NODE")
    private Character derivedIsLeafNode;

    @Column(name = "MAINT_ACTION")
    private Character maintAction;

    @Column(name = "MAINT_USER_CD")
    private String maintUserCd;

    @Column(name = "MAINT_DTM")
    private Timestamp maintDtm;

    @Column(name = "MAINT_VERSION")
    private Long maintVersion;

    @Column(name = "MAINT_FUNCTION")
    private String maintFunction;
}

