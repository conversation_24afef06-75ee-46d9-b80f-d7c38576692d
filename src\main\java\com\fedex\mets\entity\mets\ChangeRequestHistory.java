package com.fedex.mets.entity.mets;


import jakarta.persistence.*;
import lombok.*;

import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Entity
@Table(name = "CHANGE_REQUEST_HIST")
public class ChangeRequestHistory {
    @EmbeddedId
    private ChangeRequestHistoryPk changeRequestHistoryPk;

    @Column(name = "OLD_ETIC_DT_TM")
    private Timestamp oldEticDtTm;

    @Column(name = "NEW_ETIC_DT_TM")
    private Timestamp newEticDtTm;

    @Column(name = "OLD_ETIC_TEXT")
    private String oldEticText;

    @Column(name = "NEW_ETIC_TEXT")
    private String newEticText;

    @Column(name = "OLD_COMMENT")
    private String oldComment;

    @Column(name = "NEW_COMMENT")
    private String newComment;

    @Column(name = "OLD_STATUS")
    private String oldStatus;

    @Column(name = "NEW_STATUS")
    private String newStatus;

    @Column(name = "REQUEST_STATUS")
    private String requestStatus;

    @Column(name = "LAST_UPDATE_DT_TM")
    private Timestamp lastUpdateDtTm;

    @Column(name = "ENTERED_IN_ERROR")
    private String enteredInError;

    @Column(name = "CHANGE_TYPE")
    private Integer changeType;

    @Column(name = "CHANGE_EVENT_END_DT_TM")
    private Timestamp changeEventEndDtTm;

    @Column(name = "OLD_OST")
    private String oldOst;

    @Column(name = "NEW_OST")
    private String newOst;

    @Column(name = "OLD_ETIC_RSN_CD")
    private String oldEticRsnCd;

    @Column(name = "NEW_ETIC_RSN_CD")
    private String newEticRsnCd;

    @Column(name = "OLD_ETIC_RSN_COMMENT")
    private String oldEticRsnComment;

    @Column(name = "NEW_ETIC_RSN_COMMENT")
    private String newEticRsnComment;

}
