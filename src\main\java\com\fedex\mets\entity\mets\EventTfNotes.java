package com.fedex.mets.entity.mets;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Date;
import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "EVENT_TF_NOTES")
public class EventTfNotes {
    @EmbeddedId
    private EventTfNotesPk eventTfNotesPk;

    @Column(name = "EMP_NUM")
    private String empNum;

    @Column(name = "EMP_NAME")
    private String empName;

    @Column(name = "EMP_DEPARTMENT")
    private String empDepartment;

    @Column(name = "EDITED_FLAG")
    private String editedFlag;

    @Column(name = "TF_NOTE")
    private String tfNote;

    @Column(name = "NOTE_TYPE", nullable = false)
    private Integer noteType;

    @Column(name = "NOTE_ID", nullable = false)
    private Integer noteId;

    @Column(name = "CHANGE_TYPE")
    private Integer changeType;

    @Column(name = "LAST_UPDATE_DT_TM")
    private Timestamp lastUpdateDtTm;
}