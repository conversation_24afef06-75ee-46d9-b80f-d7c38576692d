
package com.fedex.mets.wsdl.discrepancy;

import java.math.BigDecimal;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for acnDiscrepFaultCdType complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="acnDiscrepFaultCdOid" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="faultCd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "acnDiscrepFaultCdType", propOrder = {
    "acnDiscrepFaultCdOid",
    "faultCd"
})
public class AcnDiscrepFaultCdType {

    @XmlElement(required = true)
    protected BigDecimal acnDiscrepFaultCdOid;
    @XmlElement(required = true)
    protected String faultCd;

    /**
     * Gets the value of the acnDiscrepFaultCdOid property.
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getAcnDiscrepFaultCdOid() {
        return acnDiscrepFaultCdOid;
    }

    /**
     * Sets the value of the acnDiscrepFaultCdOid property.
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setAcnDiscrepFaultCdOid(BigDecimal value) {
        this.acnDiscrepFaultCdOid = value;
    }

    /**
     * Gets the value of the faultCd property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFaultCd() {
        return faultCd;
    }

    /**
     * Sets the value of the faultCd property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFaultCd(String value) {
        this.faultCd = value;
    }

}
