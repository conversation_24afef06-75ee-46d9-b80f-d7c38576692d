package com.fedex.mets.dao;

import jakarta.persistence.Column;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ReportCategoryKeyValues {

    @Column(name = "EVENT_ID")
    public Integer eventId;

    @Column(name = "LEVEL_1_ID", nullable = false)
    private String level1Id;

    @Column(name = "LEVEL_2_ID", nullable = false)
    private String level2Id;

    @Column(name = "LAST_UPDATED_DT_TM")
    private Timestamp lastUpdatedDtTm;

    @Column(name = "LEVEL_2_NAME")
    private String level2Name;


}
