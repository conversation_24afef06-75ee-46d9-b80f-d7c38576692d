package com.fedex.mets.data;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class DOAData implements Serializable{
	//static final long serialVersionUID =	6643552706229952473L;
	
	public Integer		eventId;
	public String	doaOriginator;
	public String	createdAt;
	public boolean	checkFlightRequrired=false;
	public String	comment;
	public String	flightNumber;
	public String	flightDate;
	public String	flightLegNumber;
	public String	destination;
	public String	estimatedTimeOfArrival;
	/* 
	As there could be more than one discrepancy/ies for an Event a list is declared to hold the values.

	public String	ata;
	public String	discrepancy;
	public String	discrepancyText;
	*/
	public List<DOADiscrepancyData>	discVector;
	public String	additionalDescription;
	public String	closedBy;
	public String	closedAt;
	public boolean	maintenanceCrew=false;
	public String	lastUpdated;

}	