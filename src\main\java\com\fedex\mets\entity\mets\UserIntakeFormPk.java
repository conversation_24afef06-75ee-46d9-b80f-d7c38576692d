package com.fedex.mets.entity.mets;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Embeddable
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class UserIntakeFormPk implements Serializable{

    @Column(name = "role_id", nullable = false)
    private int roleId;

    @Column(name = "event_type_id", nullable = false)
    private int eventId;

    @Column(name="DSS_AUTH_CD",nullable = false)
    private String dssAuthCode;
}
