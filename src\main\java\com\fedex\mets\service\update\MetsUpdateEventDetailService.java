package com.fedex.mets.service.update;

import com.fedex.mets.data.DetailViewData;
import com.fedex.mets.data.DetailViewDataEntity;
import com.fedex.mets.data.MetsEventUpdateEntity;
import com.fedex.mets.entity.mets.Events;
import com.fedex.mets.repository.mets.EventsRepository;
import com.fedex.mets.repository.mets.GroupDictRepository;
import com.fedex.mets.service.retrieval.EventListDetailViewService;
import com.fedex.mets.util.IServerConstants;
import com.fedex.mets.util.ServerDateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.Reader;
import java.sql.Clob;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class MetsUpdateEventDetailService {

    private static final Logger log = LoggerFactory.getLogger(MetsUpdateDOAEventDetailService.class);
    @Autowired
    EventsRepository eventsRepository;

    @Autowired
    GroupDictRepository groupDictRepository;

    @Autowired
    EventListDetailViewService eventListDetailViewService;

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    public Map<String, Object> updateEventDetail(Map<String, Object> hashMap,
                                                 DetailViewDataEntity detailViewData, String accessLevel, String userId, String tokenId) throws Exception {

        List<DetailViewData> detailViewDataObj = new ArrayList<>();
		boolean resultFromBean = false;
		int eventId = detailViewData.getEventID(), lastUpdatedRecords = 0;
        String strLastUpdatedTime = detailViewData.getEventLastUpdateDateTime();

		lastUpdatedRecords =findLastUpdated(eventId, strLastUpdatedTime);
		if (lastUpdatedRecords == 0) {
			resultFromBean =
					updateEventData(
							detailViewData,
							accessLevel,
							userId,
							tokenId);
		}
		if(resultFromBean){
			if (detailViewData.isEventActive()) {
                detailViewDataObj =
                        eventListDetailViewService.getEventDetail(
								detailViewData.getEventACN(),
								userId);
			} else {
                detailViewDataObj =
                        eventListDetailViewService.getEventDetail(
                                detailViewData.getEventACN(),
                                userId);
//						detailSession.getInactiveEventDetail(
//								detailViewData.getEventID(),
//								userId,
//								tokenId);
			}
            hashMap.put(IServerConstants.DETAIL_VIEW_OBJECT, detailViewDataObj);
		}else if (lastUpdatedRecords > 0) {
            hashMap.put(
                    IServerConstants.ERROR,
                    "Could not Alter the data as the Record has been modified prior to this transaction.");
        }


        return hashMap;
    }

    /**
     * The following findLastUpdated() is used to find if the record was updated prior to UPDATE of current transaction.
     *
     * @return int records.
     * @params int eventId, String lastUpdatedTime.
     */
    public int findLastUpdated(int eventId, String lastUpdatedTime) {
        int existingRecords = 0;
		String lookupUpdatedTime =
				ServerDateHelper.getLookUpFormat(lastUpdatedTime);
        // TODO need to send the lookupUpdatedTime
        existingRecords = eventsRepository.getCountOfUpdatedRecords(eventId,lookupUpdatedTime);
        return existingRecords;

    }

    /**
     * The following updateEventData() is used to update the detail data for a particular event.
     *
     * @return boolean result.
     * @params DetailViewData detailViewData, String accessLevel, String userId, String tokenId.
     */
    public boolean updateEventData(
			DetailViewDataEntity detailViewData,
            String accessLevel,
            String userId,
            String tokenId) {
        //TODO need to implmenet token validation
        //TODO need to implement access level validation
        boolean result = false;
        Events events = new Events();
        int eventId = detailViewData.getEventID();
        String strStartDateTime = detailViewData.getStartDateTimeUTC();
        String groupId = groupDictRepository.getGroupIdByGroupTitle(detailViewData.getAcOwnerGroupId());
        events = eventsRepository.getEventsByEventId(eventId);
        if (events == null) {
            //TODO throw error
        }
        if (events != null) {
            Timestamp startTimeStamp = null,
                    endDateTimeStamp = null,
                    updatedTimestamp = null;
            if (strStartDateTime != null && !strStartDateTime.equalsIgnoreCase("null")) {
                startTimeStamp = ServerDateHelper.getConvertedTimestampUTC(strStartDateTime);
            }
            updatedTimestamp = ServerDateHelper.getTimeStamp();
            if (detailViewData.getEndDateTime() != null && !detailViewData.getEndDateTime().isEmpty() && !detailViewData.getEndDateTime().equalsIgnoreCase("null")) {
                endDateTimeStamp = ServerDateHelper.getConvertedTimestampUTC(detailViewData.getEndDateTime());
            }

            events.setStartDateTime(startTimeStamp);
            events.setLastUpdateDateTime(updatedTimestamp);
            events.setStation(detailViewData.getEventStation());
            if (endDateTimeStamp != null) {
                events.setEndDateTime(endDateTimeStamp);
            }
            events.setAcOwnerGroupId(groupId);
            events.setPrimaryContact(detailViewData.getContact());
            //TODO need to implement the below
			events.setLastUpdatedBy("240405");
            /**QES is this below one necessary */
//			events.(detailViewData.getResMgrId());

            events.setMemDeskContact(detailViewData.getMemDeskContact());
            events.setCurComment(detailViewData.getEventCurrentComment());
//			events.setMgrNotes(null);

            if (detailViewData.isEventCancelled()) {
                events.setCancelled("Y");
            } else {
                events.setCancelled("N");
            }
            Clob clobComment = null;
            Clob finalClob=null;
            String clobToString="";
            if (detailViewData.getManagerNote() != null && detailViewData.getManagerNote().trim().length() > 0) {
                clobComment = eventsRepository.findMgrNotesByEventId(eventId);

                if (clobComment != null) {
                    try {
                        log.info("CLOB Updated"+clobComment);
                        if(clobComment.getCharacterStream()!=null){
                            clobToString=readClob(clobComment);
                        }
                        String updatedString = detailViewData.getManagerNote()+clobToString;
                         finalClob = new javax.sql.rowset.serial.SerialClob(updatedString.toCharArray());
                    } catch (SQLException e) {
                        throw new RuntimeException(e);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }
            }
            events.setMgrNotes(finalClob);
            eventsRepository.save(events);
            result = true;
            messagingTemplate.convertAndSend("/topic/notifications", "Manager Notes Updated for : "+ detailViewData.getEventACN());
        }

        return result;
    }

    private String readClob(Clob clob) throws IOException, SQLException {
        // Read the CLOB data into a String
        StringBuilder sb = new StringBuilder();
        try (Reader reader = clob.getCharacterStream(1, (int) clob.length());
             BufferedReader bufferedReader = new BufferedReader(reader)) {
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                sb.append(line);
            }
        }
        return sb.toString();
    }

}
