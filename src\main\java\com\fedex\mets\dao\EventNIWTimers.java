package com.fedex.mets.dao;

import jakarta.persistence.Column;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
//@AllArgsConstructor
public class EventNIWTimers {

    @Column(name = "EVENT_ID")
    private Integer eventId;

    @Column(name = "TIMER_ID")
    private String timerId;

    private Long totalAccumulatedTime;

    public EventNIWTimers(Integer eventId, String timerId, Long totalAccumulatedTime) {
        this.eventId = eventId;
        this.timerId = timerId;
        this.totalAccumulatedTime = totalAccumulatedTime;
    }
}
