
package com.fedex.mets.wsdl.emrStatus;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for GenericRequest complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="GenericRequest">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="headerType" type="{http://www.fedex.com/airops/schemas/Common.xsd}HeaderType"/>
 *         &lt;element name="session" type="{http://www.fedex.com/airops/schemas/Common.xsd}SessionType"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "GenericRequest", propOrder = {
    "headerType",
    "session"
})
@XmlSeeAlso({
    GetLatestFlightStatusRequest.class
})
public class GenericRequest {

    @XmlElement(required = true)
    protected HeaderType headerType;
    @XmlElement(required = true)
    protected SessionType session;

    /**
     * Gets the value of the headerType property.
     * 
     * @return
     *     possible object is
     *     {@link HeaderType }
     *     
     */
    public HeaderType getHeaderType() {
        return headerType;
    }

    /**
     * Sets the value of the headerType property.
     * 
     * @param value
     *     allowed object is
     *     {@link HeaderType }
     *     
     */
    public void setHeaderType(HeaderType value) {
        this.headerType = value;
    }

    /**
     * Gets the value of the session property.
     * 
     * @return
     *     possible object is
     *     {@link SessionType }
     *     
     */
    public SessionType getSession() {
        return session;
    }

    /**
     * Sets the value of the session property.
     * 
     * @param value
     *     allowed object is
     *     {@link SessionType }
     *     
     */
    public void setSession(SessionType value) {
        this.session = value;
    }

}
