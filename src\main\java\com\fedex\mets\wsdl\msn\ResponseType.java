
package com.fedex.mets.wsdl.msn;

import jakarta.xml.bind.annotation.XmlEnum;
import jakarta.xml.bind.annotation.XmlEnumValue;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for responseType.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="responseType">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *     &lt;enumeration value="Blocker"/>
 *     &lt;enumeration value="Error"/>
 *     &lt;enumeration value="Warn"/>
 *     &lt;enumeration value="Info"/>
 *     &lt;enumeration value="Debug"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 * 
 */
@XmlType(name = "responseType")
@XmlEnum
public enum ResponseType {

    @XmlEnumValue("Blocker")
    BLOCKER("Blocker"),
    @XmlEnumValue("Error")
    ERROR("Error"),
    @XmlEnumValue("Warn")
    WARN("Warn"),
    @XmlEnumValue("Info")
    INFO("Info"),
    @XmlEnumValue("Debug")
    DEBUG("Debug");
    private final String value;

    ResponseType(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static ResponseType fromValue(String v) {
        for (ResponseType c: ResponseType.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
