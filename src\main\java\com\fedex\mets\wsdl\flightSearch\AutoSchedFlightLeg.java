
package com.fedex.mets.wsdl.flightSearch;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for AutoSchedFlightLeg complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="AutoSchedFlightLeg">
 *   &lt;complexContent>
 *     &lt;extension base="{http://www.fedex.com/airops/schemas/FlightLeg.xsd}FlightLeg">
 *       &lt;sequence>
 *         &lt;element name="autoSchedAddLeg" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="autoSchedMaintStation" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="planStartDtTm" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AutoSchedFlightLeg", namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd", propOrder = {
    "autoSchedAddLeg",
    "autoSchedMaintStation",
    "planStartDtTm"
})
public class AutoSchedFlightLeg
    extends FlightLeg
{

    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    protected boolean autoSchedAddLeg;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd")
    protected boolean autoSchedMaintStation;
    @XmlElement(namespace = "http://www.fedex.com/airops/schemas/FlightLeg.xsd", required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar planStartDtTm;

    /**
     * Gets the value of the autoSchedAddLeg property.
     * 
     */
    public boolean isAutoSchedAddLeg() {
        return autoSchedAddLeg;
    }

    /**
     * Sets the value of the autoSchedAddLeg property.
     * 
     */
    public void setAutoSchedAddLeg(boolean value) {
        this.autoSchedAddLeg = value;
    }

    /**
     * Gets the value of the autoSchedMaintStation property.
     * 
     */
    public boolean isAutoSchedMaintStation() {
        return autoSchedMaintStation;
    }

    /**
     * Sets the value of the autoSchedMaintStation property.
     * 
     */
    public void setAutoSchedMaintStation(boolean value) {
        this.autoSchedMaintStation = value;
    }

    /**
     * Gets the value of the planStartDtTm property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getPlanStartDtTm() {
        return planStartDtTm;
    }

    /**
     * Sets the value of the planStartDtTm property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setPlanStartDtTm(XMLGregorianCalendar value) {
        this.planStartDtTm = value;
    }

}
