package com.fedex.mets.entity.mets;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Embeddable
public class EventMaxiDiscPk {
    @Column(name = "EVENT_ID", nullable = false)
    private Long eventId;

    @Column(name = "ATA", nullable = false)
    private String ata;

    @Column(name = "DISC_NUM", nullable = false)
    private String discNum;
}
