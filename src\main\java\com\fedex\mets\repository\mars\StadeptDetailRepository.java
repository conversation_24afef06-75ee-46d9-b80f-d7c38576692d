package com.fedex.mets.repository.mars;

import com.fedex.mets.entity.mars.Stadeptdetail;
import com.fedex.mets.entity.mars.StadeptdetailPk;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface StadeptDetailRepository extends JpaRepository<Stadeptdetail, StadeptdetailPk> {

    @Query(value="Select MOCC_MX_SPD_DIAL from STADEPTDETAIL where STA_ID=:eventStation \n" +
            "and DEPT_ID=:tempDeptId", nativeQuery = true)
    public String getMoccMxSpdDial(@Param("eventStation") String eventStation, @Param("tempDeptId") String tempDeptId);

    @Query(value="Select MX_PHONE, STA_RAMP_PHONE from STADEPTDETAIL where STA_ID=:eventStation \n" +
            "and DEPT_ID=:tempDeptId", nativeQuery = true)
    public String getStationDeptDetail(@Param("eventStation") String eventStation, @Param("tempDeptId") String tempDeptId);
}
