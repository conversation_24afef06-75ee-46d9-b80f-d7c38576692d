
package com.fedex.mets.wsdl.msn.shortagenotice;

import java.util.ArrayList;
import java.util.List;

import jakarta.xml.bind.annotation.*;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="status" type="{http://fedex.com/airops/maxi/services/jaxws}resultType"/>
 *         &lt;element name="header" type="{http://fedex.com/airops/maxi/services/jaxws}HeaderType"/>
 *         &lt;element name="shortageNoticeList" type="{http://fedex.com/airops/maxi/services/jaxws/material}shortageNoticeType" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "status",
    "header",
    "shortageNoticeList"
})
@XmlRootElement(name = "getShortageNoticeListResponse", namespace = "http://fedex.com/airops/maxi/services/jaxws/shortagenotice")
public class GetShortageNoticeListResponse {

    @XmlElement(namespace = "http://fedex.com/airops/maxi/services/jaxws/shortagenotice", required = true)
    protected ResultType status;
    @XmlElement(namespace = "http://fedex.com/airops/maxi/services/jaxws/shortagenotice", required = true)
    protected HeaderType header;
    @XmlElement(namespace = "http://fedex.com/airops/maxi/services/jaxws/shortagenotice")
    protected List<ShortageNoticeType> shortageNoticeList;

    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link ResultType }
     *     
     */
    public ResultType getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link ResultType }
     *     
     */
    public void setStatus(ResultType value) {
        this.status = value;
    }

    /**
     * Gets the value of the header property.
     * 
     * @return
     *     possible object is
     *     {@link HeaderType }
     *     
     */
    public HeaderType getHeader() {
        return header;
    }

    /**
     * Sets the value of the header property.
     * 
     * @param value
     *     allowed object is
     *     {@link HeaderType }
     *     
     */
    public void setHeader(HeaderType value) {
        this.header = value;
    }

    /**
     * Gets the value of the shortageNoticeList property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the shortageNoticeList property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getShortageNoticeList().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link ShortageNoticeType }
     * 
     * 
     */
    public List<ShortageNoticeType> getShortageNoticeList() {
        if (shortageNoticeList == null) {
            shortageNoticeList = new ArrayList<ShortageNoticeType>();
        }
        return this.shortageNoticeList;
    }

}
