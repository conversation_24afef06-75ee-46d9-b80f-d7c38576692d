package com.fedex.mets.entity.mets;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Column;
import jakarta.persistence.Id;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "EVENT_TYPE_HISTORY")
public class EventTypeHistory {
    @Id
    @Column(name = "EVENT_ID", nullable = false)
    private Integer eventId;

    @Column(name = "OLD_TYPE", length = 4)
    private String oldType;

    @Column(name = "NEW_TYPE", length = 4)
    private String newType;

    @Column(name = "CHANGE_DT_TM", nullable = false)
    private Timestamp changeDateTime;

    @Column(name = "CHANGED_BY", length = 10)
    private String changedBy;
}
