package com.fedex.mets.config;

import com.fedex.mets.wsdl.discrepancy.GenericRequest;
import com.fedex.mets.wsdl.discrepancy.GenericResponse;
import jakarta.xml.soap.MessageFactory;
import jakarta.xml.soap.SOAPConstants;
import jakarta.xml.soap.SOAPException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.ws.client.core.WebServiceTemplate;
import org.springframework.ws.client.support.interceptor.ClientInterceptor;
import org.springframework.ws.soap.saaj.SaajSoapMessageFactory;

@Configuration
public class MachSoapClientConfig {

    @Autowired
    private Environment env;

    @Bean
    public Jaxb2Marshaller app1marshaller() {
        Jaxb2Marshaller marshaller = new Jaxb2Marshaller();

        marshaller.setClassesToBeBound(
                GenericRequest.class,
                GenericResponse.class
        );
        return marshaller;
    }

    @Bean
    public WebServiceTemplate app1webServiceTemplate() throws SOAPException {
        WebServiceTemplate template = new WebServiceTemplate();
        template.setMarshaller(app1marshaller());
        template.setUnmarshaller(app1marshaller());
        template.setCheckConnectionForFault(true);
        MessageFactory msgFactory = MessageFactory.newInstance(SOAPConstants.SOAP_1_2_PROTOCOL);
        SaajSoapMessageFactory newSoapMessageFactory = new SaajSoapMessageFactory(msgFactory);

        template.setMessageFactory(app1messageFactory());

        template.setInterceptors(new ClientInterceptor[]{new SoapLoggingInterceptor()});
        template.setMessageFactory(newSoapMessageFactory);
        String URL=env.getProperty("mach-services-url");
        template.setDefaultUri(URL);
        return template;
    }


    @Bean
    public SaajSoapMessageFactory app1messageFactory() {
        SaajSoapMessageFactory messageFactory = new SaajSoapMessageFactory();
        messageFactory.afterPropertiesSet();
        return messageFactory;
    }


}
