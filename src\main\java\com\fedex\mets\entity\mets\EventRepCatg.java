package com.fedex.mets.entity.mets;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.sql.Timestamp;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "Event_Rep_Catg")
public class EventRepCatg {

    @EmbeddedId
    private EventRepCatgPk eventRepCatgPk;

    @Column(name = "LAST_UPDATED_DT_TM")
    private Timestamp lastUpdatedDtTm;

}