
package com.fedex.mets.wsdl.msn.shortagenotice;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for shortageNoticeShipperEventsType complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="shortageNoticeShipperEventsType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="date" type="{http://www.w3.org/2001/XMLSchema}date"/>
 *         &lt;element name="shippingType" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="airline" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="flight" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="etd" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="eta" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="fromStation" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="fromDept" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="toStation" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="toDept" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ict" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="etaChanged" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="type" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="isn" type="{http://www.w3.org/2001/XMLSchema}long"/>
 *         &lt;element name="foisEta" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="key" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="seqNbr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "shortageNoticeShipperEventsType", namespace = "http://fedex.com/airops/maxi/services/jaxws/material", propOrder = {
    "date",
    "shippingType",
    "airline",
    "flight",
    "etd",
    "eta",
    "fromStation",
    "fromDept",
    "toStation",
    "toDept",
    "ict",
    "etaChanged",
    "type",
    "isn",
    "foisEta",
    "key",
    "seqNbr"
})
public class ShortageNoticeShipperEventsType {

    @XmlElement(required = true)
    @XmlSchemaType(name = "date")
    protected XMLGregorianCalendar date;
    @XmlElement(required = true)
    protected String shippingType;
    @XmlElement(required = true)
    protected String airline;
    @XmlElement(required = true)
    protected String flight;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar etd;
    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar eta;
    @XmlElement(required = true)
    protected String fromStation;
    @XmlElement(required = true)
    protected String fromDept;
    @XmlElement(required = true)
    protected String toStation;
    @XmlElement(required = true)
    protected String toDept;
    @XmlElement(required = true)
    protected String ict;
    protected boolean etaChanged;
    @XmlElement(required = true)
    protected String type;
    protected long isn;
    @XmlElement(required = true)
    protected String foisEta;
    @XmlElement(required = true)
    protected String key;
    @XmlElement(required = true)
    protected String seqNbr;

    /**
     * Gets the value of the date property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getDate() {
        return date;
    }

    /**
     * Sets the value of the date property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setDate(XMLGregorianCalendar value) {
        this.date = value;
    }

    /**
     * Gets the value of the shippingType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getShippingType() {
        return shippingType;
    }

    /**
     * Sets the value of the shippingType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setShippingType(String value) {
        this.shippingType = value;
    }

    /**
     * Gets the value of the airline property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAirline() {
        return airline;
    }

    /**
     * Sets the value of the airline property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAirline(String value) {
        this.airline = value;
    }

    /**
     * Gets the value of the flight property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFlight() {
        return flight;
    }

    /**
     * Sets the value of the flight property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFlight(String value) {
        this.flight = value;
    }

    /**
     * Gets the value of the etd property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getEtd() {
        return etd;
    }

    /**
     * Sets the value of the etd property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setEtd(XMLGregorianCalendar value) {
        this.etd = value;
    }

    /**
     * Gets the value of the eta property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getEta() {
        return eta;
    }

    /**
     * Sets the value of the eta property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setEta(XMLGregorianCalendar value) {
        this.eta = value;
    }

    /**
     * Gets the value of the fromStation property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFromStation() {
        return fromStation;
    }

    /**
     * Sets the value of the fromStation property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFromStation(String value) {
        this.fromStation = value;
    }

    /**
     * Gets the value of the fromDept property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFromDept() {
        return fromDept;
    }

    /**
     * Sets the value of the fromDept property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFromDept(String value) {
        this.fromDept = value;
    }

    /**
     * Gets the value of the toStation property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getToStation() {
        return toStation;
    }

    /**
     * Sets the value of the toStation property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setToStation(String value) {
        this.toStation = value;
    }

    /**
     * Gets the value of the toDept property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getToDept() {
        return toDept;
    }

    /**
     * Sets the value of the toDept property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setToDept(String value) {
        this.toDept = value;
    }

    /**
     * Gets the value of the ict property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIct() {
        return ict;
    }

    /**
     * Sets the value of the ict property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIct(String value) {
        this.ict = value;
    }

    /**
     * Gets the value of the etaChanged property.
     * 
     */
    public boolean isEtaChanged() {
        return etaChanged;
    }

    /**
     * Sets the value of the etaChanged property.
     * 
     */
    public void setEtaChanged(boolean value) {
        this.etaChanged = value;
    }

    /**
     * Gets the value of the type property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getType() {
        return type;
    }

    /**
     * Sets the value of the type property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setType(String value) {
        this.type = value;
    }

    /**
     * Gets the value of the isn property.
     * 
     */
    public long getIsn() {
        return isn;
    }

    /**
     * Sets the value of the isn property.
     * 
     */
    public void setIsn(long value) {
        this.isn = value;
    }

    /**
     * Gets the value of the foisEta property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFoisEta() {
        return foisEta;
    }

    /**
     * Sets the value of the foisEta property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFoisEta(String value) {
        this.foisEta = value;
    }

    /**
     * Gets the value of the key property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKey() {
        return key;
    }

    /**
     * Sets the value of the key property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKey(String value) {
        this.key = value;
    }

    /**
     * Gets the value of the seqNbr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSeqNbr() {
        return seqNbr;
    }

    /**
     * Sets the value of the seqNbr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSeqNbr(String value) {
        this.seqNbr = value;
    }

}
