package com.fedex.mets.config;
import com.fedex.mets.util.DecryptionUtil;
import jakarta.persistence.EntityManagerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        basePackages = "com.fedex.mets.repository.rampview", // Repositories for Rampview
        entityManagerFactoryRef = "rampviewEntityManagerFactory",
        transactionManagerRef = "rampviewTransactionManager"
)
public class RampviewDataSourceConfig {


    @Value("${spring.datasource.rampview.password}")
    private String encryptedPassword;

    @Value("${spring.datasource.rampview.jdbc-url}")
    private String url;

    @Value("${spring.datasource.rampview.username}")
    private String username;

    @Value("${spring.datasource.rampview.driver-class-name}")
    private String driverClassName;

    @Bean(name = "rampviewDataSource")
    public DataSource rampviewDataSource() throws Exception {
        String decryptedPassword = DecryptionUtil.decrypt(encryptedPassword, "z76yf8ScxNFLZMbxC1YVRQ==");
        return DataSourceBuilder.create().username(username).url(url).driverClassName(driverClassName)
                .password(decryptedPassword).build();
    }

    @Bean(name = "rampviewEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean rampviewEntityManagerFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("rampviewDataSource") DataSource dataSource) {
        return builder
                .dataSource(dataSource)
                .packages("com.fedex.mets.entity.rampview") // Entities for Rampview
                .persistenceUnit("rampview") // Persistence unit name for Rampview
                .build();
    }

    @Bean(name = "rampviewTransactionManager")
    public PlatformTransactionManager rampviewTransactionManager(
            @Qualifier("rampviewEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        return new JpaTransactionManager(entityManagerFactory);
    }
}
