package com.fedex.mets.entity.mets;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name="FLT_TYPE")
public class FlightType {
    @Id
    @Column(name="CODE")
    char Code;

    @Column(name="DESCRIPTION")
    String Description;
}
