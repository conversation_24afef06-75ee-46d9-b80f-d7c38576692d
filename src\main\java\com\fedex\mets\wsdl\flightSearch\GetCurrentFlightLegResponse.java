
package com.fedex.mets.wsdl.flightSearch;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;
import com.fedex.mets.wsdl.discrepancy.GenericResponse;

/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;extension base="{http://www.fedex.com/airops/schemas/Common.xsd}GenericResponse">
 *       &lt;sequence>
 *         &lt;element name="currentLeg" type="{http://www.fedex.com/airops/schemas/FlightLeg.xsd}FlightLeg"/>
 *         &lt;element name="isEMR" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "currentLeg",
    "isEMR"
})
@XmlRootElement(name = "getCurrentFlightLegResponse",namespace="http:///www.fedex.com/airops/schemas/Mach")
public class GetCurrentFlightLegResponse
    extends GenericResponse
{

    @XmlElement(required = true)
    protected FlightLeg currentLeg;
    @XmlElement(defaultValue = "false")
    protected boolean isEMR;

    /**
     * Gets the value of the currentLeg property.
     * 
     * @return
     *     possible object is
     *     {@link FlightLeg }
     *     
     */
    public FlightLeg getCurrentLeg() {
        return currentLeg;
    }

    /**
     * Sets the value of the currentLeg property.
     * 
     * @param value
     *     allowed object is
     *     {@link FlightLeg }
     *     
     */
    public void setCurrentLeg(FlightLeg value) {
        this.currentLeg = value;
    }

    /**
     * Gets the value of the isEMR property.
     * 
     */
    public boolean isIsEMR() {
        return isEMR;
    }

    /**
     * Sets the value of the isEMR property.
     * 
     */
    public void setIsEMR(boolean value) {
        this.isEMR = value;
    }

}
