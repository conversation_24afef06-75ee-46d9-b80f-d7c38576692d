
package com.fedex.mets.wsdl.emrStatus;

import jakarta.xml.bind.annotation.*;


/**
 * <p>Java class for SessionType complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="SessionType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="userId" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="token" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="authSourceSysName" type="{http://www.fedex.com/airops/schemas/Common.xsd}authSourceSysType"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SessionType", propOrder = {
    "userId",
    "token",
    "authSourceSysName"
})
@XmlRootElement(name="session",namespace="http://www.fedex.com/airops/schemas/Common.xsd")
public class SessionType {

    @XmlElement(required = true)
    protected String userId;
    @XmlElement(required = true)
    protected String token;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected AuthSourceSysType authSourceSysName;

    /**
     * Gets the value of the userId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getUserId() {
        return userId;
    }

    /**
     * Sets the value of the userId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUserId(String value) {
        this.userId = value;
    }

    /**
     * Gets the value of the token property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getToken() {
        return token;
    }

    /**
     * Sets the value of the token property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setToken(String value) {
        this.token = value;
    }

    /**
     * Gets the value of the authSourceSysName property.
     * 
     * @return
     *     possible object is
     *     {@link AuthSourceSysType }
     *     
     */
    public AuthSourceSysType getAuthSourceSysName() {
        return authSourceSysName;
    }

    /**
     * Sets the value of the authSourceSysName property.
     * 
     * @param value
     *     allowed object is
     *     {@link AuthSourceSysType }
     *     
     */
    public void setAuthSourceSysName(AuthSourceSysType value) {
        this.authSourceSysName = value;
    }

}
