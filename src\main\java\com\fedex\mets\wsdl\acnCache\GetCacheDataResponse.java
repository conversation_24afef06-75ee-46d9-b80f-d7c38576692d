
package com.fedex.mets.wsdl.acnCache;

import java.util.ArrayList;
import java.util.List;

import com.fedex.mets.wsdl.discrepancy.AcnAircraftDetail;
import com.fedex.mets.wsdl.discrepancy.GenericResponse;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;extension base="{http://www.fedex.com/airops/schemas/Common.xsd}GenericResponse">
 *       &lt;sequence>
 *         &lt;choice>
 *           &lt;element name="acnAircraftDetails" type="{http:///www.fedex.com/airops/schemas/Mach}acnAircraftDetail" maxOccurs="unbounded" minOccurs="0"/>
 *           &lt;element name="ataDetails" type="{http:///www.fedex.com/airops/schemas/Mach}ataDetail" maxOccurs="unbounded" minOccurs="0"/>
 *           &lt;element name="fleetSeriesDetails" type="{http:///www.fedex.com/airops/schemas/Mach}fleetAndSeriesDetail" maxOccurs="unbounded" minOccurs="0"/>
 *           &lt;element name="reportedBy" type="{http://www.fedex.com/airops/schemas/Common.xsd}employeeInfo" maxOccurs="unbounded" minOccurs="0"/>
 *           &lt;element name="enteredBy" type="{http://www.fedex.com/airops/schemas/Common.xsd}employeeInfo" maxOccurs="unbounded" minOccurs="0"/>
 *           &lt;element name="amtEmployee" type="{http://www.fedex.com/airops/schemas/Common.xsd}employeeInfo" maxOccurs="unbounded" minOccurs="0"/>
 *           &lt;element name="station" type="{http:///www.fedex.com/airops/schemas/Mach}stationType" maxOccurs="unbounded" minOccurs="0"/>
 *           &lt;element name="troubleShtActionId" type="{http://www.fedex.com/airops/schemas/AcnDiscrepancy.xsd}Action" maxOccurs="unbounded" minOccurs="0"/>
 *           &lt;element name="loanBorAirline" type="{http:///www.fedex.com/airops/schemas/Mach}airlineType" maxOccurs="unbounded" minOccurs="0"/>
 *           &lt;element name="acMelDetails" type="{http://www.fedex.com/airops/schemas/AircraftCapability.xsd}AcMelDetailByFleet" maxOccurs="unbounded" minOccurs="0"/>
 *           &lt;element name="tfoaCategorys" type="{http://www.w3.org/2001/XMLSchema}string" maxOccurs="unbounded" minOccurs="0"/>
 *         &lt;/choice>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "acnAircraftDetails"
})
@XmlRootElement(name = "getCacheDataResponse",namespace="http:///www.fedex.com/airops/schemas/Mach")
public class GetCacheDataResponse
    extends GenericResponse
{

    protected List<AcnAircraftDetail> acnAircraftDetails;

    /**
     * Gets the value of the acnAircraftDetails property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the acnAircraftDetails property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getAcnAircraftDetails().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link AcnAircraftDetail }
     * 
     * 
     */
    public List<AcnAircraftDetail> getAcnAircraftDetails() {
        if (acnAircraftDetails == null) {
            acnAircraftDetails = new ArrayList<AcnAircraftDetail>();
        }
        return this.acnAircraftDetails;
    }

}
