package com.fedex.mets.controller;

import com.fedex.mets.data.WizardEventData;
import com.fedex.mets.dto.MetsResponse;
import com.fedex.mets.service.addEvent.EventEntryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;

//@CrossOrigin(origins = "*") // Allows all origins
@RestController
@RequestMapping("/api/mets")
@Tag(name = "Mets Event Entry", description = "Endpoint for adding an event.")
public class MetsEventEntryController {
    private static final Logger logger = LoggerFactory.getLogger(MetsEventEntryController.class);

    @Autowired
    private EventEntryService eventEntryService;

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    @Operation(summary = "Adding an event.", description = "Adding a new event to METS system.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully added a new event."),
            @ApiResponse(responseCode = "500", description = "Internal server error", content = @Content)
    })
    @PostMapping("/addEvent")
    public ResponseEntity<MetsResponse> addEvent(@RequestBody WizardEventData request) {
        MetsResponse addEventResponse = new MetsResponse();
        HashMap hashmap = new HashMap();
        try {
            hashmap=eventEntryService.addEvent(request);
            addEventResponse.setData(hashmap);
            return ResponseEntity.ok(addEventResponse);
        }
        catch (IllegalArgumentException e) {
            logger.error("Invalid input for adding Event: Wizard Event: {}. Error: {}", request, e.getMessage());
            return ResponseEntity.badRequest().build();
        }
        catch (Exception e) {
            logger.error("Error adding an event", e);
            return ResponseEntity.status(500).build();
        }
    }
}
