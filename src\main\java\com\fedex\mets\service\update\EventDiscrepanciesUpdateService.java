package com.fedex.mets.service.update;

import com.fedex.mets.data.EventDiscrepancyList;
import com.fedex.mets.data.EventDiscrepancyListData;
import com.fedex.mets.data.MetsEventUpdateEntity;
import com.fedex.mets.entity.mets.EventMaxiDisc;
import com.fedex.mets.entity.mets.EventMaxiDiscPk;
import com.fedex.mets.entity.mets.EventMaxiDwningItm;
import com.fedex.mets.repository.mets.EventMaxiDiscRepository;
import com.fedex.mets.repository.mets.EventMaxiDwningItmRepository;
import com.fedex.mets.service.retrieval.EventDiscrepanciesService;
import com.fedex.mets.util.IServerConstants;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class EventDiscrepanciesUpdateService {
    private static final Logger logger = LoggerFactory.getLogger(EventDiscrepanciesUpdateService.class);
    @Autowired
    private EventDiscrepanciesService eventDiscrepanciesService;
    @Autowired
    private EventMaxiDiscRepository eventMaxiDiscRepository;
    @Autowired
    private EventMaxiDwningItmRepository eventMaxiDwningItmRepository;

    public Map<String, Object> addEventDiscrepancies(MetsEventUpdateEntity request, Map<String, Object> hashMap,
                                                     List<EventDiscrepancyListData> discrepancyListFromClient,  String accessLevel,String userId,String tokenId,Boolean eventActive) throws Exception {

        List<List<?>> elements = new ArrayList<>();
        List<EventDiscrepancyList> linkedDiscrepancyList = new ArrayList<>();
        List<EventDiscrepancyList> discrepancyList = new ArrayList<>();
        boolean resultFromBean = false, isEventActive = true;
        int eventId = 0;
        if (eventActive != null) {
            isEventActive = eventActive.booleanValue();
        }
        try {
            for (int disc = 0; disc < discrepancyListFromClient.size(); disc++) {
                EventDiscrepancyListData data = (EventDiscrepancyListData) discrepancyListFromClient.get(disc);
                logger.info("Begin adding discrepancy information to event with EVENT_ID: " + data.getEventId());
                eventId = data.getEventId();

                boolean modified = data.isModified();
                boolean linkedDiscrepancy = data.isLinkModified();
                logger.info("Was event discrepancy modfied? " + modified);
                logger.info("Was event a linkedDiscrepancy? " + linkedDiscrepancy);

                if (modified) {
                    if (!linkedDiscrepancy) {
                        resultFromBean = addEventDiscrepancyData(data);
                        logger.info("Added event discrepancy data success? " + resultFromBean);
                    } else {
                        resultFromBean = deleteEventDiscrepancyData(data);
                        logger.info("Delete event discrepancy data success? " + resultFromBean);
                    }
                }
            }
        } catch (Exception exec) {

            logger.warn("ERROR MetsUpdate Service addEventDiscrepancies() exec >> " + exec.getMessage());
            String msg = exec.getMessage();
            if (msg.trim().length() >= 3) {

                if (msg.substring(msg.length() - 3, msg.length()).equals("512")) {
                    hashMap.put(IServerConstants.ERROR, "512");
                } else {
                    hashMap.put(IServerConstants.ERROR, "Could not Alter DISCREPANCY record in Database " + exec.getMessage());
                }

            } else {
                hashMap.put(IServerConstants.ERROR, "Could not Alter DISCREPANCY record in Database " + exec.getMessage());
            }
        }

        if (resultFromBean) {
            String strEventId = "" + eventId;
            try {
                publishEventUpdate(eventId, IServerConstants.DISCREPANCY_UPDATE);
                logger.info("Published Event discrepancy update for event: " + eventId);
            } catch (Exception publish) {
                logger.warn("ERROR MetsUpdate Service addEventDiscrepancies() publish >> " + publish.getMessage());
            }

            try {
                elements = eventDiscrepanciesService.getDiscrepancies(strEventId,userId,tokenId);
            } catch (Exception exec) {

                logger.warn("ERROR MetsUpdate Service addEventDiscrepancies() exec1 >> " + exec.getMessage());
                hashMap.put(IServerConstants.ERROR, exec.getMessage());

            }
            logger.info("***********************************************"+elements.size());
            if (elements != null && elements.size() == 2) {
                linkedDiscrepancyList = (List) elements.get(0);
                discrepancyList = (List) elements.get(1);
            }

            for (int i = 0; i < linkedDiscrepancyList.size(); i++) {
                EventDiscrepancyList data = (EventDiscrepancyList) linkedDiscrepancyList.get(i);

                logger.info("==>" + data.getEventId()
                        + " -->> " + data.getAta() + " --> " + data.getNumber() + " --> " + data.getEventType()
                        + " --> " + data.getDiscType() + " --> " + data.getOpenDate() + " --> " + data.getOpenStation()
                        + " --> " + data.getText() + " && Link--> " + data.isLink() + " && Message--> " + data.getMessage());
            }

            logger.info("Details------------------ ");
            for (int j = 0; j < discrepancyList.size(); j++) {
                EventDiscrepancyList data = (EventDiscrepancyList) discrepancyList.get(j);

                logger.info("==>" + data.getEventId()
                        + " -->> " + data.getAta() + " --> " + data.getNumber() + " --> " + data.getEventType()
                        + " --> " + data.getDiscType() + " --> " + data.getOpenDate() + " --> " + data.getOpenStation()
                        + " --> " + data.getText() + " && Link--> " + data.isLink() + " && Message--> " + data.getMessage());
            }
        }

        hashMap.put(IServerConstants.EVENT_LINKED_DISCREPANCIES, linkedDiscrepancyList);
        hashMap.put(IServerConstants.DISCREPANCIES, discrepancyList);
        return hashMap;
    }

    public boolean addEventDiscrepancyData(EventDiscrepancyListData discrepancyData) throws Exception {
        logger.info("in the addEventDiscrepancyListData of the EventDiscrepanciesUpdateSessionBean bean..........");
        boolean result = false;

        int eventId = 0;
        String ata = "", discNumber = "", eventType = "";
        boolean isDowningItem = false;
        boolean isDowningModified= false;

        eventId = discrepancyData.getEventId();
        ata = discrepancyData.getAta();
        ata = ata.substring(0, 2) + "" + ata.substring(3, 5);

        discNumber = discrepancyData.getNumber();
        eventType = discrepancyData.getEventType();
        isDowningItem = discrepancyData.isDowningItem();
        isDowningModified =discrepancyData.isDowningModified();

        logger.info("eventId =" + eventId);
        logger.info("ata =" + ata);
        logger.info("discNumber =" + discNumber);
        logger.info("eventType =" + eventType);
        logger.info("isDowningItem =" + isDowningItem);
        logger.info("isDowningModified =" + isDowningModified);

        try {
            EventMaxiDisc newMaxiDisc = new EventMaxiDisc();
            EventMaxiDiscPk eventMaxiDiscPk = new EventMaxiDiscPk();
            eventMaxiDiscPk.setEventId((long) eventId);
            eventMaxiDiscPk.setAta(ata);
            eventMaxiDiscPk.setDiscNum(discNumber);
            newMaxiDisc.setEventMaxiDisckPk(eventMaxiDiscPk);
            newMaxiDisc.setType(eventType);
            eventMaxiDiscRepository.save(newMaxiDisc);
            result = true;

        } catch (Exception c) {
            logger.warn(" ERROR addEventDiscrepancyData() >>" + c.getMessage());
        }
        if (isDowningItem) {
            try {
                if(isDowningModified) {
                    try {
                        EventMaxiDwningItm eventMaxiDwningRecord = eventMaxiDwningItmRepository.getEventMaxiDwningItemByEventId(eventId);
                        logger.info("Deleting prior downing item for event: " + eventMaxiDwningRecord.getEventId());
                        eventMaxiDwningItmRepository.delete(eventMaxiDwningRecord);
                    } catch (Exception c) {
                        logger.warn(" ERROR Event Discrepancies addEventDiscrepancyData() remove prior downing item >> " + c.getMessage());
                    }
                }
                else {
                    try {
                        EventMaxiDwningItm newMaxiDwningItem = new EventMaxiDwningItm();
                        newMaxiDwningItem.setEventId((long) eventId);
                        newMaxiDwningItem.setAta(ata);
                        newMaxiDwningItem.setDiscNum(discNumber);
                        eventMaxiDwningItmRepository.save(newMaxiDwningItem);
                        result = (result && true);
                    } catch (Exception c) {

                        logger.warn(" ERROR Event Discrepancies addEventDiscrepancyData() add downing item >> " + c.getMessage());

                    }
                }

            } catch (Exception create) {

                logger.warn(" ERROR Event Discrepancies addEventDiscrepancyDowningItemData() create >> " + create.getMessage());
                throw new Exception("Record could not be inserted " + create.getMessage());
            }

        }

        return result;
    }

    public boolean deleteEventDiscrepancyData(EventDiscrepancyListData discrepancyData) throws Exception {
        logger.info("in the deleteEventDiscrepancyData of the EventDiscrepanciesUpdateSessionBean bean..........");
        boolean result = false;
        int eventId = 0;
        String ata = "", discNumber = "", eventType = "";

        eventId = discrepancyData.getEventId();
        ata = discrepancyData.getAta();

        ata = ata.substring(0, 2) + "" + ata.substring(3, 5);

        discNumber = discrepancyData.getNumber();
        eventType = discrepancyData.getEventType();

        logger.info("eventId	   =" + eventId);
        logger.info("ata        =" + ata);
        logger.info("discNumber =" + discNumber);
        logger.info("eventType  =" + eventType);


        try {
            EventMaxiDisc eventMaxiDiscRecord = eventMaxiDiscRepository.findMaxiDiscByEventIdAndAta(eventId, ata, Integer.parseInt(discNumber));
            eventMaxiDiscRepository.delete(eventMaxiDiscRecord);
            result = true;
        } catch (Exception c) {
            logger.warn(" ERROR Event Discrepancies deleteEventDiscrepancyData() remove >> " + c.getMessage());

        }
        return result;
    }

    public void publishEventFormUpdate(int eventId, String updateType, String flag, int sequenceNumber) throws Exception {
//			Session topicSession = null;
//			MessageProducer publisher = null;
//			TextMessage tMessage = null;
//
//			java.sql.Connection conn = null;
//			Statement stat = null;
//			ResultSet rs = null;
//			String queryString = "";
//			String strEventId = "", strACN = "";
//
//			try {
//				conn = getDataSource().getConnection();
//				stat = conn.createStatement();
//
//				queryString =
//					"Select EVENT_ID, ACN from EVENTS where EVENT_ID='" + eventId + "'";
//				getLogger().info("queryString=====================" + queryString);
//
//				rs = stat.executeQuery(queryString);
//				while (rs.next()) {
//					strEventId = rs.getString(1);
//					strACN = rs.getString(2);
//				}
//			} catch (Exception c) {
//				getLogger().warn(
//					" ERROR Event Discrepancies publishEventFormUpdate() >> "
//						+ c.getMessage());
//				//			c.printStackTrace();
//			} finally {
//				try {
//					if (rs != null)
//						rs.close();
//					if (stat != null)
//						stat.close();
//					if (conn != null)
//						conn.close();
//				} catch (Exception close) {
//					getLogger().warn(
//						" ERROR Event Discrepancies publishEventFormUpdate() closing connection >> "
//							+ close.getMessage());
//					//				close.printStackTrace();
//				}
//			}
//
//			try {
//				jmsConnection = reEstablishConnection();
//
//				publishFormMessage(
//					jmsConnection,
//					strEventId,
//					strACN,
//					updateType,
//					flag,
//					sequenceNumber);
//			} catch (RemoteException jmsException) {
//				getLogger().info("trying to reEstablish Connection.......... count ");
//				try {
//					jmsConnection = reEstablishConnection();
//
//					publishFormMessage(
//							jmsConnection,
//							strEventId,
//							strACN,
//							updateType,
//							flag,
//							sequenceNumber);
//				} catch (Exception reconnect) {
//					getLogger().warn(
//						" ERROR Event Discrepancies publishEventFormUpdate() reconnect >> "
//							+ reconnect.getMessage());
//					//				reconnect.printStackTrace();
//				}
//			} catch (Throwable t) {
//				context.setRollbackOnly();
//			}
    }


    public void publishEventUpdate(int eventId, String updateType) {
//			Connection conn = null;
//			Statement stat = null;
//			ResultSet rs = null;
//			String queryString = "";
//			String strEventId = "", strACN = "";
//
//			try {
//				conn = getDataSource().getConnection();
//				stat = conn.createStatement();
//
//				queryString =
//					"Select EVENT_ID, ACN from EVENTS where EVENT_ID='" + eventId + "'";
//				getLogger().info("queryString=====================" + queryString);
//
//				rs = stat.executeQuery(queryString);
//				while (rs.next()) {
//					strEventId = rs.getString(1);
//					strACN = rs.getString(2);
//				}
//			} catch (Exception c) {
//				getLogger().warn(
//					" ERROR Event Discrepancies publishEventUpdate() >> " + c.getMessage());
//				//			c.printStackTrace();
//			} finally {
//				try {
//					if (rs != null)
//						rs.close();
//					if (stat != null)
//						stat.close();
//					if (conn != null)
//						conn.close();
//				} catch (Exception close) {
//					getLogger().warn(
//						" ERROR Event Discrepancies publishEventUpdate() >> closing connection"
//							+ close.getMessage());
//					//				close.printStackTrace();
//				}
//			}
//
//			try {
//				jmsConnection = reEstablishConnection();
//
//				publishMessage(jmsConnection, strEventId, strACN, updateType);
//			} catch (RemoteException jmsException) {
//				getLogger().info("trying to reEstablish Connection.......... count ");
//				try {
//					jmsConnection = reEstablishConnection();
//
//					publishMessage(jmsConnection, strEventId, strACN, updateType);
//				} catch (Exception reconnect) {
//					getLogger().warn(
//						" ERROR Event Discrepancies publishEventUpdate()reconnect >> "
//							+ reconnect.getMessage());
//					//				reconnect.printStackTrace();
//				}
//			} catch (Throwable t) {
//				context.setRollbackOnly();
//			}
    }

    public void securityCheck(String userId, String tokenId, boolean isEventActive) throws Exception {

        try {

            List<Object> acessFlagList = null;

            if (userId != null && tokenId != null) {
                try {

//					acessFlagVector = SecurityHelper.getAccessFlags(userId, tokenId, strTransactionId);
                    acessFlagList = new ArrayList<Object>();
                    acessFlagList.add("SUCCESS");
                    acessFlagList.add("80");

                } catch (Exception e) {
                    logger.warn("ERROR SecurityHelper.getAccessFlags exception " + e.getMessage());
                    throw new Exception("512");
                }
            }

            //for Start/Stop Timer check access flag 1 is 80 or 90 else throw exception
            if (acessFlagList != null) {
                String firstElement = (String) acessFlagList.get(0);

                if (firstElement.trim().equals("SUCCESS")) {
                    if (isEventActive) {
                        String strSecurityAccess = (String) acessFlagList.get(1);

                        if (strSecurityAccess.trim().equals("80") || strSecurityAccess.trim().equals("90")) {

                            logger.info(" ");
                            logger.info("User " + userId + " has access to START/STOP Timer of an Event.");

                        } else {
                            throw new Exception("User does not have permission/access to START/STOP Timer of to an Event.");
                        }

                    } else {
                        String strSecurityAccess = (String) acessFlagList.get(2);

                        if (strSecurityAccess.trim().equals("99")) {

                            logger.info(" ");
                            logger.info("User " + userId + " has access to START/STOP Timer of an Event.");

                        } else {
                            throw new Exception("User does not have permission/access to to START/STOP Timer of an Inactive Event.");
                        }
                    }
                } else {

                    if (firstElement.trim().equals("512")) {
                        throw new Exception(firstElement);
                    } else {
                        String strSecurityAccessError = (String) acessFlagList.get(1);
                        throw new Exception(strSecurityAccessError);
                    }

                }
            } else if (acessFlagList == null) {
                throw new Exception("User does not have permission/access to START/STOP Timer of an Event.");
            }

        } catch (Exception securityRemote) {

            logger.warn(" ERROR securityRemote >> " + securityRemote.getMessage());
            throw new Exception(securityRemote.getMessage());

        }

    }

}
