package com.fedex.mets.service.retrieval;

import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

import com.fedex.mets.data.EventEmailData;
import com.fedex.mets.entity.mets.EventTfNotes;
import com.fedex.mets.repository.mets.EventTfNotesRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import java.util.*;

public class TfNotesEmailServiceTest {

    @Mock
    private EventTfNotesRepository tfNotesRepo;

    @Mock
    private JavaMailSender mailSender;

    @InjectMocks
    private TfNotesEmailService tfNotesEmailService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testSendMail_Success() {
        EventEmailData eventEmailData = new EventEmailData();
        eventEmailData.setEmailSubject("Test Subject");
        eventEmailData.setEmailBody("Test Body");
        eventEmailData.setEmailFrom("<EMAIL>");
        eventEmailData.setEmailTo("<EMAIL>");
        eventEmailData.setEmailEventId(1);
        eventEmailData.setEmpId("123");
        eventEmailData.setEmpName("Test User");
        eventEmailData.setDepartment("Test Department");

        doNothing().when(mailSender).send(any(SimpleMailMessage.class));
        when(tfNotesRepo.getMaxNoteId(eventEmailData.getEmailEventId())).thenReturn(1);
        when(tfNotesRepo.save(any(EventTfNotes.class))).thenReturn(new EventTfNotes());

        boolean result = tfNotesEmailService.sendMail(eventEmailData);


        assertTrue(result);
    }

    @Test
    public void testSendMail_Exception() {

        EventEmailData eventEmailData = new EventEmailData();
        eventEmailData.setEmailSubject("Test Subject");
        eventEmailData.setEmailBody("Test Body");
        eventEmailData.setEmailFrom("<EMAIL>");
        eventEmailData.setEmailTo("<EMAIL>");

        doThrow(new RuntimeException("Mail error")).when(mailSender).send(any(SimpleMailMessage.class));

        boolean result = tfNotesEmailService.sendMail(eventEmailData);


        assertFalse(result);
    }

    @Test
    public void testAddTFNotes_Success() {
        EventEmailData eventEmailData = new EventEmailData();
        eventEmailData.setEmailEventId(1);
        eventEmailData.setEmpId("123");
        eventEmailData.setEmpName("Test User");
        eventEmailData.setDepartment("Test Department");
        String tfNote = "Test Note";

        when(tfNotesRepo.getMaxNoteId(eventEmailData.getEmailEventId())).thenReturn(1);
        when(tfNotesRepo.save(any(EventTfNotes.class))).thenReturn(new EventTfNotes());

        boolean result = tfNotesEmailService.addTFNotes(eventEmailData, tfNote);

        assertTrue(result);
    }

    @Test
    public void testAddTFNotes_Exception() {
        EventEmailData eventEmailData = new EventEmailData();
        eventEmailData.setEmailEventId(1);
        String tfNote = "Test Note";

        when(tfNotesRepo.getMaxNoteId(eventEmailData.getEmailEventId())).thenThrow(new RuntimeException("Repository error"));

        boolean result = tfNotesEmailService.addTFNotes(eventEmailData, tfNote);

        assertFalse(result);
    }
}
