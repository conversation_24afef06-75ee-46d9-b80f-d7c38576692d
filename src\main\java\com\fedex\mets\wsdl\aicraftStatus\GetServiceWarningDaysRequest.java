package com.fedex.mets.wsdl.aicraftStatus;


import com.fedex.mets.wsdl.discrepancy.GenericRequest;
import jakarta.xml.bind.annotation.*;


/**
 * <p>Java class for anonymous complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;extension base="{http://www.fedex.com/airops/schemas/Common.xsd}GenericRequest">
 *       &lt;sequence>
 *         &lt;element name="acn" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "acn"
})
@XmlRootElement(name = "getServiceWarningDaysRequest",namespace="http:///www.fedex.com/airops/schemas/Mach")
public class GetServiceWarningDaysRequest
        extends GenericRequest {

    @XmlElement(required = true)
    protected String acn;

    /**
     * Gets the value of the acn property.
     *
     * @return possible object is
     * {@link String }
     */
    public String getAcn() {
        return acn;
    }

    /**
     * Sets the value of the acn property.
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setAcn(String value) {
        this.acn = value;
    }

}