package com.fedex.mets.data;
import lombok.*;

import java.io.*;
import java.math.BigDecimal;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class EventDiscrepancyList implements Serializable{
	public int eventId;
	public boolean link=false;
	public String ata;
	public String number;
	public String discType;
	public String eventType;
	public String openDate;
	public String openStation;
	public boolean inWork=false;
	public String closed;
	public String[] text;
	public String[] message;
	public String status;
	public boolean isModified=false;
	public boolean isLinkModified=false;
	public boolean isDowningModified=false;
	public boolean isDowningItem=false;
	public String priority;
	public String timeRemaining;
	public BigDecimal discrepancyOid;
}