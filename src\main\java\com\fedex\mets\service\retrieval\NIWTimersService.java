package com.fedex.mets.service.retrieval;

import com.fedex.mets.dao.EventNIWTimers;
import com.fedex.mets.dto.MetsRequest;
import com.fedex.mets.data.EventNIWTimersDataEntity;
import com.fedex.mets.data.NIWTimersDataEntity;
import com.fedex.mets.dto.NiwTimersResponse;
import com.fedex.mets.entity.mets.EventTimers;
import com.fedex.mets.entity.mets.Timers;
import com.fedex.mets.repository.mets.EventTimersRepository;
import com.fedex.mets.repository.mets.TimersRepository;
import com.fedex.mets.util.IServerConstants;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@SuppressWarnings("unused")
@Service
@RequiredArgsConstructor
public class NIWTimersService {
    private static final Logger logger = LoggerFactory.getLogger(NIWTimersService.class);
    @Autowired
    private TimersRepository timersRepository;

    @Autowired
    private EventTimersRepository eventTimersRepository;

    /**
     * Private method to get the Not In Work Timers for a particular event.
     *
     * @return hashmap containing NIWTimers Object.
     * @params EventId.
     */
    @SuppressWarnings("unchecked")
    public NiwTimersResponse getEventNIWTimers(
            String eventId) {
        NiwTimersResponse niwTimersResponse = new NiwTimersResponse();
        List<List<?>> elements = new ArrayList<>();
        List<EventTimers> timerDataList = new ArrayList<EventTimers>();
        List<EventNIWTimersDataEntity> eventActiveTimerDataList = new ArrayList<EventNIWTimersDataEntity>();
        List<EventNIWTimersDataEntity> eventTimerDataList = new ArrayList<EventNIWTimersDataEntity>();

        try {
            logger.info("==> before calling the getNIWTimers()");
            elements = getNIWTimers(eventId);

            if (elements != null) {
                timerDataList = (List<EventTimers>) elements.get(0);
                eventActiveTimerDataList = (List<EventNIWTimersDataEntity>) elements.get(1);
                eventTimerDataList = (List<EventNIWTimersDataEntity>) elements.get(2);
            }
            niwTimersResponse.setTimerDataList(timerDataList);
            niwTimersResponse.setEventActiveTimerDataList(eventActiveTimerDataList);
            niwTimersResponse.setEventTimerDataList(eventTimerDataList);
        } catch (Exception e) {
            logger.warn(
                    "ERROR MetsRetrieval Servlet getNIWTimers() remote exception >> "
                            + e.getMessage());
        }
        return niwTimersResponse;
    }


    /**
     * Private method to get the Not In Work Timer Details for a particular event & Timer.
     *
     * @return hashMap containing NIWTimerDetail Object.
     * @params EventId, timerId.
     */
    public NiwTimersResponse getNIWTimerDetail(
            String eventId,
            String timerId) {
        NiwTimersResponse niwTimerResponse = new NiwTimersResponse();
        List<EventTimers> elements = new ArrayList<>();

        try {
            logger.info("before calling the getNIWTimerDetails()");
            elements = getNIWTimerDetails(eventId, timerId);
            niwTimerResponse.setTimerDataList(elements);

        } catch (Exception e) {
            logger.warn(
                    "ERROR MetsRetrieval Servlet getNIWTimerDetails() remote exception >> "
                            + e.getMessage());
        }
        return niwTimerResponse;

    }


    public List<List<?>> getNIWTimers(String eventId) throws Exception {

        List<List<?>> resultList = new ArrayList<>();
        List<Timers> timerDataList = new ArrayList<>();
        List<EventTimers> eventActiveTimerDataList = new ArrayList<>();
        List<EventNIWTimers> eventTimerDataList = new ArrayList<>();


        try {
            timerDataList = timersRepository.getTimers();

        } catch (Exception e) {
            logger.warn(" ERROR NIW Timers getNIWTimers() >> " + e.getMessage());
            throw new Exception("getNIWTimers() Problem finding the NIW Timers Data");
        }
        try {
            eventActiveTimerDataList = eventTimersRepository.getEventActiveTimers(Integer.parseInt(eventId));
        } catch (Exception ee) {
            logger.warn(" ERROR NIW Timers getNIWTimers() ee>> " + ee.getMessage());
            throw new Exception("getNIWTimers() Problem finding the Event NIW Timers Data");
        }
        resultList.add(timerDataList);
        resultList.add(eventActiveTimerDataList);
        resultList.add(eventTimerDataList);
        return resultList;
    }

    /**
     * The following getNIWTimerDetails() is used to retreive the detail of a NIW
     * TImer.
     *
     * @return List of Not In Work Timers data.
     * @params String eventId, String timerId.
     */
    public List<EventTimers> getNIWTimerDetails(String eventId, String timerId) {
        List<EventTimers> timerDetailList = new ArrayList<>();

        try {
            timerDetailList = eventTimersRepository.getNIWTimerDetails(eventId, timerId);
        } catch (Exception ee) {
            ee.printStackTrace();
            logger.warn(" ERROR NIW Timers getNIWTimerDetails() >> " + ee.getMessage());
        }
        return timerDetailList;
    }
}

