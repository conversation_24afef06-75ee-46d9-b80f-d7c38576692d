package com.fedex.mets.entity.mets;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "event_flt_delays")
public class EventFlightDelays {
    @Id
    @Column(name = "event_id")
    public Integer eventId;

    @Column(name = "delay_code")
    public String delayCode;

    @Column(name = "DELAY_TIME")
    public Integer delayTime;
}
