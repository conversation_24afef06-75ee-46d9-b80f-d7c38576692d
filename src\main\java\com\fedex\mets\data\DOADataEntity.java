package com.fedex.mets.data;

import lombok.*;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class DOADataEntity {
	
	private int	eventId;	
	private String doaOriginator;
	private String createdAt;
	private boolean checkFlightRequrired;
	private String comment;
	private String flightNumber;
	private String flightDate;
	private String flightLegNumber;
	private String destination;
	private String estimatedTimeOfArrival;
	private List<Object> discList;
	private String additionalDescription;
	private String closedBy;
	private String closedAt;
	private boolean maintenanceCrew;
	private String lastUpdated;

	
}
